/*
 * SGI IOC3 master driver and IRQ demuxer
 *
 * Copyright (c) 2005 Stanislaw <PERSON>w<PERSON> <<EMAIL>>
 * Heavily based on similar work by:
 *   <PERSON> <b<PERSON><EMAIL>> - IOC4 master driver
 *   <PERSON> <<EMAIL>> - IOC3 serial port IRQ demuxer
 */

#include <linux/errno.h>

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/types.h>
#include <linux/netdevice.h>
#include <linux/skbuff.h>
#include <linux/netfilter_ipv4.h>
#include <linux/inet.h>
#include <linux/in.h>
#include <linux/ip.h>
#include <linux/tcp.h>
#include <linux/time.h>
#include <net/netlabel.h> //add by svk
#include <linux/ieee80211.h>
#include "speedlimit.h"
#ifdef CONFIG_IPV6_PORTAL
#include <linux/netfilter_ipv6.h>
#include <linux/ipv6.h>
#include <net/ipv6.h>
#include <linux/in6.h>
#include <net/ip6_checksum.h>
#endif

extern int init_procfs_cjPortal(void);
extern void cleanup_procfs_cjportal(void);
extern int checkNew(CLIENT_LIST *cli);
extern int findIp(unsigned short port);
extern void saveIp(unsigned short port, int ip);
extern void clientListInit(void);
extern CLIENT_LIST * inNewList(CLIENT_LIST *cli);
extern void move2AuthList(CLIENT_LIST *cli);
extern CLIENT_LIST * inAuthList(CLIENT_LIST *cli);
extern void add2Auth(CLIENT_LIST *cli);
//extern struct mutex clientMutex;
extern atomic_t clientMutex;


IPADR localIP = {
    .char_ip[0] = 192,
    .char_ip[1] = 168,
    .char_ip[2] = 100,
    .char_ip[3] = 0
};

IPADR localIPMask = {
    .char_ip[0] = 255,
    .char_ip[1] = 255,
    .char_ip[2] = 255,
    .char_ip[3] = 0
};

IPADR localGateway = {
    .char_ip[0] = 192,
    .char_ip[1] = 168,
    .char_ip[2] = 100,
    .char_ip[3] = 1
};

#ifdef CONFIG_IPV6_PORTAL
// IPv6 local network configuration
struct in6_addr localIPv6 = {
    .s6_addr32 = {
        htonl(0xfe800000), htonl(0x00000000),
        htonl(0x00000000), htonl(0x00000000)
    }
};

struct in6_addr localIPv6Mask = {
    .s6_addr32 = {
        htonl(0xffff0000), htonl(0x00000000),
        htonl(0x00000000), htonl(0x00000000)
    }
};

struct in6_addr localGatewayv6 = {
    .s6_addr32 = {
        htonl(0xfe800000), htonl(0x00000000),
        htonl(0xa82ea4ff), htonl(0xfeedf062)
    }
};

// IPv6 macros for local network detection - Linux 3.4.x compatible
#define LOCAL_V6(ip6) (ipv6_masked_addr_cmp(&localIPv6, &localIPv6Mask, ip6) == 0)
#define GATEWAY_V6(ip6) (ipv6_addr_equal_compat(ip6, &localGatewayv6))

// IPv6 whitelist
struct in6_addr whitelist_v6[MAX_WHITELIST_IP];
int whitelist_v6_count = 0;
#endif

// IPv4 whitelist - 动态管理
__be32 whitelist_ipv4[MAX_WHITELIST_IP];
int whitelist_ipv4_count = 0;

#define DEBUG_NF 1

#define LOCAL(ip) (localIP.int_ip == (localIPMask.int_ip & ip)? 1:0)
#define GATEWAY(ip) ((((~localIPMask.int_ip) & localGateway.int_ip) == ((~localIPMask.int_ip) & ip))? 1:0)

int mytime(void) {
    struct timeval t1;
    do_gettimeofday(&t1);
    return t1.tv_sec;
}


unsigned short calcCheckSum(void *buf, int len,unsigned int additional)
// len is taken granted times of 2....
{
    unsigned int m,i,k=additional;
    unsigned short *j;
    j = (unsigned short *)buf;
    for(i=0; i<(len+1)>>1; i++) {
        k += j[i];
    }
    while(k&0xFFFF0000) {
        m = (k&0xFFFF0000u)>>16;
        k &= 0x0000FFFF;
        k+=m;
    }
    k = ~k;
    return k;
}

void calcIphChecksum(struct iphdr *iph)
{
    iph->check = 0;
    iph->check = calcCheckSum((void*)iph,iph->ihl*4,0);
}

void calcThdChecksum(struct iphdr* iph,struct tcphdr *thd)
{
    int i;
    unsigned int m,k=0;
    unsigned short *j;
    struct psd_header psd;

    psd.daddr = iph->daddr;
    psd.saddr = iph->saddr;
    psd.mbz = 0;
    psd.ptcl = 6;
    psd.tcpl = htons(ntohs(iph->tot_len)-iph->ihl*4);

//	printk("iph->ihl :%d, iph->tot-len: %d tcpl %d  psd header len %d total len %d\n",iph->ihl,ntohs(iph->tot_len),ntohs(psd.tcpl),sizeof(struct psd_header),sizeof(struct psd_header)+sizeof(struct tcphdr));

    thd->check = 0;

//	printk("\ntcp header:");
    for (i = ntohs(iph->tot_len)-iph->ihl*4,j=(unsigned short *)thd; i>1; j++,i-=2) {
//		printk("%04x ",ntohs(*j));
        k+=htons(*j);
    }
    if(i) {
//		printk("%02x ",*((unsigned char *)j));
        k+= htons((unsigned short)(*((unsigned char *)j)));
    }

//printk("\npsedo header:");
    for(j=(unsigned short *)&psd,i=0; i<sizeof(struct psd_header)/2; i++) {
//		printk("%04x ",htons(j[i]));
        k+= htons(j[i]);
    }

    while(k&0xFFFF0000) {
        m = (k&0xFFFF0000u)>>16;
        k &= 0x0000FFFF;
        k+=m;
    }
    k = ~k;
//	printk("\n check sum:%04x",k);
    thd->check =htons(k);

}

#ifdef CONFIG_IPV6_PORTAL
static int ipv6_addr_equal_compat(const struct in6_addr *a1, const struct in6_addr *a2)
{
    return memcmp(a1, a2, sizeof(struct in6_addr)) == 0;
}

// IPv6 loopback check - Linux 3.4.x compatible
static int ipv6_addr_loopback_compat(const struct in6_addr *a)
{
    return (a->s6_addr32[0] | a->s6_addr32[1] | a->s6_addr32[2] | (a->s6_addr32[3] ^ htonl(1))) == 0;
}

// IPv6 multicast check - Linux 3.4.x compatible
static int ipv6_addr_is_multicast_compat(const struct in6_addr *a)
{
    return (a->s6_addr[0] & 0xff) == 0xff;
}

// Get IPv6 header - Linux 3.4.x compatible
static struct ipv6hdr *ipv6_hdr_compat(const struct sk_buff *skb)
{
    return (struct ipv6hdr *)skb_network_header(skb);
}
#endif

// IPv4 白名单管理函数
int add_ipv4_whitelist(const char *ip_str)
{
    __be32 ip_addr;
    int i;

    if (!ip_str || whitelist_ipv4_count >= MAX_WHITELIST_IP) {
        printk("IPv4 whitelist is full or invalid IP string\n");
        return -1;
    }

    ip_addr = in_aton(ip_str);
    if (ip_addr == 0) {
        printk("Invalid IPv4 address: %s\n", ip_str);
        return -1;
    }

    // 检查是否已存在
    for (i = 0; i < whitelist_ipv4_count; i++) {
        if (whitelist_ipv4[i] == ip_addr) {
            printk("IPv4 address %s already in whitelist\n", ip_str);
            return 0;
        }
    }

    whitelist_ipv4[whitelist_ipv4_count] = ip_addr;
    whitelist_ipv4_count++;

    printk("Added IPv4 %s to whitelist (total: %d)\n", ip_str, whitelist_ipv4_count);
    return 0;
}

void clear_ipv4_whitelist(void)
{
    whitelist_ipv4_count = 0;
    printk("IPv4 whitelist cleared\n");
}

void init_ipv4_whitelist(void)
{
    // 初始化 IPv4 白名单，添加一些默认地址
    whitelist_ipv4_count = 0;

    // 添加默认的白名单地址
    add_ipv4_whitelist("***********");      // 电信认证域名的IP
    add_ipv4_whitelist("***************");  // wuxing
    add_ipv4_whitelist("**************");   // wuxing
    add_ipv4_whitelist("*************");    // wuxing
    add_ipv4_whitelist("*************");    // wuxing
    add_ipv4_whitelist("**************");   // wuxing

    printk("IPv4 whitelist initialized with %d entries\n", whitelist_ipv4_count);
}


// ip is not realible to tell the new arrival, use for first version, should change to MAC in future. :)
// the second problem is the expiration of the IP/MAC. should save the arrival of new ip?

time_t curTime = 0;

int totalPackets = 0;
int registered = 0;

static unsigned int preRouting(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    __be32 sip,dip;
    time_t cur;

    // 使用动态白名单进行检查

    if(skb) {
        struct iphdr *iph;
        CLIENT_LIST *cli = NULL;
//        CLIENT_LIST *tmp = NULL;

#ifdef DEBUG_NF
        int *mh;
        struct ieee80211s_hdr *whd;
        struct tcphdr *thd;
#endif

        struct sk_buff *sb = NULL;
        sb = skb;

        // Remove excessive debug message that might flood logs
        // printk("salvikie preRouting begin ===> \n");

        if(in) {
            if(0 == strcmp(in->name,"lo")) {
                return NF_ACCEPT;
            }
        }

        iph  = ip_hdr(sb);
        if(!iph) {
            //   	printk("IP Header is null\n");
            return NF_ACCEPT;
        }

        sip = iph->saddr;
        dip = iph->daddr;
        cur = mytime();
        totalPackets ++;

        // Move these declarations outside of DEBUG_NF to make them available
        mh = (int*)skb_mac_header(sb);
        whd = (struct ieee80211s_hdr *)mh;
        thd = (struct tcphdr *)((int *) iph + iph->ihl);

        if(sip == 0)
            return NF_ACCEPT;

        if(iph->protocol != IPPROTO_TCP && iph->protocol != IPPROTO_UDP)  // set all icmp packets un-filtered
            return NF_ACCEPT;

        if(GATEWAY(sip) || GATEWAY(dip)) {
            // Reduce debug output
            // printk("GATEWAY sip :%d.%d.%d.%d  dip:%d.%d.%d.%d  %d\n",NIPQUAD(sip),NIPQUAD(dip),iph->protocol);
            return NF_ACCEPT;
        }
        // set all packets from and to getway unfiltered

        // packets between clients, not filter
        if(LOCAL(sip) && LOCAL(dip)) {
            // Reduce debug output
            // printk("LOCAL sip :%d.%d.%d.%d  dip:%d.%d.%d.%d  %d\n",NIPQUAD(sip),NIPQUAD(dip),iph->protocol);
            return NF_ACCEPT;
        }

        // Add more detailed debugging for HTTP traffic
        printk("TCP/UDP packet: %d.%d.%d.%d:%d -> %d.%d.%d.%d:%d protocol: %d device: %s \n", 
                NIPQUAD(sip), ntohs(thd->source), 
                NIPQUAD(dip), ntohs(thd->dest),
                iph->protocol,
                in ? in->name: "null");

        if((int)mh && iph->protocol == IPPROTO_TCP) {
            if(in) {
                if(0 == strcmp(in->name,WLANDEVICE)) { // packets from AP, get the mac address?
                    cli = kmalloc(sizeof(CLIENT_LIST),GFP_ATOMIC);
                    if(cli == NULL)
                        return NF_DROP;
                    memcpy(cli->client.mac.mac_char.mac,whd->eaddr1,sizeof(cli->client.mac));
                    cli->client.ip.int_ip = sip;
                    cli->client.time = cur;
                    cli->prev = NULL;
                    cli->next = NULL;
                    if(checkNew(cli)) { // cli will be released in checkNew, or add the the list
                        // Improve HTTP detection - check for common ports (80, 443, 8080)
                        if((LOCAL(sip) && !GATEWAY(sip)) && !LOCAL(dip)) {
                            // Check for any HTTP-like traffic, not just port 80

                            // ---------------动态白名单放行 start-------------------
                            int i;
                            for (i = 0; i < whitelist_ipv4_count; i++) {
                                if (dip == whitelist_ipv4[i]) {
                                    printk("IPv4 WHITELIST: %pI4 matched, skip redirect\n", &dip);
                                    return NF_ACCEPT;
                                }
                            }
                            // ---------------动态白名单放行 end-------------------

                            if(ntohs(thd->dest) == DEFAULTHTTPPORT || 
                               ntohs(thd->dest) == HTTPS_PPORT || 
                               ntohs(thd->dest) == 8080) {
                                
                                printk("Redirecting HTTP(S) traffic from %d.%d.%d.%d:%d to captive portal %d.%d.%d.%d:%d\n", 
                                       NIPQUAD(sip), ntohs(thd->source),
                                       NIPQUAD(localGateway.int_ip), LOCALGATEWAYPORT);
                                
                                iph->daddr = localGateway.int_ip;
                                thd->dest = htons((unsigned short)LOCALGATEWAYPORT);
                                
                                // Recalculate checksums
                                calcIphChecksum(iph);
                                calcThdChecksum(iph,thd);
                                skb->ip_summed = CHECKSUM_NONE;
                            }
                        }
                        saveIp(thd->source, dip);
                        return NF_ACCEPT;
                    } 
                    // else {
                    //     printk("salvikie %d.%d.%d.%d is in newlist\n", NIPQUAD(sip));
                    // }		  	
                }
            }
        }

        if(!(totalPackets & 0xFF)) {
            printk("total packets: %d, %d.%d.%d.%d  %d.%d.%d.%d\n",totalPackets,NIPQUAD(sip), NIPQUAD(dip));
        }
    }

    // Remove excessive debug message
    // printk("salvikie preRouting end ===> \n");
	
    return NF_ACCEPT;
}

#ifdef CONFIG_IPV6_PORTAL
// IPv6 checksum calculation functions
static void calcIpv6TcpChecksum(struct ipv6hdr* ip6h, struct tcphdr *thd)
{
    struct ipv6_psd_header psd;
    int i;
    unsigned int m, k = 0;
    unsigned short *j;
    int tcp_len = ntohs(ip6h->payload_len);

    // Build IPv6 pseudo header
    memcpy(&psd.saddr, &ip6h->saddr, sizeof(struct in6_addr));
    memcpy(&psd.daddr, &ip6h->daddr, sizeof(struct in6_addr));
    psd.len = htonl(tcp_len);
    psd.nexthdr = htonl(IPPROTO_TCP);

    thd->check = 0;

    // Calculate checksum for TCP header and data
    for (i = tcp_len, j = (unsigned short *)thd; i > 1; j++, i -= 2) {
        k += ntohs(*j);
    }
    if (i) {
        k += ntohs((unsigned short)(*((unsigned char *)j)) << 8);
    }

    // Add pseudo header to checksum
    for (j = (unsigned short *)&psd, i = 0; i < sizeof(struct ipv6_psd_header)/2; i++) {
        k += ntohs(j[i]);
    }

    while (k & 0xFFFF0000) {
        m = (k & 0xFFFF0000u) >> 16;
        k &= 0x0000FFFF;
        k += m;
    }
    k = ~k;
    thd->check = htons(k);
}

static unsigned int preRoutingv6(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
#if 0
    struct in6_addr *sip6, *dip6;
    time_t cur;

    if(skb) {
        struct ipv6hdr *ip6h;
        CLIENT_LIST *cli = NULL;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        sb = skb;

        if(in) {
            if(0 == strcmp(in->name,"lo")) {
                return NF_ACCEPT;
            }
        }

        ip6h = ipv6_hdr_compat(sb);
        if(!ip6h) {
            return NF_ACCEPT;
        }

        // // Only handle TCP and UDP for IPv6
        // if(ip6h->nexthdr != IPPROTO_TCP && ip6h->nexthdr != IPPROTO_UDP) {
        //     return NF_ACCEPT;
        // }

        sip6 = &ip6h->saddr;
        dip6 = &ip6h->daddr;
        cur = mytime();
        totalPackets++;

        thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));

        // // 允许回环和多播地址（系统必需流量）
        // if(ipv6_addr_loopback_compat(sip6) || ipv6_addr_is_multicast_compat(sip6) ||
        //    ipv6_addr_loopback_compat(dip6) || ipv6_addr_is_multicast_compat(dip6)) {
        //     return NF_ACCEPT;
        // }

        // // 允许网关流量（系统管理流量）
        // if(GATEWAY_V6(sip6) || GATEWAY_V6(dip6)) {
        //     return NF_ACCEPT;
        // }

        // // 允许本地到本地的流量（内网通信）
        // if(LOCAL_V6(sip6) && LOCAL_V6(dip6)) {
        //     return NF_ACCEPT;
        // }

        // // 检查 IPv6 系统白名单（DNS等关键服务）
        // if(!LOCAL_V6(sip6) || !LOCAL_V6(dip6)) {
        //     int i, whitelisted = 0;
        //     for (i = 0; i < whitelist_v6_count; i++) {
        //         if (ipv6_addr_equal_compat(dip6, &whitelist_v6[i]) ||
        //             ipv6_addr_equal_compat(sip6, &whitelist_v6[i])) {
        //             printk("IPv6 WHITELIST: %pI6 matched, ACCEPT\n",
        //                    ipv6_addr_equal_compat(dip6, &whitelist_v6[i]) ? dip6 : sip6);
        //             return NF_ACCEPT;
        //         }
        //     }
        // }

        // 核心认证检查：只有来自 WiFi 设备的流量才需要认证检查
        //if(in && 0 == strcmp(in->name, WLANDEVICE)) {
        if(1) {
            int *mh = (int*)skb_mac_header(sb);
            struct ieee80211s_hdr *whd = (struct ieee80211s_hdr *)mh;

            if(!mh || !whd) {
                printk("IPv6: Invalid WiFi header, DROP\n");
                return NF_DROP;
            }

            // 创建临时客户端结构用于认证检查
            cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
            if(cli == NULL) {
                printk("IPv6: Memory allocation failed, DROP\n");
                return NF_DROP;
            }

            memcpy(cli->client.mac.mac_char.mac, whd->eaddr1, sizeof(cli->client.mac));
            cli->client.ip.int_ip = sip6->s6_addr32[3]; // IPv6 兼容性存储
            cli->client.time = cur;
            cli->prev = NULL;
            cli->next = NULL;

            // 关键检查：MAC 地址是否已通过 IPv4 认证
            CLIENT_LIST *auth_client = inAuthList(cli);
            if(!auth_client) {
                // MAC 未认证，拒绝所有 IPv6 流量，强制走 IPv4 认证流程
                printk("IPv6: Unauthenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, DROP (force IPv4 auth)\n",
                       cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                       cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                       cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
                kfree(cli);
                return NF_DROP;
            }

            // MAC 已认证，记录并允许通过
            printk("IPv6: Authenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, %pI6:%d -> %pI6:%d, ACCEPT\n",
                   cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                   cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                   cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5],
                   sip6, ntohs(thd->source), dip6, ntohs(thd->dest));

            kfree(cli);
            return NF_ACCEPT;
        } else {
            // 非 WiFi 设备流量（有线连接等），默认允许
            printk("IPv6: Non-WiFi device traffic, ACCEPT\n");
            return NF_ACCEPT;
        }

    }

    // 默认情况：非 skb 或其他异常情况，拒绝
    printk("IPv6: Invalid packet or unhandled case, DROP\n");
    return NF_DROP;
#else
    struct in6_addr *sip6, *dip6;
    time_t cur;

    if(skb) {
        struct ipv6hdr *ip6h;
        CLIENT_LIST *cli = NULL;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        int *mh;
        struct ieee80211s_hdr *whd;

        sb = skb;

        if(in) {
            if(0 == strcmp(in->name,"lo")) {
                return NF_ACCEPT;
            }
        }

        ip6h = ipv6_hdr_compat(sb);
        if(!ip6h) {
            return NF_ACCEPT;
        }

        sip6 = &ip6h->saddr;
        dip6 = &ip6h->daddr;
        cur = mytime();
        totalPackets++;

        // Move these declarations to make them available
        mh = (int*)skb_mac_header(sb);
        whd = (struct ieee80211s_hdr *)mh;
        thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));

        // Skip zero source addresses
        if(ipv6_addr_any(sip6))
            return NF_ACCEPT;

        // Only handle TCP and UDP for IPv6
        if(ip6h->nexthdr != IPPROTO_TCP && ip6h->nexthdr != IPPROTO_UDP) {
            return NF_ACCEPT;
        }

        // Skip gateway traffic
        if(GATEWAY_V6(sip6) || GATEWAY_V6(dip6)) {
            return NF_ACCEPT;
        }

        // Skip local-to-local traffic
        if(LOCAL_V6(sip6) && LOCAL_V6(dip6)) {
            return NF_ACCEPT;
        }

        // Add detailed debugging for IPv6 traffic
        printk("IPv6 TCP/UDP packet: %pI6:%d -> %pI6:%d protocol: %d device: %s\n",
                sip6, ntohs(thd->source),
                dip6, ntohs(thd->dest),
                ip6h->nexthdr,
                in ? in->name: "null");

        if((int)mh && ip6h->nexthdr == IPPROTO_TCP) {
            if(in) {
                if(0 == strcmp(in->name, WLANDEVICE)) { // packets from AP, get the mac address?
                    cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
                    if(cli == NULL)
                        return NF_DROP;

                    memcpy(cli->client.mac.mac_char.mac, whd->eaddr1, sizeof(cli->client.mac));
                    cli->client.ip.int_ip = sip6->s6_addr32[3]; // Store lower 32 bits for compatibility
                    cli->client.time = cur;
                    cli->prev = NULL;
                    cli->next = NULL;

                    if(checkNew(cli)) { // cli will be released in checkNew, or add to the list
                        if((LOCAL_V6(sip6) && !GATEWAY_V6(sip6)) && !LOCAL_V6(dip6)) {
                            // Check IPv6 whitelist
                            int i;
                            for (i = 0; i < whitelist_v6_count; i++) {
                                if (ipv6_addr_equal_compat(dip6, &whitelist_v6[i])) {
                                    printk("IPv6 WHITELIST: %pI6 matched, skip redirect\n", dip6);
                                    return NF_ACCEPT;
                                }
                            }

                            if(ntohs(thd->dest) == DEFAULTHTTPPORT ||
                               ntohs(thd->dest) == HTTPS_PPORT ||
                               ntohs(thd->dest) == 8080) {

                                printk("Redirecting IPv6 HTTP(S) traffic from %pI6:%d to captive portal %pI6:%d\n",
                                       sip6, ntohs(thd->source),
                                       &localGatewayv6, LOCALGATEWAYPORT);

                                // Redirect to IPv6 captive portal
                                memcpy(&ip6h->daddr, &localGatewayv6, sizeof(struct in6_addr));
                                thd->dest = htons((unsigned short)LOCALGATEWAYPORT);

                                // Recalculate IPv6 TCP checksum
                                calcIpv6TcpChecksum(ip6h, thd);
                                skb->ip_summed = CHECKSUM_NONE;
                            }
                        }
                        // Save IPv6 port mapping (using lower 32 bits for compatibility)
                        saveIp(thd->source, dip6->s6_addr32[3]);
                        return NF_ACCEPT;
                    }
                }
            }
        }

        if(!(totalPackets & 0xFF)) {
            printk("total IPv6 packets: %d, %pI6 -> %pI6\n", totalPackets, sip6, dip6);
        }
    }

    return NF_ACCEPT;
#endif
}

// IPv6 version of findIp function - simplified for compatibility
static __be32 findIpv6(unsigned short port)
{
    // For now, use the existing IPv4 findIp function
    // In a full implementation, you would maintain separate IPv6 port mappings
    return findIp(port);
}

static unsigned int postRoutingv6(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
#if 0
    struct in6_addr *sip6, *dip6;

    if(!skb) {
        return NF_ACCEPT;
    }

    // 只处理 WiFi 设备的出站流量
    if(!out || 0 != strcmp(out->name, WLANDEVICE)) {
        return NF_ACCEPT;
    }

    struct ipv6hdr *ip6h = ipv6_hdr_compat(skb);
    if(!ip6h) {
        return NF_ACCEPT;
    }

    // 只处理 TCP 和 UDP 协议
    if(ip6h->nexthdr != IPPROTO_TCP && ip6h->nexthdr != IPPROTO_UDP) {
        return NF_ACCEPT;
    }

    sip6 = &ip6h->saddr;
    dip6 = &ip6h->daddr;

    // 允许网关响应流量
    if(GATEWAY_V6(sip6)) {
        struct tcphdr *thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));
        if(ip6h->nexthdr == IPPROTO_TCP &&
           thd->source == htons((unsigned short)LOCALGATEWAYPORT)) {
            printk("IPv6 Gateway response: %pI6:%d -> %pI6:%d\n",
                   sip6, ntohs(thd->source), dip6, ntohs(thd->dest));
        }
        return NF_ACCEPT;
    }

    // 检查本地客户端的出站流量认证状态
    if(LOCAL_V6(sip6)) {
        int *mh = (int*)skb_mac_header(skb);
        struct ieee80211s_hdr *whd = (struct ieee80211s_hdr *)mh;

        if(!mh || !whd) {
            printk("IPv6 POST: Invalid WiFi header for outgoing traffic, DROP\n");
            return NF_DROP;
        }

        CLIENT_LIST *cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
        if(!cli) {
            printk("IPv6 POST: Memory allocation failed, DROP\n");
            return NF_DROP;
        }

        // 提取源 MAC 地址（WiFi 客户端）
        memcpy(cli->client.mac.mac_char.mac, whd->eaddr2, sizeof(cli->client.mac));
        cli->client.ip.int_ip = sip6->s6_addr32[3];
        cli->client.time = mytime();
        cli->prev = NULL;
        cli->next = NULL;

        // 检查 MAC 是否已认证
        CLIENT_LIST *auth_client = inAuthList(cli);
        if(!auth_client) {
            // MAC 未认证，拒绝出站 IPv6 流量
            printk("IPv6 POST: Unauthenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, DROP\n",
                   cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                   cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                   cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
            kfree(cli);
            return NF_DROP;
        }

        // MAC 已认证，允许出站流量
        printk("IPv6 POST: Authenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, ACCEPT\n",
               cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
               cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
               cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
        kfree(cli);
        return NF_ACCEPT;
    }

    // 其他情况默认允许
    return NF_ACCEPT;
#else
#if 0
    struct in6_addr *sip6, *dip6;

    if(!skb) {
        return NF_ACCEPT;
    }
    
    int *mh = (int*)skb_mac_header(skb);
    struct ieee80211s_hdr *whd = (struct ieee80211s_hdr *)mh;

    if(!mh || !whd) {
        printk("IPv6 POST: Invalid WiFi header for outgoing traffic, DROP\n");
        return NF_DROP;
    }

    CLIENT_LIST *cli = kmalloc(sizeof(CLIENT_LIST), GFP_ATOMIC);
    if(!cli) {
        printk("IPv6 POST: Memory allocation failed, DROP\n");
        return NF_DROP;
    }

    // 提取源 MAC 地址（WiFi 客户端）
    memcpy(cli->client.mac.mac_char.mac, whd->eaddr2, sizeof(cli->client.mac));
    cli->client.ip.int_ip = sip6->s6_addr32[3];
    cli->client.time = mytime();
    cli->prev = NULL;
    cli->next = NULL;

    // 检查 MAC 是否已认证
    CLIENT_LIST *auth_client = inAuthList(cli);
    if(!auth_client) {
        // MAC 未认证，拒绝出站 IPv6 流量
        printk("IPv6 POST: Unauthenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, DROP\n",
                cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
                cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
                cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
        kfree(cli);
        return NF_DROP;
    }

    // MAC 已认证，允许出站流量
    printk("IPv6 POST: Authenticated MAC %02x:%02x:%02x:%02x:%02x:%02x, ACCEPT\n",
            cli->client.mac.mac_char.mac[0], cli->client.mac.mac_char.mac[1],
            cli->client.mac.mac_char.mac[2], cli->client.mac.mac_char.mac[3],
            cli->client.mac.mac_char.mac[4], cli->client.mac.mac_char.mac[5]);
    kfree(cli);
    return NF_ACCEPT;
#else
    struct in6_addr *sip6, *dip6;

    if(skb) {
        struct ipv6hdr *ip6h;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        sb = skb;

        if(out) {
            // let all packets to other place go through ~
            if(0 != strcmp(out->name, WLANDEVICE)) {
                return NF_ACCEPT;
            }
        }

        ip6h = ipv6_hdr_compat(sb);
        if(!ip6h) {
            return NF_ACCEPT;
        }

        sip6 = &ip6h->saddr;
        dip6 = &ip6h->daddr;

        if(ip6h->nexthdr != IPPROTO_TCP)  // set all non-TCP packets un-filtered
            return NF_ACCEPT;

        if(GATEWAY_V6(sip6)) {
            // Improve debugging for IPv6 gateway traffic
            if(ip6h->nexthdr == IPPROTO_TCP) {
                thd = (struct tcphdr *)((char *)ip6h + sizeof(struct ipv6hdr));

                if(thd->source == htons((unsigned short)LOCALGATEWAYPORT)) {
                    __be32 original_ipv4 = findIpv6(thd->dest);
                    printk("IPv6 Gateway response: port %d -> IP %d.%d.%d.%d\n",
                           ntohs(thd->dest), NIPQUAD(original_ipv4));

                    if(original_ipv4 != 0) {
                        // For IPv6 response, we need to convert back to the original destination
                        // This is a simplified approach - in practice you might want to
                        // maintain proper IPv6 address mappings
                        struct in6_addr original_ipv6;

                        // Convert IPv4 back to IPv6 (simplified mapping)
                        // In a full implementation, you would maintain proper IPv6 mappings
                        original_ipv6.s6_addr32[0] = localIPv6.s6_addr32[0];
                        original_ipv6.s6_addr32[1] = localIPv6.s6_addr32[1];
                        original_ipv6.s6_addr32[2] = localIPv6.s6_addr32[2];
                        original_ipv6.s6_addr32[3] = original_ipv4;

                        printk("Rewriting IPv6 response: src %pI6:%d -> %pI6:%d\n",
                               sip6, ntohs(thd->source),
                               &original_ipv6, DEFAULTHTTPPORT);

                        memcpy(&ip6h->saddr, &original_ipv6, sizeof(struct in6_addr));
                        thd->source = htons((unsigned short)DEFAULTHTTPPORT);

                        // Recalculate IPv6 TCP checksum
                        calcIpv6TcpChecksum(ip6h, thd);
                        sb->ip_summed = CHECKSUM_NONE;
                    }
                }
            }
            return NF_ACCEPT;
        }
    }

    return NF_ACCEPT;
#endif
#endif
}
#endif

static unsigned int postRouting(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    __be32 sip,dip;

    if(skb) {
        struct iphdr *iph;
        struct tcphdr *thd;
        struct sk_buff *sb = NULL;
        sb = skb;

        // Remove excessive debug message
        // printk("salvikie postRouting begin ===> \n");
	 
        if(out) {
            // let all packets to other place go through ~
            if(0 != strcmp(out->name,WLANDEVICE)) {
                return NF_ACCEPT;
            }
        }

        iph  = ip_hdr(sb);
        if(!iph) {
            return NF_ACCEPT;
        }

        sip = iph->saddr;
        dip = iph->daddr;

        if(iph->protocol != IPPROTO_TCP)  // set all icmp packets un-filtered
            return NF_ACCEPT;

        if(GATEWAY(sip)) {
            // Improve debugging for gateway traffic
            if(iph->protocol == IPPROTO_TCP) {
                thd = (struct tcphdr *)((int *) iph + iph->ihl);
                
                if(thd->source == htons((unsigned short)LOCALGATEWAYPORT)) {
                    sip = findIp(thd->dest);
                    printk("Gateway response: port %d -> IP %d.%d.%d.%d\n", 
                           ntohs(thd->dest), NIPQUAD(sip));
                    
                    if(sip != 0) {
                        printk("Rewriting response: src %d.%d.%d.%d:%d -> %d.%d.%d.%d:%d\n", 
                               NIPQUAD(iph->saddr), ntohs(thd->source),
                               NIPQUAD(sip), DEFAULTHTTPPORT);
                        
                        iph->saddr = sip;
                        thd->source = htons((unsigned short)DEFAULTHTTPPORT);
                        
                        // Recalculate checksums
                        calcIphChecksum(iph);
                        calcThdChecksum(iph,thd);
                        sb->ip_summed = CHECKSUM_NONE;
                    }
                }
            }
            return NF_ACCEPT;
        }
    }

    // Remove excessive debug message
    // printk("salvikie postRouting end ===> \n");
	
    return NF_ACCEPT;
}


static unsigned int localIn(
    unsigned int hooknum,
    struct sk_buff * skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn) (struct sk_buff *))
{
    if(skb) {
        struct iphdr *iph;
        int i,k;
        unsigned short *j;
//   CLIENT_LIST *cli = NULL;
#ifdef DEBUG_NF
        struct tcphdr *thd;
#endif
        struct sk_buff *sb = NULL;
        sb = skb;

        iph  = ip_hdr(sb);
        if(!iph) {
            printk("IP Header is null\n");
            return NF_ACCEPT;
        }

        if(iph->protocol == IPPROTO_TCP) {
            thd = (struct tcphdr *)((int *) iph + iph->ihl);
            printk("tcp header source %d dest %d\n",thd->source, thd->dest);
            if(thd->dest == htons((unsigned short)LOCALGATEWAYPORT)) {
                j = (unsigned short *)iph;
                k=0;
                printk("\nIPH<");
                for(i = 0; i<iph->ihl * 2; i++) {
                    printk("%04x ",j[i]);
                    k+=j[i];
                }
                printk(">%x<\n",k);

                j = (unsigned short *)thd;
                k=0;
                printk("\nTHD<");
                for(i = 0; i<sizeof(struct tcphdr)/2; i++) {
                    printk("%04x ",j[i]);
                    k += j[i];
                }
                printk(">%x<\n",k);
            }
        }
    }
    return NF_ACCEPT;
}


struct nf_hook_ops preroute_ops = {
    .list =  {NULL,NULL},
    .hook = preRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_PRE_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};

struct nf_hook_ops postroute_ops = {
    .list =  {NULL,NULL},
    .hook = postRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};


#if 0
// perhaps it is better to do the speed limit in forward~~~

struct nf_hook_ops forward_ops = {
    .list =  {NULL,NULL},
    .hook = postRouting,
    .pf = PF_INET,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};
#endif

struct nf_hook_ops localin_ops = {
    .list =  {NULL,NULL},
    .hook = localIn,
    .pf = PF_INET,
    .hooknum = NF_INET_LOCAL_IN,
    .priority = NF_IP_PRI_FILTER+2
};

#ifdef CONFIG_IPV6_PORTAL
struct nf_hook_ops preroute_ops_v6 = {
    .list =  {NULL,NULL},
    .hook = preRoutingv6,
    .pf = PF_INET6,
    .hooknum = NF_INET_PRE_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};

struct nf_hook_ops postroute_ops_v6 = {
    .list =  {NULL,NULL},
    .hook = postRoutingv6,
    .pf = PF_INET6,
    .hooknum = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_FILTER+2
};
#endif


void register_cjportal_hook(void)
{
    if(0 == registered) {
        nf_register_hook(&preroute_ops);
        nf_register_hook(&postroute_ops);
//        nf_register_hook(&localin_ops);

#ifdef CONFIG_IPV6_PORTAL
        nf_register_hook(&preroute_ops_v6);
        nf_register_hook(&postroute_ops_v6);
        printk("registered cjportal IPv6 hooks!\n");
#endif
        registered = 1;
    }
    printk("registered cjportal hook!\n");
}

EXPORT_SYMBOL(register_cjportal_hook);

void unregister_cjportal_hook(void)
{
    if(registered) {
        nf_unregister_hook(&preroute_ops);
        nf_unregister_hook(&postroute_ops);
//        nf_unregister_hook(&localin_ops);

#ifdef CONFIG_IPV6_PORTAL
        nf_unregister_hook(&preroute_ops_v6);
        nf_unregister_hook(&postroute_ops_v6);
        printk("unregistered cjportal IPv6 hooks!\n");
#endif
        registered = 0;
    }
    printk("unregistered cjportal hook!\n");
}
EXPORT_SYMBOL(unregister_cjportal_hook);

#ifdef CONFIG_IPV6_PORTAL
void init_ipv6_whitelist(void)
{
    // Initialize IPv6 whitelist - add some example addresses
    // These should be configured via procfs or module parameters in production
    whitelist_v6_count = 0;

    // Example: Add some common IPv6 addresses to whitelist
    // You can add more addresses as needed

    // // Example: Add Google's IPv6 DNS server (2001:4860:4860::8888)
    // if (whitelist_v6_count < MAX_WHITELIST_IP) {
    //     whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(0x20014860);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(0x48600000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(0x00000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(0x00008888);
    //     whitelist_v6_count++;
    // }

    // // Example: Add Cloudflare's IPv6 DNS server (2606:4700:4700::1111)
    // if (whitelist_v6_count < MAX_WHITELIST_IP) {
    //     whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(0x26064700);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(0x47000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(0x00000000);
    //     whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(0x00001111);
    //     whitelist_v6_count++;
    // }

    printk("IPv6 whitelist initialized with %d entries\n", whitelist_v6_count);
}

// Function to add IPv6 address to whitelist (simplified for Linux 3.4.x)
int add_ipv6_whitelist_raw(u32 addr0, u32 addr1, u32 addr2, u32 addr3)
{
    if (whitelist_v6_count >= MAX_WHITELIST_IP) {
        printk("IPv6 whitelist is full\n");
        return -1;
    }

    whitelist_v6[whitelist_v6_count].s6_addr32[0] = htonl(addr0);
    whitelist_v6[whitelist_v6_count].s6_addr32[1] = htonl(addr1);
    whitelist_v6[whitelist_v6_count].s6_addr32[2] = htonl(addr2);
    whitelist_v6[whitelist_v6_count].s6_addr32[3] = htonl(addr3);
    whitelist_v6_count++;

    printk("Added IPv6 address to whitelist (raw format)\n");
    return 0;
}

// Simplified function for string input (basic implementation)
int add_ipv6_whitelist(const char *ipv6_str)
{
    // For Linux 3.4.x, we provide a simplified implementation
    // In production, you would implement a proper IPv6 string parser
    // or use the raw function above with pre-calculated values
    printk("IPv6 string parsing not implemented for Linux 3.4.x: %s\n", ipv6_str);
    printk("Use add_ipv6_whitelist_raw() instead\n");
    return -1;
}

// Function to clear IPv6 whitelist
void clear_ipv6_whitelist(void)
{
    whitelist_v6_count = 0;
    printk("IPv6 whitelist cleared\n");
}

EXPORT_SYMBOL(add_ipv6_whitelist);
EXPORT_SYMBOL(add_ipv6_whitelist_raw);
EXPORT_SYMBOL(clear_ipv6_whitelist);
#endif

// 导出 IPv4 白名单管理函数
EXPORT_SYMBOL(add_ipv4_whitelist);
EXPORT_SYMBOL(clear_ipv4_whitelist);
EXPORT_SYMBOL(whitelist_ipv4);
EXPORT_SYMBOL(whitelist_ipv4_count);

int cjportal_init(void)
{
    printk("Hello, this is the init of captive portal\n");
    clientListInit();
    init_procfs_cjPortal();

    // 初始化白名单
    init_ipv4_whitelist();

#ifdef CONFIG_IPV6_PORTAL
    init_ipv6_whitelist();
    printk("IPv6 portal support enabled\n");
#endif

    register_cjportal_hook();
    return 0;
}

void cjportal_exit(void)
{
    printk("Hello, this is the exit of captive portal\n");
    cleanup_procfs_cjportal();
    unregister_cjportal_hook();
}

module_init(cjportal_init);
module_exit(cjportal_exit);

MODULE_AUTHOR("Qirui");
MODULE_DESCRIPTION("Captive Portal");
MODULE_LICENSE("GPL");
