#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/slab.h>
#include <linux/time.h>
//#include <linux/mutex.h>
#include <linux/atomic.h>

#include "speedlimit.h"

extern int portalLifetime;

CLIENT_LIST *newClient = NULL;
CLIENT_LIST *authClient = NULL;

//struct mutex clientMutex;
atomic_t clientMutex;


#define LIFETIME 5
#define MAXPORTLIST 64
REDDIR redirection[MAXPORTLIST] = {{0,0,0}};

int findIp(unsigned short port)
{
    int i;
    for(i=0; i<MAXPORTLIST; i++) {
        if(redirection[i].port == port)
            return redirection[i].ip;
    }
    return 0;
}
EXPORT_SYMBOL(findIp);

void saveIp(unsigned short port, int ip)
{
    int i,j=MAXPORTLIST;
    int found = 0;
    time_t cur = mytime();
    for(i=0; i<MAXPORTLIST; i++) {
        if(redirection[i].port == port) {
            if(found == 0) {
                found = 1;
                redirection[i].ip = ip;
                redirection[i].time = cur;
            }
            else {
                memset(&redirection[i],0,sizeof(struct redir));
            }
        }
        else if(redirection[i].port == 0) {
            j = i;
        }
        else if(redirection[i].time < cur - LIFETIME) {
            memset(&redirection[i],0,sizeof(struct redir));
            j=i;
        }
    }
    if(found == 0 && j < MAXPORTLIST) {
        redirection[j].port = port;
        redirection[j].ip = ip;
        redirection[j].time = cur;
    }

}
EXPORT_SYMBOL(saveIp);

CLIENT_LIST * findInList(CLIENT_LIST *cli,CLIENT_LIST *list)
{
    CLIENT_LIST *tmp = list;
    while(tmp) {
//		printk("MAC in %s: %08x %04x\n",(list==newClient)?"NEW":(list==authClient)?"AUTH":(list==saveClient)?"SAVE":"NULL", tmp->client.mac.mac_int.mac1,tmp->client.mac.mac_int.mac2);
        if(tmp->client.mac.mac_int.mac1 == cli->client.mac.mac_int.mac1 && \
                tmp->client.mac.mac_int.mac2 == cli->client.mac.mac_int.mac2 ) {
            return tmp;
        }
        tmp = tmp->next;
    }
    return NULL;
}

CLIENT_LIST * inNewList(CLIENT_LIST *cli)
{
    return findInList(cli,newClient);
}

CLIENT_LIST * inAuthList(CLIENT_LIST *cli)
{
    return findInList(cli,authClient);
}

void updateClientInfo(CLIENT_LIST *cli, CLIENT_LIST *tmp)
{
    tmp->client.ip.int_ip = cli->client.ip.int_ip;
    tmp->client.mac.mac_int.mac1 = cli->client.mac.mac_int.mac1;
    tmp->client.mac.mac_int.mac2 = cli->client.mac.mac_int.mac2;
    tmp->client.time = cli->client.time;
}

void add2List(CLIENT_LIST *cli,CLIENT_LIST **list)
{
    CLIENT_LIST *tmp = *list;
    if(tmp == NULL) {
        *list = cli;
//		printk("add to list  cli %x  new %x auth %x\n",(int)cli,(int)newClient,(int)authClient);
        return;
    }

    while(tmp->next) {
        tmp = tmp->next;
    }
    tmp->next = cli;
    cli->prev = tmp;
//	printk("add to list  cli %x  new %x auth %x\n",(int)cli,(int)newClient,(int)authClient);
}


void add2Auth(CLIENT_LIST *cli)
{
    add2List(cli, &authClient);
}
EXPORT_SYMBOL(add2Auth);

void removeFromList(CLIENT_LIST *cli,CLIENT_LIST** list)
{
    CLIENT_LIST *tmp = *list;
//	printk("removeFromList %x list %x new %x auth %x\n",(int)cli,(int)*list,(int)newClient,(int)authClient);
    if(cli == tmp) {
        if(tmp->next != NULL) {
            tmp->next->prev = NULL;
            *list = tmp->next;
        }
        else {
            *list = NULL;
//	printk("removeFromList %x list %x new %x auth %x\n",(int)cli,(int)*list,(int)newClient,(int)authClient);

            return;
        }
    }
    else {
        cli->prev->next =cli->next;
        if(cli->next != NULL) {
            cli->next->prev = cli->prev;
        }
    }
    cli->next = NULL;
    cli->prev = NULL;
}

void move2NewList(CLIENT_LIST *cli)
{
    removeFromList(cli,&authClient);
    add2List(cli,&newClient);
}

void move2AuthList(CLIENT_LIST *cli)
{
    removeFromList(cli,&newClient);
    add2List(cli,&authClient);
}

void clientAuthorized(MACADR mac_adr)
{
    CLIENT_LIST cli;
    CLIENT_LIST * cliFound=NULL;
    cli.client.mac.mac_int.mac1 = mac_adr.mac_int.mac1;
    cli.client.mac.mac_int.mac2 = mac_adr.mac_int.mac2;
    printk("client authorized!\n");
    //mutex_lock(&clientMutex);
    atomic_inc(&clientMutex);
    cliFound = findInList(&cli,newClient);
    if(cliFound) {
        printk("found mac %x\n",(int)cliFound);
        move2AuthList(cliFound);
    }
    //mutex_unlock(&clientMutex);
    atomic_dec(&clientMutex);
    

}
EXPORT_SYMBOL(clientAuthorized);

// the operation of clent list should be mutex protected?
// if the wifidog function triggers the move of client from new to authorized, mutex seems to be nessary.

/* return 1 means need redirect
    return 0 means don't need redirect
    */

int checkNew(CLIENT_LIST *cli)
{
    CLIENT_LIST *tmp;

    if(!cli)
        return 0;

    atomic_inc(&clientMutex);

    if(portalLifetime == 3600) 
    {
        //printk("salvikie portalLifetime is 3600 use disable portal");
        kfree(cli);
        atomic_dec(&clientMutex);
        return 0;
    }

    tmp= inAuthList(cli);
    if(tmp != NULL) {// found in authorized list
        if((cli->client.ip.int_ip != tmp->client.ip.int_ip) || (cli->client.time -tmp->client.time > portalLifetime)) { // the parameter should be configurable
//			printk("in Auth %x %x %x %x\n",cli->client.ip.int_ip,tmp->client.ip.int_ip,(int)cli->client.time,(int)tmp->client.time);
            updateClientInfo(cli,tmp);
            move2NewList(tmp);
            kfree(cli);
            atomic_dec(&clientMutex);
            return 1;
            /// do hack
        }
        else {
            updateClientInfo(cli,tmp);
            kfree(cli);
            atomic_dec(&clientMutex);
            return 0;
        }
    }

    tmp = inNewList(cli);
    if(tmp != NULL) {
        updateClientInfo(cli,tmp);
        kfree(cli);
    }
    else {
        add2List(cli,&newClient);
    }
    atomic_dec(&clientMutex);
    return 1;
}
EXPORT_SYMBOL(checkNew);

void clientListInit(void)
{
    //mutex_init(&clientMutex);
    atomic_set(&clientMutex, 0);
}
EXPORT_SYMBOL(clientListInit);

void deleteAuthClient(MACADR mac_adr) 
{
    CLIENT_LIST cli;
    CLIENT_LIST *cliFound = NULL;
    
    // 正确初始化MAC地址
    cli.client.mac.mac_int.mac1 = mac_adr.mac_int.mac1;
    cli.client.mac.mac_int.mac2 = mac_adr.mac_int.mac2;
    
    printk("Deleting authorized client: %pM\n", mac_adr.mac_char.mac);
    
    atomic_inc(&clientMutex);
    
    cliFound = findInList(&cli, authClient);
    if (cliFound) {
        printk("Found client %pM, moving to new list\n", mac_adr.mac_char.mac);
        // 重置超时时间
        cliFound->client.time = mytime(); 
        move2NewList(cliFound);
    }
    
    atomic_dec(&clientMutex);
}
EXPORT_SYMBOL(deleteAuthClient);

// Function to clear all authenticated clients, forcing them to re-authenticate
void clearAuthList(void)
{
    CLIENT_LIST *currentClient, *next;
    
    atomic_inc(&clientMutex);
    
    currentClient = authClient;
    while (currentClient != NULL) {
        next = currentClient->next;
        
        // Move client back to new list to force re-authentication
        printk("Moving client %02x:%02x:%02x:%02x:%02x:%02x back to new list for re-authentication\n",
               currentClient->client.mac.mac_char.mac[0], currentClient->client.mac.mac_char.mac[1],
               currentClient->client.mac.mac_char.mac[2], currentClient->client.mac.mac_char.mac[3],
               currentClient->client.mac.mac_char.mac[4], currentClient->client.mac.mac_char.mac[5]);
        
        move2NewList(currentClient);
        currentClient = next;
    }
    
    atomic_dec(&clientMutex);
}
EXPORT_SYMBOL(clearAuthList);
EXPORT_SYMBOL(move2NewList);
