#include <linux/unistd.h>
#include <linux/types.h>   
#include <linux/socket.h>   
#include <linux/in.h>   

#include <linux/ioctl.h> 
#include <linux/inet.h>   
#include <linux/if_arp.h> 
#include <linux/if.h>   

 

int getlocaip(int *ip)    
{   
    int sockfd;      
    struct ifreq req;     
    struct sockaddr_in *host;     

    if(-1 == (sockfd = socket(PF_INET, SOCK_STREAM, 0)))   
    { 
        return -1;   
    }   
  
    bzero(&req, sizeof(struct ifreq));     
    strcpy(req.ifr_name, "rmnet0");     
    if(ioctl(sockfd, SIOCGIFADDR, &req) < 0)
	return -1;

    host = (struct sockaddr_in*)&req.ifr_addr;    
    memcpy(ip,&host->sin_addr,sizeof(struct int));     
    close(sockfd);     
    return 1;    

}  
