/*
 * Linux 3.4 Captive Portal Kernel Module
 * Features: HTTP transparent redirection, connection state tracking, client management
 */

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/version.h>
#include <linux/netfilter.h>
#include <linux/netfilter_ipv4.h>
#include <linux/ip.h>
#include <linux/tcp.h>
#include <linux/jiffies.h>
#include <linux/spinlock.h>
#include <linux/list.h>
#include <net/netfilter/nf_conntrack.h>
#include <linux/skbuff.h>
#include <net/ip.h>
#include <net/tcp.h>
#include <linux/proc_fs.h>

#define MOD_NAME        "portal"
#define DEFAULT_GW_IP   (192 << 24 | 168 << 16 | 100 << 8 | 1) // *************
//#define DEFAULT_GW_IP   (1 << 24 | 100 << 16 | 168 << 8 | 192) // *************
#define CAPTIVE_PORT    9000
#define CLIENT_TIMEOUT  (30 * HZ)  // 30-second client timeout

/* Configurable parameters */
static unsigned int gw_ip = DEFAULT_GW_IP;
module_param(gw_ip, uint, 0644);
MODULE_PARM_DESC(gw_ip, "Gateway IP address (network byte order)");

static int debug = 1;
module_param(debug, int, 0644);
MODULE_PARM_DESC(debug, "Debug mode (0=disable 1=enable)");

/* Client state structure */
struct portal_client {
    struct list_head list;
    __be32 orig_daddr;     // Original destination IP
    __be16 orig_dport;     // Original destination port
    unsigned long expiry;  // Expiration timestamp
};

/* Global data structures */
static LIST_HEAD(client_list);
static DEFINE_SPINLOCK(client_lock);
static atomic_t redirect_count = ATOMIC_INIT(0);

/******************** Logging System ********************/
#define LOG(lvl, fmt, ...) \
    printk(KERN_##lvl MOD_NAME ": " fmt, ##__VA_ARGS__)

#define DEBUG(fmt, ...) \
    do { if (debug) LOG(DEBUG, "DEBUG: " fmt, ##__VA_ARGS__); } while(0)

#define INFO(fmt, ...)  LOG(INFO, "INFO: " fmt, ##__VA_ARGS__)
#define WARN(fmt, ...)  LOG(WARNING, "WARN: " fmt, ##__VA_ARGS__)

/* Add these new parameters */
static int redirect_enabled = 0; // 1=enabled, 0=disabled
module_param(redirect_enabled, int, 0644);
MODULE_PARM_DESC(redirect_enabled, "Redirect control (1=on 0=off)");

static int toggle_cleanup = 1; // 1=cleanup when disabled, 0=keep state
module_param(toggle_cleanup, int, 0644);
MODULE_PARM_DESC(toggle_cleanup, "Cleanup clients when disabling redirect");

/******************** Core Functionality ********************/
static int is_target_port(__be16 port)
{
    switch (ntohs(port)) {
    case 80:   // HTTP
    case 443:  // HTTPS
    case 8080: // Alternative HTTP
        return 1;
    default:
        return 0;
    }
}

/* Add client with local traffic filtering */
static void client_add(__be32 daddr, __be16 dport, __be32 saddr)
{
    /* Filter gateway-directed traffic */
    if (daddr == htonl(gw_ip)) {
        DEBUG("Skipping gateway-directed traffic: %pI4\n", &daddr);
        return;
    }

    struct portal_client *cli = kmalloc(sizeof(*cli), GFP_ATOMIC);
    if (!cli) {
        WARN("Client memory allocation failed\n");
        return;
    }

    cli->orig_daddr = daddr;
    cli->orig_dport = dport;
    cli->expiry = jiffies + CLIENT_TIMEOUT;

    spin_lock(&client_lock);
    list_add(&cli->list, &client_list);
    spin_unlock(&client_lock);

    DEBUG("New client: %pI4:%hu Lifetime: %lums\n", 
          &daddr, ntohs(dport), jiffies_to_msecs(CLIENT_TIMEOUT));
}

/******************** Add Cleanup Function ********************/
static void cleanup_clients(void)
{
    struct portal_client *cli, *tmp;
    
    spin_lock(&client_lock);
    list_for_each_entry_safe(cli, tmp, &client_list, list) {
        list_del(&cli->list);
        kfree(cli);
    }
    spin_unlock(&client_lock);
    
    INFO("Cleared all client entries\n");
}

/******************** PREROUTING Processing ********************/
static unsigned int pre_hook(
    unsigned int hooknum,
    struct sk_buff *skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn)(struct sk_buff *))
{
    struct iphdr *iph;
    struct tcphdr *thd;
    struct nf_conn *ct;
    enum ip_conntrack_info ctinfo;
    struct nf_conntrack_tuple new_tuple;

    /* Check redirect status first */
    if (!redirect_enabled) {
        DEBUG("pre_hook Redirect disabled, bypassing\n");
        return NF_ACCEPT;
    }

    /* Basic validation */
    if (!skb || !(iph = ip_hdr(skb)) || iph->protocol != IPPROTO_TCP)
        return NF_ACCEPT;

     /* 修复 tcp 头获取的不对 */
    //if (!(thd = tcp_hdr(skb)))
    if (!(thd = (struct tcphdr *)((int *) iph + iph->ihl)))
        return NF_ACCEPT;

     /* 🆕 Log incoming packet details */
    DEBUG("PRE: TCP %pI4:%hu -> %pI4:%hu device %s\n",
          &iph->saddr, ntohs(thd->source),
          &iph->daddr, ntohs(thd->dest), in ? in->name : "null");

    /* Filter local traffic to gateway */
    if (iph->daddr == htonl(gw_ip)) {
        //DEBUG("Bypassing dest-ip local gateway traffic\n");
        return NF_ACCEPT;
    }

    if(!is_target_port(thd->dest)) {
        //DEBUG("Bypassing dest-port not http port");
        return NF_ACCEPT;
    }

    /* Prepare skb for modification */
    // if (skb_make_writable(skb, skb_headlen(skb))) {
    //     WARN("SKB not writable\n");
    //     return NF_DROP;
    // }
    // unsigned int tcphoff = skb_transport_offset(skb);  // 获取 TCP 头的偏移量
    // if (!skb_make_writable(skb, tcphoff + sizeof(*thd))) {
    //     WARN("SKB not writable\n");
    //     return NF_DROP;
    // }


    /* Conntrack check */
    if (!(ct = nf_ct_get(skb, &ctinfo)) || nf_ct_is_untracked(ct)) {
        DEBUG("Untracked connection\n");
        return NF_ACCEPT;
    }

    unsigned int tcphoff = skb_transport_offset(skb);  // TCP 头的偏移量
    unsigned int tcplen = thd->doff * 4;             // TCP 头长度
    if (!skb_make_writable(skb, tcphoff + tcplen)) {  // 确保 IP 头和 TCP 头可写
        WARN("SKB not writable\n");
        return NF_DROP;
    }


    /* Update header pointers */
    // iph = ip_hdr(skb);
    // thd = tcp_hdr(skb);

    /* Register client */
    client_add(iph->daddr, thd->dest, iph->saddr);

    /* Modify destination */
    iph->daddr = htonl(gw_ip);
    thd->dest = htons(CAPTIVE_PORT);

    /* Update conntrack */
    new_tuple = ct->tuplehash[IP_CT_DIR_REPLY].tuple;
    new_tuple.dst.u3.ip = htonl(gw_ip);
    new_tuple.dst.u.tcp.port = htons(CAPTIVE_PORT);
    nf_conntrack_alter_reply(ct, &new_tuple);

    /* Recalculate checksums */
    iph->check = 0;
    iph->check = ip_fast_csum((u8 *)iph, iph->ihl);
    DEBUG("IP Header Checksum: %04x\n", ntohs(iph->check));
    
    thd->check = 0;
    thd->check = tcp_v4_check(thd->doff * 4, iph->saddr, iph->daddr,
                            csum_partial(thd, thd->doff * 4, 0));
    DEBUG("TCP Header Checksum: %04x\n", ntohs(thd->check));

    atomic_inc(&redirect_count);
     /* 🆕 Log redirection details */
    INFO("rill Redirect %pI4:%hu => %pI4:%d via %s\n", 
         &ct->tuplehash[IP_CT_DIR_ORIGINAL].tuple.src.u3.ip,
         ntohs(ct->tuplehash[IP_CT_DIR_ORIGINAL].tuple.src.u.tcp.port),
         &iph->daddr, ntohs(thd->dest),
         in ? in->name : "null");

    return NF_ACCEPT;
}

/******************** POSTROUTING Processing ********************/
static unsigned int post_hook(
    unsigned int hooknum,
    struct sk_buff *skb,
    const struct net_device *in,
    const struct net_device *out,
    int (*okfn)(struct sk_buff *))
{
    struct iphdr *iph;
    struct tcphdr *thd;
    struct portal_client *cli, *tmp;
    struct nf_conntrack_tuple new_tuple;
    enum ip_conntrack_info ctinfo;

    /* Check redirect status */
    if (!redirect_enabled) {
        DEBUG("post_hook Redirect disabled, bypassing\n");
        return NF_ACCEPT;
    }

    /* Basic validation */
    if (!skb || !(iph = ip_hdr(skb)) || iph->protocol != IPPROTO_TCP)
        return NF_ACCEPT;

    if (!(thd = (struct tcphdr *)((int *) iph + iph->ihl)))
        return NF_ACCEPT;

    DEBUG("POST: TCP %pI4:%hu -> %pI4:%hu device %s\n",
          &iph->saddr, ntohs(thd->source),
          &iph->daddr, ntohs(thd->dest), out ? out->name : "null");

    /* Filter gateway-originated responses */
    if (iph->saddr != htonl(gw_ip) || ntohs(thd->source) != CAPTIVE_PORT) {
        //DEBUG("Bypassing source-ip not gw_ip or source-port not %d", CAPTIVE_PORT);
        return NF_ACCEPT;
    }
        
    /* Cleanup expired entries */
    spin_lock(&client_lock);
    list_for_each_entry_safe(cli, tmp, &client_list, list) {
        if (time_after(jiffies, cli->expiry)) {
            DEBUG("Cleaning expired client: %pI4:%hu\n", 
                  &cli->orig_daddr, ntohs(cli->orig_dport));
            list_del(&cli->list);
            kfree(cli);
        }
    }
    spin_unlock(&client_lock);

    /* Restore original addresses */
    spin_lock(&client_lock);
    list_for_each_entry(cli, &client_list, list) {
        if (cli->orig_dport == thd->dest) {
            struct nf_conn *ct = nf_ct_get(skb, &ctinfo);
            if (ct) {
                /* Restore original addresses */
                iph->saddr = cli->orig_daddr;
                thd->source = cli->orig_dport;

                /* Update conntrack */
                new_tuple = ct->tuplehash[IP_CT_DIR_ORIGINAL].tuple;
                new_tuple.src.u3.ip = cli->orig_daddr;
                new_tuple.src.u.tcp.port = cli->orig_dport;
                nf_conntrack_alter_reply(ct, &new_tuple);

                /* Recalculate checksums */
                iph->check = 0;
                iph->check = ip_fast_csum((u8 *)iph, iph->ihl);
                
                thd->check = 0;
                thd->check = tcp_v4_check(thd->doff * 4, iph->saddr, iph->daddr,
                                        csum_partial(thd, thd->doff * 4, 0));

                /* 🆕 Log restoration details */
                INFO("rill Restored %pI4:%hu via %s\n", 
                    &cli->orig_daddr, ntohs(cli->orig_dport),
                    out ? out->name : "null");

                break;
            }
        }
    }
    spin_unlock(&client_lock);

    return NF_ACCEPT;
}

/******************** Module Infrastructure ********************/
static struct nf_hook_ops pre_ops = {
    .hook     = pre_hook,
    .owner    = THIS_MODULE,
    .pf       = PF_INET,
    .hooknum  = NF_INET_PRE_ROUTING,
    .priority = NF_IP_PRI_FIRST,
};

static struct nf_hook_ops post_ops = {
    .hook     = post_hook,
    .owner    = THIS_MODULE,
    .pf       = PF_INET,
    .hooknum  = NF_INET_POST_ROUTING,
    .priority = NF_IP_PRI_LAST,
};


/******************** Add Proc Interface (Optional) ********************/
#ifdef CONFIG_PROC_FS
static struct proc_dir_entry *proc_entry;

/* 定义 proc_show 函数 */
static int proc_show(struct seq_file *m, void *v)
{
    seq_printf(m, "Redirect status: %s\n", 
              redirect_enabled ? "Enabled" : "Disabled");
    seq_printf(m, "Active clients: %d\n", 
              atomic_read(&redirect_count));
    return 0;
}

/* 定义 proc_open 函数 */
static int proc_open(struct inode *inode, struct file *file)
{
    return single_open(file, proc_show, NULL);
}

/* 定义 proc_write 函数 */
static ssize_t proc_write(struct file *file, const char __user *buffer,
                        size_t length, loff_t *ppos)
{
    char cmd;
    int value;

    if (copy_from_user(cmd, buffer, min(length, sizeof(cmd))))
        return -EFAULT;

    if (sscanf(cmd, "enable=%d", &value) == 1) {
        redirect_enabled = value;
        if (value == 0 && toggle_cleanup)
            cleanup_clients();
        INFO("Redirect %s via proc\n", value ? "enabled" : "disabled");
    }
    
    return length;
}

/* 定义 proc_fops 结构体 */
static const struct file_operations proc_fops = {
    .owner   = THIS_MODULE,
    .open    = proc_open,
    .read    = seq_read,
    .write   = proc_write,
    .llseek  = seq_lseek,
    .release = single_release,
};

/* 创建 proc 文件 */
static int __init create_portal_proc_entry(void)
{
    proc_entry = proc_create("portal_control", 0644, NULL, &proc_fops);
    return proc_entry ? 0 : -ENOMEM;
}

/* 删除 proc 文件 */
static void remove_portal_proc_entry(void)
{
    if (proc_entry) {
#if LINUX_VERSION_CODE >= KERNEL_VERSION(3, 10, 0)
        proc_remove(proc_entry);
#else
        remove_proc_entry("portal_control", NULL);
#endif
    }
}
#endif

/******************** Modified Init/Exit ********************/
static int __init portal_init(void)
{
    int ret = 0;

    INFO("Module loaded (Redirect: %s)\n", 
        redirect_enabled ? "ENABLED" : "DISABLED");
        
#ifdef CONFIG_PROC_FS
    if (create_portal_proc_entry())
        WARN("Failed to create proc entry\n");
#endif
    unsigned int nl_gw_ip = htonl(gw_ip);
    
    INFO("Module loaded (Gateway: %pI4:%d)\n", &nl_gw_ip, CAPTIVE_PORT);
    
    /* 修复后的钩子注册 */
    if ((ret = nf_register_hook(&pre_ops))) {
        WARN("Pre-routing hook registration failed (%d)\n", ret);
        goto err_pre;
    }

    if ((ret = nf_register_hook(&post_ops))) {
        WARN("Post-routing hook registration failed (%d)\n", ret);
        goto err_post;
    }

    return 0;

err_post:
    nf_unregister_hook(&pre_ops);
err_pre:
    return ret;
}

static void __exit portal_exit(void)
{
    struct portal_client *cli, *tmp;

#ifdef CONFIG_PROC_FS
    remove_portal_proc_entry();
#endif
    
    nf_unregister_hook(&pre_ops);
    nf_unregister_hook(&post_ops);

    /* Cleanup all clients */
    spin_lock(&client_lock);
    list_for_each_entry_safe(cli, tmp, &client_list, list) {
        list_del(&cli->list);
        kfree(cli);
    }
    spin_unlock(&client_lock);

    INFO("Module unloaded, Total redirects: %d\n", atomic_read(&redirect_count));
}


module_init(portal_init);
module_exit(portal_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("RILL");
MODULE_DESCRIPTION("Captive Portal Traffic Redirection Module");