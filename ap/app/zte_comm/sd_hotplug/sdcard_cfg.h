#ifndef _SDCARD_CFG_H_
#define _SDCARD_CFG_H_

#define ZTE_FOLDER_STR                   "document"
#define ZTE_FILE_STR                     "file"

#ifdef _MBB_OS_UCLINUX
#define SD_CARD_PATH                     "/mnt/jffs2/etc_rw/config/mmc2"          // "/mmc2"
#define SD_CARD_PATH_PR                  "/mnt/jffs2/etc_rw/config"
#define ZTE_HTTPSHARE_DB_DIR             "/mnt/jffs2/etc_rw/config/httpshare_db"                  //usr/httpshare_db 
#define ZTE_HTTPSHARE_DB_PATH            "/mnt/jffs2/etc_rw/config/httpshare_db/httpshare.db"     //"/usr/httpshare_db/httpshare.db"  
#define TCARD_UPLOAD_FILE                "/mnt/jffs2/etc_rw/config/upload.data"
#else
#define SD_CARD_PATH                     "/etc_rw/config/mmc2"          // "/mmc2"
#define SD_CARD_PATH_PR                  "/etc_rw/config"
#define ZTE_HTTPSHARE_DB_DIR             "/etc_rw/config/httpshare_db"                  //usr/httpshare_db 
#define ZTE_HTTPSHARE_DB_PATH            "/etc_rw/config/httpshare_db/httpshare.db"     //"/usr/httpshare_db/httpshare.db"  
#define TCARD_UPLOAD_FILE                "/etc_rw/config/upload.data"
#endif

#define USB_DEV_SDCARD_PATH              "/dev/mmcblk0p1"
#define USB_DEV_SDCARD_PATH_BACK         "/dev/mmcblk0"

#define TCARD_SIZE_FILE                  "/proc/proc_sd/size"

#define HTTPSHARE_PATH_INLEGAL           "/mmc2/.."    // user document path

#define USB_HTTPSHARE_LUN0_PATH           "/sys/devices/platform/zx29_hsotg.0/gadget/lun0/file"
#define USB_HTTPSHARE_LUN1_PATH           "/sys/devices/platform/zx29_hsotg.0/gadget/lun1/file"

#endif

