include $(zte_app_mak)
#include ../net_team.mk
include $(COMMON_MK)

ifeq ($(CUR_USED_OS),LINUX)
CC	= $(CROSS_COMPILE)gcc
STRIP	= $(CROSS_COMPILE)strip
LD	= $(CROSS_COMPILE)ld
AR	= $(CROSS_COMPILE)ar
endif

EXEC_ZX = fota_dm_zx
EXEC_ZX_TEST = fota_zx_test


AM_CFLAGS += -D__packed__= \
	-I./inc  \
	-I$(zte_lib_path)/libzte_dmapp/inc \
	-I$(zte_app_path)/include \
	-I$(zte_lib_path)/libnvram

CFLAGS += $(AM_CFLAGS) -O2 -fPIC -c -g -Wall
LDFLAGS +=  -lnvram  -L$(zte_lib_path)/libnvram
LDFLAGS +=  -lpthread  
LDFLAGS +=  -lztedmapp  -L$(zte_lib_path)/libzte_dmapp
LDFLAGS +=  -lsoftap    -L$(zte_lib_path)/libsoftap
LDFLAGS  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer


CFLAGS += -I$(zte_lib_path)/libdmgr

LDFLAGS_ZX += -lcurl -L$(zte_lib_path)/libcurl/install/lib
LDFLAGS_ZX += -ldmgr -L$(zte_lib_path)/libdmgr $(LDFLAGS)


h_sources += $(wildcard ./inc/*.h)
c_sources = $(wildcard ./src/*.c)
OBJS += $(patsubst %.c,%.o, $(c_sources))

OBJS_TEST = fota_test.o ./src/fota_dm_dl_gs.o ./src/fota_dm_utils.o ./src/fota_dm_netdog.o



all:$(EXEC_ZX) $(EXEC_ZX_TEST) 
ifeq ($(PC_LINT_CHECK), no)
	$(shell echo "$(filter -D% -I%, $(CFLAGS))" | tr " " "\n" > $(ROOT_DIR)/build/pclint/mywork.lnt )
	$(PC-LINT) $(patsubst %.o, %.c, $(OBJS)) > ./fota.lnt
endif	


$(EXEC_ZX):$(OBJS)
	@echo "=====================build fota_dm_zx====================="
	$(CC) -o $@  $(OBJS) -Wl,--start-group $(LDFLAGS_ZX) -Wl,--end-group
	cp $(EXEC_ZX) $(EXEC_ZX).elf
	
%.o : %.c $(h_sources)
	$(CC) $(CFLAGS) -o $@ $<


$(EXEC_ZX_TEST): $(OBJS_TEST)
	@echo "=====================build fota_zx_test====================="
	$(CC) -o $@  $(OBJS_TEST) -Wl,--start-group $(LDFLAGS_ZX) -Wl,--end-group
	cp $(EXEC_ZX_TEST) $(EXEC_ZX_TEST).elf
	
romfs:
	@echo "==========> zte FOTA rootfs $(bin_PROGRAMS)<=========="
	$(ROMFSINST) $(EXEC_ZX) /bin/$(EXEC_ZX)
#	$(ROMFSINST) $(EXEC_ZX_TEST) /bin/$(EXEC_ZX_TEST)
ifeq ($(CONFIG_USER_SINGLE_DM), gs)
	rm -rf $(ROOTFS_DIR)//bin/$(EXEC_ZX)
endif
	
clean: 
	@echo "==========> clean fota dm zx<=========="
	rm -f $(EXEC_ZX) $(EXEC_ZX_TEST) *.gdb $(EXEC_ZX).elf $(EXEC_ZX_TEST).elf ./src/*.o  *.o
