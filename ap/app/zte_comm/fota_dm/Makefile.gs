include $(zte_app_mak)
#include ../net_team.mk
include $(COMMON_MK)

ifeq ($(CUR_USED_OS),LINUX)
CC	= $(CROSS_COMPILE)gcc
STRIP	= $(CROSS_COMPILE)strip
LD	= $(CROSS_COMPILE)ld
AR	= $(CROSS_COMPILE)ar
endif


EXEC_GS = fota_dm_gs
EXEC_GS_TEST = fota_gs_test

AM_CFLAGS += -D__packed__= \
	-I./inc  \
	-I$(zte_lib_path)/libzte_dmapp/inc \
	-I$(zte_app_path)/include \
	-I$(zte_lib_path)/libnvram

CFLAGS += $(AM_CFLAGS) -O2 -fPIC -c -g -Wall

LDFLAGS +=  -lnvram  -L$(zte_lib_path)/libnvram
LDFLAGS +=  -lpthread  
LDFLAGS +=  -lztedmapp  -L$(zte_lib_path)/libzte_dmapp
LDFLAGS +=  -lsoftap    -L$(zte_lib_path)/libsoftap
LDFLAGS  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer

LDFLAGS  += -lcurl -L$(zte_lib_path)/libcurl/install/lib


CFLAGS 	+= -I./gs_lib


LIBSHARE_GS =  ./gs_lib/libdmgr.so.1
LDFLAGS_GS +=  -L./gs_lib -ldmgr  $(LDFLAGS)


h_sources += $(wildcard ./inc/*.h)
c_sources = $(wildcard ./src/*.c)

OBJS += $(patsubst %.c,%.o, $(c_sources))


OBJS_TEST = fota_test.o ./src/fota_dm_dl_gs.o ./src/fota_dm_utils.o ./src/fota_dm_netdog.o



all:$(EXEC_GS) $(EXEC_GS_TEST)
ifeq ($(PC_LINT_CHECK), no)
	$(shell echo "$(filter -D% -I%, $(CFLAGS))" | tr " " "\n" > $(ROOT_DIR)/build/pclint/mywork.lnt )
	$(PC-LINT) $(patsubst %.o, %.c, $(OBJS)) > ./fota.lnt
endif	


	
%.o : %.c $(h_sources)
	$(CC) $(CFLAGS) -o $@ $<


$(EXEC_GS): $(OBJS)
	@echo "=====================build fota_dm_gs====================="
	(cd ./gs_lib;rm libdmgr.so;ln -s libdmgr.so.1  libdmgr.so; cd ..)
	$(CC) -o $@  $(OBJS) -Wl,--start-group $(LDFLAGS_GS) -Wl,--end-group
	cp $(EXEC_GS) $(EXEC_GS).elf

	
$(EXEC_GS_TEST): $(OBJS_TEST)
	@echo "=====================build fota_gs_test====================="
	$(CC) -o $@  $(OBJS_TEST) -Wl,--start-group $(LDFLAGS_GS) -Wl,--end-group
	cp $(EXEC_GS_TEST) $(EXEC_GS_TEST).elf	
	
romfs:
	@echo "==========> zte FOTA rootfs $(bin_PROGRAMS)<=========="
	$(ROMFSINST) $(EXEC_GS) /bin/$(EXEC_GS)
	$(ROMFSINST) $(LIBSHARE_GS) /lib/
#	$(ROMFSINST) $(EXEC_GS_TEST) /bin/$(EXEC_GS_TEST)
ifeq ($(CONFIG_USER_SINGLE_DM), zx)
	rm -rf $(ROOTFS_DIR)//bin/$(EXEC_GS)
endif
	
clean: 
	@echo "==========> clean fota dm gs<=========="
	rm -f $(EXEC_GS) $(EXEC_GS_TEST) *.gdb $(EXEC_GS).elf $(EXEC_GS_TEST).elf ./src/*.o  *.o
