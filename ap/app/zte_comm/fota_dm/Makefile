#*******************************************************************************
# Default target
#*******************************************************************************

.PHONY: all romfs

ALL: all romfs

all:
	${MAKE} -j1 -f Makefile.zx  clean
	${MAKE} -j1 -f Makefile.zx  all
	${MAKE} -j1 -f Makefile.gs  clean 
	${MAKE} -j1 -f Makefile.gs  all 


romfs:
	${MAKE} -j1 -f Makefile.gs  romfs 
	${MAKE} -j1 -f Makefile.zx  romfs

clean:
	${MAKE} -j1 -f <PERSON>file.gs  clean 
	${MAKE} -j1 -f <PERSON><PERSON><PERSON>.zx  clean
