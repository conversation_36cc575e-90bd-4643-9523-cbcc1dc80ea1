#ifndef __FOTA_DM_MACROS_H_
#define __FOTA_DM_MACROS_H_

#define FOTA_CFG_GET_ITEMLEN 1024

#define NV_FOTA_UPGRADE_FLAG                "fota_update_flag"
/*有三个更新结果，需要优化*/
#define NV_FOTA_UPGRADE_RESULT                  "fota_upgrade_result"
#define NV_FOTA_UPGRADE_RESULT_MMI              "fota_upgrade_result_mmi"

#define NV_FOTA_CURR_UPGRADE_STATE              "fota_current_upgrade_state"
#define NV_FOTA_VERSION_FORCE_INSTALL                  "fota_version_force_install"

#define NV_FOTA_PWRON_AUTO_CHECK                     "pwron_auto_check"

typedef enum {
	ZTE_DUA_UNKNOWN = -1,
	ZTE_DUA_VERIFY_SUCCESS = 0,
	ZTE_DUA_VERIFY_FAIL,
	ZTE_DUA_SYSTEM_UPDATE_SUCCESS,
	ZTE_DUA_SYSTEM_UPDATE_FAIL,
	ZTE_DUA_RECOVERY_UPDATE_SUCCESS,
	ZTE_DUA_RECOVERY_UPDATE_FAIL,
	ZTE_DUA_NO_NEED_UPDATE,
	ZTE_DUA_NEED_UPDATE
} ZTE_update_status_type;



//FOTA自动检测设置
typedef enum {
	FOTA_POLLING_SET_DISABLE,
	FOTA_POLLING_SET_ENABLE,
} FOTA_POLLING_SET;

//FOTA搜包结果
typedef enum {
	FOTA_NO_NEW_VERSION,
	FOTA_HAS_OPTIONAL_VERSION,
	FOTA_HAS_CRITICAL_VERSION,
	FOTA_SEARCH_RLT_FAIL,
	FOTA_PKG_TOO_BIG,
	FOTA_CI_RUNNING,
} FOTA_SEARCH_RLT;

//FOTA升级包下载确认
typedef enum {
	FOTA_PKG_DL_CANCLE,
	FOTA_PKG_DL_ACCEPT,
} FOTA_PKG_DL_REQ;

//FOTA升级包下载结果
typedef enum {
	FOTA_PKG_DL_FAIL,
	FOTA_PKG_DL_SUCC,
} FOTA_PKG_DL_RLT;

//FOTA升级请求及确认
typedef enum {
	FOTA_UPDATE_DEFAULT = -1,
	FOTA_UPDATE_NOT_RDY,
	FOTA_UPDATE_RDY,
} FOTA_UPDATE_REQ;

//FOTA升级结果
typedef enum {
	FOTA_UPDATE_FAIL,
	FOTA_UPDATE_SUCC,
	FOTA_UPDATE_REQ_TIMEOUT,
} FOTA_UPDATE_RLT;

//FOTA复位操作结果
typedef enum {
	FOTA_RESET_RLT_FAIL,
	FOTA_RESET_RLT_SUCC,
	FOTA_RESET_NOT_ALLOW,
} FOTA_RESET_RLT;



#endif
