#ifndef __FOTA_DM_UTILS_H_
#define __FOTA_DM_UTILS_H_



#include <stdio.h>
#include <stdlib.h>

#include "softap_api.h"

#define TRUE 1
#define FALSE 0

#ifndef LOG_ERR
#define LOG_ERR  (3)
#endif

#ifndef LOG_DBG
#define LOG_DBG (1)
#endif

#ifndef LOG_NORM
#define LOG_NORM (2)
#endif

#define MODULE_NAME "fota_dm"


#if 1
#define FOTA_LOG(level, ...) slog(MODULE_NAME,level,__VA_ARGS__)

#define LOGD(fmt, args...) FOTA_LOG(LOG_NORM, "[FOTA_DM][NORMAL][%s-%d] "fmt,  __FUNCTION__, __LINE__, ##args)
#define LOGE(fmt, args...) FOTA_LOG(LOG_ERR, "[FOTA_DM][ERROR][%s-%d] "fmt,  __FUNCTION__, __LINE__, ##args)
#else
#define LOGD(fmt, args...) do {printf("[FOTA-DM][DEBUG][%s:%d]"fmt, __FUNCTION__, __LINE__, ##args);}while(0)
#define LOGE(fmt, args...)  do {printf("[FOTA-DM][ERROR][%s:%d]"fmt, __FUNCTION__, __LINE__, ##args);}while(0)
#endif
#define FREE(ptr) if(ptr){free(ptr); ptr=NULL;}
#define CASE_RETURN_STR(x) case x: return #x;

#define FOTA_DM_RTC_TIMER_ID 0
#define FOTA_DM_UTC_TIMER_ID 1


#define GOTO_OUT_ERR_IF(exper)                      \
    do {                                            \
        if (exper) {                                \
            goto out_err;                           \
        }                                           \
    } while (0)



#define FOTA_DM_WAKE_LOCK "fota_dm"


#define NV_FOTA_UPGRADE_RESULT_INTERNAL                  "fota_upgrade_result_internal"
#define NV_FOTA_PKG_DOWNLOADED							 "fota_pkg_downloaded"


/*********************nv related************************************ */

/*nv update flags*/
int fota_dm_get_update_flag(int *fota_flag);
void fota_dm_clear_update_flag(void);
void fota_dm_set_update_flag(void);


int fota_dm_is_auto_polling_on(void);
int fota_dm_is_poweron_auto_check(void);

int fota_dm_is_roaming(void);

int fota_dm_get_inner_version(char* version, size_t size);
int fota_dm_get_imei(char* imei, size_t size);

int fota_dm_get_dl_url(char* url, size_t size, char* suffix);
int fota_dm_get_chk_url(char* url, size_t size, char *suffix);
int fota_dm_get_reg_url(char* url, size_t size, char* suffix);
int fota_dm_get_report_dlr_url(char* url, size_t size, char* suffix);
int fota_dm_get_report_upgr_url(char* url, size_t size, char* suffix);
int fota_dm_get_report_sales_url(char* url, size_t size, char* suffix);


int fota_dm_get_fota_oem(char* fota_oem, size_t size);

int fota_dm_get_fota_token(char *fota_token, size_t size, char* suffix);

int fota_dm_get_fota_device_type(char *fota_device_type, size_t size);

int fota_dm_get_platform(char *fota_platform, size_t size);

int fota_dm_get_models(char * fota_models, size_t size);


int fota_dm_get_product_id(char * fota_product_id, size_t size);


int fota_dm_get_product_secret(char * fota_product_secret, size_t size);


int fota_dm_get_app_version(char * fota_app_version, size_t size);


int fota_dm_get_network_type(char * fota_network_type, size_t size);



int fota_dm_is_force_package(void);

int fota_dm_set_force_package(void);

int fota_dm_clear_force_package(void);


int fota_dm_set_nv_int(char* nv_name, int value);
int fota_dm_set_nv_long(char* nv_name, long value);
int fota_dm_get_nv_int(char* nv_name);

int fota_dm_get_nv_string(char* nv_name, char* nv_value, size_t buf_size);
int fota_dm_set_nv_string(char *nv_name, char* value);

int fota_dm_set_download_progress(int progress);
/*****************************update status*************************************/

int fota_dm_clear_update_status(void);
int fota_dm_get_update_status(int *fota_status);

int fota_dm_allow_roaimg_update(void);

int fota_dm_is_power_enough(void);

/*********************os interface ************************************ */

size_t  fota_dm_get_free_space(const char* path);

size_t fota_dm_get_file_size(const char* path);

int fota_dm_rename_file(const char*oldpath, const char*newpath);
int fota_dm_sync_file(char *file_path);

int fota_dm_mkdirs(const char* path, mode_t mode);
int fota_dm_is_file_exist(const char* path);
int fota_dm_write_file(const char*path, const char*value, int size);

int fota_dm_write_file_int(const char*path, int value);
int fota_dm_read_file(const char*path, char*buf, size_t sz);

int fota_dm_read_file_int(const char* path, int *val);
int fota_dm_remove(const char*path);

void fota_dm_set_process_name(const char* name);
void fota_dm_set_thread_name(const char*name);

/***********************fota dm reboot************************/
void fota_dm_reboot(void);

/***********************call fota_update ************************/
int fota_dm_update_recoveryfs(void);
int  fota_dm_verify_package(void);


/***********************fota dm alarm************************/
int fota_dm_add_rtc_alarm(int seconds, int alarm_id);
int fota_dm_add_utc_alarm(struct tm* sec, int alarm_id);
int fota_dm_del_alarm(int alarm_id);
/***********************fota dm network************************/
int fota_dm_is_network_avaiable(void);
int fota_dm_wait_network_ready(int time_interval, int retry_times);

/**********************wake lock************************************/
int fota_dm_wake_unlock(const char* lock_name);
int fota_dm_wake_lock(const char* lock_name);
int fota_dm_timeout_lock_sec(const char* lock_name, int sec);
int fota_dm_timeout_wakelock();


#endif
