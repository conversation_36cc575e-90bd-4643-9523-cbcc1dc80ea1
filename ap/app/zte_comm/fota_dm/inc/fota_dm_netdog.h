#ifndef __FOTA_DM_NETDOG_H_
#define __FOTA_DM_NETDOG_H_


/*set whether network is ok, 0:network ok, 1:network has problem*/
int fota_dm_netdog_set_network_exception(int has_exception);

/*set whether check version failed, 0: check ok, other: failed*/
int fota_dm_netdog_set_check_version_result(int result);

/*set download version result, 0: download ok, other: download failed*/
int fota_dm_netdog_set_download_version_result(int result);

/* set whether sntp failed, 0: not failed, 1:failed*/
int fota_dm_netdog_set_sntp_exception(int has_exception);

/* set whether get imei failed, 0: not failed, 1:failed*/
int fota_dm_netdog_set_imei_exception(int has_exception);

/* set whether get cr_inner_version failed, 0: not failed, 1:failed*/
int fota_dm_netdog_set_cr_inner_version_exception(int has_exception);
#endif
