/**
 * Copyright (C) 2017 Sanechips Technology Co., Ltd.
 * <AUTHOR>
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

/*******************************************************************************
 *                           Include header files                              *
 ******************************************************************************/

#define _GNU_SOURCE


#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <errno.h>
#include <sys/types.h>

#include <unistd.h>
#include <sys/ipc.h>

#include <sys/msg.h>
#include <signal.h>
#include <string.h>
#include <assert.h>
#include <semaphore.h>
#include <time.h>
#include <libgen.h>
#include <limits.h>


#include "fota_dm_utils.h"
#include "softap_api.h"
#include "fota_dm_macros.h"
#include "fota_common.h"
#include "rtc_timer.h"
#include "fota_dm_dl.h"
//#include "fota_dm_netdog.h"

/*******************************************************************************
 *                             Macro definitions                               *
 ******************************************************************************/


/*Commands, main thread send to work thread*/
typedef enum {
    FOTA_DM_RESUME_UPGRADE = 0,
    FOTA_DM_RESUME_DOWNLOAD,
    FOTA_DM_CHECK_VERSION,
    FOTA_DM_START_UPDATE,
    FOTA_DM_DOWNLOAD_VERSION,
    FOTA_DM_START_POLLING,

} FOTA_DM_MSG;

/*Response, work thread send to main thread for result*/
typedef enum {
    FOTA_DM_CHECK_VERSION_RSP = MSG_CMD_FOTADLRESUME_IND + 100,
    FOTA_DM_DOWNLOAD_VERSION_RSP,
    FOTA_DM_POLLING_RSP,
} FOTA_DM_RSP;


#define AT_MSG_NULL -1
/*******************************************************************************
 *                             Type definitions                                *
 ******************************************************************************/
#define POLLING_INTERVAL_FOR_TESTMODE (60*5)  //Test mode polling interval
#define AUTO_POLLING_AFTER_START (60*5) //auto polling 5 minutes after system boot up
#define DEFAULT_POLLING_INTERVAL  (1) // default polling interval
#define SECONDS_PER_DAY (60*60*24)  //seconds per day


/*******************************************************************************
 *                        Local function declarations                          *
 ******************************************************************************/

/*******************************************************************************
 *                         Local variable definitions                          *
 ******************************************************************************/

/*******************************************************************************
 *                        Global variable definitions                          *
 ******************************************************************************/

/*semaphore, used to coordinate the work thread and main thread*/
static sem_t sem;

/*word thread id*/
static pthread_t g_workthread_id;

static int g_utc_alarm_setted = FALSE;

/*******************************************************************************
 *                      Inline function implementations                        *
 ******************************************************************************/

/*******************************************************************************
 *                      Local function implementations                         *
 ******************************************************************************/

/*convert msgid to string, used in log*/
static char * fota_dm_msgid_to_string(int msgid)
{
    switch(msgid) {
        CASE_RETURN_STR(MSG_CMD_FOTA_WEBUI_CHANGE_PARAMETER);
        CASE_RETURN_STR(MSG_CMD_FOTADL_REQ);
        CASE_RETURN_STR(MSG_CMD_FOTADY_REQ);
        CASE_RETURN_STR(MSG_CMD_RESET_NOTIFY);
        CASE_RETURN_STR(MSG_CMD_FOTAMANUAL_REQ);
        CASE_RETURN_STR(MSG_CMD_FOTA_WEBUI_START_FOTA);
        CASE_RETURN_STR(RTC_MSG_ALARM_ELAPSED);
        CASE_RETURN_STR(FOTA_DM_CHECK_VERSION_RSP);
        CASE_RETURN_STR(FOTA_DM_DOWNLOAD_VERSION_RSP);
        CASE_RETURN_STR(FOTA_DM_POLLING_RSP);
		CASE_RETURN_STR(RTC_MSG_TIME_CHANGED);
    default:
        return "MSG_CMD_UNKNOWN";
    }
}

/*msg id to string, used in log*/
static char* thread_msgid_to_string(int msgid)
{
    switch(msgid) {
        CASE_RETURN_STR(FOTA_DM_CHECK_VERSION);
        CASE_RETURN_STR(FOTA_DM_RESUME_DOWNLOAD);
        CASE_RETURN_STR(FOTA_DM_START_UPDATE);
        CASE_RETURN_STR(FOTA_DM_DOWNLOAD_VERSION);
        CASE_RETURN_STR(FOTA_DM_START_POLLING);
    default:
        return "MSG_CMD_UNKNOWN";
    }
}
/*check and make fota related directories*/
static int fota_dm_check_and_make_fota_dir(void)
{
    int bexist = FALSE;
    int ret = -1;
    bexist = fota_dm_is_file_exist(ZTE_FOTA_MAIN_PATH);
    if(!bexist) {
        ret = fota_dm_mkdirs(ZTE_FOTA_MAIN_PATH, S_IRWXU);
        if(ret < 0) {
            LOGE("failed to mkdir %s\n", ZTE_FOTA_MAIN_PATH);
            return -1;
        }
    }
    if(!fota_dm_is_file_exist(FOTA_UPDATE_STATUS_FILE)) {
        /*file not exist, just create it*/
        fota_dm_write_file_int(FOTA_UPDATE_STATUS_FILE, ZTE_DUA_NO_NEED_UPDATE);
    }
    return 0;
}

/*create message queue*/
static int fota_dm_create_msg_queue(int module_id)
{
    int msq_id = msgget(module_id, IPC_CREAT | 0600);
    if(msq_id == -1) {
        LOGE("failed to create msg queue module_id=%d, errno=%d\n", module_id, errno);
    }
    return msq_id;
}


/*wrapper of send IPC message interface*/
static int fota_dm_send_message_wrapper(int src_id, int dst_id, int cmd,
                                        int cmd_param)
{

    if(cmd_param == AT_MSG_NULL)
        ipc_send_message(src_id, dst_id, cmd, 0, NULL, 0);
    else {
        char msginfo[32] = {0};
        snprintf(msginfo, 32, "%d", cmd_param);
        return ipc_send_message(src_id, dst_id, cmd, strlen(msginfo) + 1,
                                (unsigned char*)msginfo, 0);
    }
    return 0;
}


/*main thread send message to work thread*/
static int fota_dm_send_message_to_work(int cmd, int cmd_param)
{
	fota_dm_timeout_wakelock();
    LOGD("send cmd=%d (%s) param=%d to work!\n",  cmd, thread_msgid_to_string(cmd),
         cmd_param);
    return fota_dm_send_message_wrapper(MODULE_ID_DM_WEBUI_AT, MODULE_ID_DM, cmd,
                                        cmd_param);

}

/*work thread send message to main thread*/
static int fota_dm_send_message_to_main(int cmd, int cmd_param)
{
	fota_dm_timeout_wakelock();
    LOGD("send cmd=%d (%s) param=%d to main!\n",  cmd, fota_dm_msgid_to_string(cmd),
         cmd_param);
    return fota_dm_send_message_wrapper(MODULE_ID_DM, MODULE_ID_DM_WEBUI_AT, cmd,
                                        cmd_param);

}
static const char* fota_msg_to_atctl_to_string(unsigned int msgid)
{
    switch(msgid) {
        CASE_RETURN_STR(MSG_CMD_FOTAVERSION_IND);
        CASE_RETURN_STR(MSG_CMD_FOTAPKG_IND);
        CASE_RETURN_STR(MSG_CMD_IMEI_REQ);
        CASE_RETURN_STR(MSG_CMD_ZVERSION_REQ);
        CASE_RETURN_STR(MSG_CMD_FOTADLRESUME_IND);
        CASE_RETURN_STR(MSG_CMD_FOTARLT_IND);
    default:
        return "MSG_UNKNOWN";
    }
}
/*main thread, send message to atctl*/
static int fota_dm_send_cmd_to_atctl(int cmd, int cmd_param)
{
    LOGD("send cmd=%d (%s) param=%d to at_ctl!\n",  cmd,
         fota_msg_to_atctl_to_string(cmd), cmd_param);
    return fota_dm_send_message_wrapper(MODULE_ID_DM_WEBUI_AT, MODULE_ID_AT_CTL,
                                        cmd, cmd_param);
}

static int fota_dm_send_soc_msg_to_mmi(int cmd, int cmd_param)
{
	fota_dm_timeout_wakelock();
    LOGD("send cmd=%d (%s) param=%d soc msg to mmi!\n",  cmd,
         fota_msg_to_atctl_to_string(cmd), cmd_param);
	if(cmd_param == AT_MSG_NULL)
		ipc_send_message2(MODULE_ID_DM_WEBUI_AT, MODULE_ID_MMI_SVR, cmd, 0, NULL,0);
		//send_soc_msg(NEAR_PS, MODULE_ID_ZCORE, cmd, 0, NULL);
    else {
        return ipc_send_message2(MODULE_ID_DM_WEBUI_AT, MODULE_ID_MMI_SVR, cmd, sizeof(cmd_param), (UCHAR *)(&cmd_param), 0);
			//platform_send_msg(MODULE_ID_DM_WEBUI_AT, MODULE_ID_MMI_SVR, cmd, sizeof(cmd_param), (UCHAR *)(&cmd_param));
			//send_soc_msg(NEAR_PS,MODULE_ID_ZCORE, cmd, sizeof(cmd_param), (UCHAR *)(&cmd_param));
    }
    return 0;
}

/*wrapper of msgrcv*/
static ssize_t fota_dm_msg_recv(int msqid, void* msgp, size_t msgsz,
                                long msgtype, int msgflag)
{
    return msgrcv(msqid, msgp, msgsz, msgtype, msgflag);
}

/*save version state to nv*/
void fota_set_version_state_to_nv(char *version_state)
{
    if(NULL == version_state)
        return;
    cfg_set(NV_FOTA_NEW_VERSION_STATE, version_state);
}
/*save upgrade state to nv*/
void fota_set_upgrade_state_to_nv(char *upgrade_state)
{
    if(NULL == upgrade_state)
        return;
    cfg_set(NV_FOTA_CURR_UPGRADE_STATE, upgrade_state);
}
/*save user selector info to nv*/
void fota_set_selector_to_nv(char *selector)//klocwork
{
    if(NULL == selector)
        return;
    cfg_set(NV_FOTA_UPGRADE_SELECTOR, selector);
}

/*read upgrade status from nv*/
int fota_get_upgrade_status_from_nv(char* upgrade_status, size_t size)
{
    return fota_dm_get_nv_string(NV_FOTA_CURR_UPGRADE_STATE, upgrade_status, size);
}

/*for webui, set upgrade state and version state to IDLE*/
void fota_dm_clear_update_nv_for_ui(void)
{
    fota_set_upgrade_state_to_nv(IDLE);
    fota_set_version_state_to_nv(IDLE);
}

/*save fota upgrade result to nv*/

void fota_dm_report_update_result(char *result)
{
    if(NULL == result)
        return;
	LOGD("save fota upgrade result to nv %s, %s, %s, to %s", NV_FOTA_UPGRADE_RESULT, NV_FOTA_UPGRADE_RESULT_MMI, NV_FOTA_UPGRADE_RESULT_INTERNAL, result);
    cfg_set(NV_FOTA_UPGRADE_RESULT, result);
    cfg_set(NV_FOTA_UPGRADE_RESULT_MMI, result);
	cfg_set(NV_FOTA_UPGRADE_RESULT_INTERNAL, result);
	
	cfg_save();
	LOGD("save fota upgrade result to nv %s, %s, %s, to %s cfg_save", NV_FOTA_UPGRADE_RESULT, NV_FOTA_UPGRADE_RESULT_MMI, NV_FOTA_UPGRADE_RESULT_INTERNAL, result);

}



/*read from nv, whether need user confirm to download the version*/
static int fota_dm_need_user_confirm_download(void)
{
    int value = fota_dm_get_nv_int(NV_FOTA_NEED_USER_CONFIRM_DOWNLOAD);
    return (value == 1) ? TRUE : FALSE;

}

/*read from nv, whether need user confirm to start update*/
static int fota_dm_need_user_confirm_update(void)
{
    int value = fota_dm_get_nv_int(NV_FOTA_NEED_USER_CONFIRM_UPDATE);
    return (value == 1) ? TRUE : FALSE;

}

void fota_dm_check_power_to_update()
{
    if(fota_dm_is_power_enough() == FALSE) {
        LOGD("Battery too low to update\n");
        fota_set_upgrade_state_to_nv(LOW_BATTERY);
    } else {
        fota_dm_send_message_to_work(FOTA_DM_START_UPDATE, AT_MSG_NULL);
    }
}

/*create thread, and detach it*/
static int  fota_dm_create_thread(pthread_t *pt, const char* thread_name,
                                  void * (*thread_entry)(void*))
{
    int ret = 0;
    pthread_attr_t attribute;
    pthread_attr_init(&attribute);
    pthread_attr_setdetachstate(&attribute, PTHREAD_CREATE_DETACHED);

    ret = pthread_create(pt, &attribute, thread_entry, (void*)thread_name);
	pthread_attr_destroy(&attribute);
	if(ret != 0) {
        LOGE("failed to create thread %s, errno=%d\n", thread_name, errno);
    }
    return ret;
}
/**************************** main thread handle msg******************************************/

/* main handle manual search command */
static void  main_handle_fota_manual_search(void)
{
    LOGD("main_handle_fota_manual_search Enter!\n");
    char status[FOTA_CFG_GET_ITEMLEN] = {0};
    fota_get_upgrade_status_from_nv(status, FOTA_CFG_GET_ITEMLEN);
    LOGD("current status %s\n", status);
    if(!strcmp(status, CHECKING) || !strcmp(status, DOWNLOADING)) {
        LOGD("current status %s, no need to manual search\n", status);
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAVERSION_IND, FOTA_CI_RUNNING);
        return;
    }
    fota_dm_clear_update_nv_for_ui();
    fota_dm_send_message_to_work(FOTA_DM_CHECK_VERSION, AT_MSG_NULL);

}

/*main handle download version command*/
static void main_handle_fota_download_version(MSG_BUF *msg)
{
    LOGD("main_handle_fota_download_version Enter!\n");
    assert(msg);
    int result = *(int*)(msg->aucDataBuf);
    if(result == 1) {
        fota_dm_send_message_to_work(FOTA_DM_DOWNLOAD_VERSION, AT_MSG_NULL);
    } else {
        LOGE("User Cancel Download\n");
    }

}

/*main handle user start update command*/
static void main_handle_fota_update_request(MSG_BUF *msg)
{
    LOGD("main_handle_fota_update_request Enter!\n");
    assert(msg);
    int result = *(int*)(msg->aucDataBuf);
    if(result == 1) {
        fota_dm_send_message_to_work(FOTA_DM_START_UPDATE, AT_MSG_NULL);
    } else {
        LOGE("User Cancel upgrade\n");
    }
}
/*main handle user request to factrory reset*/
static void main_handle_fota_reset_request(void)
{
	//Close the Monitor flag for factory reset.
	fota_dm_write_file_int("/proc/sys/vm/file_delete_monitor_flag", 0);
	
    LOGD("main_handle_fota_reset_request Enter!\n");
    int flag;
    int ret ;
    ret = fota_dm_get_update_flag(&flag);
#if 0 	
    if((ret == 0) && (flag == 1)) {
        LOGE("update in progress, can not reset\n");
        return;
    }
#else
    if(flag == 1) {
    	LOGE("update in progress, can not reset\n");
    	return;
    }
#endif
//    pthread_cancel(g_workthread_id);
    fota_dm_remove(ZTE_FOTA_MAIN_PATH);
    //Reply msg to Mainctrl for Factory Reset!
    fota_dm_send_message_wrapper(MODULE_ID_DM_WEBUI_AT, MODULE_ID_MAIN_CTRL,
                                 MSG_CMD_RESET_RSP, AT_MSG_NULL);

}

/*main handle work thread report check version result*/
static void main_handle_fota_check_version_response(MSG_BUF *msg)
{
    LOGD("main_handle_fota_check_version_response Enter!\n");
    assert(msg);
    int result = atoi((char*)msg->aucDataBuf);
    if(fota_dm_need_user_confirm_download()) {
        /*if need user confirm download, report result to mmi*/
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAVERSION_IND, result);
        /*if has critical version, download directly*/
#if(PRODUCT_TYPE != PRODUCT_PHONE)
        if(result == FOTA_HAS_CRITICAL_VERSION)
            fota_dm_send_message_to_work(FOTA_DM_DOWNLOAD_VERSION, AT_MSG_NULL);
#endif
    } else {
        /*no need to confirm, download directly, if has new version*/
        if(result == FOTA_HAS_OPTIONAL_VERSION || result == FOTA_HAS_CRITICAL_VERSION) {
            fota_dm_send_message_to_work(FOTA_DM_DOWNLOAD_VERSION, AT_MSG_NULL);
        }
    }

}

/* main handle work thread report download version result*/
static void main_handle_fota_download_version_response(MSG_BUF *msg)
{
    LOGD("main_handle_fota_download_version_response Enter!\n");
    assert(msg);
    int result = atoi((char*)msg->aucDataBuf);
    if(fota_dm_need_user_confirm_update()) {
        /* report download result to mmi */
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAPKG_IND, result);
        /* download success, and critical version, start to upgrade */

#if(PRODUCT_TYPE != PRODUCT_PHONE)
        if(result == FOTA_PKG_DL_SUCC && fota_dm_is_force_package())
            fota_dm_send_message_to_work(FOTA_DM_START_UPDATE, AT_MSG_NULL);
#endif

    } else {
        /* download success, and no need user confirm, start to upgrade */
        if(result == FOTA_PKG_DL_SUCC) {
            fota_dm_check_power_to_update();
        }
    }

}
static void fota_dm_save_next_polling_time_to_nv(time_t seconds, struct tm* ptm)
{

    char timestr[64] = {0};
    snprintf(timestr, sizeof(timestr), "%4d-%02d-%02d %02d:%02d",
             1900 + ptm->tm_year, ptm->tm_mon + 1, ptm->tm_mday, ptm->tm_hour, ptm->tm_min);
    fota_dm_set_nv_string(NV_DM_NEXTPOLLINGTIME, timestr);
    fota_dm_set_nv_long(NV_FOTA_POLLING_NEXT_TIME, seconds);
}

static void fota_dm_clear_next_polling_time_in_nv()
{
    char timestr[64] = {0};
    strncpy(timestr, "0000-00-00 00:00", sizeof(timestr));
    fota_dm_set_nv_string(NV_DM_NEXTPOLLINGTIME, timestr);
    fota_dm_set_nv_long(NV_FOTA_POLLING_NEXT_TIME, 0);
}

static void schedule_next_polling(int days)
{
    int seconds = 0;
    struct tm tm;
    time_t cur = time(NULL);
	int fotaTestMode = fota_dm_get_nv_int(NV_FOTA_TESTMODE_SWITCH);

    if(fota_dm_is_auto_polling_on() != TRUE) {
        LOGD("auto polling is off\n");
        fota_dm_clear_next_polling_time_in_nv();
        return;
    }
	if(fotaTestMode)
	{
		//Fota Test Mode: next polling 10 mints
		fota_dm_add_rtc_alarm(POLLING_INTERVAL_FOR_TESTMODE, FOTA_DM_RTC_TIMER_ID);
		LOGD("Fota Test Mode: RTC alarm 10mins Setted!\n");
		return;
	}
	if(days <= 0)
       	days = DEFAULT_POLLING_INTERVAL;
    seconds = days * SECONDS_PER_DAY;
    cur += seconds;
    localtime_r(&cur, &tm);
    fota_dm_add_utc_alarm(&tm, FOTA_DM_UTC_TIMER_ID);
    g_utc_alarm_setted = TRUE;
    fota_dm_save_next_polling_time_to_nv(cur, &tm);
}

/*main handle rtc alarm msg, start polling*/
static void main_handle_rtc_alarm(void)
{
    char status[FOTA_CFG_GET_ITEMLEN] = {0};
    LOGD("rtc alarm triggered\n");

    //TODO:check
    if(fota_dm_is_roaming() && !fota_dm_allow_roaimg_update()) {
        /*roaming and roaming update is now allowed*/
        LOGD("is roaming, and not allow polling,  schedule next polling\n");
        schedule_next_polling(DEFAULT_POLLING_INTERVAL);
        return;
    }
    if(fota_dm_is_file_exist(FOTA_PACKAGE_FILE)) {
        LOGD("%s file exist, no need polling, schedule next polling\n",
             FOTA_PACKAGE_FILE);
        schedule_next_polling(DEFAULT_POLLING_INTERVAL);
        return;
    }
    fota_get_upgrade_status_from_nv(status, FOTA_CFG_GET_ITEMLEN);
    LOGD("current status %s\n", status);
    if(!strcmp(status, CHECKING) || !strcmp(status, DOWNLOADING) ||
       !strcmp(status, DOWNLOAD_SUCCESS) || !strcmp(status, LOW_BATTERY)
       || !strcmp(status, PREPARE_INSTALL)) {
        LOGD("current status %s, no need polling, schedule next polling\n", status);
        schedule_next_polling(DEFAULT_POLLING_INTERVAL);
        return;
    }
    fota_dm_send_message_to_work(FOTA_DM_START_POLLING, AT_MSG_NULL);
}

/*get polling interval from nv*/
static int fota_dm_get_polling_interval()
{
    int interval = fota_dm_get_nv_int(NV_DM_POLLINGCYCLE);
    if(interval < 0 || interval > INT_MAX-1){
        interval = 0;
	}

    return interval;
}
/*handle polling result*/
static void main_handle_polling_response(MSG_BUF *msg)
{
    assert(msg);
    int result = atoi((char*)msg->aucDataBuf);
    LOGD("main_handle_polling_response, Enter! result=%d\n",  result);
    if(result == FOTA_NO_NEW_VERSION || result == FOTA_PKG_TOO_BIG ||
       result == FOTA_SEARCH_RLT_FAIL) {
        /*polling failed or no new version, schedule next polling*/
        schedule_next_polling(DEFAULT_POLLING_INTERVAL);
    } else {

        if(fota_dm_need_user_confirm_download()) {
            /*report polling result to mmi*/
			fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAVERSION_IND, result);

            /*critical version, download*/
#if(PRODUCT_TYPE != PRODUCT_PHONE)
            if(fota_dm_is_force_package())
                fota_dm_send_message_to_work(FOTA_DM_DOWNLOAD_VERSION, AT_MSG_NULL);
#endif
        } else {
            /*no need user confirm, start download*/
            fota_dm_send_message_to_work(FOTA_DM_DOWNLOAD_VERSION, AT_MSG_NULL);
        }
        int interval = fota_dm_get_polling_interval();

        schedule_next_polling(interval);
    }
}

void  main_handle_webui_change_parameter(void)
{
    LOGD("Enter\n");
    /*webui change parameter, we only care about enable/disable auto polling*/
    int auto_polling_on = fota_dm_is_auto_polling_on();
    if(!auto_polling_on) {
        LOGD("auto_polling_on=%d, delete the FOTA_DM_UTC_TIMER\n", auto_polling_on);
        fota_dm_del_alarm(FOTA_DM_UTC_TIMER_ID);
        g_utc_alarm_setted = FALSE;
        fota_dm_clear_next_polling_time_in_nv();
    } else {
        if(g_utc_alarm_setted) {
            LOGD("auto_polling_on=%d, FOTA_DM_UTC_TIMER already setted, ignore\n",
                 auto_polling_on);
        } else {
            LOGD("auto_polling_on=%d, FOTA_DM_UTC_TIMER not setted, set it\n",
                 auto_polling_on);
            schedule_next_polling(DEFAULT_POLLING_INTERVAL);
        }
    }
}

static char* fota_dm_time_to_str(time_t seconds, char *buf, size_t buf_len)
{
	struct tm tm;
	time_t cur = seconds;
	localtime_r(&cur, &tm);
	snprintf(buf, buf_len-1, "%4d-%02d-%02d %02d:%02d", 1900+tm.tm_year, tm.tm_mon+1,
		tm.tm_mday, tm.tm_hour, tm.tm_min);
	return buf;	

}

int fota_dm_check_polling_time(void)
{
	char buf[64] = {0};
	time_t cur = time(NULL);
	char polling_nexttime[FOTA_CFG_GET_ITEMLEN] = {0};
	cfg_get_item(NV_FOTA_POLLING_NEXT_TIME, polling_nexttime, sizeof(polling_nexttime));
	
	//LOGD("polling_nexttime=%ld  polling_nexttime=%ld\n",cur,atol(polling_nexttime));

	LOGD("current time: %lu, %s\n", cur, fota_dm_time_to_str(cur,  buf, sizeof(buf)));
	LOGD("next polling time: %lu,  %s\n", atol(polling_nexttime), fota_dm_time_to_str(atol(polling_nexttime),  buf, sizeof(buf)));
	
	if(cur >atol(polling_nexttime)){
		return TRUE;
	}else{
		return FALSE;
	}
}

static void main_handle_time_changed_notify(void)
{
	
	if(fota_dm_is_auto_polling_on() != TRUE) {
        LOGD("auto polling is off\n");
        fota_dm_clear_next_polling_time_in_nv();
        return;
    }
	
	if(g_utc_alarm_setted) {   
		if(fota_dm_check_polling_time()){
			LOGD("FOTA_DM_UTC_TIMER timeout, set it again\n");
			schedule_next_polling(DEFAULT_POLLING_INTERVAL);
		}else{
			LOGD("FOTA_DM_UTC_TIMER not expired,  don't set it again\n");
		}				
    } else {
        LOGD("FOTA_DM_UTC_TIMER not setted, set it\n");
//        schedule_next_polling(DEFAULT_POLLING_INTERVAL); // not setted is first start, wait powon polling
    }	
}

/* main thread, handle msg*/
static void main_handle_msg(MSG_BUF *msg)
{
    assert(msg);

    switch(msg->usMsgCmd) {
        /*rtc alarm*/
    case RTC_MSG_ALARM_ELAPSED:
        main_handle_rtc_alarm();
        break;

        /*below msg send from at-ctrl*/
    case MSG_CMD_FOTA_WEBUI_CHANGE_PARAMETER:
        main_handle_webui_change_parameter();
        break;
    case MSG_CMD_FOTADL_REQ:
    case MSG_CMD_FOTA_WEBUI_START_DOWNLOAD:
        main_handle_fota_download_version(msg);
        break;
    case MSG_CMD_FOTADY_REQ:
        main_handle_fota_update_request(msg);
        break;
    case MSG_CMD_FOTAMANUAL_REQ:
    case MSG_CMD_FOTA_WEBUI_START_FOTA:
        main_handle_fota_manual_search();
        break;

        /*below msg from work thread*/
    case FOTA_DM_CHECK_VERSION_RSP:
        main_handle_fota_check_version_response(msg);
        break;
    case FOTA_DM_DOWNLOAD_VERSION_RSP:
        main_handle_fota_download_version_response(msg);
        break;
    case FOTA_DM_POLLING_RSP:
        main_handle_polling_response(msg);
        break;
        /*Factory Reset msg send from  mainctrl*/
    case MSG_CMD_RESET_NOTIFY:
        main_handle_fota_reset_request();
        break;
		/*time changed msg send from  module rtc*/
	case RTC_MSG_TIME_CHANGED:
        main_handle_time_changed_notify();
        break;
    default:
        LOGE("%d should not reach here\n",  msg->usMsgCmd);
    }
}


/*sig handler*/
static void sig_handler(int sig)
{
    /*if((sig == SIGINT) || (sig == SIGTERM)) {
        LOGD("receive signal %d\n", sig);
        exit(0);
    }*/
}


static void sig_init(void)
{
    pthread_t tid = pthread_self();
    LOGD("tid:%lu\n", tid);
    struct sigaction sigact;
    int ret;
    sigset_t signal_mask;
    sigemptyset(&signal_mask);
    sigaddset(&signal_mask, SIGPIPE);
    ret = pthread_sigmask(SIG_BLOCK, &signal_mask, NULL);
    if(ret != 0) {
        LOGD("block SIGPIPE error\n");
    }

    sigact.sa_handler = sig_handler;
    sigact.sa_flags = 0;
    sigemptyset(&sigact.sa_mask);
    sigaction(SIGINT, &sigact, NULL);
    sigaction(SIGTERM, &sigact, NULL);

    /*ignore SIGPIPE*/
    sigact.sa_handler = SIG_IGN;
    sigaction(SIGPIPE, &sigact, NULL);

}

/*request imei and inner-version from at-ctrl, read them from nv later */
static void fota_dm_request_imei_version_from_atctl(void)
{
    //fota_dm_send_cmd_to_atctl(MSG_CMD_IMEI_REQ, AT_MSG_NULL);
    //fota_dm_send_cmd_to_atctl(MSG_CMD_ZVERSION_REQ, AT_MSG_NULL);
}

/*resume update*/
int resume_update(void)
{
    LOGD("resume_update Enter!\n");
    int result;
    int status;
    if(fota_dm_is_power_enough() == FALSE) {
        LOGD("Battery too low to resume update\n");
        fota_set_upgrade_state_to_nv(LOW_BATTERY);
        return 0;
    }
    fota_dm_wake_lock(FOTA_DM_WAKE_LOCK);

    result = fota_dm_get_update_status(&status);
    fota_dm_set_update_flag();
    fota_set_upgrade_state_to_nv(PREPARE_INSTALL);

    if(result < 0) {
        LOGE("failed to read the update status file\n");
    } else if(status == ZTE_DUA_RECOVERY_UPDATE_SUCCESS
              || status == ZTE_DUA_VERIFY_SUCCESS) {
        /* no need to verify again */
        LOGD("no need verify,continue upgrade recoveryfs\n");
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAUPRESUME_IND, AT_MSG_NULL);
		sleep(4); // sleep for mmi to display "prepare to restart" when resume;
        fota_dm_update_recoveryfs();

    } else {
        fota_dm_verify_package();
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAUPRESUME_IND, AT_MSG_NULL);
        result = fota_dm_get_update_status(&status);
        if(status != ZTE_DUA_VERIFY_SUCCESS) {
            /*failed to verify, reboot*/
            fota_dm_clear_update_flag();
            fota_dm_reboot();

        } else {
            /*verify success, update recovery*/
            fota_dm_update_recoveryfs();
        }
    }
    fota_dm_wake_unlock(FOTA_DM_WAKE_LOCK);
    return 0;
}
/*************************************work thread handle msg***********************************************/

/*handle check version command*/
static  void thread_entry_handle_check_new_version(void)
{

    int ret = 0;

    if(fota_dm_is_file_exist(FOTA_PACKAGE_FILE)) {
        fota_dm_send_message_to_main(FOTA_DM_DOWNLOAD_VERSION_RSP, FOTA_PKG_DL_SUCC);
        fota_set_version_state_to_nv(ALREADY_HAS_PKG);
        return;
    }
    fota_set_upgrade_state_to_nv(CHECKING);
    //TODO check
    fota_set_version_state_to_nv(IDLE);
    fota_set_selector_to_nv(NONE);
    fota_dm_clear_force_package();

    ret = fota_dm_check_new_version();

    fota_dm_set_download_progress(0);

    int has_new_version = fota_dm_has_new_version();
    int mandatory = fota_dm_has_mandatory_version();
    LOGD("thread_entry_handle_check_new_version Enter! ret=%d has_new_version:%d  mandatory:%d\n",
         ret, has_new_version, mandatory);
    if(ret == 0) {
        if(!has_new_version) {
            fota_dm_send_message_to_main(FOTA_DM_CHECK_VERSION_RSP, FOTA_NO_NEW_VERSION);
            fota_set_version_state_to_nv(NO_NEW_VERSION);
        } else {
            int pkg_size = fota_dm_get_pkg_total_size();
            int free_space = fota_dm_get_free_space(ZTE_FOTA_MAIN_PATH);
            LOGD("pkg_size=%d, free_space=%d\n", pkg_size, free_space);
            if(pkg_size >= free_space) {
                fota_dm_send_message_to_main(FOTA_DM_CHECK_VERSION_RSP, FOTA_PKG_TOO_BIG);
                fota_set_version_state_to_nv(PKG_TOO_BIG);

            } else if(!mandatory) {
                fota_dm_send_message_to_main(FOTA_DM_CHECK_VERSION_RSP,
                                             FOTA_HAS_OPTIONAL_VERSION);
                fota_set_version_state_to_nv(HAS_OPTIONAL);
            } else {
                fota_dm_send_message_to_main(FOTA_DM_CHECK_VERSION_RSP,
                                             FOTA_HAS_CRITICAL_VERSION);
                fota_set_version_state_to_nv(HAS_CRITICAL);
            }
        }
    } else {
        fota_dm_send_message_to_main(FOTA_DM_CHECK_VERSION_RSP, FOTA_SEARCH_RLT_FAIL);
        fota_set_version_state_to_nv(CHECK_FAILED);
    }
	fota_set_upgrade_state_to_nv(CHECK_COMPLETE);
}

/*handle auto polling msg*/
static void  thread_entry_handle_start_polling(void)
{

    int has_new_version = FALSE;
    int mandatory = FALSE;

    fota_set_upgrade_state_to_nv(CHECKING);
    fota_set_selector_to_nv(NONE);
    fota_set_version_state_to_nv(IDLE);
    fota_dm_clear_force_package();

    int ret = fota_dm_check_new_version();

    has_new_version = fota_dm_has_new_version();
    mandatory = fota_dm_has_mandatory_version();
    LOGD("thread_entry_handle_start_polling Enter! ret=%d has_new_version:%d  mandatory:%d\n",
         ret, has_new_version, mandatory);
    if(ret == 0) {
        if(!has_new_version) {
            fota_dm_send_message_to_main(FOTA_DM_POLLING_RSP, FOTA_NO_NEW_VERSION);
            fota_set_version_state_to_nv(NO_NEW_VERSION);
        } else {
            int pkg_size = fota_dm_get_pkg_total_size();
            int free_space = fota_dm_get_free_space(ZTE_FOTA_MAIN_PATH);
            LOGD("pkg_size=%d, free_space=%d\n", pkg_size, free_space);
            if(pkg_size >= free_space) {
                fota_dm_send_message_to_main(FOTA_DM_POLLING_RSP, FOTA_PKG_TOO_BIG);
                fota_set_version_state_to_nv(PKG_TOO_BIG);

            } else if(!mandatory) {
                fota_dm_send_message_to_main(FOTA_DM_POLLING_RSP, FOTA_HAS_OPTIONAL_VERSION);
                fota_set_version_state_to_nv(HAS_OPTIONAL);
            } else {
                fota_dm_send_message_to_main(FOTA_DM_POLLING_RSP, FOTA_HAS_CRITICAL_VERSION);
                fota_set_version_state_to_nv(HAS_CRITICAL);
            }
        }
    } else {
        /*polling failed*/
        fota_dm_send_message_to_main(FOTA_DM_POLLING_RSP, FOTA_SEARCH_RLT_FAIL);
        fota_set_version_state_to_nv(CHECK_FAILED);
    }
	fota_set_upgrade_state_to_nv(CHECK_COMPLETE);
}

/*resume download command*/
static void thread_entry_handle_resume_download(void)
{
    int ret = fota_dm_download_version();
    LOGD("thread_entry_handle_resume_download Enter! ret:%d\n", ret);
    if(ret < 0) {
        fota_dm_send_message_to_main(FOTA_DM_DOWNLOAD_VERSION_RSP, FOTA_PKG_DL_FAIL);
        fota_set_upgrade_state_to_nv(DOWNLOAD_FAILED);
    } else {
        fota_dm_send_message_to_main(FOTA_DM_DOWNLOAD_VERSION_RSP, FOTA_PKG_DL_SUCC);
        fota_set_upgrade_state_to_nv(DOWNLOAD_SUCCESS);
    }
}
/*start update */
static void thread_entry_handle_start_update(void)
{
    LOGD("thread_entry_handle_start_update Enter!\n");
    int result;
    int status;

    fota_dm_wake_lock(FOTA_DM_WAKE_LOCK);
    fota_dm_set_update_flag();
    fota_set_upgrade_state_to_nv(PREPARE_INSTALL);
	
	#if(PRODUCT_TYPE == PRODUCT_MIFI_CPE)
	// ÉèÍêprepare_installºó,sleep 5s£¬·ÀÖ¹webuiÀ´²»¼°µ¯³ö"Download completed...."ÌáÊ¾
	sleep(5);
	#endif
	
    fota_dm_verify_package();
    result = fota_dm_get_update_status(&status);
    if(result < 0) {
        LOGE("failed to read the update_status file\n");
    } else if(status != ZTE_DUA_VERIFY_SUCCESS) {
        LOGE("verify failed\n");
        fota_dm_clear_update_flag();
        fota_dm_reboot();
    } else {
        // Ð£Ñé³É¹¦£¬¿ªÊ¼Éý¼¶
        fota_dm_update_recoveryfs();
    }
    fota_dm_wake_unlock(FOTA_DM_WAKE_LOCK);
}

/* work thread handle download version command*/
static void thread_entry_handle_download_version(void)
{
	system("rm -rf /var/log");
    fota_set_upgrade_state_to_nv(DOWNLOADING);
	if(fota_dm_is_file_exist(DL_TMP_FILENAME)) {
        fota_dm_remove(DL_TMP_FILENAME);
    }
	
    int ret = fota_dm_download_version();
    LOGD("thread_entry_handle_download_version Enter! ret:%d\n", ret);
    if(ret < 0) {
        fota_dm_send_message_to_main(FOTA_DM_DOWNLOAD_VERSION_RSP, FOTA_PKG_DL_FAIL);
        fota_set_upgrade_state_to_nv(DOWNLOAD_FAILED);
    } else {
        fota_dm_send_message_to_main(FOTA_DM_DOWNLOAD_VERSION_RSP, FOTA_PKG_DL_SUCC);
        fota_set_upgrade_state_to_nv(DOWNLOAD_SUCCESS);
    }
}


/*work thread handle command*/
static void thread_handle_msg(MSG_BUF *msg)
{
    switch(msg->usMsgCmd) {
    case FOTA_DM_CHECK_VERSION:
        thread_entry_handle_check_new_version();
        break;
    case FOTA_DM_RESUME_DOWNLOAD:
        thread_entry_handle_resume_download();
        break;
    case FOTA_DM_START_UPDATE:
        thread_entry_handle_start_update();
        break;
    case FOTA_DM_DOWNLOAD_VERSION:
        thread_entry_handle_download_version();
        break;
    case FOTA_DM_START_POLLING:
        thread_entry_handle_start_polling();
        break;
    default:
        break;
    }
}

/*work thread entry*/
static void * work_thread_entry(void *data)
{
    char *thread_name = data;
    MSG_BUF msg;
    int msqid;
    int ret = 0;
    if(thread_name != NULL)
        fota_dm_set_thread_name(thread_name);
    msqid = fota_dm_create_msg_queue(MODULE_ID_DM);
    if(msqid < 0) {
        LOGE("failed to crate msg %d\n", MODULE_ID_DM);
        assert(0);
    }
    sem_post(&sem);
    while(1) {
        memset(&msg, 0x00, sizeof(MSG_BUF));
        ret = fota_dm_msg_recv(msqid, &msg, (sizeof(MSG_BUF) - sizeof(long)), 0, 0);
        if(ret <= 0)
            continue;
        LOGD("recv msg %s\n",  thread_msgid_to_string(msg.usMsgCmd));
        thread_handle_msg(&msg);
    }

}

/*update status to string, used in log*/
static const char* update_status_to_string(int status)
{
    switch(status) {
        CASE_RETURN_STR(ZTE_DUA_VERIFY_SUCCESS);
        CASE_RETURN_STR(ZTE_DUA_VERIFY_FAIL);
        CASE_RETURN_STR(ZTE_DUA_SYSTEM_UPDATE_SUCCESS);
        CASE_RETURN_STR(ZTE_DUA_SYSTEM_UPDATE_FAIL);
        CASE_RETURN_STR(ZTE_DUA_RECOVERY_UPDATE_SUCCESS);
        CASE_RETURN_STR(ZTE_DUA_RECOVERY_UPDATE_FAIL);
        CASE_RETURN_STR(ZTE_DUA_NO_NEED_UPDATE);
        CASE_RETURN_STR(ZTE_DUA_NEED_UPDATE);
    }
    return "ZTE_DUA_UNKNOWN";
}
static void fota_dm_upgrade_from_rbfota(void)
{
    int status = 0;
    fota_dm_read_file_int(FOTA_UPDATE_STATUS_FILE_OLD, &status);
    LOGD("read from %s update status=%d(%s)\n", FOTA_UPDATE_STATUS_FILE_OLD,
         status, update_status_to_string(status));
    //report update status
    if(status == ZTE_DUA_SYSTEM_UPDATE_SUCCESS) {
        /* update success */
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTARLT_IND, FOTA_UPDATE_SUCC);
        fota_dm_report_update_result(UPDATE_SUCCESS);
    } else if(status == ZTE_DUA_SYSTEM_UPDATE_FAIL) {
        /*update failed*/
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTARLT_IND, FOTA_UPDATE_FAIL);
        fota_dm_report_update_result(UPDATE_FAIL);
    } else {
        LOGE("status=%d\n", status);
    }
    /*handle nv*/
    fota_dm_clear_update_flag();
    fota_dm_clear_update_status();
    fota_dm_clear_update_nv_for_ui();
    fota_dm_clear_force_package();
    fota_dm_set_download_progress(0);
    //delete /cache/zte_fota/dua
    fota_dm_write_file_int("/proc/sys/vm/file_delete_monitor_flag", 0);
    fota_dm_remove("/cache/zte_fota/dua");
    fota_dm_remove("/cache/zte_fota/tmp");
    fota_dm_write_file_int("/proc/sys/vm/file_delete_monitor_flag", 1);
    return;

}

static void check_update_package_status(void)
{

    if(fota_dm_is_file_exist(FOTA_UPDATE_STATUS_FILE_OLD)) {
        /*upgrade from rb-fota*/
        return fota_dm_upgrade_from_rbfota();
    }

    if(fota_dm_is_file_exist(DL_TMP_FILENAME)) {
        /*if exist DL_TMP_FILENAME, we need to resume download*/
        LOGD("%s file exist, resume Download\n", DL_TMP_FILENAME);
		fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTADLRESUME_IND, AT_MSG_NULL);
        fota_dm_send_message_to_work(FOTA_DM_RESUME_DOWNLOAD, AT_MSG_NULL);
		fota_set_upgrade_state_to_nv(DOWNLOADING);
 
    } else if(fota_dm_is_file_exist(FOTA_PACKAGE_FILE)) {

        int status;
        int result;
        fota_dm_set_download_progress(0);
        result = fota_dm_get_update_status(&status);
        LOGD("result=%d update status=%s\n", result, update_status_to_string(status));
        if(result < 0) {
            /*read update_status failed*/
            //TODO : check
            fota_dm_remove(FOTA_PACKAGE_FILE);
            fota_dm_clear_force_package();
            fota_dm_clear_update_flag();
            fota_dm_clear_update_nv_for_ui();
			fota_dm_clear_update_status();

        } else if(status == ZTE_DUA_SYSTEM_UPDATE_SUCCESS) {
            /* system udpate success, remove the update package and clear flag */
			fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTARLT_IND, FOTA_UPDATE_SUCC);
			fota_dm_remove(FOTA_PACKAGE_FILE);
			sync();
            fota_dm_clear_update_flag();
            fota_dm_clear_update_nv_for_ui();
            fota_dm_clear_force_package();
			fota_dm_report_update_result(UPDATE_SUCCESS);
			fota_dm_clear_update_status();
			
        } else if(status == ZTE_DUA_SYSTEM_UPDATE_FAIL
                  || status == ZTE_DUA_RECOVERY_UPDATE_FAIL || status == ZTE_DUA_VERIFY_FAIL) {
            /*verify failed, recovery update failed, system update failed, backup the package*/
			fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTARLT_IND, FOTA_UPDATE_FAIL);
            fota_dm_report_update_result(UPDATE_FAIL);
            fota_dm_rename_file(FOTA_PACKAGE_FILE, FOTA_PACKAGE_FILE_FAILED);
            fota_dm_clear_update_flag();
            fota_dm_clear_force_package();
            fota_dm_clear_update_nv_for_ui();
			fota_dm_clear_update_status();
        } else if(status == ZTE_DUA_VERIFY_SUCCESS
                  || status == ZTE_DUA_RECOVERY_UPDATE_SUCCESS || status == ZTE_DUA_NEED_UPDATE) {
            /*verify success or recovery udpate success, we need to resume update*/
            resume_update();
        } else {
            /*other situation, prepare to update*/
            LOGD("status==%d\n", status);
            if(fota_dm_need_user_confirm_update()) {
				fota_dm_send_soc_msg_to_mmi(MSG_CMD_FOTAPKG_IND, FOTA_PKG_DL_SUCC);
            } else {
                resume_update();
            }
        }
    } else {
        //Çå³ýÉý¼¶±ê¼Ç
        fota_dm_set_download_progress(0);
        fota_set_upgrade_state_to_nv(IDLE);
        fota_set_version_state_to_nv(IDLE);
        fota_set_selector_to_nv(NONE);
    }
}


int fota_dm_main(int argc, char**argv)
{
    int ret = 0;
    pthread_t pt;
    int dm_msq = -1;
    int auto_polling_on, poweron_auto_check;
    MSG_BUF msg;
    //LOGD("start %s build date: %s %s\n", basename(argv[0]), __DATE__, __TIME__);
	LOGD("build date: %s %s\n", __DATE__, __TIME__);
    loglevel_init();
    fota_dm_set_process_name("fota_dm");
    sig_init();

    //fota_dm_request_imei_version_from_atctl();

    fota_dm_check_and_make_fota_dir();

    dm_msq = fota_dm_create_msg_queue(MODULE_ID_DM_WEBUI_AT);
    if(dm_msq < 0) {
        LOGE("failed to create msq\n");
        assert(0);
    }
    sem_init(&sem, 0, 0);

	
    ret = fota_dm_create_thread(&pt, "fota_dm-work", work_thread_entry);
    if(ret != 0) {
        LOGE("failed to create thread\n");
        assert(0);
    }
    g_workthread_id = pt;
    sem_wait(&sem);

    check_update_package_status();

    /*if auto polling on, set rtc alarm*/
    auto_polling_on = fota_dm_is_auto_polling_on();
    poweron_auto_check = fota_dm_is_poweron_auto_check();
    LOGD("auto polling %s\n",  auto_polling_on ? "on" : "off");
    LOGD("poweron auto check %s\n",  poweron_auto_check ? "on" : "off");
    ret = auto_polling_on || poweron_auto_check;
    if(ret) {
        ret = fota_dm_add_rtc_alarm(AUTO_POLLING_AFTER_START, FOTA_DM_RTC_TIMER_ID);
        if(ret < 0)
            LOGE("failed to create AUTO_POLLING_TIMER\n");
        else
            LOGD("start rtc alarm\n");
    } else {
        fota_dm_clear_next_polling_time_in_nv();
    }
    while(1) {
        memset(&msg, 0x00, sizeof(MSG_BUF));
        ret = fota_dm_msg_recv(dm_msq, &msg, (sizeof(MSG_BUF) - sizeof(long)), 0, 0);
        if(ret <= 0)
            continue;
        LOGD("recv msg: %d %s\n",  msg.usMsgCmd, fota_dm_msgid_to_string(msg.usMsgCmd));
        main_handle_msg(&msg);
    }
}
