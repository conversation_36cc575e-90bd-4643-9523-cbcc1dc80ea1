/**
 * Copyright (C) 2017 Sanechips Technology Co., Ltd.
 * <AUTHOR> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

/*******************************************************************************
 *                         Wrapper of GS Download LIB                          *
 ******************************************************************************/

#define _GNU_SOURCE


#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <assert.h>



#include "fota_dm_utils.h"
#include "fota_dm_macros.h"
#include "fota_common.h"
#include "softap_api.h"
#include "fota_dm_netdog.h"
#include "libzte_dmapp.h"

int fota_dm_netdog_set_network_exception(int has_exception)
{
    if(has_exception) {
        zte_fota_set_netdogflag(NETWORK_NOT_AVAILABLE, NET_DOG_EXCEPTION_FLAG);
    } else {
        zte_fota_set_netdogflag(NETWORK_NOT_AVAILABLE, NET_DOG_INIT_FLAG);
    }
    return 0;
}

int fota_dm_netdog_set_check_version_result(int result)
{
    if(result == 0) {
        zte_fota_set_netdogflag(DMSESSION_ABORTED_HIGH, NET_DOG_INIT_FLAG);
        zte_fota_set_netdogflag(DMSESSION_ABORTED_LOW, NET_DOG_INIT_FLAG);
    } else {
        zte_fota_set_netdogflag(DMSESSION_ABORTED_HIGH, (result >> 8));
        zte_fota_set_netdogflag(DMSESSION_ABORTED_LOW, (result & 0x00ff));
    }
    return 0;
}

int fota_dm_netdog_set_download_version_result(int result)
{
    if(result == 0) {
        zte_fota_set_netdogflag(DLSESSION_ABORTED_HIGH, NET_DOG_INIT_FLAG);
        zte_fota_set_netdogflag(DLSESSION_ABORTED_LOW, NET_DOG_INIT_FLAG);
    } else {
        zte_fota_set_netdogflag(DLSESSION_ABORTED_HIGH, (result >> 8));
        zte_fota_set_netdogflag(DLSESSION_ABORTED_LOW, (result & 0x00ff));
    }
    return 0;
}


int fota_dm_netdog_set_sntp_exception(int has_exception)
{
    if(has_exception) {
        zte_fota_set_netdogflag(SNTP_FAILED, NET_DOG_EXCEPTION_FLAG);
    } else {
        zte_fota_set_netdogflag(SNTP_FAILED, NET_DOG_INIT_FLAG);
    }
    return 0;
}
int fota_dm_netdog_set_imei_exception(int has_exception)
{
    if(has_exception) {
        zte_fota_set_netdogflag(IMEI_INVALID, NET_DOG_EXCEPTION_FLAG);
    } else {
        zte_fota_set_netdogflag(IMEI_INVALID, NET_DOG_INIT_FLAG);
    }
    return 0;
}
int fota_dm_netdog_set_cr_inner_version_exception(int has_exception)
{
    if(has_exception) {
        zte_fota_set_netdogflag(CR_INNER_VERSION_INVALID, NET_DOG_EXCEPTION_FLAG);
    } else {
        zte_fota_set_netdogflag(CR_INNER_VERSION_INVALID, NET_DOG_INIT_FLAG);
    }
    return 0;
}
