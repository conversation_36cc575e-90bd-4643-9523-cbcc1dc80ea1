
/************************************************************************
*���ܽ��ܣ�at_ctl��PS��ص��¼�ʵ�ֺ�������ӿڣ����������Զ����ŵ��¼�����web ui�����ҵ����
*�����ˣ�
*�޸��գ�
*�޸����ݣ�
*�汾�ţ�
************************************************************************/

#include "ps_normal.h"
#include "ps_pdp.h"
#include "at_context.h"
#include <sys/time.h>
#include <time.h>
#include <limits.h>

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
#include "libcpnv.h" 
#endif
#include "rtc_timer.h"
#if (APP_OS_TYPE == APP_OS_TOS)
#include "zctrm_ext_locknet.h"
#endif

//bsim
#include <openssl/aes.h>

typedef struct
{
	unsigned int pubKeyRsaE[32];
	unsigned int pubKeyRsaN[32];
	unsigned int secureFlag;
	unsigned int pubKeyHash[4];
	unsigned int secureDevId[3];
}T_ZDrvEfuse_Secure;

#define EFUSE_IOC_MAGIC 	'E'
#define EFUSE_GET_DATA 		_IOWR(EFUSE_IOC_MAGIC, 1, char *)
#define PPPOE_CODE_LEN 		32 //webui limit 30

static unsigned char atctl_aes_key[16] = {0};

#define PROFILE_APN_LEN 65
#define PROFILE_APN_AES_LEN 129

static int apn_encrypt_code(void);
static int ipv6apn_encrypt_code(void);
//bsim end

/*CNUM�ϱ�*/
typedef struct
{
    char    alpha[80];
    char    number[80];
    unsigned char  type;
} T_zAt_CnumRes;

struct time_info{
	SINT32 time_zone;
	SINT32 sav_time;
	char univer_time[64];
};

//extern AT_PDP_ACT_REQ_INFO * g_pdp_set_info;
psnormal_state psinfo={0};
static T_zAt_Zsec_Res g_LockZsec = {0};
static int cfun_state=0;//���ڱ��cfun״̬
static int g_plmnLen = 5;//ccmnc����
static char mccNum[10] 		= {0};
static char mncNum[10] 		= {0};
static int g_zUfi_Mode=-1;//Mode�ϱ��仯���б�ɲŲ�ѯSysinfo
static int g_zUfi_SubMode=-1;//Mode�ϱ��仯���б�ɲŲ�ѯSysinfo
static int g_zUfi_Stat=-1;//CEREG\CREG\CGREG�仯���б仯�Ų�ѯSysinfo
static int g_isSearchingNetwork = FALSE;
static int g_zUfi_canPdpDail = TRUE;
static int g_zUfi_firstCsq = TRUE;
static char *crsmrsp=NULL;//���ڼ�¼crsm�ظ����
static char *mactozssid=NULL;//��¼�յ���MAC��ַ
static BOOL g_SimSlotFlag = TRUE;//��¼sim������γ�״̬
static int sysinfo_flag = 0;
int g_modem_model = 0;
int g_support_sms = 0;
int g_support_pb = 0;
int g_smspb_init = 0;//�ϱ�pb��ɳ�ʼ����־ 0:δ�ϱ� 1:���ϱ�
int g_need_smspb_init = 0;//sms��pb�Ƿ���Խ��г�ʼ����ÿ�ο������ܽ���һ�γ�ʼ����������Ϊ0ʱ��ʾ���Գ�ʼ����һ����ʼ������Ϊ1
#if (APP_OS_TYPE == APP_OS_TOS)
extern T_zCtrm_LockListPara simLockListPara;
extern T_zCtrm_SIMPara simPara;
extern SINT32 g_Sec_Status;
extern int crsm_reason;
#endif
extern struct defcid_mng_t g_defcid_mng;
char imsi[50] = {0};
//zdm ���׷�ȷ���Ƿ���Ҫͬ����AP��
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
char simSelect[32] = {0};
char prevSimSelect[32] = {0};
char simPreMode[32] = {0};

int at_file_operate(char *filepath, char *buf)
{
        int ret_fd = 0;
        int len = 0;

        ret_fd = open(filepath, O_RDWR);
        if (ret_fd == -1) {
                at_print(AT_ERR,"at_file_operate write file: %s buf %s failed!!!\n", filepath, buf);
                return -1;
        }

        len = strlen(buf);
        if (write(ret_fd, buf, len) != len) {
                at_print(AT_ERR,"at_file_operate write file: %s buf %s failed!!!\n", filepath, buf);
                close((int)ret_fd);
                return -1;
        }

        close(ret_fd);

        at_print(AT_DEBUG,"at_file_operate write file: %s buf %s success!!!\n", filepath, buf);

        return 0;
}


int at_sim_switch_operate(void)
{
    MSG_BUF *buf=NULL;
    int32_t simCardFlag = 0;
    unsigned int retCode = CPNV_ERROR;

    if(0 == strcmp(simSelect, "ESIM2_only"))
    {
        // //AT+ZCARDSWITCH=3 �е� sim1
        // // ��web ����˲��� -> ֱ�Ӱl��AT
        // buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCARDSWITCH_MSG,2,"3");
        // rcv_clt_req_msg_proc(buf);
        // free(buf);
        //��ʼ������AT+ZCARDSWITCH=3 �� 6003 error ����ֱ���޸�nv
    #if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5) || defined(JCV_HW_MZ806_V1) || defined(JCV_HW_DZ803_V1_0)
        simCardFlag = 0;
    #else
        simCardFlag = 3;
    #endif
		retCode = cpnv_NvItemWrite(0x29217d, (unsigned char *)(&simCardFlag), 1);
		at_print(AT_DEBUG,"at_sim_switch_operate: salvikie cpnv_NvItemWrite  simCardFlag %d retCode = %d\n", simCardFlag, retCode);
		if(CPNV_OK == retCode)
		{
            retCode= cpnv_NvramFlush();
			at_print(AT_DEBUG,"at_sim_switch_operate: cpnv_NvramFlush retCode = %d\n", retCode);
        }

        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "1");
        //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

// #ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
//         at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
//         sleep(1);
//         at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
// #else
        //ext sim exist need cfun=0     so sim_switch_ctrl not 0,0 need cfun=0
        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
        rcv_clt_req_msg_proc(buf);
        free(buf);
// #endif
    }
    else if( 0 == strcmp(simSelect, "ESIM1_only"))
    {
        // //AT+ZCARDSWITCH=3 �е� sim1
        // // ��web ����˲��� -> ֱ�Ӱl��AT
        // buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCARDSWITCH_MSG,2,"3");
        // rcv_clt_req_msg_proc(buf);
        // free(buf);
    #if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5) || defined(JCV_HW_MZ806_V1) || defined(JCV_HW_DZ803_V1_0)
        simCardFlag = 0;
    #else
        simCardFlag = 3;
    #endif
		retCode = cpnv_NvItemWrite(0x29217d, (unsigned char *)(&simCardFlag), 1);
		at_print(AT_DEBUG,"at_sim_switch_operate: salvikie cpnv_NvItemWrite  simCardFlag %d retCode = %d\n", simCardFlag, retCode);
		if(CPNV_OK == retCode)
		{
            retCode= cpnv_NvramFlush();
			at_print(AT_DEBUG,"at_sim_switch_operate cpnv_NvramFlush retCode = %d\n", retCode);
        }

        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "0");
        //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

#ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
        at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
        sleep(1);
        at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
#else
        //ext sim exist need cfun=0     so sim_switch_ctrl not 0,0 need cfun=0
        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
        rcv_clt_req_msg_proc(buf);
        free(buf);
#endif
    }
    else if( 0 == strcmp(simSelect, "RSIM_only"))
    {// ֻ��ǰһ�� ��esim1 ���� esim2 ����Ҫ��������
        // buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCARDSWITCH_MSG,2,"0");
        // rcv_clt_req_msg_proc(buf);
        // free(buf);
    #if defined(JCV_HW_DZ802_V1_0) || defined(JCV_HW_UZ901_V1_6) || defined(JCV_HW_UZ901_V2_5) || defined(JCV_HW_MZ806_V1) || defined(JCV_HW_DZ803_V1_0)
        //dz802 ʵ�����ڿ�2��
        simCardFlag = 3;
    #else
        simCardFlag = 0;
    #endif
        
		retCode = cpnv_NvItemWrite(0x29217d, (unsigned char *)(&simCardFlag), 1);
		at_print(AT_DEBUG,"at_sim_switch_operate: salvikie cpnv_NvItemWrite  simCardFlag %d retCode = %d\n", simCardFlag, retCode);
		if(CPNV_OK == retCode)
		{
            retCode= cpnv_NvramFlush();
			at_print(AT_DEBUG,"at_sim_switch_operate cpnv_NvramFlush retCode = %d\n", retCode);
        }

#if defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        //dz802 ʵ�����ڿ�2 ����ͨ������ Ҫcfun 0 -> cfun 1
        at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
        sleep(1);
        at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
#else
        //ext sim exist need cfun=0     so sim_switch_ctrl not 0,0 need cfun=0
        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
        rcv_clt_req_msg_proc(buf);
        free(buf);
#endif

    }
}
 
#endif

char* zurdy_convert_cmd(void *msg,struct at_context *context)
{   
    char *at_str = NULL;
    
    at_str = (char *)malloc(20);
    if(at_str == NULL){
		softap_assert("");
		return NULL;
	}
    strcpy(at_str, "AT+ZSCHPLMN=1\r\n");
    return at_str;
}

int zmmi_auto_act(char *at_paras ,int is_query_report)
{
	
	char mode[10] = {0};
	struct time_info timeinfo = {0};
	struct tm set_tm = {0};
	struct timeval time_tv = {0};
	char temp1[256] = {0};
	char temp2[256] = {0};
	void *p[5] = {&timeinfo.time_zone,&timeinfo.sav_time,temp1,temp2,timeinfo.univer_time};
	int time_zone = 0;
	
    cfg_get_item("sntp_time_set_mode", mode, sizeof(mode));
	if(strcmp(mode, "manual") == 0)
	{
		at_print(AT_ERR,"zmmi_auto_act: do not need to nitz \n");
		return AT_END;
	}
	parse_param2("%d,%d,%256s,%256s,%64s", at_paras, p);
	time_zone = timeinfo.time_zone;// / 4;time_zone�ļ��㷽ʽ���ԣ����ʱ����+22�Ļ����ͻᶪʧ0.5���ĵ������
	set_tm.tm_isdst = timeinfo.sav_time;
	if(0 == strlen(timeinfo.univer_time))
	{
		at_print(AT_ERR,"zmmi_auto_act,univer_time invalid");
		return AT_END;
	}
	sscanf(timeinfo.univer_time,"%2d/%2d/%2d,%2d:%2d:%2d",&set_tm.tm_year,
		&set_tm.tm_mon,&set_tm.tm_mday,&set_tm.tm_hour,&set_tm.tm_min,&set_tm.tm_sec);
	
	at_print(AT_ERR, "zmmi_auto_act, year:%d, month:%d, day:%d, hour:%d, min:%d, sec:%d, wday:%d \n", 
		set_tm.tm_year, set_tm.tm_mon, set_tm.tm_mday, set_tm.tm_hour, set_tm.tm_min, set_tm.tm_sec, set_tm.tm_wday);

	if(set_tm.tm_year < 0 || set_tm.tm_year > INT_MAX-1) // kw 3
	{
		set_tm.tm_year = 0;
	}

	if(set_tm.tm_mon < 0 || set_tm.tm_mon > INT_MAX-1)
	{
		set_tm.tm_mon = 0;
	}

		
	set_tm.tm_year = set_tm.tm_year + 100;
	set_tm.tm_mon = set_tm.tm_mon - 1;	

	time_tv.tv_sec = mktime(&set_tm);
	time_tv.tv_sec = time_tv.tv_sec +	time_zone * 3600 /4;
		
	
	if (0 != settimeofday(&time_tv,NULL))
	{
		at_print(AT_ERR,"zmmi_auto_act,set time of system wrong");
		return AT_END;
	}
	rtc_set_time(MODULE_ID_AT_CTL);
	return AT_END;
}

char* normal_getsysconfigread()
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    sprintf(at_next,"AT^SYSCONFIG?\r\n");
	return at_next;
}

/***********at�����ȡ****************/
//pin/puk��ʣ�������ѯ����
char* normal_getzrapread()
{
    char *at_next=NULL;
    at_next=malloc(20);
	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+ZRAP?\r\n");
	return at_next;
}

//pin���ѯ����
char* normal_getcpinread()
{
    char *at_next=NULL;
    at_next=malloc(16);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,16);
	sprintf(at_next,"AT+CPIN?\r\n");
	return at_next;
}

//pin����������
char* normal_getcpinset(char *pin)
{
    if(pin == NULL){
		softap_assert("");
		return NULL;
	}
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CPIN=\"%s\"\r\n",pin);
	return at_next;
}

//��puk������
char* normal_getcpukset(char *puk,char *newpin)
{
    if(puk == NULL){
		softap_assert("");
		return NULL;
	}
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CPIN=\"%s\",\"%s\"\r\n",puk,newpin);
	return at_next;
}

//IMEI�Ż�ȡ����
char* normal_getcgsn()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CGSN\r\n");
	return at_next;
}

//ZICCID��ȡ
char* normal_getziccid()
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+ZICCID?\r\n");
	return at_next;
}

//��������SIM�����ʣ���ǰ������Ҫ�ǻ�ȡPLMN��λ��
char* normal_getcrsmset(char *param)
{
    if(param == NULL){
		softap_assert("");
		return NULL;
	}
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CRSM=%s\r\n",param);
	return at_next;
}

//IMSI�Ż�ȡ
char* normal_getcimi()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CIMI\r\n");
	return at_next;
}

//�û������ȡ��Ŀǰ���صĶ�ΪERROR
char* normal_CnumRead()
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CNUM\r\n");
    if(AT_CMD_MAX <= strlen(at_next)){softap_assert("");}
	return at_next;
}

/*���ݵ�ǰNVֵ,����sysconfig��������*/
char* normal_getsysconfigset()
{
    char *at_next=NULL;
    int mode = ZAT_SYSCONFIG_MODE_AUTO;
    int acqorder = ZAT_SYSCONFIG_PREF_ACQ_AUTO;
	int roam = 1;
    normal_getsysconfigsetParam(&mode, &acqorder, &roam);
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    sprintf(at_next,"AT^SYSCONFIG=%d,%d,%d,2\r\n", mode, acqorder, roam);
	return at_next;
}

/*���ݵ�ǰNVֵ,����cgdcont PDP��������������*/
char* normal_getcgdcontset(int cid)
{
    char *at_next=NULL;
    char strPdptype[10] = {0};
    char strAPN[APN_MAX_LEN] = {0};
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    at_print(AT_DEBUG,"normal_getcgdcontset cid=%d\n",cid);
    //����ipv4v6��֧��
    cfg_get_item(NV_PDP_TYPE,strPdptype,sizeof(strPdptype));
    cfg_get_item(NV_WAN_APN,strAPN,sizeof(strAPN)); 
    if(strcmp("IPV6", strPdptype) == 0 || strcmp(strPdptype,"IPv6") == 0)
    {
        strncpy(strPdptype, "IPV6",sizeof(strPdptype)-1);
        cfg_get_item(NV_IPV6_WAN_APN,strAPN,sizeof(strAPN)); 
    }
    else if(strcmp(strPdptype,"IPV4V6") == 0 || strcmp(strPdptype,"IPv4v6") == 0)
    {
        strncpy(strPdptype, "IPV4V6",sizeof(strPdptype)-1);
        cfg_get_item(NV_IPV6_WAN_APN,strAPN,sizeof(strAPN)); 
    }
    sprintf(at_next,"AT+CGDCONT=%d,\"%s\",\"%s\"\r\n", cid, strPdptype, strAPN);
    return at_next;
}

/*���ݵ�ǰNVֵ,����zgpcoauth��Ȩ��������*/
char* normal_getzgpcoauthset(int cid)
{
    char *at_next=NULL;
    char strAuthtype[10]             = {0};
    char strPdptype[20 ]  = {0};
    char strUsename[PDP_AUTH_USER_PWD_LENGTH]  = {0};
    char strPawd[PDP_AUTH_USER_PWD_LENGTH] = {0};
    int auth_type = -1;
    at_print(AT_DEBUG,"normal_getzgpcoauthset cid=%d\n",cid);

    cfg_get_item(NV_PDP_TYPE,strPdptype,sizeof(strPdptype));
    
    if(strcmp("IPV6", strPdptype) == 0 || strcmp(strPdptype,"IPv6") == 0)
    {
        cfg_get_item(NV_IPV6_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
    	cfg_get_item(NV_IPV6_PPP_USERNAME,strUsename,sizeof(strUsename));
    	cfg_get_item(NV_IPV6_PPP_PASSWD,strPawd,sizeof(strPawd));
    
    }
    else if(strcmp(strPdptype,"IPV4V6") == 0 || strcmp(strPdptype,"IPv4v6") == 0)
    {
        cfg_get_item(NV_IPV6_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
    	cfg_get_item(NV_IPV6_PPP_USERNAME,strUsename,sizeof(strUsename));
    	cfg_get_item(NV_IPV6_PPP_PASSWD,strPawd,sizeof(strPawd));
    }
    else 
    {

        cfg_get_item(NV_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
        cfg_get_item(NV_PPP_USERNAME,strUsename,sizeof(strUsename));
        cfg_get_item(NV_PPP_PASSWD,strPawd,sizeof(strPawd));
    }
    auth_type = normal_getauthtype(strAuthtype);

    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    sprintf(at_next,"AT+ZGPCOAUTH=%d,\"%s\",\"%s\",%d\r\n", cid, strUsename, strPawd, auth_type);
    return at_next;
}


char* normal_getcfunset(char *cmdtype)
{
    char *at_next=NULL;

    at_next=malloc(128);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,128);
    if(cmdtype != ZUFI_NULL)//�ж��ǿ���Ƶ���ǽ������ģʽ����
    {  
        if(memcmp(cmdtype, ZAT_POWEROFF, strlen(ZAT_POWEROFF)) == 0)
        {
            cfun_state=0;
        }
        else if(memcmp(cmdtype, ZAT_AIRMODE, strlen(ZAT_AIRMODE)) == 0)
        {
            cfun_state=4;
			cfg_set(NV_NETWORK_TYPE, "No Service");
			cfg_set(NV_SUB_NETWORK_TYPE, "No Service");
        }
        else if(memcmp(cmdtype, ZAT_SIMOFF, strlen(ZAT_SIMOFF)) == 0)
        {
            cfun_state=5;
        }
        else
        {
            cfun_state=1;
        }
    }
	else
	{
        cfun_state=1;	
	}
	if(cfun_state == 1)
    {   	    
	   	sprintf(at_next,"AT+ZSCHPLMN=1;+CFUN=%d\r\n",cfun_state);
    }
	else
	    sprintf(at_next,"AT+CFUN=%d\r\n",cfun_state);
	cfg_set("cfun_work", "work");
	return at_next;
}

//����CGREG�����ϱ�
char* normal_getcgregset(char *param)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CGREG=%s\r\n",param);
	return at_next;
}

//����CEREG�����ϱ�
char* normal_getceregset(char *param)
{
    char *at_next=NULL;
    at_next=malloc(16);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,16);
	sprintf(at_next,"AT+CEREG=%s\r\n",param);
	return at_next;
}

//�Զ��������ֶ���������
char* normal_getcopsset(int paramnum,...)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    va_list ap;    
    va_start(ap, paramnum);
    if(paramnum==1)
    {
        sprintf(at_next,"AT+COPS=%s\r\n",va_arg(ap,char*));//����ѡ��ģʽ��һ�������Ϊ0����ʾ�Զ�����
    }
    else if(paramnum==2)
    {
        sprintf(at_next,"AT+COPS=1,2,\"%s\",%d\r\n",va_arg(ap,char*),va_arg(ap,int));//�ֶ��������ã���Я������ʽ
    }
    else if(paramnum==3)
    {
        sprintf(at_next,"AT+COPS=1,2,\"%s\",%d,%d\r\n",va_arg(ap,char*),va_arg(ap,int),va_arg(ap,int));	//�ֶ��������ã�Я������ʽ
    }
    else
    {
    	free(at_next);
		va_end(ap);
        return NULL;
    }
    va_end(ap);
    at_print(AT_DEBUG,"normal_getcopsset %s!\n",at_next);
    return at_next;
}

//�ֶ�����ʱ����ȡ��ǰ�����д��ڵ���Ӫ���б�
char* normal_getcopstest()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+COPS=?\r\n");
	return at_next;
}

//���ص�ǰ������ģʽ
char* normal_getcopsread()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+COPS?\r\n");
	return at_next;
}

//��ѯ�ź�ǿ��
char* normal_getcsq()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CSQ\r\n");
    cfg_set(NV_CSQ_DONE, "1");
	return at_next;
}

//��ѯ�����ڲ��汾������
char* normal_getzversion()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT^ZVERSION\r\n");
	return at_next;
}

//����ʱ������
char* normal_getsyctimeset(long sec,long usec)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    sprintf(at_next,"AT+SYCTIME=%ld,%ld\r\n",sec,usec);
    return at_next;
}

//����mtnet��������
char* normal_getmtnetset(char *mcc)
{
    char *at_next=NULL;
	char mcc_list[256] = {0};
	
    cfg_get_item("mtnet_test_mcc", mcc_list, sizeof(mcc_list));
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	if(strlen(mcc_list) > 3 && mcc && strlen(mcc) == 3 && strstr(mcc_list,mcc) != NULL)
	{
		sprintf(at_next,"AT+ZSET=\"MTNET_TEST\",1\r\n");
	}
	else
	{
		sprintf(at_next,"AT+ZSET=\"MTNET_TEST\",0\r\n");
	}
    return at_next;
}

//��ȡ���(Ŀǰ���Ϊ�գ��Ƿ��Ǳ�������??)
char* normal_getboardnumread()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+BOARDNUM?\r\n");
	return at_next;
}

//ϵͳ��Ϣ��ȡ
char* normal_getsysinfo()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT^SYSINFO\r\n");
	return at_next;
}

//USSDȡ������
char* normal_getcusdset_clean()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CUSD=2\r\n");
	return at_next;
}

//USSD��������
char* normal_getcusdset(char *param)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CUSD=1,\"%s\",15\r\n",param);
	return at_next;
}

char* normal_getclckset(int paramnum,...)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    va_list ap;    
    va_start(ap, paramnum);
    if(paramnum==2)//��ѯ��ǰSIM��״̬
    {
        sprintf(at_next,"AT+CLCK=\"%s\",%d\r\n",va_arg(ap,char*),va_arg(ap,int));
    }
    else if(paramnum==3)//�رջ��ߴ�PIN�빦��
    {
        sprintf(at_next,"AT+CLCK=\"%s\",%d,\"%s\"\r\n",va_arg(ap,char*),va_arg(ap,int),va_arg(ap,char*));
    }
    else
    {
    	free(at_next);
		va_end(ap);
        return NULL;
    }
    va_end(ap);
    return at_next;
}

char* normal_getcpwdset(char *type,char *oldPin,char *newPin)
{
    char *at_next=NULL;
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
	sprintf(at_next,"AT+CPWD=\"%s\",\"%s\",\"%s\"\r\n",type,oldPin,newPin);
	return at_next;
}

char* normal_getmacread()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+MAC?\r\n");
	return at_next;
}

char* normal_getmac2read()
{
    char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+MAC2?\r\n");
	return at_next;
}

//LTE��Ƶ,δʹ��
char *normal_getzltelcset()
{
    char *at_next=NULL;
    T_zAt_ZltelcPara zltelcPara = {0};
    char actionLte[30] = {0};
    char uarfcnLte[30] = {0};
    char cellPara[30] = {0};
    at_next=malloc(AT_CMD_MAX);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,AT_CMD_MAX);
    cfg_get_item(NV_ACTIONLTE,actionLte,sizeof(actionLte));
    cfg_get_item(NV_UARFCNLTE,uarfcnLte,sizeof(uarfcnLte));
	cfg_get_item(NV_CELLPARAIDLTE,cellPara,sizeof(cellPara));
    zltelcPara.actionlte        = atoi(actionLte);
    zltelcPara.uarfcnlte        = atoi(uarfcnLte);
    zltelcPara.cellParaIdlte    = atoi(cellPara); 
    sprintf(at_next,"AT+ZLTELC=%d,%d,%d\r\n", (int)zltelcPara.actionlte, (int)zltelcPara.uarfcnlte, (int)zltelcPara.cellParaIdlte); 
    return at_next;
}
/***********at�����ȡ****************/
//��ȡcidʱ�贫��pdpsetinfo
int normal_getcurparam(struct pdp_act_req *pdpsetinfo)
{
    char strPdptype[10] = {0};
    char strAPN[APN_MAX_LEN] = {0};
    //pdpsetinfo->pdp_type=PDP_NORMAL;
    
    cfg_get_item(NV_PDP_TYPE,strPdptype,sizeof(strPdptype));
    cfg_get_item(NV_WAN_APN,strAPN,sizeof(strAPN)); 
    if(strcmp("IPV6", strPdptype) == 0 || strcmp(strPdptype,"IPv6") == 0)
    {
        strncpy(strPdptype, "IPV6",sizeof(strPdptype)-1);
        cfg_get_item(NV_IPV6_WAN_APN,strAPN,sizeof(strAPN)); 
    }
    else if(strcmp(strPdptype,"IPV4V6") == 0 || strcmp(strPdptype,"IPv4v6") == 0)
    {
        strncpy(strPdptype, "IPV4V6",sizeof(strPdptype)-1);
        cfg_get_item(NV_IPV6_WAN_APN,strAPN,sizeof(strAPN)); 
    }
    strncpy(pdpsetinfo->comm_info.apn,strAPN,sizeof(pdpsetinfo->comm_info.apn)-1);
    strncpy(pdpsetinfo->comm_info.ip_type,strPdptype,sizeof(pdpsetinfo->comm_info.ip_type)-1);
    at_print(AT_DEBUG,"normal_getcurparam apn=%s,apntype=%s\n",pdpsetinfo->comm_info.apn,pdpsetinfo->comm_info.ip_type);
    
    char strAuthtype[10]             = {0};
    char strUsename[PDP_AUTH_USER_PWD_LENGTH]  = {0};
    char strPawd[PDP_AUTH_USER_PWD_LENGTH] = {0};
    if(strcmp("IPV6", strPdptype) == 0 || strcmp(strPdptype,"IPv6") == 0)
    {
        cfg_get_item(NV_IPV6_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
        cfg_get_item(NV_IPV6_PPP_USERNAME,strUsename,sizeof(strUsename));
        cfg_get_item(NV_IPV6_PPP_PASSWD,strPawd,sizeof(strPawd));
        
    }
    else if(strcmp(strPdptype,"IPV4V6") == 0 || strcmp(strPdptype,"IPv4v6") == 0)
    {
        cfg_get_item(NV_IPV6_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
        cfg_get_item(NV_IPV6_PPP_USERNAME,strUsename,sizeof(strUsename));
        cfg_get_item(NV_IPV6_PPP_PASSWD,strPawd,sizeof(strPawd));
    }
    else 
    {
        
        cfg_get_item(NV_PPP_AUTH_MODE,strAuthtype,sizeof(strAuthtype));
        cfg_get_item(NV_PPP_USERNAME,strUsename,sizeof(strUsename));
        cfg_get_item(NV_PPP_PASSWD,strPawd,sizeof(strPawd));
    }
    pdpsetinfo->comm_info.auth_type = normal_getauthtype(strAuthtype);
    strncpy(pdpsetinfo->comm_info.username,strUsename,sizeof(pdpsetinfo->comm_info.username)-1);
    strncpy(pdpsetinfo->comm_info.password,strPawd,sizeof(pdpsetinfo->comm_info.password)-1);
    at_print(AT_DEBUG,"normal_getcurparam authtype=%d,user=%s,psw=%s\n",pdpsetinfo->comm_info.auth_type,pdpsetinfo->comm_info.username,pdpsetinfo->comm_info.password);
    return 0;
}

DbResult noraml_dbopen(char *filename, sqlite3**pDb)
{
	//zdm tos���Ӳ���
	#if (APP_OS_TYPE == APP_OS_LINUX)
    sqlite3* pTmpDb = NULL;
    if(NULL == pDb)
    {
        at_print(AT_ERR,"noraml_dbopen:invalide inputs.");/*lint  !e26*/
        return DB_ERROR_INVALIDPTR;
    }
    at_print(AT_DEBUG,"noraml_dbopen:sqlite3_open call");/*lint  !e26*/
    if(sqlite3_open(filename, &pTmpDb))
    {
        at_print(AT_DEBUG,"noraml_dbopen:can not open db,sqlite3_errmsg:%s.",sqlite3_errmsg(pTmpDb));
        (VOID) sqlite3_close(pTmpDb);
        return DB_ERROR_NOTOPENDB;
    }
    *pDb = pTmpDb;
	#else
    softap_assert("");
	#endif
	
    return DB_OK;
}

DbResult normal_dbclose(sqlite3 *pDb)
{
	#if (APP_OS_TYPE == APP_OS_LINUX)
	
    if(sqlite3_close(pDb))
    {
        at_print(AT_ERR,"normal_dbclose:can not close db");/*lint  !e26*/
        return DB_ERROR;
    }
	#else
		softap_assert("");
	#endif
    return DB_OK;
}

DbResult normal_execdbsql(char *filename, const char *pSql, sqlite3_callback callback,VOID *pFvarg)
{
    sqlite3 *pDb = NULL;
    char dbErrMsg[128] = {0};
	#if (APP_OS_TYPE == APP_OS_LINUX)
    
    if(DB_OK != noraml_dbopen(filename, &pDb))
    {
        at_print(AT_ERR,"normal_execdbsql:open pbm.db failed.");
        return DB_ERROR_NOTOPENDB;
    }
    at_print(AT_DEBUG,"normal_execdbsql:atPb_ExecDbSql:%s\n",pSql);
    if(sqlite3_exec(pDb,pSql,callback,pFvarg,NULL))
    {
        strncpy(dbErrMsg,sqlite3_errmsg(pDb),sizeof(dbErrMsg)-1);
        at_print(AT_ERR,"normal_execdbsql:can not exec sql,sqlite3_errmsg:%s.",dbErrMsg);
        (VOID)sqlite3_close(pDb);
        return DB_ERROR;
    }
    (VOID)normal_dbclose(pDb);
	#else
		softap_assert("");
	#endif
    return DB_OK;
}

int normal_count_callback(void *fvarg,int line,char **zresult,char **lname)
{
	if(1 > line)
	{
		at_print(AT_DEBUG,"atBase:record no data.");
		return -1;
	}
	*(int*)fvarg = atoi(zresult[0]);
	return 0;
}

/*�����豸�ͺź�plmn,��ѯlocknet.db*/
int normal_islocknetwhitelist(char *pmodel, char *pplmn)
{
    char sql[128] = {0};
	int lcount = 0;
    int result = DB_OK;
    
    snprintf(sql,128-1,"select count(*) from net_list where product_model=\'%s\' and plmn=\'%s\'", pmodel, pplmn);
    result = normal_execdbsql(ZLOCKNET_DB_PATH, sql, normal_count_callback, &lcount);
    if(DB_OK != result)
    {
        at_print(AT_DEBUG,"atBase_isLockNetWhiteList error:%d !\n", result); 
        return -1;
    }
    if(lcount > 0)
    {
        at_print(AT_DEBUG,"atBase_isLockNetWhiteList succ:%d !\n", lcount); 
		return 0;
	}
	at_print(AT_DEBUG,"atBase_isLockNetWhiteList not int list:%d, model:%s, plmn:%s !\n", lcount, pmodel, pplmn); 
    return -1;
}

/*�������*/
BOOL normal_locknetmatch(const char* mcc, const char* mnc)
{
	char plmn[LOCK_NET_PLMN_LEN] = {0};
	char product_model[LOCK_NET_PLMN_LEN] = {0};
	char useLockNet[10] = {0};
	snprintf(plmn, LOCK_NET_PLMN_LEN-1, "%s%s", mcc, mnc);
	cfg_get_item("product_model", product_model, sizeof(product_model));
    cfg_get_item("use_lock_net", useLockNet, sizeof(useLockNet));
	if(0 == strcmp("yes", useLockNet))
	{
		if(normal_islocknetwhitelist(product_model, plmn) == 0)
		{
			return TRUE;
		}
		return FALSE;
	}
	else
	{
		return TRUE;
	}	
}

/*ѡ����webui NV����һ�µ�sysconfig����*/
int normal_getsysconfigsetParam(int *mode, int *acqOrder, int *roam)
{
    long  i                           = 0;
    char    netSelect[20]  = {0};
    char    modeSelect[20] = {0};
    long  tableSize                   = sizeof(G_ZAT_SYSCONFIG_CFGPARA_SET)/sizeof(T_zAt_SysConfigParaSet);
	char needTds[50] = {0};
    char needGsm[50] = {0};
    char roamSet[10] = {0};
    
    cfg_get_item(NV_NET_SELECT,netSelect,sizeof(netSelect));
    cfg_get_item(NV_PRE_MODE,modeSelect,sizeof(modeSelect));
    cfg_get_item(NV_NETWORK_NEED_TDS,needTds,sizeof(needTds));
    cfg_get_item(NV_NETWORK_NEED_GSM,needGsm,sizeof(needGsm));
    cfg_get_item(NV_ROAM_SETTING_OPTION,roamSet,sizeof(roamSet));
	at_print(AT_DEBUG,"atBase_AtSysconfigSet:netSelect:%s! modeSelect:%s roamSet:%s.\n",netSelect,modeSelect,roamSet);
    if(0 == strcmp(roamSet, "on"))
	{
		*roam = 1;
	}
	else if(0 == strcmp(roamSet, "off"))
	{
		*roam = 0;
	}
	else
	{
		*roam = 2;
	}
    if(0 == strcmp(netSelect, "NETWORK_auto"))  
    {
        if(0 == strcmp(modeSelect, "LTE_TD_pre"))
        {
            *mode       = ZAT_SYSCONFIG_MODE_GSM_TD_LTE;
            *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_LTEPRE;
            return TRUE;
        }
        else if(0 == strcmp(modeSelect, "TD_GSM_pre"))
        {
            *mode       = ZAT_SYSCONFIG_MODE_GSM_TD_LTE;
            *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_TDPRE;
            return TRUE;
        }
		else if(0 == strcmp(modeSelect, "")) 
        {        	
			char auto_acqorder[50] = {0};
        	
			cfg_get_item("auto_acqorder",auto_acqorder,sizeof(auto_acqorder));

			//�û�ѡ���Զ�������ʹ��NV��������������򣬸�NVӦ�ڰ汾������һ�α���������
			if(0 == strlen(auto_acqorder))
			{
				//softap_assert("");
				at_print(AT_ERR,"normal_getsysconfigsetParam auto_acqorder null\n");
			}
			*mode       = ZAT_SYSCONFIG_MODE_AUTO;
            *acqOrder   = atoi(auto_acqorder);
#if 0
			if((strcmp("yes", needTds) == 0) && (strcmp("yes", needGsm) == 0))
			{
				*mode       = ZAT_SYSCONFIG_MODE_AUTO;
                *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_AUTO;
			}
			else if(strcmp("yes", needGsm) == 0)
			{
				*mode       = ZAT_SYSCONFIG_MODE_W_GSM_LTE;
                *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_AUTO;
			}
			else if(strcmp("yes", needTds) == 0)
			{
				*mode       = ZAT_SYSCONFIG_MODE_TD_W_LTE;
                *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_AUTO;
			}
			else
			{
				*mode       = ZAT_SYSCONFIG_MODE_WCDMA_LTE;
                *acqOrder   = ZAT_SYSCONFIG_PREF_ACQ_LTE_W_GSM;
			}			
            return TRUE;
#endif
        }
        else
        {
            at_print(AT_DEBUG,"atBase_AtSysconfigSet Value Is not support!\n");
            return FALSE;
        }
    }
    else
    {
        for(i = 0; i < tableSize; i++)
        {
            if( 0 ==  strcmp(G_ZAT_SYSCONFIG_CFGPARA_SET[i].netselect, netSelect))
            {
                *mode       = G_ZAT_SYSCONFIG_CFGPARA_SET[i].modeValue;
                *acqOrder   = G_ZAT_SYSCONFIG_CFGPARA_SET[i].acqOrder;
                
                at_print(AT_DEBUG,"atBase_AtSysconfigSet:mode:%d/acqOrder:%d.\n",*mode,*acqOrder);
                return TRUE;
            }
        }
        at_print(AT_DEBUG,"atBase_AtSysconfigSet Error\n");
        return FALSE;
    }
	return FALSE;
}

int normal_getauthtype(char* pAuthType)
{

	if(strcmp(PDP_AUTH_TYPE_PAP, pAuthType) == 0 || strcmp("PAP", pAuthType) == 0)
	{
		return PAP_AUTH;
	}
	else if(strcmp(PDP_AUTH_TYPE_CHAP, pAuthType) == 0 || strcmp("CHAP", pAuthType) == 0)
	{
		return CHAP_AUTH;
	}
	else if(strcmp(PDP_AUTH_TYPE_PAP_CHAP, pAuthType) == 0)
	{
		return PAP_CHAP_AUTH;
	}

	return NONE_AUTH;
}


//10�����źţ�pdp�ز�ʱ��0S,5S,10S,10S...
VOID  *normal_pdptimeractstart(VOID *arg)
{
    static UINT32  iCount = 0;
    iCount++;
    if(iCount%10 == 0)
    {
        char csqDone[50]={0};
        char networkType[50]={0};
        cfg_get_item(NV_CSQ_DONE,csqDone,sizeof(csqDone));
        cfg_get_item(NV_NETWORK_TYPE,networkType,sizeof(networkType));
        if(0==strcmp("1",csqDone)&&strcmp("No Service",networkType))
        {   
            cfg_set(NV_CSQ_DONE, "0");
            if(0!=ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_AT_CTL,MSG_CMD_QUERY_CSQ_REQ, 0, NULL,04000))
            {
                cfg_set(NV_CSQ_DONE, "1");
            }
        }	
    }
    return ZUFI_NULL;
}

void normal_cfunOkSet()
{
#if(APP_OS_TYPE == APP_OS_TOS)
	softap_assert("");
#else 
    /*������ʱPDP���߶�ʱ��*/
    CreateSoftTimer(PdpTimerID,
                        TIMER_FLAG_RESTART,
                        PdpTimer_INTERVAL,
                        normal_pdptimeractstart,
                        NULL);
#endif   
}

void* normal_copstimeractstart(VOID *arg)
{
    static UINT32  iCount = 0;
    iCount++;
    if(iCount>=2)
    {
        at_print(AT_DEBUG,"normal_copstimeractstart Timeout!\n");
        struct at_context * context = context_head[NEAR_PS];
        char at_str[AT_CMD_MAX]="AT\r\n";//����msg_buf����at�ж�������׷Ḻ��
        for(;context;context = context->next)
        {
            if(context->context_type == AT_CLIENT && 0 == at_strcmp(context->at_cmd_prefix,"COPS=?"))
            {
                at_print(AT_DEBUG,"normal_copstimeractstart write AT!\n");
                write(context->at_channel->at_fd,at_str, strlen(at_str));//for cov ��ʱ���ﲻ����sleep
                //at_write(context->at_channel->at_fd,at_str, strlen(at_str));//������ֹat�����׷Ḻ����д�����at_stlȡ������
            }
        }
        iCount=0;
    }
    return 0;
}

void normal_copstest_timerout(void *msg)
{
	//zdm ������ʹ��������ã�Ҳ�����Ӳ�������Ҫ��֧�ְ�æ���
	#if (APP_OS_TYPE == APP_OS_LINUX)
    /*AT+COPS=?��ʱ��*/
    CreateSoftTimer(CopstestTimerID,
                        TIMER_FLAG_RESTART,
                        CopstestTimer_INTERVAL,
                        normal_copstimeractstart,
                        NULL);
	#endif
}

void normal_getsignalbarinfo(T_zAt_SignalBarInfo* signalBarInfo, T_zAt_SignalBar_Type signalBarType)
{
	UINT8 tableSize = 0;
	int i, prefixIndex = -1;
	char signalBarName[30] = {0};
	int signalBarVal[SIGNALBAR_NUM_MAX] = {0};
	char strSigbar[10] = { 0 };	
	       
	prefixIndex = (int)	signalBarType; 	
	tableSize = sizeof(suffixSignalBarNV) / 20;
    at_print(AT_DEBUG,"atBase_GetSignalBarInfo enter, tableSize = %d, prefixIndex = %d!\n", tableSize, prefixIndex); 
#if 0  // kw 3 UNREACH.RETURN  
    if(tableSize > SIGNALBAR_NUM_MAX)
	{
		return;
	}
#endif    
	for(i = 0; i < tableSize; i++)
	{
		memset(strSigbar, 0, 10);
		memset(signalBarName, 0, 30);
		snprintf(signalBarName, 30, "%s%s", prefixSignalBarNV[prefixIndex], suffixSignalBarNV[i]);	
			    
		cfg_get_item(signalBarName, strSigbar,sizeof(strSigbar));
		if(strlen(strSigbar) > 0)
		{
			signalBarVal[i] = atoi(strSigbar);
		}
		else
		{
			signalBarVal[i] = -1;
		}
	}
   signalBarInfo->signalbar0_low =  signalBarVal[0];
   signalBarInfo->signalbar0_high = signalBarVal[1];
   signalBarInfo->signalbar1_low =  signalBarVal[2];
   signalBarInfo->signalbar1_high = signalBarVal[3];
   signalBarInfo->signalbar2_low =  signalBarVal[4];
   signalBarInfo->signalbar2_high = signalBarVal[5];
   signalBarInfo->signalbar3_low =  signalBarVal[6];
   signalBarInfo->signalbar3_high = signalBarVal[7];
   signalBarInfo->signalbar4_low =  signalBarVal[8];
   signalBarInfo->signalbar4_high = signalBarVal[9];
   signalBarInfo->signalbar5_low =  signalBarVal[10];
   signalBarInfo->signalbar5_high = signalBarVal[11];
}

/**
**                 RSSI < - 115dBm     1��(0-25)
**  -115dBm <= RSSI < -108 dBm   2��(26-32)
**  -108dBm <= RSSI < -102 dBm   3��(33-38)    
**  -102dBm <= RSSI < -96 dBm     4��(39-44)    
**  -96dBm <= RSSI                      5��(>=45)    
**  
**/
void normal_calcltesignalbar(long rssi)
{
	T_zAt_SignalBarInfo signalBarInfo = {0};
	normal_getsignalbarinfo(&signalBarInfo, ZAT_SIGNALBAR_TYPE_LTE);
    printf("atBase_CalcLteSignalBar: %d <= sigbar0 <= %d, %d <= sigbar1 <= %d, %d <= sigbar2 <= %d, %d <= sigbar3 <= %d,%d <= sigbar4 <= %d, %d <= sigbar5 <= %d,!\n", \
		         signalBarInfo.signalbar0_low, signalBarInfo.signalbar0_high, signalBarInfo.signalbar1_low, signalBarInfo.signalbar1_high,signalBarInfo.signalbar2_low, \
				 signalBarInfo.signalbar2_high,signalBarInfo.signalbar3_low,signalBarInfo.signalbar3_high,signalBarInfo.signalbar4_low, signalBarInfo.signalbar4_high, \
		         signalBarInfo.signalbar5_low, signalBarInfo.signalbar5_high); 
	if((rssi >= signalBarInfo.signalbar0_low) && (rssi <= signalBarInfo.signalbar0_high))
    {
        cfg_set("signalbar", "0");
    }
	else if((rssi >= signalBarInfo.signalbar1_low) && (rssi <= signalBarInfo.signalbar1_high))
    {
        cfg_set("signalbar", "1");
    }
    else if((rssi >= signalBarInfo.signalbar2_low) && (rssi <= signalBarInfo.signalbar2_high))
    {
        cfg_set("signalbar", "2");
    }
    else if((rssi >= signalBarInfo.signalbar3_low) && (rssi <= signalBarInfo.signalbar3_high))
    {
        cfg_set("signalbar", "3");
    }
    else if((rssi >= signalBarInfo.signalbar4_low) && (rssi <= signalBarInfo.signalbar4_high))
    {
        cfg_set("signalbar", "4");
    }
	else if(rssi==99)
    {
        normal_calcunknowsignalbar();	
        return;
    }		
    else
    {
        cfg_set("signalbar", "5");
    }   
}

/**
**            RSSI <= -110dBm   0��(0-6)
**  -110dBm < RSSI <= -104 dBm  1��(7-12)
**  -104dBm < RSSI <= -98 dBm   2��(13-18)    
**  -98dBm < RSSI <= -90 dBm    3��(19-26)    
**  -90dBm < RSSI <= -80 dBm    4��(27-36)    
**  -80dBm < RSSI               5��(>=37   )
**/
void normal_calctdsignalbar(long rssi)
{
	T_zAt_SignalBarInfo signalBarInfo = {0};
	normal_getsignalbarinfo(&signalBarInfo, ZAT_SIGNALBAR_TYPE_TDS);
    printf("atBase_CalcTdSignalBar: %d <= sigbar0 <= %d, %d <= sigbar1 <= %d, %d <= sigbar2 <= %d, %d <= sigbar3 <= %d,%d <= sigbar4 <= %d, %d <= sigbar5 <= %d!\n", \
		         signalBarInfo.signalbar0_low, signalBarInfo.signalbar0_high, signalBarInfo.signalbar1_low, signalBarInfo.signalbar1_high,signalBarInfo.signalbar2_low, \
				 signalBarInfo.signalbar2_high,signalBarInfo.signalbar3_low,signalBarInfo.signalbar3_high,signalBarInfo.signalbar4_low, signalBarInfo.signalbar4_high, \
		         signalBarInfo.signalbar5_low, signalBarInfo.signalbar5_high); 
	if((rssi >= signalBarInfo.signalbar0_low) && (rssi <= signalBarInfo.signalbar0_high))
    {
        cfg_set("signalbar", "0");
    }
	else if((rssi >= signalBarInfo.signalbar1_low) && (rssi <= signalBarInfo.signalbar1_high))
    {
        cfg_set("signalbar", "1");
    }
    else if((rssi >= signalBarInfo.signalbar2_low) && (rssi <= signalBarInfo.signalbar2_high))
    {
        cfg_set("signalbar", "2");
    }
    else if((rssi >= signalBarInfo.signalbar3_low) && (rssi <= signalBarInfo.signalbar3_high))
    {
        cfg_set("signalbar", "3");
    }
    else if((rssi >= signalBarInfo.signalbar4_low) && (rssi <= signalBarInfo.signalbar4_high))
    {
        cfg_set("signalbar", "4");
    }
	else if(rssi==99)
    {
        normal_calcunknowsignalbar();	
        return;
    }
    else
    {
        cfg_set("signalbar", "5");
    }  
}

/**
**                 RSSI <= -110dBm   0��(0-1)
**  -110dBm < RSSI <= -104 dBm  1��(2-4)
**  -104dBm < RSSI <= -98 dBm   2��(5-7)    
**  -98dBm < RSSI <= -90 dBm    3��(8-11)    
**  -90dBm < RSSI <= -80 dBm    4��(12-16)    
**  -80dBm < RSSI               5��(>=17   )
**/
void normal_calcothersignalbar(long rssi)
{
	T_zAt_SignalBarInfo signalBarInfo = {0};

	normal_getsignalbarinfo(&signalBarInfo, ZAT_SIGNALBAR_TYPE_GSM);
    printf("atBase_CalcOtherSignalBar: %d <= sigbar0 <= %d, %d <= sigbar1 <= %d, %d <= sigbar2 <= %d, %d <= sigbar3 <= %d,%d <= sigbar4 <= %d, %d <= sigbar5 <= %d,!\n", \
		         signalBarInfo.signalbar0_low, signalBarInfo.signalbar0_high, signalBarInfo.signalbar1_low, signalBarInfo.signalbar1_high,signalBarInfo.signalbar2_low, \
				 signalBarInfo.signalbar2_high,signalBarInfo.signalbar3_low,signalBarInfo.signalbar3_high,signalBarInfo.signalbar4_low, signalBarInfo.signalbar4_high, \
		         signalBarInfo.signalbar5_low, signalBarInfo.signalbar5_high); 
    if(rssi==99)
    {
		normal_calcunknowsignalbar();			
        return;
    }
	if((rssi >= signalBarInfo.signalbar0_low) && (rssi <= signalBarInfo.signalbar0_high))
    {
        cfg_set("signalbar", "0");
    }
	else if((rssi >= signalBarInfo.signalbar1_low) && (rssi <= signalBarInfo.signalbar1_high))
    {
        cfg_set("signalbar", "1");
    }
    else if((rssi >= signalBarInfo.signalbar2_low) && (rssi <= signalBarInfo.signalbar2_high))
    {
        cfg_set("signalbar", "2");
    }
    else if((rssi >= signalBarInfo.signalbar3_low) && (rssi <= signalBarInfo.signalbar3_high))
    {
        cfg_set("signalbar", "3");
    }
    else if((rssi >= signalBarInfo.signalbar4_low) && (rssi <= signalBarInfo.signalbar4_high))
    {
        cfg_set("signalbar", "4");
    }
    else if((rssi>=signalBarInfo.signalbar5_low) && (rssi<=signalBarInfo.signalbar5_high))
    {
        cfg_set("signalbar", "5");
    }
}

void normal_calcunknowsignalbar(void)
{
    char signalbar_99[30] = {0};
    char netType[50]={0};
    cfg_get_item(NV_NETWORK_TYPE,netType,sizeof(netType));
    cfg_get_item("signalbar_99", signalbar_99,sizeof(signalbar_99));
    if(strcmp("0", signalbar_99) == 0)
    {
        cfg_set("signalbar", "0");
    }
    else if (g_zUfi_firstCsq
            &&0!=strcmp("No Service",netType)
            &&0!=strcmp("Limited Service",netType)
            &&0!=strcmp(NETWORK_TYPE_INIT,netType))
    {
        cfg_set("signalbar", "3");
    }
}

void normal_calcsignalbar(T_zAt_SignalBar_Type signalBarType)
{	
    char netType[30] = {0};
    char strRssi[30] = { 0 };
    long  csq  = 0;
    at_print(AT_DEBUG,"normal_calcsignalbar!\n");
    UINT32 signalbar = 0;
    char oldSignalbar[10] = {0};
    char newSignalbar[10] = {0};
    cfg_get_item("signalbar",oldSignalbar,sizeof(oldSignalbar));
    at_print(AT_DEBUG,"oldSignalbar=%s!\n",oldSignalbar);
    
    cfg_get_item("csq",strRssi,sizeof(strRssi));
    csq = atoi(strRssi);
	if(csq < 0 || csq > INT_MAX-1) // kw 3
	{
	    csq = 0;
	}
    cfg_get_item("network_type",netType,sizeof(netType));
    if(signalBarType == ZAT_SIGNALBAR_TYPE_LTE)
    {
        normal_calcltesignalbar(csq - 100);
    }
    else if(signalBarType == ZAT_SIGNALBAR_TYPE_WCDMA)
    {
        normal_calctdsignalbar(csq - 100);
    }
    else if(signalBarType == ZAT_SIGNALBAR_TYPE_TDS)
    {
        normal_calctdsignalbar(csq - 100);
    }
    else if(signalBarType == ZAT_SIGNALBAR_TYPE_GSM)
    {
        normal_calcothersignalbar(csq);
    }
    else if(signalBarType == ZAT_SIGNALBAR_TYPE_NONE)
    {
        if(csq == 99 || csq == 199)
        {
            normal_calcunknowsignalbar();	
        }
    }
    cfg_get_item("signalbar",newSignalbar,sizeof(newSignalbar));
    at_print(AT_DEBUG,"newSignalbar=%s!\n",newSignalbar);
    if(strcmp(newSignalbar, oldSignalbar))
    {
        signalbar = atoi(newSignalbar);
    	if(signalbar > UINT_MAX-1)  // kw 3 cov M
    	{
    	    signalbar = UINT_MAX;
    	}		
        int result = ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MMI,MSG_CMD_OUT_REG_GET_SIGNAL_NUM, sizeof(signalbar), (UINT8 *)&signalbar,0);
        at_print(AT_DEBUG,"normal_calcsignalbar %d!\n", result);
    }
}

void normal_calcltesignalstrength(long rssi)
{
	CHAR rssi_buf[20] = {0};
	int dbm_val = 0;
	if(0 == rssi)
    {
        snprintf(rssi_buf, 20, "%s", "<-140");		
    }
	else if(97 == rssi)
	{
		snprintf(rssi_buf, 20, "%s", ">=-44");
	}
	else if(99 == rssi)
	{
       return;
	}
	else if((rssi > 0) && (rssi < 97))
	{
		dbm_val = 141 - rssi;
		snprintf(rssi_buf, 20, "-%d", dbm_val);
	}
	cfg_set("lte_rsrp", rssi_buf);
	cfg_set("rssi", rssi_buf);
}

void normal_calctdsignalstrength(long rssi)
{
	char rssi_buf[20] = {0};
	int dbm_val = 0;
	if(0 == rssi)
    {
        snprintf(rssi_buf, 20, "%s", "<-115");		
    }
	else if(91 == rssi)
	{
		snprintf(rssi_buf, 20, "%s", ">=-25");
	}
	else if(99 == rssi)
	{
       return;
	}
	else if((rssi > 0) && (rssi < 91))
	{
		dbm_val = 116 - rssi;
		snprintf(rssi_buf, 20, "-%d", dbm_val);
	}
	cfg_set("rscp", rssi_buf);
	cfg_set("rssi", rssi_buf);
	cfg_set("lte_rsrp", "");
}

void normal_calcothersignalstrength(long rssi)
{
	char rssi_buf[20] = {0};
	int dbm_val = 0;
	if(0 == rssi)
    {
        snprintf(rssi_buf, 20, "%s", "<=-113");		
    }
	else if(31 == rssi)
	{
		snprintf(rssi_buf, 20, "%s", ">=-51");
	}
	else if(99 == rssi)
	{
       return;
	}
	else if((rssi > 0) && (rssi < 31))
	{
		dbm_val = 113 - 2*rssi;
		snprintf(rssi_buf, 20, "-%d", dbm_val);
	}
	cfg_set("rssi", rssi_buf);
	cfg_set("lte_rsrp", "");
}

void normal_calcsignalstrength(T_zAt_SignalBar_Type signalBarType, long  rssi)
{	
	if(signalBarType == ZAT_SIGNALBAR_TYPE_LTE)
	{
		normal_calcltesignalstrength(rssi - 100);
	}
    else if((signalBarType == ZAT_SIGNALBAR_TYPE_WCDMA) || (signalBarType == ZAT_SIGNALBAR_TYPE_TDS))
    {
        normal_calctdsignalstrength(rssi - 100);
	}
	else if(signalBarType == ZAT_SIGNALBAR_TYPE_GSM)
	{
		normal_calcothersignalstrength(rssi);
	}
}

VOID normal_sysinfosysmodecfgset(long sysModeIn, long sysSubModeIn)
{
    UINT32 i            =   0;
    UINT32 tableSize = sizeof(G_ZAT_SYSINFO_SET_NETTYPE)/sizeof(T_zAt_SysInfoSetNetType);

	for(i = 0; i < tableSize; i++)
    {
        if(G_ZAT_SYSINFO_SET_NETTYPE[i].sysmode == sysModeIn)
        {
            cfg_set(NV_NETWORK_TYPE,G_ZAT_SYSINFO_SET_NETTYPE[i].netType);
        }
    }
	
	tableSize = sizeof(G_ZAT_SYSINFO_SET_SUBNETTYPE)/sizeof(T_zAt_SysInfoSetNetType);
	for(i = 0; i < tableSize; i++)
    {
        if(G_ZAT_SYSINFO_SET_SUBNETTYPE[i].sysmode == sysSubModeIn)
        {
            cfg_set(NV_SUB_NETWORK_TYPE,G_ZAT_SYSINFO_SET_SUBNETTYPE[i].netType);
        }
    }
	
	return;
}

VOID normal_sysinfonetworktypeSet(long sysMode,long sysSubmode)
{
    UINT32 i            =   0;
    UINT32 tableSize = sizeof(G_ZAT_SYSINFO_CMCC_NETTYPE)/sizeof(T_zAt_SysInfoSetNetType);
    if(sysMode==ZAT_SYSINFORES_SYSMODE_GSM && sysSubmode==3)
    {
        cfg_set(NV_NETWORK_TYPE,"E");
        return;
    }
    for(i = 0; i < tableSize; i++)
    {
        if(G_ZAT_SYSINFO_CMCC_NETTYPE[i].sysmode == sysMode)
        {
            cfg_set(NV_NETWORK_TYPE,G_ZAT_SYSINFO_CMCC_NETTYPE[i].netType);
        }
    }
}

long normal_isroam(char *sim_plmn, char *net_plmn)
{
    char sql[128] = {0};
	int lcount = 0;
    int result = DB_OK;
	
    if(ZUFI_NULL == sim_plmn || ZUFI_NULL == net_plmn)
    {
		at_print(AT_DEBUG,"atBase_IsRoam paraments error !\n"); 
        return -1;
    }

    snprintf(sql,128-1,"select count(*) from roam_list where sim_plmn=\'%s\' and net_plmn=\'%s\'", sim_plmn, net_plmn);
    result = normal_execdbsql(ZROAM_DB_PATH, sql, normal_count_callback, &lcount);
    if(DB_OK != result)
    {
        at_print(AT_DEBUG,"atBase_IsRoam error:%d !\n", result); 
        return -1;
    }
    if(lcount > 0)
    {
        at_print(AT_DEBUG,"atBase_IsRoam succ:%d !\n", lcount); 
		return 0;
	}
	at_print(AT_DEBUG,"atBase_IsRoam not int list:%d, sim_plmn:%s, net_plmn:%s !\n", lcount, sim_plmn, net_plmn); 
    return -1;
}

BOOL normal_checkroam(const char* mcc, const char* mnc)
{
    char imcc[10] = {0};
    char imnc[10] = {0};
	char checkRoam[10] = {0};
	char sim_plmn[10] = {0};
	char net_plmn[10] = {0};

    cfg_get_item("check_roam", checkRoam,sizeof(checkRoam));
	cfg_get_item("mcc", imcc,sizeof(imcc));
	cfg_get_item("mnc", imnc,sizeof(imnc));
	if(0 == strcmp("yes", checkRoam))
	{
	    snprintf(sim_plmn, 10-1, "%s%s", mcc, mnc);
	    snprintf(net_plmn, 10-1, "%s%s", imcc, imnc);
		
		if(normal_isroam(sim_plmn, net_plmn) == 0)
		{
			return TRUE;
		}

		return FALSE;
	}
	else
	{
		return FALSE;
	}	

}

VOID normal_simcardcfgreset()
{
	cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_undetected");
    cfg_set("lte_pci", "");
	cfg_set("lte_earfcn", "");
	cfg_set("lte_rsrp", "");
	cfg_set("lte_sinr", "");
	cfg_set("sim_zpbic_status","no_card");
	cfg_set(NV_MSISDN,"");
	cfg_set(NV_SIMCARD_TYPE,"");
	cfg_set(NV_IMEI, "");
	cfg_set(NV_OPER_NUM, "");
	cfg_set(NV_PDP_STATUS, "0");
    cfg_set(NV_NETWORK_PROVIDER, "");
    cfg_set(NV_NETWORK_TYPE, "");
	cfg_set(NV_SUB_NETWORK_TYPE, "");
    cfg_set(NV_SIMCARD_ROAM, "");
    cfg_set(NV_CSQ, "");
    cfg_set(NV_RSSI, "");
    cfg_set(NV_ECIO, "");
    cfg_set(NV_LAC_CODE,"");
    cfg_set(NV_CELL_ID,"");
    cfg_set(NV_WAN_IPADDR, "0.0.0.0");
    cfg_set(NV_WAN_NETMASK,"0.0.0.0");
    cfg_set(NV_WAN_GATEWAY,"0.0.0.0");
    cfg_set(NV_PREFER_DNS_AUTO, "0.0.0.0");
    cfg_set(NV_STANDBY_DNS_AUTO, "0.0.0.0");
    cfg_set(NV_SIM_PIN, "0");
    cfg_set(NV_SIM_PUK, "0");
    cfg_set(NV_PIN_MANAGE_RESULT,"");
    cfg_set(NV_PINNUMBER,"3");
    cfg_set(NV_PUKNUMBER,"10");
    cfg_set(NV_PIN_STATUS,"");
    cfg_set(NV_PIN_PUK_PROCESS,"");
    cfg_set(NV_PIN_MANAGE_PROCESS,"");
    cfg_set(NV_UNLOCK_AT_WAIT,"0");
    cfg_set(NV_SIM_SPN,"");
    cfg_set(NV_SEND_PIN_FLAG,"0"); //0:��ʾ�����PIN��û�з���  ��1��ʾ�Ѿ�����
    cfg_set(NV_MCC,"");
    cfg_set(NV_MNC,"");
    cfg_set(NV_LOCK_UI,"" );
    cfg_set(NV_ACTION,"");
    cfg_set(NV_UARFCN,"");
    cfg_set(NV_CELLPARAID,"");
    cfg_set(NV_BANDLOCKSET,"");
    cfg_set(NV_BANDLOCKSTATE,"UnLocked");
    cfg_set(NV_PPP_DIAL,"");
    cfg_set(NV_ZCELL_RESULT,"");
    cfg_set(NV_ZCONSTAT_UP_RESULT, "");
    cfg_set(NV_CGATT_SET_RESULT, "");
    cfg_set(NV_ZGIPDNS_RESULT_DEFAULTCID,"0");
    cfg_set(NV_ZGIPDNS_RESULT_PPPDCID,"0");
	cfg_set(NV_ZGACT_RESULT_DEFAULTCID,"0");
	cfg_set(NV_ZGACT_RESULT_PPPDCID,"0");
    cfg_set(NV_RESETMODEM,"");
    cfg_set(NV_SIGNALBAR,"");// ���㣬��ֹ����sim�����������źŸ���
    //cfg_set(NV_AUTO_RECONNECT,"");//��ֹ�Զ�����ʧ��
    cfg_set(NV_IPV6_WAN_IPADDR,"");
    cfg_set(NV_IPV6_WAN_DEFAULT_GW_ADDR,"");
    cfg_set(NV_IPV6_PREFER_DNS_AUTO,"");
    cfg_set(NV_IPV6_STANDBY_DNS_AUTO,"");
    cfg_set(NV_CFUN_STATE,"1");
    cfg_set(NV_CSQ_DONE, "1");
    //cfg_set(NV_SD_CARD_STATE,"0");	
    cfg_set(NV_SIM_IMSI, "");
    cfg_set(NV_SIM_ICCID,"");
	cfg_set(NV_ZICCID,"");
    cfg_set(NV_M_NETSELECT_MODE, "0");
    cfg_set(NV_SYS_MODE, "");
    cfg_set(NV_SYS_SUBMODE, "");
    cfg_set(NV_DAIL_STEP, "1");

    cfg_set(LOCK_CELL_STATE_FLAG, "0");
    cfg_set("ussd_cancel_flag","no");//ussd
	
}

char *normal_findsubstr(const char *pStr, const char *pSubStr)
{
    long  i = 0;
    long  flg = 0;
    long  nLengthStr = 0;
    long  nLengthSubStr = 0;
    const char *pchTemp = ZUFI_NULL;
    
    /* ȡ�������ַ����ĳ��� */
    nLengthStr      = (long)strlen(pStr);
    nLengthSubStr   = (long)strlen(pSubStr);
    if (nLengthStr < nLengthSubStr)
    {
        return NULL;
    }
    pchTemp = pStr;
    /* ��ʼ���� */
    for (i = 0; i <= nLengthStr - nLengthSubStr; i++)
    {
        if ('"' == *pchTemp)
        {
            if (0 == flg)
            {
                flg = 1;
            }
            else
            {
                flg = 0;
            }
        }
        if ((*pchTemp == *pSubStr) && (0 == flg))
        {
            if (0 == at_strncmp(pchTemp, pSubStr, (UINT32)nLengthSubStr))
            {
                return (char *) pchTemp;
            }
        }
        pchTemp++;
    }
    return NULL;
}

/*Ԥ����*/
VOID normal_preprocresparalineex(char *pParaLine,int paraSize)
{
    UINT32 i = 0;
    UINT32 length = 0;
    char *pSource = ZUFI_NULL;
    char *pDest = ZUFI_NULL;
    char strDestLine[AT_CMD_MAX] = {0};
	
    if (AT_CMD_MAX <= strlen(pParaLine)) /* ������� �����ȼ��*/
    {
    	softap_assert("");
        return;
    }
	if(0 == strlen(pParaLine))
		return;
    length = (UINT32)strlen(pParaLine);
    pSource = pParaLine;                /* ѭ������ */
    pDest = strDestLine;
    for (i = 0; i < length; i++)
    {
        if ('(' == *pSource)
        {
            ;                           /* ������ ɾ�� */
        }
        else if (')' == *pSource)
        {
            if (',' == *(pSource + 1))  /* ������,�жϽ��������Ƿ񶺺�                    */
            {
                pSource++;              /* �Ƕ��ţ�ɾ������������Ŀ�Ĵ��н�����ת��Ϊ�ֺ�   */
                *pDest = ';';
                pDest++;
            }
        }
        else
        {
            *pDest = *pSource;
            pDest++;
        }
        pSource++;
    }
    /* ��Ԥ����������ݿ����ز������� */
    memset(pParaLine, 0, paraSize);
    strncpy(pParaLine, strDestLine,paraSize-1);
}

/*Ԥ����*/
void normal_preprocresparalist(T_zAt_RES_PARAS_LIST *pParaList, const char *pParas)
{
    long i = 0;
    char *pHead = ZUFI_NULL;
    char *pEnd = ZUFI_NULL;
	
	if(!at_strcmp(pParas, "OK"))
	{
		pParaList->listCount = 0;
		return;
	}
	
    /* ѭ������';' */
	
    pHead = pEnd = (char *) pParas;
    pEnd = normal_findsubstr(pHead, ";");
    while (pEnd != ZUFI_NULL)
    {
        /* �ҵ�';' */
        /* Խ�籣�� */
        if ((pParaList->listCount >= ZAT_RES_PARAM_MAX_NUM)
            || (pEnd - pHead >= ZAT_RES_PARAM_MAX_LEN))
        {
            return;
        }
        /* �������� */
        strncpy(pParaList->strParasLine[i], pHead, (UINT32)(pEnd - pHead));
        pParaList->listCount++;
        i++;
        pHead = pEnd + 1;
        pEnd = normal_findsubstr(pHead, ";");// yx  new  add  2013-5-20
    }
	
    if ((strlen(pHead) != 0) && (pParaList->listCount < ZAT_RES_PARAM_MAX_NUM))
    {
        strncpy(pParaList->strParasLine[i], pHead,ZAT_RES_PARAM_MAX_LEN-1);
        pParaList->listCount++;
    }
}

//cops=?�����ϱ��������
VOID normal_recvcopstestrsp(char *at_paras)
{
	UINT32 listIndex = 0;
	UINT32 copsIndex = 0;
	T_zAt_CopsTestRes		 tPara		 = {0};
	T_zAt_RES_PARAS_LIST	tParasList	= {0};
	const char *pCopHeader = NULL;
	char *pCopEnd = NULL;
	int len = 0;
			
	pCopHeader = at_paras;
	pCopEnd = normal_findsubstr(pCopHeader, ",,");
	{
		char copsContent[AT_CMD_MAX] = {0};
		if(pCopEnd == NULL)
		{
			strncpy(copsContent, at_paras,sizeof(copsContent)-1);
		}
		else
		{
		    if(pCopEnd - pCopHeader >= sizeof(copsContent))
			{
			    len = sizeof(copsContent)-1;
			}
		    else
			{
			    len = pCopEnd - pCopHeader;
			}
			snprintf(copsContent,len+1,"%s",at_paras);
			//strncpy(copsContent, at_paras, len);
		}
		
		normal_preprocresparalineex(copsContent,sizeof(copsContent));			   /* Ԥ�������Ӳ����� */
		normal_preprocresparalist(&tParasList, copsContent);   /* Ԥ���������б�   */
	}
	{
		char operatorInfo[256]	= {0};
		char copsList[512] = {0};
		char copsList_tmp[512] = {0};
		for(listIndex = 0; listIndex < tParasList.listCount; listIndex++)
		{	
			void *p[6] = {&tPara.oplist[copsIndex].stat,tPara.oplist[copsIndex].lalphaOper,tPara.oplist[copsIndex].salphaOper,
					tPara.oplist[copsIndex].numericOper,&tPara.oplist[copsIndex].netType,&tPara.oplist[copsIndex].subType};
			at_print(AT_DEBUG,"normal_recvcopstestrsp at_paras=%s!\n",tParasList.strParasLine[listIndex]);
		    parse_param2("%d,%s,%s,%s,%d,%d", tParasList.strParasLine[listIndex], p);
		    at_print(AT_DEBUG,"normal_recvcopstestrsp stat=%d,lalphaOper=%s,salphaOper=%s,numericOper=%s,netType=%d,subType=%d!\n",
		    tPara.oplist[copsIndex].stat,tPara.oplist[copsIndex].lalphaOper,tPara.oplist[copsIndex].salphaOper,
			tPara.oplist[copsIndex].numericOper,tPara.oplist[copsIndex].netType,tPara.oplist[copsIndex].subType);  
			
			if(0 == tPara.oplist[copsIndex].stat && 0 ==strcmp("", tPara.oplist[copsIndex].numericOper))
			{
				continue;
			}
			snprintf(operatorInfo, sizeof(operatorInfo), "%d,%s,%s,%d,%d", 
				tPara.oplist[copsIndex].stat, tPara.oplist[copsIndex].salphaOper, 
				tPara.oplist[copsIndex].numericOper, tPara.oplist[copsIndex].netType, tPara.oplist[copsIndex].subType);
			if(0 == copsIndex)
			{
				snprintf(copsList,sizeof(copsList),"%s", operatorInfo);	
			}
			else
			{
				memcpy(copsList_tmp,copsList,sizeof(copsList_tmp));
				snprintf(copsList,sizeof(copsList),"%s;%s", copsList_tmp ,operatorInfo); 
			}
		    copsIndex++;
		}
		char strValue[20] = {0};
		snprintf(strValue,sizeof(strValue), "%ld", copsIndex);
		cfg_set(NV_NET_NUM, strValue);
		cfg_set(NV_M_NETSELECT_CONTENTS, copsList);
		cfg_set(NV_M_NETSELECT_STATUS, "manual_selected");
	}
}

int normal_netprovider_callback(void *fvarg,int line,char **zresult,char **lname)
{
	T_zAt_OperatorName operatorName = {0};
	if(1 > line)
	{
		at_print(AT_DEBUG,"atBase: NetProvider record no data.");
		return -1;
	}
    if(zresult[0] != NULL)
    {
    	strncpy(operatorName.OperFullName,  zresult[0], sizeof(operatorName.OperFullName)-1);
    }
    if(zresult[1] != NULL)
	{
    	strncpy(operatorName.OperShortName, zresult[1], sizeof(operatorName.OperShortName)-1);
		
	}
	memcpy(fvarg,&operatorName,sizeof(operatorName));
	return 0;
}


long normal_number2networkprovider(char *pplmn, T_zAt_OperatorName *opraName)
{
    char sql[128] = {0};
	T_zAt_OperatorName operatorName = {0};
    int result = DB_OK;
	
    if(ZUFI_NULL == pplmn)
    {
        return -1;
    }

    snprintf(sql,128-1,"select full_name,short_name from net_list where plmn=\'%s\' limit 1", pplmn);
    result = normal_execdbsql(ZNETPROVIDER_DB_PATH, sql, normal_netprovider_callback, &operatorName);
    if(DB_OK != result)
    {
        return ZUFI_FAIL;
    }
    at_print(AT_DEBUG,"normal_number2networkprovider OperShortName=%s\n",operatorName.OperShortName);
	strncpy(opraName->OperShortName, operatorName.OperShortName, sizeof(opraName->OperShortName)-1);
    if(operatorName.OperFullName[0] == '\0')
    {
        return ZUFI_FAIL;
    }
	strncpy(opraName->OperFullName, operatorName.OperFullName, sizeof(opraName->OperFullName)-1);
		
	return ZUFI_SUCC;

}

long normal_getnetworkprovidername(char *MCC, char *MNC,char *Net_Provider_Name,int len)
{
    long   listSize       = 0;
    long   iCount         = 0;
    long   mccNum         = 0;
    long   mncNum         = 0;
    long   qualListMcc    = 0;
    long   quallistMnc    = 0;
	
    if(ZUFI_NULL == MCC || ZUFI_NULL == MNC ||  ZUFI_NULL == Net_Provider_Name)
    {
        at_print(AT_ERR,"the paraments of function is wrong!");
        return ZUFI_FAIL;
    }
    mccNum = atoi( MCC );
    mncNum = atoi( MNC );
	
    listSize = sizeof(G_ZAT_QUALCOMMLISTNODE)/sizeof(T_zAt_QualcommListNode);
	
    for(iCount = 0; iCount< listSize; iCount++)
    {
        qualListMcc = atoi( G_ZAT_QUALCOMMLISTNODE[iCount].mcc );
        quallistMnc = atoi( G_ZAT_QUALCOMMLISTNODE[iCount].mnc );
        if( (qualListMcc == mccNum)  && (quallistMnc == mncNum))
        {
            strncpy(Net_Provider_Name,G_ZAT_QUALCOMMLISTNODE[iCount].networkProvider,len-1);
            break;
        }
    }
	
    if (iCount < listSize)
    {
        /* �ҵ�ƥ����Ӫ������ */
        at_print(AT_DEBUG,"get provider name in list  ok !\n");
        return ZUFI_SUCC;
    }
    else
    {
        /* δ�ҵ�ƥ����Ӫ������ */
        at_print(AT_DEBUG,"can not get provider name in list!\n");
        return  ZUFI_FAIL;
    }
}


VOID normal_recvcopsreadrsp(char * at_paras)
{
    char sProvider[70]       = {0};
    char netProviderName[70] = {0};
    char sMCC[10]            = {0};
    char sMNC[10]            = {0};
	char needCopsNumFormat[10] = {0};
	T_zAt_CopsReadRes  copsReadPara = {0};
	T_zAt_OperatorName operatorName = {0};
	
	void *p[5] = {&copsReadPara.mode,&copsReadPara.format,copsReadPara.oper,&copsReadPara.act,&copsReadPara.subact};
	at_print(AT_DEBUG,"normal_recvcopsreadrsp at_paras=%s!\n",at_paras);
	parse_param2("%d,%d,%s,%d,%d", at_paras, p);
	at_print(AT_DEBUG,"normal_recvcopsreadrsp mode=%ld,format=%ld,oper=%s,act=%ld,subact=%ld!\n",
	copsReadPara.mode,copsReadPara.format,copsReadPara.oper,copsReadPara.act,copsReadPara.subact);	

    strncpy(sProvider, copsReadPara.oper, sizeof(sProvider)-1);
	
    strncpy(sMCC, sProvider, 3);
    at_print(AT_DEBUG,"MCC is %s\n", sMCC);
	
    strncpy(sMNC, sProvider+3,sizeof(sMNC)-1);
    at_print(AT_DEBUG,"MNC is %s\n", sMNC);
	
    cfg_set(NV_MCC, sMCC);
    cfg_set(NV_MNC, sMNC);
    cfg_set(NV_OPER_NUM, copsReadPara.oper);
    cfg_get_item("need_cops_number_format", needCopsNumFormat, sizeof(needCopsNumFormat));
    if(0 == strcmp("yes", needCopsNumFormat))
	{
		if(2 == copsReadPara.format && ZUFI_SUCC == normal_number2networkprovider(sProvider, &operatorName))
		{
			strncpy(netProviderName,operatorName.OperFullName,sizeof(netProviderName)-1);
			cfg_set("network_provider",netProviderName);
            at_print(AT_DEBUG,"operator brand found in the Qualcomm list ! \n");
		}
		else
	    {
	        if(0 == strcmp(copsReadPara.oper,"CHINA MOBILE"))
	        {
	            strcpy(copsReadPara.oper, "China Mobile");
	        }
	        cfg_set("network_provider",copsReadPara.oper);
	        at_print(AT_DEBUG,"operator brand not found in the Qualcomm list ! \n");
	    }
	}
	else
    {
        if(2 == copsReadPara.format && ZUFI_SUCC == normal_getnetworkprovidername(sMCC, sMNC,netProviderName,sizeof(netProviderName)))
        {
            cfg_set(NV_NETWORK_PROVIDER,netProviderName);
        }
        else
        {
            if(0 == strcmp(copsReadPara.oper,"CHINA MOBILE"))
            {
                strncpy(copsReadPara.oper, "China Mobile",sizeof(copsReadPara.oper)-1);
            }
            cfg_set(NV_NETWORK_PROVIDER,copsReadPara.oper);    
            at_print(AT_DEBUG,"operator brand not found in the Qualcomm list ! \n");
        }
    }
    at_print(AT_DEBUG,"normal_recvcopsreadrsp send to mmi MSG_CMD_GET_NET_PROVIDER\n");
    ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MMI,MSG_CMD_GET_NET_PROVIDER, 0, NULL,0);   
}


int normal_getautoapn_callback(void *fvarg,int line,char **zresult,char **lname)
{
    T_zAt_APN_PROFILE para = {0};
    if(1 > line)
    {
        at_print(AT_DEBUG,"zte_sms:record no data.\n");
        cfg_get_item(NV_PDP_TYPE,para.pdp_type,sizeof(para.pdp_type));
        return -1;
    }
    if(zresult[0] != NULL)
    {
        strncpy(para.profile_name, zresult[0], ZAT_PROFILE_MEMBER_LEN - 1);
    }
    if(zresult[1] != NULL)
    {
        strncpy(para.apn_name, zresult[1], ZAT_PROFILE_MEMBER_LEN - 1);
        at_print(AT_DEBUG,"atBase_GetAutoApn_Callback: apn_name != NULL!\n");
    }
    if(zresult[2] != NULL)
    {
        strncpy(para.dial_num, zresult[2], ZAT_PROFILE_MEMBER_LEN - 1);
    }
    if(zresult[3] != NULL)
    {
        strncpy(para.ppp_auth_mode, zresult[3], ZAT_PROFILE_MEMBER_LEN - 1);
    }
    if(zresult[4] != NULL)
    {
        strncpy(para.ppp_username, zresult[4], ZAT_PROFILE_MEMBER_LEN - 1);
        at_print(AT_DEBUG,"atBase_GetAutoApn_Callback: para.ppp_username != NULL!\n");
    }
    if(zresult[5] != NULL)
    {
        strncpy(para.ppp_passwd, zresult[5], ZAT_PROFILE_MEMBER_LEN - 1);
    }
    if(zresult[6] != NULL)
    {
        strncpy(para.pdp_type, zresult[6], ZAT_PROFILE_MEMBER_LEN - 1);
    }    
    memcpy(fvarg,&para,sizeof(para));
    
    return 0;
}

void normal_get_cardtype(char *ccmnc)
{
	if (strcmp(ccmnc, "46000") == 0 || 
        strcmp(ccmnc, "46002") == 0 || 
        strcmp(ccmnc, "46007") == 0 ||
        strcmp(ccmnc, "46008") == 0)
	{
		cfg_set(NV_SIMCARD_TYPE, "1");
	}
	else if (strcmp(ccmnc, "46001") == 0 || 
        strcmp(ccmnc, "46006") == 0)
	{
		cfg_set(NV_SIMCARD_TYPE, "2");
	}
	else if (strcmp(ccmnc, "46003") == 0 || 
        strcmp(ccmnc, "46005") == 0 ||
        strcmp(ccmnc, "46011") == 0 )
	{
		cfg_set(NV_SIMCARD_TYPE, "3");
	}
	else
	{
		cfg_set(NV_SIMCARD_TYPE, "4");

	}

}

/*��ȡauto apn*/
long normal_getautoapn(char *CCMNC)
{
    char sql[128] = {0};
    char apn_auto_config[256];
    T_zAt_APN_PROFILE apnpara = {0};
    DbResult result = DB_OK;
    char apnMode[50] = {0};
    cfg_get_item(NV_APN_MODE,apnMode,sizeof(apnMode));
    
    snprintf(sql,sizeof(sql),"select ConfigFileName,APN,LoginNumber,AuthType,UserName,Password,PDPType from apn_list where CCMNC='%s' limit 1",CCMNC);
    result = normal_execdbsql(ZAPN_DB_PATH,sql, normal_getautoapn_callback, &apnpara);
	
    at_print(AT_DEBUG,"atBase_GetAutoApn CCMNC:%s!\n",CCMNC);
    if(DB_OK != result)
    {
        return -1;
    }
	if(strlen(apnpara.profile_name) == 0)
	{
		at_print(AT_ERR,"normal_getautoapn no item CCMNC:%s!\n",CCMNC);
		return -1;
	}
    if(strlen(apnpara.pdp_type) == 0)
    {
        strncpy(apnpara.pdp_type, "IP", ZAT_PROFILE_MEMBER_LEN - 1);
    }
    
    if(0 == strcmp("auto", apnMode))
    {
        cfg_set(NV_PDP_TYPE, apnpara.pdp_type);
        if(strcmp("IPv6", apnpara.pdp_type) == 0 || strcmp(apnpara.pdp_type, "IPv4v6") == 0)
        {
            cfg_set(NV_IPV6_WAN_APN, apnpara.apn_name);
    	    cfg_set(NV_IPV6_PDP_TYPE, apnpara.pdp_type);
    	    cfg_set(NV_WAN_APN, apnpara.apn_name);
    	    cfg_set(NV_IPV6_PPP_AUTH_MODE, apnpara.ppp_auth_mode);
			cfg_set(NV_IPV6_PPP_USERNAME,apnpara.ppp_username);
			cfg_set(NV_IPV6_PPP_PASSWD,apnpara.ppp_passwd);
			ipv6apn_encrypt_code();
        }
        else
        {
            cfg_set(NV_WAN_APN, apnpara.apn_name);
	        cfg_set(NV_PPP_AUTH_MODE, apnpara.ppp_auth_mode);
			cfg_set(NV_PPP_USERNAME,apnpara.ppp_username);
			cfg_set(NV_PPP_PASSWD,apnpara.ppp_passwd);
			apn_encrypt_code();
        }	
        cfg_set(NV_M_PROFILE_NAME, apnpara.profile_name);
		if(g_modem_model)
		{
			cfg_set("default_apn", apnpara.apn_name);
			MSG_BUF *buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_APN_SET_REQ,0,NULL);
			rcv_clt_req_msg_proc(buf);
			free(buf);
		}
    }
    snprintf(apn_auto_config, sizeof(apn_auto_config),"%s($)%s($)auto($)%s($)%s($)%s($)%s($)%s($)auto($)($)auto($)($)", 
		apnpara.profile_name, apnpara.apn_name, apnpara.dial_num, apnpara.ppp_auth_mode, apnpara.ppp_username, 
		apnpara.ppp_passwd, apnpara.pdp_type);	
    at_print(AT_DEBUG,"atBase_GetAutoApn apn_auto_config = %s!\n",apn_auto_config);
    cfg_set(NV_APN_AUTO_CONFIG, apn_auto_config);
#ifdef MULTI_CPU
	sync_default_apn(NULL);
#endif
    return 0;
}

void normal_recvzrapok()
{
    char modemState[50] = {0};
    char pinProcess[50] = {0};
    char pinManage[50] = {0};
    cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
    cfg_get_item(NV_PIN_PUK_PROCESS,pinProcess,sizeof(pinProcess));
    cfg_get_item(NV_PIN_MANAGE_PROCESS,pinManage,sizeof(pinManage));
    
    if(strcmp(pinProcess,"begin") == 0
	    &&0 == strcmp("modem_init_complete",modemState))
    {
        cfg_set(NV_PIN_PUK_PROCESS, "end");
        printf("pin_puk_process = end --- modem_init_complete"); 
    }
    /*web pin��puk����֤���������󣬷�ZAT_SYSCONFIGREAD_CMD*/
    else if(strcmp(pinProcess,"begin") == 0)
    {
        cfg_set(NV_PIN_PUK_PROCESS, "end");
        printf("pin_puk_process = end"); 
    }
		
    /*web pin�������������Zrap������һ��AT����AT����Դ��ֵΪATMIAN*/
    if(strcmp(pinManage,"begin") == 0)
    {
        printf("pin_manage_process = end");        
        cfg_set(NV_PIN_MANAGE_PROCESS, "end");
    }
}

MSG_BUF *normal_getmsg(unsigned short src_id,unsigned short dst_id,unsigned short Msg_cmd,unsigned short us_DataLen,unsigned char *pData)
{
    MSG_BUF *buf=malloc(sizeof(MSG_BUF));
    if(buf == NULL){
		softap_assert("");
		return NULL;
	}
    memset(buf, 0, sizeof(MSG_BUF));
    buf->ulMagic = MSG_MAGIC_WORD;
    buf->lMsgType = MSG_TYPE_DEFAULT;
    buf->src_id = src_id;
    buf->dst_id = dst_id;
    buf->usMsgCmd = Msg_cmd;
    buf->usDataLen = us_DataLen;
    if(us_DataLen > 0)
    {
        memcpy(buf->aucDataBuf, pData, us_DataLen);
    }
    return buf;
}

char* normal_zschplmnset(int mode)
{
	char *at_next=NULL;
	at_next=malloc(20);
 	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
 	memset(at_next,0,20);
	snprintf(at_next,20,"AT+ZSCHPLMN=%d\r\n",mode);
	return at_next;

}

int zupci_auto_act(char *at_paras ,int is_query_report)
{
    at_print(AT_ERR,"zupci=%s\n",at_paras);
	MSG_BUF *buf=NULL;
	buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_ZUTR_REQ,strlen(at_paras),(unsigned char *)at_paras);
	rcv_clt_req_msg_proc(buf);
	free(buf);
    return AT_END;
}

char* start_zutr_cmd(void *msg,struct at_context *context)
{
	int cmd_type=0;
	int cmd_num=0;
	int cmd_qual=0;
	MSG_BUF *buf=NULL;
	char *at_paras=malloc(strlen((char *)((MSG_BUF*)msg)->aucDataBuf)+1);
	if(at_paras == NULL)
		return NULL;
	memset(at_paras,0,strlen((char *)((MSG_BUF*)msg)->aucDataBuf)+1);
	strcpy(at_paras,(char *)((MSG_BUF*)msg)->aucDataBuf);
	
	void *p[3] = {&cmd_type,&cmd_num,&cmd_qual};
	at_print(AT_DEBUG,"start_zutr_cmd at_paras=%s!\n",at_paras);
	parse_param2("%d,%d,%d", at_paras, p);
	at_print(AT_ERR,"start_zutr_cmd %d,%d,%d!\n",cmd_type,cmd_num,cmd_qual);
	free(at_paras);

	if(cmd_type == 33 || cmd_type == 34 || cmd_type == 35 || cmd_type == 36 || cmd_type == 37)
	{
		char *at_next=NULL;
		at_next=malloc(64);
		if(at_next == NULL){
			softap_assert("");
			return NULL;
		}
		memset(at_next,0,64);
		if(cmd_type == 33)
			snprintf(at_next,64,"AT+ZUTR=%d,%d,%d,0\r\n",cmd_type,cmd_qual,cmd_num);
		else
			snprintf(at_next,64,"AT+ZUTR=%d,%d,%d,32\r\n",cmd_type,cmd_qual,cmd_num);
		return at_next;
	}
		
	return NULL;
}

int zmsri_auto_act(char *at_paras ,int is_query_report)
{
    at_print(AT_DEBUG,"modem init start!\n");
	MSG_BUF *buf=NULL;
	
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        if( 0 == strcmp(simSelect, "ESIM2_only") || 
            0 == strcmp(simSelect, "ESIM1_only") || 
           (0 == strcmp(simSelect, "RSIM_only") && (0 == strcmp(prevSimSelect, "ESIM2_only") ||
                                                    0 == strcmp(prevSimSelect, "ESIM1_only"))))
        {
                // �v�� cfun=5
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_OFF_MSG,0,NULL);
                rcv_clt_req_msg_proc(buf);
                free(buf);
        }
        else if(0 == strcmp(simSelect, "SIM_auto"))
        {
        #if 0
                //kernel driver default 0,0 do not need set again
                if(0 == strcmp(simPreMode, "RSIM_ESIM2_pre"))
                {// real sim preference init use real sim
                        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "0");
                        at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");
                }
                else if(0 == strcmp(simPreMode, "RSIM_ESIM1_pre"))
                {// real sim preference init use real sim
                        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "0");
                        at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");
                }
                else if(0 == strcmp(simPreMode, "ESIM2_RSIM_pre"))
        #else
                if(0 == strcmp(simPreMode, "ESIM2_RSIM_pre"))
        #endif
                {//  esim2 preference init use esim2
                        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "1");
                        //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

                        //ext sim exist need cfun=0     so sim_switch_ctrl not 0,0 need cfun=0
                        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
                        rcv_clt_req_msg_proc(buf);
                        free(buf);

                        //need cause 0,0->1,0
                        sleep(2);
                }
                else if(0 == strcmp(simPreMode, "ESIM1_RSIM_pre"))
                {//  esim1 preference init use esim1
                        at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "1");
                        //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "1");

                        //ext sim exist need cfun=0     so sim_switch_ctrl not 0,0 need cfun=0
                        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
                        rcv_clt_req_msg_proc(buf);
                        free(buf);

                        //need
                        sleep(2);
                }

                //at+cpin?
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_FIRST_CPIN_MSG,0,NULL);
                rcv_clt_req_msg_proc(buf);
                free(buf);
        }
        else
        {
                at_print(AT_DEBUG,"salvikie real sim use origin init\n");

                //int guodian = 0;
                normal_simcardcfgreset();
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCHNELSET_MSG,0,NULL);
                rcv_clt_req_msg_proc(buf);
                free(buf);
                
                buf = normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_IMEI_REQ,0,NULL);//modem�1�7�1�7�0�3�1�7�1�7�1�7�1�7�0�7�8�2�1�7�0�9imei�1�7�1�7
                rcv_clt_req_msg_proc(buf);
                free(buf);
                buf = NULL;
            //#ifdef GUODIAN
            //	guodian = 1;
            //#endif
                if(g_customer_type)
                {
                        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_CVMOD_MSG,0,NULL);
                        rcv_clt_req_msg_proc(buf);
                        free(buf);
                }
                if(g_modem_model || g_customer_type)
                {
                        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
                        rcv_clt_req_msg_proc(buf);
                        free(buf);
                }
                if(!g_modem_model)
                {
                        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_NITZ_REQ,2,"1");
                        rcv_clt_req_msg_proc(buf);
                        free(buf);
                }
        }

#else
	at_print(AT_DEBUG,"salvikie not support esim switch\n");

    //int guodian = 0;
	normal_simcardcfgreset();
	buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCHNELSET_MSG,0,NULL);
	rcv_clt_req_msg_proc(buf);
	free(buf);
	
	buf = normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_IMEI_REQ,0,NULL);//modem�1�7�1�7�0�3�1�7�1�7�1�7�1�7�0�7�8�2�1�7�0�9imei�1�7�1�7
	rcv_clt_req_msg_proc(buf);
	free(buf);
	buf = NULL;
//#ifdef GUODIAN
//	guodian = 1;
//#endif

	if(g_customer_type)
	{
		buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_CVMOD_MSG,0,NULL);
		rcv_clt_req_msg_proc(buf);
		free(buf);
	}
	if(g_modem_model || g_customer_type)
	{
		buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
		rcv_clt_req_msg_proc(buf);
		free(buf);
	}
	if(!g_modem_model)
	{
		buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_NITZ_REQ,2,"1");
		rcv_clt_req_msg_proc(buf);
		free(buf);
	}
#endif
    return AT_END;
}

int zrefreshind_auto_act(char *at_paras ,int is_query_report)
{
	at_print(AT_ERR,"zrefreshind start!\n");
	if(g_modem_model)
	{
		char needrestart[50]  = {0};
		cfg_get_item("need_restart_when_sim_insert",needrestart,sizeof(needrestart));
		if(strcmp("yes", needrestart) == 0)//��������
		{
			at_print(AT_DEBUG,"zrefreshind_auto_act RESTART\n");
			ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MAIN_CTRL, MSG_CMD_RESTART_REQUEST, 0, NULL,0);
		}
		else//������������������һ�鿪�����̣�TODO
		{
			MSG_BUF *buf=NULL;
			T_zAt_ZuslotRes zuslotPara = {0};//���ðο�����
			if(g_support_sms)	
			ipc_send_message2(MODULE_ID_AT_CTL,MODULE_ID_SMS,MSG_CMD_ZUSLOT_IND,sizeof(zuslotPara),(unsigned char *)&zuslotPara,0);
			if(g_support_pb)	
			ipc_send_message2(MODULE_ID_AT_CTL,MODULE_ID_PB,MSG_CMD_ZUSLOT_IND,sizeof(zuslotPara),(unsigned char *)&zuslotPara,0);
			buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
			rcv_clt_req_msg_proc(buf);
			free(buf);
		}
	}
	return AT_END;
}

int zuslot_auto_act(char *at_paras ,int is_query_report)
{
    at_print(AT_DEBUG,"zuslot:%s start!\n",at_paras);
	if(g_modem_model)
	{
		MSG_BUF *buf=NULL;
		buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZUSLOT_MSG,strlen(at_paras),(unsigned char *)at_paras);
		rcv_clt_req_msg_proc(buf);
		free(buf);
	}
	else
	{
		cfg_set(NV_AUTO_RECONNECT,"");
	}
    return AT_END;
}

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
char* start_start_first_cpin(void *msg,struct at_context *context)
{
    return normal_getcpinread();
}

char* start_start_sim_hotswap_cfun_off(void *msg,struct at_context *context)
{
    return normal_getcfunset(ZAT_POWEROFF);
}

char* start_start_sim_power_off(void *msg,struct at_context *context)
{
    return normal_getcfunset(ZAT_SIMOFF);
}

char* start_start_zcardswitch(void *msg,struct at_context *context)
{
    char *at_next=NULL;
	
	at_next=malloc(20);
	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	snprintf(at_next,20,"AT+ZCARDSWITCH=%s\r\n",((MSG_BUF*)msg)->aucDataBuf); 
	return at_next;
}
#endif


char* start_start_zmsri(void *msg,struct at_context *context)
{
	cfg_set("ppp_status", "ppp_disconnected");
    //psinfo.zrap_state = ZRAP_READ_FIR_CMD;
    //return normal_getzrapread();
    return normal_getcfunset(ZAT_POWERON);
}

//��ѯ�ź�ǿ��at+csq
char* start_query_csq(void *msg,struct at_context *context)
{
    return normal_getcsq();
}


char* start_verifypin_cmd(void *msg,struct at_context *context)
{
    char modemState[50] = {0};
    if (msg == NULL)
    {
        return NULL;
    }
    cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
    at_print(AT_DEBUG,"VerifyPin %s.modem_main_state is %s.\n",((MSG_BUF*)msg)->aucDataBuf,modemState);
    if(0 == strcmp("modem_waitpin",modemState))//��Ҫpin��
    {
        if(strcmp((char *)((MSG_BUF*)msg)->aucDataBuf, "") != 0)
        {
            return normal_getcpinset((char *)((MSG_BUF*)msg)->aucDataBuf);
        }
    }
    return NULL;
}

char* start_verifypuk_cmd(void *msg,struct at_context *context)
{
    T_zAt_CpinPukSet tCpinSet = {0};
    char modemState[50]   = {0};
    char pinNumber[50] = {0};
	
    if (msg == ZUFI_NULL)
    {
		return NULL;
    }
    cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
    cfg_get_item(NV_PINNUMBER,pinNumber,sizeof(pinNumber));
	at_print(AT_DEBUG,"VerifyPuk modem_main_state is %s.\n", modemState);
 	if(0 == strcmp("modem_waitpuk",modemState) || 0 == strcmp("0",pinNumber)) //��Ҫpuk��
	{
		memcpy(&tCpinSet,((MSG_BUF*)msg)->aucDataBuf, sizeof(T_zAt_CpinPukSet));
		
		at_print(AT_DEBUG,"VerifyPuk puk=%s,newpin=%s\n", tCpinSet.pin, tCpinSet.newpin);
		if (0 != strcmp(tCpinSet.pin,"") && 0 != strcmp(tCpinSet.newpin,""))
		{        
            return normal_getcpukset(tCpinSet.pin, tCpinSet.newpin);
        }
    }
    return NULL;
}

char* start_netselect_cmd(void *msg,struct at_context *context)
{
    char *at_next = NULL;
    char netMode[50] = {0};
    cfg_get_item(NV_NET_SELECT_MODE,netMode,sizeof(netMode));
    if(0 == strcmp("manual_select", netMode))//�ֶ�����
    {
        at_print(AT_DEBUG,"atWeb_NetSelect manual_select!\n");
        cfg_set("manual_search_network_status", "searching");
        at_next=normal_getcopstest();
        normal_copstest_timerout(msg);
    }
    else if(0 == strcmp("auto_select", netMode))
    {
        at_print(AT_DEBUG,"atWeb_NetSelect auto_select!\n");
        at_next=normal_getcfunset(ZAT_AIRMODE);
    }
	else
	{
        at_print(AT_DEBUG,"atWeb_NetSelect ???!\n");
        at_next=normal_getcfunset(ZAT_AIRMODE);
	}
    return at_next;
}

char *start_pinmanage_cmd(void *msg,struct at_context *context)
{
    char *at_next=NULL;
    char pinMode[10] = {0};
    T_zAt_PinManage *ptPara = ZUFI_NULL;
    if(msg == NULL)
    {
        return NULL;
    }
    ptPara = (T_zAt_PinManage*)(((MSG_BUF*)msg)->aucDataBuf);
	at_print(AT_DEBUG,"start_pinmanage_webui action=%d,pincode=%s,newpincode=%s!\n",ptPara->action,ptPara->oldPin,ptPara->newPin);
    if(ZAT_PIN_MANAGE_ENABLE == ptPara->action || ZAT_PIN_MANAGE_DISABLE == ptPara->action)
    {
        at_next=normal_getclckset(3,"SC",ptPara->action,ptPara->oldPin);
        psinfo.clck_state=CLCK_SET_CMD;
        snprintf(pinMode,sizeof(pinMode), "%d",ptPara->action);
        cfg_set(NV_CLCK_SET_MODE,pinMode);
    }
    else if (ZAT_PIN_MANAGE_MODIFY == ptPara->action)
    {
        at_next=normal_getcpwdset("SC",ptPara->oldPin,ptPara->newPin);
    }
    return at_next;
}

char* start_setnetwork_cmd(void *msg,struct at_context *context)
{
    char *at_next=NULL;
    char subType[10] = {0};
    T_zAt_CopsSet copsSetPara = {0};
    char netType[30] = {0};
    cfg_get_item("current_subrat_tmp",subType,sizeof(subType));
    cfg_get_item(NV_STR_NUM_RPLMN_TMP,copsSetPara.oper,sizeof(copsSetPara.oper));
    cfg_get_item(NV_CURRENT_RAT_TMP,netType,sizeof(netType));
    copsSetPara.nettype=atoi(netType);
    if(0 == strcmp(subType, ""))
    {
        copsSetPara.subtype = -1;;
    }
    else
    {
        copsSetPara.subtype = atoi(subType);
    }
    at_print(AT_DEBUG, "atWeb_NetManualselect: %s,--%ld--,%ld\n",copsSetPara.oper,copsSetPara.nettype,copsSetPara.subtype);
    if(-1 == copsSetPara.subtype)
    {
        at_next=normal_getcopsset(2,copsSetPara.oper,copsSetPara.nettype);
    }
    else
    {
        at_next=normal_getcopsset(3,copsSetPara.oper,copsSetPara.nettype,copsSetPara.subtype);			
    }	
    cfg_set(NV_NETWORK_TYPE, "No Service");
	cfg_set(NV_SUB_NETWORK_TYPE, "No Service");

    return at_next;
}

char* start_syctimeset_cmd(void *msg,struct at_context *context)
{
    struct timeval tp;

    if (0 != gettimeofday(&tp,NULL))
    {
        at_print(AT_ERR,"get time of system wrong");
        return NULL;
    }
    return normal_getsyctimeset(tp.tv_sec, tp.tv_usec);
}

char *start_ussdset_cmd(void *msg,struct at_context *context)
{
    char networktype[50]  = {0};
    char ussdstring[50]  = {0};
	cfg_get_item("network_type",networktype,sizeof(networktype));
    if((strcmp("No Service",networktype) == 0) || (strcmp("Limited Service",networktype) == 0) || (strcmp("Searching",networktype) == 0))
    {
        cfg_set("ussd_write_flag","1");
        return NULL;
    }
    cfg_get_item("ussd_string",ussdstring,sizeof(ussdstring));
    return normal_getcusdset(ussdstring);
}

char *start_ussdcancel_cmd(void *msg,struct at_context *context)
{
    char *at_next=NULL;
    char cancelflag[50]  = {0};
	cfg_get_item("ussd_cancel_flag",cancelflag,sizeof(cancelflag));
    if(strcmp(cancelflag,"yes") == 0)
    {
        at_next=normal_getcusdset_clean();
    }
    else
    {
        cfg_set("ussd_write_flag","13");
    }
    return at_next;
}

char *start_airmode_cmd(void *msg,struct at_context *context)
{
    char *at_next=NULL;
    int par = *((int *)(((MSG_BUF*)msg)->aucDataBuf));
    if(1 == par)
    {
        at_next=normal_getcfunset(ZAT_AIRMODE);
    }
    else if(0 == par)
    {
        at_next=normal_getcfunset(ZAT_POWERON);
    }
    return at_next;
}

//LTE��Ƶ,δʹ��
char *start_celllock_cmd(void *msg,struct at_context *context)
{
    return normal_getzltelcset();
}

char *start_imeireq_cmd(void *msg,struct at_context *context)
{
    return normal_getcgsn();
}

char *start_zversionreq_cmd(void *msg,struct at_context *context)
{
    return normal_getzversion();
}

char *start_cgdcontset_cmd(void *msg,struct at_context *context)
{
	char creg_stat[20] = {0};
	char cereg_stat[20] = {0};
	
	cfg_get_item("cgreg_stat", creg_stat, sizeof(creg_stat));
	cfg_get_item("cereg_stat", cereg_stat, sizeof(cereg_stat));
	at_print(AT_ERR,"cgdcontset cgreg =%s, cereg =%s!\n",creg_stat,cereg_stat);
	if(1 != atoi(creg_stat) && 5 != atoi(creg_stat) && 1 != atoi(cereg_stat) && 5 != atoi(cereg_stat))
	{//û�ѵ�����Ҫ��������APN��autoapn��webui����ʱ��Ч
		return normal_getcgdcontset(1);
	}
	return NULL;
}

//ϵͳ��Ϣ��ȡ����at^sysinfo
char *start_query_sysinfo(void *msg,struct at_context *context)
{
    return normal_getsysinfo();
}

//zuslot�����ϱ�����Ĵ���
char *start_start_zuslot(void *msg,struct at_context *context)
{
    T_zAt_ZuslotRes   zuslotPara = {0};
    char needrestart[50]  = {0};
    MSG_BUF *buf=NULL;
    char *at_paras=malloc(strlen((char *)((MSG_BUF*)msg)->aucDataBuf)+1);
	if(at_paras == NULL)
		return NULL;
    memset(at_paras,0,strlen((char *)((MSG_BUF*)msg)->aucDataBuf)+1);
    strcpy(at_paras,(char *)((MSG_BUF*)msg)->aucDataBuf);
	
	void *p[2] = {&zuslotPara.slot,&zuslotPara.slot_state};
	at_print(AT_DEBUG,"start_start_zuslot at_paras=%s!\n",at_paras);
	parse_param2("%d,%d", at_paras, p);
	at_print(AT_DEBUG,"start_start_zuslot slot=%ld,slot_state=%ld!\n",zuslotPara.slot,zuslotPara.slot_state);
    free(at_paras);
    if(zuslotPara.slot_state == 0)//�ο�����
    {	
        at_print(AT_DEBUG,"zuslot simcard plugout\n");
        cfg_set(NV_NEED_SIM_PIN,"");
	    g_SimSlotFlag = FALSE;
        normal_simcardcfgreset();
        //��sms��pbģ�鷢��Ϣ��nv���ã�������š���ϵ��ɾ��
	    if(g_modem_model)
	    {
			if(g_support_sms)	
            ipc_send_message2(MODULE_ID_AT_CTL,MODULE_ID_SMS,MSG_CMD_ZUSLOT_IND,sizeof(zuslotPara),(unsigned char *)&zuslotPara,0);
			if(g_support_pb)	
            ipc_send_message2(MODULE_ID_AT_CTL,MODULE_ID_PB,MSG_CMD_ZUSLOT_IND,sizeof(zuslotPara),(unsigned char *)&zuslotPara,0);
	    }
        //psinfo.powersave_state=POWERSAVE_CARDREMOVE_CMD;
        //return normal_getpowersave();
    }
    else if(zuslotPara.slot_state == 1)//�忨����
    {
        at_print(AT_DEBUG,"zuslot simcard plugin\n");
        cfg_get_item("need_restart_when_sim_insert",needrestart,sizeof(needrestart));
        if(strcmp("yes", needrestart) == 0)//��������
        {
            at_print(AT_DEBUG,"zuslot simcard plugin,sendmsg to MODULE_ID_MAIN_CTRL\n");
            ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MAIN_CTRL, MSG_CMD_RESTART_REQUEST, 0, NULL,0);
        }
        else//������������������һ�鿪������
        {
            g_SimSlotFlag = TRUE;
		    g_zUfi_canPdpDail = TRUE;
			g_need_smspb_init = 0;
            buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
            rcv_clt_req_msg_proc(buf);
            free(buf);
        }
    }	
    return NULL;
}

char *start_setroam_cmd(void *msg,struct at_context *context)
{
    return normal_getsysconfigset();
}

int sysconfigread_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"sysconfigread_ok_act msg_id=%x!\n",context->msg_id);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            psinfo.zrap_state = ZRAP_READ_FIR_CMD;
            *next_req = normal_getzrapread();
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
    }
    return AT_END;
}

int sysconfigread_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zrap_err_act msg_id=%x!\n",context->msg_id);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            psinfo.zrap_state = ZRAP_READ_FIR_CMD;
            *next_req = normal_getzrapread();
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
    }
    return AT_END;
}

int  zrap_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zrap_ok_act msg_id=%x!\n",context->msg_id);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        case MSG_CMD_VERIFY_PIN_REQ:
        case MSG_CMD_VERIFY_PUK_REQ:
        {
            if(psinfo.zrap_state == ZRAP_READ_FIR_CMD)//���������ߵķ�֧
            {
                *next_req = normal_getcpinread();
				if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
				}
            }
            else if(psinfo.zrap_state == ZRAP_READ_SEC_CMD)//pin��puk����֤���ߵķ�֧
            {
                char pinSet[50]={0};
                normal_recvzrapok();
                cfg_get_item(NV_PINSET_RESULT,pinSet,sizeof(pinSet));
                if(0 == strcmp(pinSet,"succ"))
                {
                    *next_req = normal_getcgsn();
					if(*next_req){
                        *next_len = strlen(*next_req);
                        return AT_CONTINUE;
					}
                }
            }
            psinfo.zrap_state=ZRAP_OTHER_CMD;
            return AT_END;
        }
        case MSG_CMD_PIN_NUM_REQ:
        {
            *next_req=malloc(10);
			if(*next_req){
                *next_len=10;
                cfg_get_item("pinnumber",*next_req, 10-1);
                return AT_END;
			}
			break; // cov 	M
        }
        case MSG_CMD_PIN_MANAGE_REQ:
        {
            normal_recvzrapok();
            return AT_END;
        }
    }
    return AT_END;
}

int  zrap_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        case MSG_CMD_PIN_MANAGE_REQ:
        case MSG_CMD_VERIFY_PIN_REQ:
        case MSG_CMD_VERIFY_PUK_REQ:
        {
            psinfo.zrap_state=ZRAP_OTHER_CMD;
            if ((0 == strcmp(at_str, "10"))    /*no sim card*/
                             ||(0 == strcmp(at_str, "13"))    /*card initialize failed*/
                             ||(0 == strcmp(at_str, "15")))    /*card error*/
            {
                if(0 == strcmp(at_str, "13"))
                {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_destroy");
                    at_print(AT_DEBUG,"modem_sim_destroy:%s\n",at_str);
                }
                else
                {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_undetected");
                    at_print(AT_DEBUG,"modem_sim_undetected:%s\n",at_str);
                }

				
				ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI, MSG_CMD_SIM_ABNORMAL_IND, 0, NULL,0);
				
                /*��ʼ��pbm��AP ��绰��׼���ɹ��������޿�ʱwebuiתȦ*/
                cfg_set("pbm_init_flag","0");
                
                //������ģʽ
                //*next_req = normal_getpowersave();
                //*next_len = strlen(*next_req);
                //psinfo.powersave_state=POWERSAVE_CARDERROR_CMD;
                /*struct timeval tp;
                if (0 != gettimeofday(&tp,NULL))
                {
                    at_print(AT_DEBUG,"get time of system wrong");
                    return AT_END;
                }
                *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);*/
				*next_req = normal_getmtnetset(mccNum);
				if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
				}
            }
            return AT_END;
        }
        case MSG_CMD_PIN_NUM_REQ:
        {
            *next_req=malloc(10);
			if(*next_req){
            *next_len=10;
            strcpy(*next_req,"0");
            return AT_END;
			}
        }
    }
    return AT_END;
}

int sysconfig_auto_act( char *at_paras ,int is_query_report)
{
    int mode = ZAT_SYSCONFIG_MODE_AUTO;
    int acqorder = ZAT_SYSCONFIG_PREF_ACQ_AUTO;
    int  modenow = -1;
    int  acqordernow = -1;
    int  roam = -1;
    int  roamnow = -1;
    int  srvdomain = -1;	
	char auto_acqorder[50] = {0};
	char strTemp[10] = {0};
	
	cfg_get_item("auto_acqorder",auto_acqorder,sizeof(auto_acqorder));

	at_print(AT_DEBUG,"sysconfig_auto_act\n");	
    sscanf(at_paras,"%d,%d,%d,%d",&modenow,&acqordernow,&roamnow,&srvdomain);

	at_print(AT_DEBUG,"sysconfig_auto_act modenow=%d, acqordernow=%d, auto_acqorder=%s\n",modenow, acqordernow, auto_acqorder);	

	//�Զ�����������������Ϊ�գ�����ǰ��ѯ����������ʽ���Զ����򱣴�����������
	if(0 == strlen(auto_acqorder) && modenow == 2)
	{		
	    snprintf(strTemp, sizeof(strTemp), "%d", acqordernow);
	    cfg_set("auto_acqorder", strTemp);
	}
	
	normal_getsysconfigsetParam(&mode, &acqorder, &roam);
	if(mode != modenow || acqorder != acqordernow || roam != roamnow)
	{
		MSG_BUF *msg_buf = NULL;
		msg_buf=normal_getmsg(MODULE_ID_AT_CTL, MODULE_ID_AT_CTL, MSG_CMD_NET_SELECT_REQ, 0, NULL);
		rcv_msg_proc(msg_buf);
		free(msg_buf);
	}

    return AT_END;
}

int zrap_auto_act( char *at_paras ,int is_query_report)
{
    char strTemp[10] = {0};
    int  pinNum1 = -1;
    int  pinNum2 = -1;
    int  pukNum1 = -1;
    int  pukNum2 = -1;
    at_print(AT_DEBUG,"zrap_auto_act\n");	
    sscanf(at_paras,"%d,%d,%d,%d",&pinNum1,&pinNum2,&pukNum1,&pukNum2);

    snprintf(strTemp, sizeof(strTemp),"%d", pinNum1);
    cfg_set(NV_PINNUMBER, strTemp);
	
    snprintf(strTemp, sizeof(strTemp),"%d", pukNum1);
    cfg_set(NV_PUKNUMBER, strTemp);
    return AT_END;
}

//cpin��ѯ����ok��������
int cpin_read_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
	#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        case ATCTL_INNER_FIRST_CPIN_MSG:
        {
			MSG_BUF *buf=NULL;

            at_print(AT_DEBUG,"ATCTL_INNER_FIRST_CPIN_MSG: cpin ready:%s simSelect %s simPreMode %s\n", at_str, simSelect, simPreMode);
        
	
			if(g_customer_type)
			{
				buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_CVMOD_MSG,0,NULL);
				rcv_clt_req_msg_proc(buf);
				free(buf);
			}
			if(g_modem_model || g_customer_type)
			{
				buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
				rcv_clt_req_msg_proc(buf);
				free(buf);
			}
			if(!g_modem_model)
			{
				buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_NITZ_REQ,2,"1");
				rcv_clt_req_msg_proc(buf);
				free(buf);
			}
            return AT_END;
		}
	#endif

        case ATCTL_INNER_ZMSRI_MSG:
        {
            char simPin[50] = {0};
            char simPuk[50] = {0};
            char autoSimpin[50] = {0};
            char autoCode[50] = {0};
            char pinNumber[50] = {0};
            cfg_get_item(NV_SIM_PIN,simPin,sizeof(simPin));
            cfg_get_item(NV_SIM_PUK,simPuk,sizeof(simPuk));
            
            /*����ҪPIN�룬��CGSN��ѯ����*/
            if(strcmp("0",simPin) == 0 && strcmp("0",simPuk) == 0 )
            {	
				*next_req = normal_getcgregset("2");
				if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
				}
            }
            /*��Ҫ��֤*/
            else if(strcmp("1",simPin)==0)
            {
            	ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI, MSG_CMD_SIM_ABNORMAL_IND, 0, NULL,0);

                cfg_get_item(NV_AUTO_SIMPIN,autoSimpin,sizeof(autoSimpin));
                cfg_get_item(NV_AUTO_SIMPIN_CODE,autoCode,sizeof(autoCode));
                cfg_get_item(NV_PINNUMBER,pinNumber,sizeof(pinNumber));
                /*�Զ�pin����֤*/
                if((0 == strcmp(autoSimpin,"1")) && (strcmp(autoCode,"") != 0))
                {
                    *next_req = normal_getcpinset(autoCode);
					if(*next_req){
                    *next_len = strlen(*next_req);
                    return AT_CONTINUE;
					}
                }
                /*��pin��ʣ�����Ϊ0�����Ƿ���ҪPuk���Ϊ1*/
                else if(strcmp("0",pinNumber)==0)
                {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpuk");
                    return AT_END;
                }
                return AT_END;
            }

			break; // cov M MISSING_BREAK
        }
        case MSG_CMD_PIN_STATUS_REQ:
        {
            char simPin[50] = {0};
            char simPuk[50] = {0};
            
            cfg_get_item("sim_pin",simPin,sizeof(simPin));
            cfg_get_item("sim_puk",simPuk,sizeof(simPuk));  
            *next_req=malloc(10);
			if(*next_req){
            *next_len=10;
            if(strcmp("0", simPin) == 0 && strcmp("0", simPuk) == 0 )
            {
                sprintf(*next_req,"%d", PIN_STATUS_NO_PIN);
            }
            else if(strcmp("1", simPin)==0)
            {
                sprintf(*next_req,"%d", PIN_STATUS_PIN1);
            }  
            else if(strcmp("1", simPuk)==0)
            {
                sprintf(*next_req,"%d", PIN_STATUS_PUK1);
            }  
            return AT_END;
			}
        }
    }
    return AT_END;
}

int cpin_read_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        case ATCTL_INNER_FIRST_CPIN_MSG:
        {
            MSG_BUF *buf=NULL;

            at_print(AT_DEBUG,"ATCTL_INNER_FIRST_CPIN_MSG: cpin error:%s simSelect %s simPreMode %s\n", at_str, simSelect, simPreMode);

            if(0 == strcmp(simSelect, "SIM_auto"))
	    {
		    if(0 == strcmp(simPreMode, "RSIM_ESIM2_pre"))
		    {// switch to esim2
			    at_print(AT_DEBUG,"ext sim not ready need switch to esim2\n");

			    at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "1");
			    //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

		#ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
			    sleep(1);
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
		#else
			    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
			    rcv_clt_req_msg_proc(buf);
			    free(buf);
		#endif
		    }
		    else if(0 == strcmp(simPreMode, "RSIM_ESIM1_pre"))
		    {// switch to esim1
			    at_print(AT_DEBUG,"ext sim not ready need switch to esim1\n");

			    at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "1");
			    //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "1");

		#ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
			    sleep(1);
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
		#else
			    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
			    rcv_clt_req_msg_proc(buf);
			    free(buf);
		#endif
		    }
		    else if(0 == strcmp(simPreMode, "ESIM2_RSIM_pre"))
		    {// switch to real sim
			    at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "0");
			    //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

		#ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
			    sleep(1);
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
		#else
			    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
			    rcv_clt_req_msg_proc(buf);
			    free(buf);
		#endif
		    }
		    else if(0 == strcmp(simPreMode, "ESIM1_RSIM_pre"))
		    {// switch to real sim
			    at_file_operate("/sys/class/leds/sim_switch_a_ctrl/brightness", "0");
			    //at_file_operate("/sys/class/leds/sim_switch_b_ctrl/brightness", "0");

		#ifdef JCV_FEATURE_SIM_HOTSWAP_SUPPORT
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "0");
			    sleep(1);
			    at_file_operate("/sys/class/leds/sim_hotswap/brightness", "1");
		#else
			    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG,0,NULL);
			    rcv_clt_req_msg_proc(buf);
			    free(buf);
		#endif
		    }
	    }

            at_print(AT_DEBUG,"ATCTL_INNER_FIRST_CPIN_MSG: next cvmod \n");

            if(g_customer_type)
	    {
		    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_CVMOD_MSG,0,NULL);
		    rcv_clt_req_msg_proc(buf);
		    free(buf);
	    }
	    if(g_modem_model || g_customer_type)
	    {
		    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
		    rcv_clt_req_msg_proc(buf);
		    free(buf);
	    }
	    if(!g_modem_model)
	    {
		    buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_NITZ_REQ,2,"1");
		    rcv_clt_req_msg_proc(buf);
		    free(buf);
	    }

            return AT_END;
        }
#endif
        case ATCTL_INNER_ZMSRI_MSG:
        {
            if ((0 == strcmp(at_str, "10"))    /*no sim card*/
                             ||(0 == strcmp(at_str, "13"))    /*card initialize failed*/
                             ||(0 == strcmp(at_str, "15")))    /*card error*/
            {
                if(0 == strcmp(at_str, "13"))
                {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_destroy");
                    at_print(AT_DEBUG,"modem_sim_destroy:%s\n",at_str);
                }
                else
                {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_undetected");
                    at_print(AT_DEBUG,"modem_sim_undetected:%s\n",at_str);					
                }

				ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI, MSG_CMD_SIM_ABNORMAL_IND, 0, NULL,0);

                /*��ʼ��pbm��AP ��绰��׼���ɹ��������޿�ʱwebuiתȦ*/
                cfg_set("pbm_init_flag","0");
                
                //������ģʽ
                //*next_req = normal_getpowersave();
                //*next_len = strlen(*next_req);
                /*struct timeval tp;
                if (0 != gettimeofday(&tp,NULL))
                {
                    at_print(AT_DEBUG,"get time of system wrong");
                    return AT_END;
                }
                *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);*/
				*next_req = normal_getmtnetset(mccNum);
				if(*next_req){
                *next_len = strlen(*next_req);
                //psinfo.powersave_state=POWERSAVE_CARDERROR_CMD;
                return AT_CONTINUE;
				}
            }
            return AT_END;
        }
        case MSG_CMD_PIN_STATUS_REQ:
        {
            *next_req=malloc(10);
			if(*next_req){
            *next_len=10;
            sprintf(*next_req,"%d", PIN_STATUS_NO_PIN);
            return AT_END;
			}
        }
    }
    return AT_END;
}


int cpin_set_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        case MSG_CMD_VERIFY_PIN_REQ:
        {
            char pinProcess[50] = {0};
            MSG_BUF *buf=NULL;
            cfg_get_item(NV_PIN_PUK_PROCESS,pinProcess,sizeof(pinProcess));
            if(strcmp(pinProcess,"begin") == 0)
            {
                cfg_set(NV_PIN_PUK_PROCESS, "end");        
            }
            cfg_set(NV_PINSET_RESULT,"succ");
            cfg_set(NV_MODEM_MAIN_STATE,"modem_ready");
            /*at_next=malloc(AT_CMD_MAX);
            //PIN�����óɹ�����AT+ZRAP?��Ϊ�˸���PIN���puk��ʣ�����
            sprintf(at_next,"AT+ZRAP?\r\n");
            *next_req = at_next;
            *next_len = strlen(at_next);
            psinfo.zrap_state = ZRAP_READ_SEC_CMD;
            return AT_CONTINUE;*/
            buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
            rcv_clt_req_msg_proc(buf);
            free(buf);
            return AT_END;
        }
        case MSG_CMD_VERIFY_PUK_REQ:
        {
            char modemState[50] = {0};
			char pinProcess[50] = {0};

			cfg_set(NV_PINSET_RESULT,"succ");
			cfg_get_item(NV_PIN_PUK_PROCESS,pinProcess,sizeof(pinProcess));
			if(strcmp(pinProcess,"begin") == 0)
		    {
		        cfg_set(NV_PIN_PUK_PROCESS, "end");
		    }
            
            cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
            if(strcmp("modem_init_complete",modemState))
            {
                cfg_set(NV_MODEM_MAIN_STATE,"modem_ready");
            }
            *next_req = normal_getclckset(2,"SC",2);
			if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
			}
			break;
        }
        case MSG_CMD_PIN_VERIFY_REQ:
        {
            *next_req=malloc(10);
			if(*next_req){
                *next_len=10;
                strcpy(*next_req,"0");
                return AT_END;
			}
			break;
        }
    }
    return AT_END;
}

int cpin_set_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        case MSG_CMD_VERIFY_PIN_REQ://PIN����֤
        {
            char autoSimpin[50] = {0};
            cfg_get_item(NV_AUTO_SIMPIN,autoSimpin,sizeof(autoSimpin));
            if(0 == strcmp(autoSimpin,"1"))
            {
                cfg_set(NV_AUTO_SIMPIN,"0");
            }
            if(0 == strcmp(at_str, "16"))
            {
                cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpin");
            }
            else if (0 == strcmp(at_str, "12"))	
            {
                /*���3��pin������Ϊwaitpuk*/
                cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpuk");
            }
            cfg_set(NV_PINSET_RESULT,"fail");
            *next_req = normal_getzrapread();
			if(*next_req){
                *next_len = strlen(*next_req);
                psinfo.zrap_state = ZRAP_READ_SEC_CMD;
                return AT_CONTINUE;
			}

			break; // cov M MISSING_BREAK
        }
        case MSG_CMD_VERIFY_PUK_REQ://PUK����֤
        {
            char modemState[50] = {0};
            if (0 == strcmp(at_str, "16"))       
            {
                cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
                if(strcmp("modem_init_complete",modemState))
        	    {
                    cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpuk");
        	    }		
            }
			else if (0 == strcmp(at_str, "13"))
            {
                cfg_set(NV_MODEM_MAIN_STATE,"modem_sim_destroy");
                at_print(AT_DEBUG,"modem_sim_destroy:%s\n",at_str);
            }
        	cfg_set(NV_PINSET_RESULT,"fail");
            *next_req = normal_getzrapread();
			if(*next_req){
                *next_len = strlen(*next_req);
                psinfo.zrap_state = ZRAP_READ_SEC_CMD;
                return AT_CONTINUE;
			}

			break; // cov M MISSING_BREAK
        }
        case MSG_CMD_PIN_VERIFY_REQ://wifiģ�鷢��������Ϣ
        {
            *next_req=malloc(10);
			if(*next_req){
                *next_len=10;
                strcpy(*next_req,"1");
                return AT_END;
			}

			break;
        }
    }
    return AT_END;
}

//����cops�ж�ǰ׺����������
int cpin_auto_act(char *at_paras ,int is_query_report)
{
    /*��ӦΪReady������ҪPIn����֤*/
    if (strcmp("READY", at_paras) == 0)
    {
        cfg_set(NV_SIM_PIN, "0");
        cfg_set(NV_SIM_PUK, "0");
		//cfg_set(NV_NEED_SIM_PIN,"");
    }
    /*�����ǰ������������ΪPIN��PIN2��������+CPIN=<pin>����У��*/
    else if(strcmp("SIM PIN",at_paras) == 0 || strcmp("SIM PIN2",at_paras) == 0)
    {
        cfg_set(NV_SIM_PIN, "1");
        cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpin");
		cfg_set(NV_NEED_SIM_PIN,"yes");
    }
    /*�����ǰ������������ΪPUK��PUK2��������+CPIN=<pin>,<newpin>���н���*/
    else if(strcmp("SIM PUK",at_paras) == 0 || strcmp("SIM PUK2",at_paras) == 0)
    {
        cfg_set(NV_SIM_PUK, "1");
        cfg_set(NV_MODEM_MAIN_STATE,"modem_waitpuk");
		cfg_set(NV_NEED_SIM_PIN,"yes");
    }

    return AT_END;
}


int  cgsn_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_GET_MAC_REQ:
        {
#if 0        
            *next_req=normal_getmacread();
            *next_len=strlen(*next_req);
            return AT_CONTINUE;
#endif			
        }
    }
    return AT_END;
}

int  cgsn_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_GET_MAC_REQ:
        {
#if 0        
            *next_req=normal_getmacread();
            *next_len=strlen(*next_req);
            return AT_CONTINUE;
#endif			
        }
    }
    return AT_END;
}

int cgsn_auto_act(char *at_paras ,int is_query_report)
{
	at_print(AT_DEBUG,"cgsn_auto_act\n");	
    cfg_set(NV_IMEI,at_paras);
    return AT_END;
}

int ziccid_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {        
		case MSG_CMD_MODEMREG_INFO_REQ:
		{
			*next_req = normal_getcimi();
			if(*next_req){
			*next_len = strlen(*next_req);
			return AT_CONTINUE;
			}
		}
    }
    return AT_END;
}

int ziccid_auto_act( char *at_paras ,int is_query_report)
{
    cfg_set(NV_ZICCID, at_paras);
    return AT_END;
}

int  crsm_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"crsm_ok_act msg_id=%d\n",context->msg_id);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            char strPlmn[70] = {0};
            char *pRes=NULL;
            char strPlmnLast[10] = {0};
            cfg_get_item("crsm_plmn", strPlmn, sizeof(strPlmn));
            pRes = strstr(strPlmn, "144,0,");//��ʾ��ȷ��ȡ
            if(0 == strlen(strPlmn) || NULL == pRes)
            {
                g_plmnLen = 5;
            }
            strncpy(strPlmnLast, &strPlmn[strlen(strPlmn)-2], 2);
            if(0 == strcmp("03", strPlmnLast))
            {
                g_plmnLen = 6;
            }
            else
            {
                g_plmnLen = 5;
            }
            at_print(AT_DEBUG,"crsm_ok_act, g_plmnLen = %d \n", g_plmnLen);
            *next_req = normal_getcimi();;
			if(*next_req){
                *next_len = strlen(*next_req);
                return  AT_CONTINUE;
			}
			break; // cov M MISSING_BREAK
        }
        case MSG_CMD_CRSM_REQ:
        {
            if(crsmrsp == NULL){
				softap_assert("");
				return AT_END;
			}
            *next_req=crsmrsp;
            *next_len=strlen(*next_req);
            return AT_END;
        }
    }
    return AT_END;
}

int  crsm_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            at_print(AT_DEBUG,"Recv CrsmPlmnlen ERR.\n");
            *next_req = normal_getcimi();
			if(*next_req){
            *next_len = strlen(*next_req);
            return  AT_CONTINUE;
			}
        }
        case MSG_CMD_CRSM_REQ:
        {
            return AT_END;
        }
    }
    return AT_END;
}


int crsm_auto_act(char *at_paras ,int is_query_report)
{  
    struct at_context *context = (struct at_context *)is_query_report;

    if(context == NULL)
        return AT_END;   
    
    at_print(AT_DEBUG,"crsm_auto_act msg_id=%x,at_paras=%s!\n",context->msg_id,at_paras);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            cfg_set("crsm_plmn",at_paras);
            return AT_END;
        }
        case MSG_CMD_CRSM_REQ:
        {
            crsmrsp=malloc(256);
            if(crsmrsp == NULL){
				softap_assert("");
				return AT_END;
			}
            memset(crsmrsp,0,256);
            sprintf(crsmrsp,at_paras);
            return AT_END;
        }
    }
    return AT_END;
}

int  cimi_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            if(normal_locknetmatch(mccNum, mncNum) == TRUE)
            {
	            /*struct timeval tp;
	            if (0 != gettimeofday(&tp,NULL))
	            {
	                at_print(AT_DEBUG,"get time of system wrong");
	                return AT_END;
	            }
	            *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);*/
				*next_req = normal_getmtnetset(mccNum);
				if(*next_req){
    	            *next_len = strlen(*next_req);
    	            return AT_CONTINUE;
				}
	        }
            else
            {
                cfg_set("modem_main_state","modem_imsi_waitnck"); 
                return  AT_END;
            }

			break; // cov M
        }
        case MSG_CMD_CIMI_REQ:
        {
            *next_req=malloc(50);
			if(*next_req){
                *next_len=50;
                cfg_get_item(NV_SIM_IMSI,*next_req,50);
                return AT_END;
			}

			break; // cov M
        }
		case MSG_CMD_MODEMREG_INFO_REQ:
		{
			*next_req = normal_CnumRead();
			if(*next_req){
                *next_len = strlen(*next_req);
    			return AT_CONTINUE;
			}

			break; // cov M
		}
    }
    return AT_END;
}

int  cimi_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            if(normal_locknetmatch(mccNum, mncNum) == TRUE)
            {
	            /*struct timeval tp;
	            if (0 != gettimeofday(&tp,NULL))
	            {
	                at_print(AT_DEBUG,"get time of system wrong");
	                return AT_END;
	            }
	            *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);*/
				*next_req = normal_getmtnetset(mccNum);
				if(*next_req){
    	            *next_len = strlen(*next_req);
    	            return AT_CONTINUE;
				}
	        }
            else
            {
                cfg_set("modem_main_state","modem_imsi_waitnck"); 
                return  AT_END;
            }

			break; // cov M MISSING_BREAK
        }
        case MSG_CMD_CIMI_REQ:
        {
            return AT_END;
        }
		case MSG_CMD_MODEMREG_INFO_REQ:
		{
			*next_req = normal_CnumRead();
			if(*next_req){
                *next_len = strlen(*next_req);
    			return AT_CONTINUE;
			}

			break; // cov M MISSING_BREAK
		}
    }
    return AT_END;
}

/*
int  zapnsave_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_INNER_PDP_INIT:
        {
            return AT_END;
        }
		//case MSG_CMD_SET_DEFAULT_APN_REQ:
        //{
        //    return AT_END;
        //}
        default:
            softap_assert("");
        
    }
    return AT_END;
}

int  zapnsave_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    softap_assert("");
    return AT_END;
}
*/
int cimi_auto_act(char *at_paras, int is_query_report)
{  
    char ccmncNum[10]         = {0};
    struct at_context *context = (struct at_context *)is_query_report;
    
    strncpy(imsi,at_paras,sizeof(imsi)-1);
	imsi[49]=0;
    strncpy(mccNum, imsi, 3);
    at_print(AT_DEBUG,"mcc is %s", mccNum);
#if (APP_OS_TYPE == APP_OS_LINUX)	
    if(6 == g_plmnLen)
	{
		strncpy(mncNum, imsi+3, 3);
	}
	else
	{
		strncpy(mncNum, imsi+3, 2);
	}
    
    snprintf(ccmncNum, 10-1, "%s%s", mccNum, mncNum);
	cfg_set(NV_SIM_IMSI, imsi);
	normal_get_cardtype(ccmncNum);
    switch(context->msg_id)
    {		
		case ATCTL_INNER_ZMSRI_MSG:
		case MSG_CMD_CIMI_REQ:
		{
			normal_getautoapn(ccmncNum);
			break;
		}
	  	default:
        {           
            break;
        }
    }
#endif
    return AT_END;
}

int sysconfig_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_NET_SELECT_REQ:
        {
            *next_req = normal_getcfunset(ZAT_POWERON);
			if(*next_req){
                *next_len = strlen(*next_req);
                cfg_set(NV_NET_SELECT_RESULT, "sucess");
                return  AT_CONTINUE;
			}

			break; // cov M MISSING_BREAK
        }

		default:
		{
		    break;
		}
    }
    return AT_END;
}

int sysconfig_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
     switch(context->msg_id)
     {
        case MSG_CMD_NET_SELECT_REQ:
        {
            *next_req = normal_getcfunset(ZAT_POWERON);
			if(*next_req){
                *next_len = strlen(*next_req);
                cfg_set(NV_NET_SELECT_RESULT, "fail");
                return  AT_CONTINUE;
			}

			break; // cov M MISSING_BREAK
        }

		default:
		{
		    break;
		}
     }
     return AT_END;
}

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
int zcardswitch_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zcardswitch_ok_act: salvikie msg_id %d\n", context->msg_id);
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZCARDSWITCH_MSG:
        {
            at_sim_switch_operate();
            return AT_END;
        }
    }
    return AT_END;
}

int zcardswitch_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zcardswitch_err_act: salvikie msg_id %d\n", context->msg_id);
	switch(context->msg_id)
    {
        case ATCTL_INNER_ZCARDSWITCH_MSG:
        {
            return AT_END;
        }
    }
    return AT_END;
}

#endif

int cfun_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
    MSG_BUF *buf=NULL;
#endif

	cfg_set("cfun_work", "none");
    switch(context->msg_id)
    {
        //����������AT+CFUN=1
        case ATCTL_INNER_ZMSRI_MSG:
        {
            char modemState[50]  = {0};
            normal_cfunOkSet();
            cfg_get_item(NV_MODEM_MAIN_STATE,modemState,sizeof(modemState));
            if(0 == strcmp("modem_syscfg_exption",modemState))
            {
                cfg_set(NV_MODEM_MAIN_STATE,"modem_destroy"); 
            }
            else
            {
                cfg_set(NV_MODEM_MAIN_STATE,"modem_init_complete");      
            }
            //*next_req = normal_getcgregset("2");
            //psinfo.zrap_state = ZRAP_READ_FIR_CMD;
            //*next_req = normal_getzrapread();
        //#ifdef GUODIAN
            if(g_customer_type == CUSTOMER_GUODIAN || g_customer_type == CUSTOMER_NANDIAN)
            {
			    return  AT_END;
            }
		//#else
		    else
            {      
    			*next_req = normal_getsysconfigread();
				if(*next_req){
                    *next_len = strlen(*next_req);
                    return  AT_CONTINUE;
				}
            }

			break; // cov M MISSING_BREAK
		//#endif
        }
        //�Զ�����
        case MSG_CMD_NET_SELECT_REQ:
        {
            if(cfun_state==4)
            {
                *next_req = normal_getsysconfigset();
				if(*next_req){
                    *next_len = strlen(*next_req);
                    return  AT_CONTINUE;
				}
            }
            else if(cfun_state==1)
            {
                char mMode[50] = {0};
                cfg_get_item(NV_M_NETSELECT_MODE,mMode,sizeof(mMode));
                if(0 == strcmp("1", mMode))
                {
                    *next_req = normal_getcopsset(1,"0");
					if(*next_req){
                    *next_len = strlen(*next_req);
                    return  AT_CONTINUE;
					}
                }
                else
                {
                    return AT_END;
                }
            }
			
            return AT_END;
        }
        case MSG_CMD_AIRMODE_SET_REQ:
        {
#ifdef JCV_FEATURE_MY_RESET_FACTORY
            char strBuf[10] = {0};

            at_print(AT_DEBUG,"salvikie cfun_state %d\n", cfun_state);
            snprintf(strBuf, sizeof(strBuf), "%d", cfun_state);
            cfg_set("airmode_cfun", strBuf);
#endif
            return AT_END;
        }
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        case ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG:
        {
            //salvikie
            at_print(AT_DEBUG,"salvikie at_sim_switch_operate done continue origin init\n");

            // origin init 
            //int guodian = 0;
            normal_simcardcfgreset();
            buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCHNELSET_MSG,0,NULL);
            rcv_clt_req_msg_proc(buf);
            free(buf);
            
            buf = normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_IMEI_REQ,0,NULL);//modem�1�7�1�7�0�3�1�7�1�7�1�7�1�7�0�7�8�2�1�7�0�9imei�1�7�1�7
            rcv_clt_req_msg_proc(buf);
            free(buf);
            buf = NULL;

            //#ifdef GUODIAN
            //	guodian = 1;
            //#endif

            if(g_customer_type)
            {
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_CVMOD_MSG,0,NULL);
                rcv_clt_req_msg_proc(buf);
                free(buf);
            }
            if(g_modem_model || g_customer_type)
            {
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
                rcv_clt_req_msg_proc(buf);
                free(buf);
            }
            if(!g_modem_model)
            {
                buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_NITZ_REQ,2,"1");
                rcv_clt_req_msg_proc(buf);
                free(buf);
            }

            return AT_END;
        }
        case ATCTL_INNER_SIM_OFF_MSG:
        {
            if(0 == strcmp(simSelect, "ESIM2_only") || 0 == strcmp(simSelect, "ESIM1_only"))
            {
                // //AT+ZCARDSWITCH=3 �е� sim1
                // // ��web ����˲��� -> ֱ�Ӱl��AT
                // buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCARDSWITCH_MSG,2,"3");
                // rcv_clt_req_msg_proc(buf);
                // free(buf); 
                at_sim_switch_operate();
            }
            else if(0 == strcmp(simSelect, "RSIM_only") && strcmp(simSelect, prevSimSelect) != 0)
            {
                // //AT+ZCARDSWITCH=3 �е� sim1
                // // ��web ����˲��� -> ֱ�Ӱl��AT
                // buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZCARDSWITCH_MSG,2,"0");
                // rcv_clt_req_msg_proc(buf);
                // free(buf); 
                at_sim_switch_operate();
            }
            
            return AT_END;
        }
#endif

    }
    return AT_END;
}

int cfun_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
	cfg_set("cfun_work", "none");
    switch(context->msg_id)
    {
        //����������AT+CFUN=1
        case ATCTL_INNER_ZMSRI_MSG:
        {
            cfg_set(NV_MODEM_MAIN_STATE,"modem_destroy");
            return  AT_END;
        }
        //�Զ�����
        case MSG_CMD_NET_SELECT_REQ:
        {
            if(cfun_state==4)
            {
                *next_req = normal_getsysconfigset();
				if(*next_req){
                    *next_len = strlen(*next_req);
                    return  AT_CONTINUE;
				}
            }
            else if(cfun_state==1)
            {
                char mMode[50] = {0};
                cfg_get_item(NV_M_NETSELECT_MODE,mMode,sizeof(mMode));
                if(0 == strcmp("1", mMode))
                {
                    *next_req = normal_getcopsset(1,"0");
					if(*next_req){
                    *next_len = strlen(*next_req);
                    return  AT_CONTINUE;
					}
                }
                else
                {
                    return AT_END;
                }
            }
            return AT_END;
        }
        case MSG_CMD_AIRMODE_SET_REQ:
        {
            return AT_END;
        }
  
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
	case ATCTL_INNER_SIM_HOTSWAP_CFUN_OFF_MSG:
    case ATCTL_INNER_SIM_OFF_MSG:
	{
	    return AT_END;
	}
#endif

    }
    return AT_END;
}

int cgreg_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    *next_req = normal_getceregset("2");
	if(*next_req)
    *next_len = strlen(*next_req);
    return  AT_CONTINUE;
}

int cgreg_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    *next_req = normal_getceregset("2");
	if(*next_req)
    *next_len = strlen(*next_req);
    return  AT_CONTINUE;
}

int cereg_set_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{

    char *at_next = NULL;
    
    //at_next=normal_getcgmr();
    at_next=normal_getcrsmset("176,28589,0,0,4");

    *next_req = at_next;
    //*next_req = normal_getzsqrset("0");
	if(*next_req)
    *next_len = strlen(*next_req);
    return  AT_CONTINUE;
}

int cereg_set_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    char *at_next = NULL;
    
    //at_next=normal_getcgmr();
    at_next=normal_getcrsmset("176,28589,0,0,4");
    
    *next_req = at_next;
    //*next_req = normal_getzsqrset("0");
	if(*next_req)
    *next_len = strlen(*next_req);
    return  AT_CONTINUE;
}

int zsqr_auto_act(char *at_paras ,int is_query_report)
{
    char strBuf[10] = {0};
    T_zAt_CsqUnSolicite zsqrResPara = {0};
    sscanf(at_paras, "%ld,%ld,%ld", &zsqrResPara.rssi, &zsqrResPara.ber, &zsqrResPara.act);

#if 1    // kw 3 SV.TAINTED.CALL.BINOP
    if(zsqrResPara.rssi < 0 || zsqrResPara.rssi > LONG_MAX-1)
    {
        zsqrResPara.rssi = 0;
    }

    if(zsqrResPara.ber < 0 || zsqrResPara.ber > LONG_MAX-1)
    {
        zsqrResPara.ber = 0;
    }

    if(zsqrResPara.act < 0 || zsqrResPara.act > LONG_MAX-1)
    {
        zsqrResPara.act = 0;
    }    
#endif 

    snprintf(strBuf, sizeof(strBuf),"%ld", zsqrResPara.rssi);
    cfg_set(NV_CSQ, strBuf);
    if(zsqrResPara.act == 3) 
	{
		normal_calcsignalbar(ZAT_SIGNALBAR_TYPE_GSM);
		normal_calcsignalstrength(ZAT_SIGNALBAR_TYPE_GSM, zsqrResPara.rssi);
	}
	else if(zsqrResPara.act == 5)
	{
		normal_calcsignalbar(ZAT_SIGNALBAR_TYPE_WCDMA);
		normal_calcsignalstrength(ZAT_SIGNALBAR_TYPE_WCDMA, zsqrResPara.rssi);
	}
	else if(zsqrResPara.act == 15)
	{
		normal_calcsignalbar(ZAT_SIGNALBAR_TYPE_TDS);
		normal_calcsignalstrength(ZAT_SIGNALBAR_TYPE_TDS, zsqrResPara.rssi);
	}
	else if(zsqrResPara.act == 17)
	{		
		normal_calcsignalbar(ZAT_SIGNALBAR_TYPE_LTE);
		normal_calcsignalstrength(ZAT_SIGNALBAR_TYPE_LTE, zsqrResPara.rssi);
	}
    else if(zsqrResPara.act == 0)
    {
        normal_calcsignalbar(ZAT_SIGNALBAR_TYPE_NONE);
    }
    return AT_END;
}

int cops_set_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_QUERY_SYSINFO_REQ:
        {
            //*next_req = normal_getcgmr();
			*next_req = normal_getcopsread();
			if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
			}
			break; // cov M
        }
        case MSG_CMD_NET_SELECT_REQ:
        {
            cfg_set(NV_M_NETSELECT_MODE, "0");
            return AT_END;
        }
        case MSG_CMD_NETWORK_SET_REQ:
        {
            char    rplmnTmp[50]  = {0};
            char    ratTmp[50]  = {0};
            cfg_get_item(NV_STR_NUM_RPLMN_TMP,rplmnTmp,sizeof(rplmnTmp));
            cfg_get_item(NV_CURRENT_RAT_TMP,ratTmp,sizeof(ratTmp));
            cfg_set(NV_M_NETSELECT_RESULT, "manual_success");
            cfg_set(NV_STR_NUM_RPLMN,rplmnTmp);
            cfg_set(NV_CURRENT_RAT,ratTmp);
            cfg_set(NV_M_NETSELECT_MODE, "1");
            *next_req = normal_getsysinfo();
			if(*next_req){
                *next_len = strlen(*next_req);
                return AT_CONTINUE;
			}

			break; // cov M
        }
    }
    return AT_END;
}

int cops_set_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_QUERY_SYSINFO_REQ:
        {
            //*next_req = normal_getcgmr();
			*next_req = normal_getcopsread();
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
        case MSG_CMD_NET_SELECT_REQ:
        {
            return AT_END;
        }
        case MSG_CMD_NETWORK_SET_REQ:
        {
            cfg_set(NV_M_NETSELECT_RESULT, "manual_fail");
            *next_req = normal_getsysinfo();
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
    }
    return AT_END;
}

int cops_read_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"cops_read_ok_act %x\n",context->msg_id);
    switch(context->msg_id)
    {
        case MSG_CMD_QUERY_SYSINFO_REQ:
        case MSG_CMD_NETWORK_SET_REQ:
        {
            if(g_zUfi_firstCsq)
            {
                g_zUfi_firstCsq = FALSE;
                *next_req = normal_getcsq();
				if(*next_req){
                *next_len = strlen(*next_req);
                return  AT_CONTINUE;
				}
            }
            return AT_END;
        }
	}
    return AT_END;
}

int cops_read_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"cops_read_ok_act %x\n",context->msg_id);
    switch(context->msg_id)
    {
        case MSG_CMD_QUERY_SYSINFO_REQ:
        {
            if(g_zUfi_firstCsq)
            {
                g_zUfi_firstCsq = FALSE;
                *next_req = normal_getcsq();
				if(*next_req){
                *next_len = strlen(*next_req);
                return  AT_CONTINUE;
				}
            }
            return AT_END;
        }
		case MSG_CMD_MODEMREG_INFO_REQ:
		{
			*next_req = normal_getcsq();
			if(*next_req){
            *next_len = strlen(*next_req);
            return  AT_CONTINUE;
			}
		}
    }
    return AT_END;
}

int cops_test_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    cfg_set("manual_search_network_status", "finished");
    return AT_END;
}

int cops_test_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    cfg_set(NV_NET_NUM, "0");
    cfg_set(NV_M_NETSELECT_STATUS, "manual_search_fail");
    return AT_END;
}

int cops_auto_act(char *at_paras ,int is_query_report)
{
    //at+cops=?����ֵ
    //zdm �����ã�Ҳ���Ӳ���
    #if (APP_OS_TYPE == APP_OS_TOS)
		softap_assert("");
	#else
    
    struct at_context *context = (struct at_context *)is_query_report;

    if(context == NULL)
        return AT_END;   

    at_print(AT_DEBUG,"cops_auto_act prefix=%s!\n",context->at_cmd_prefix);
    if(strstr(context->at_cmd_prefix,"=?"))
    {
#if (APP_OS_TYPE == APP_OS_LINUX)	
		DeleteSoftTimer(CopstestTimerID);
#elif (APP_OS_TYPE == APP_OS_TOS)	
		zOss_StopTimer(CopstestTimerID);
#endif
        at_print(AT_DEBUG,"come in222!\n");
        normal_recvcopstestrsp(at_paras);
    }
    else if(strstr(context->at_cmd_prefix,"?"))
    {
        normal_recvcopsreadrsp(at_paras);
    }
	#endif
    return AT_END;
}

int  zversion_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zversion_ok_act\n");
/*
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            struct timeval tp;
            if (0 != gettimeofday(&tp,NULL))
            {
                at_print(AT_DEBUG,"get time of system wrong");
                return AT_END;
            }
            *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
	}
*/	
    return AT_END;
}

int  zversion_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"zversion_err_act\n");
/*
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            struct timeval tp;
            if (0 != gettimeofday(&tp,NULL))
            {
                at_print(AT_DEBUG,"get time of system wrong");
                return AT_END;
            }
            *next_req = normal_getsyctimeset(tp.tv_sec, tp.tv_usec);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
    }
*/	
    return AT_END;
}

int zversion_auto_act(char *at_paras ,int is_query_report)
{
    cfg_set(NV_CR_INNER_VERSION,at_paras);
    return AT_END;
}

int  syctime_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            *next_req = normal_getclckset(2,"SC",2);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
        case MSG_CMD_SYCTIME_SET_REQ:
        {
            return AT_END;
        }
    }
    return AT_END;
}

int  syctime_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            *next_req = normal_getclckset(2,"SC",2);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
        case MSG_CMD_SYCTIME_SET_REQ:
        {
            return AT_END;
        }
    }
    return AT_END;
}

int  zsetmtnet_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            *next_req = normal_getclckset(2,"SC",2);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
        default:
        {
            return AT_END;
        }
    }
    return AT_END;
}

int  zsetmtnet_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case ATCTL_INNER_ZMSRI_MSG:
        {
            *next_req = normal_getclckset(2,"SC",2);
			if(*next_req){
            *next_len = strlen(*next_req);
            return AT_CONTINUE;
			}
        }
        default:
        {
            return AT_END;
        }
    }
    return AT_END;
}

int  mac_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"mac_ok_act\n");
    *next_req=normal_getmac2read();
	if(*next_req)
    *next_len=strlen(*next_req);
    return AT_CONTINUE;
}

int  mac_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_ERR,"mac_err_act\n");
    if(mactozssid != NULL)
        free(mactozssid);
    mactozssid=NULL;
    return AT_END;
}

int  mac2_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_DEBUG,"mac2_ok_act\n");
    cfg_set("at_wifi_mac",mactozssid);
	cfg_save();
    free(mactozssid);
    mactozssid=NULL;
    return AT_END;
}

int  mac2_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    at_print(AT_ERR,"mac2_err_act\n");
    if(mactozssid != NULL)
        free(mactozssid);
    mactozssid=NULL;
    return AT_END;
}

int  zltelc_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    long action = 0;
    char actionLte[30]= {0};
    cfg_get_item(NV_ACTIONLTE,actionLte,sizeof(actionLte));
    action  = atoi(actionLte);
    cfg_set(NV_CELLLOCKSET,"CellSuccess");
    if(1 == action)
    {
        cfg_set(NV_CELLLOCKSTATE,"Locked");
    }
    else if(0 == action)
    {
        cfg_set(NV_CELLLOCKSTATE,"UnLocked");
    }
    return AT_END;
}

int  zltelc_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    cfg_set(NV_CELLLOCKSET,"CellFail");
    return AT_END;
}

int  sysinfo_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    char netType[50]={0};
	char needCopsNumFormt[10] = {0};
    cfg_get_item(NV_NETWORK_TYPE,netType,sizeof(netType));
    cfg_get_item("need_cops_number_format",needCopsNumFormt,sizeof(needCopsNumFormt));
	sysinfo_flag = 0;
    if (0!=strcmp("No Service",netType)&&0!=strcmp("Limited Service",netType)
        &&0!=strcmp(NETWORK_TYPE_INIT,netType))
    {
        if(0 == strcmp("yes", needCopsNumFormt) && context->msg_id != MSG_CMD_NETWORK_SET_REQ)//������Ӫ����Ϣ��ʽ
        {
            *next_req =normal_getcopsset(1,"3,2");
			if(*next_req)
			*next_len = strlen(*next_req);
        }
        else
        {
            *next_req = normal_getcopsread();
			if(*next_req)
            *next_len = strlen(*next_req);
        }
        return AT_CONTINUE;
    }
    return AT_END;
}

int  sysinfo_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    char netType[50]={0};
	char needCopsNumFormt[10] = {0};
    cfg_get_item(NV_NETWORK_TYPE,netType,sizeof(netType));
	cfg_get_item("need_cops_number_format",needCopsNumFormt,sizeof(needCopsNumFormt));
	sysinfo_flag = 0;
    if (0!=strcmp("No Service",netType)&&0!=strcmp("Limited Service",netType)
        &&0!=strcmp(NETWORK_TYPE_INIT,netType))
    {
        if(0 == strcmp("yes", needCopsNumFormt))//������Ӫ����Ϣ��ʽ
        {
            *next_req =normal_getcopsset(1,"3,2");
			if(*next_req)
			*next_len = strlen(*next_req);
        }
        else
        {
            *next_req = normal_getcopsread();
			if(*next_req)
            *next_len = strlen(*next_req);
        }
        return AT_CONTINUE;
    }
    return AT_END;
}

int  cusd_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    cfg_set("ussd_write_flag","4");
    cfg_set("ussd_cancel_flag","no");
    return AT_END;
}

int  clck_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_PIN_MANAGE_REQ:
        {
            if(psinfo.clck_state==CLCK_SET_CMD)
            {
                char clckMode[10] = {0};
                cfg_get_item(NV_CLCK_SET_MODE,clckMode,sizeof(clckMode));
                if (0==strcmp(clckMode,"0"))
                {
                    cfg_set(NV_NEED_SIM_PIN,"");
                }
                else if (0==strcmp(clckMode,"1"))
                {
                    cfg_set(NV_NEED_SIM_PIN,"yes");
                }
                cfg_set(NV_PIN_MANAGE_RESULT, "0");
                *next_req=normal_getclckset(2,"SC",2);
				if(*next_req){
                *next_len=strlen(*next_req);
                psinfo.clck_state=CLCK_REQ_CMD;
                return AT_CONTINUE;
				}
            }
            else if(psinfo.clck_state==CLCK_REQ_CMD)
            {
                *next_req=normal_getzrapread();
				if(*next_req){
                *next_len=strlen(*next_req);
                psinfo.clck_state=CLCK_OTHER_CMD;
                return AT_CONTINUE;
				}
            }
            return AT_END;
        }
        case MSG_CMD_VERIFY_PUK_REQ:
        {
            MSG_BUF *buf=NULL;
            buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
            rcv_clt_req_msg_proc(buf);
            free(buf);
            return AT_END;
        }
#if (APP_OS_TYPE == APP_OS_LINUX)
		case MSG_CMD_CLCK_REQ:
		{
//			return atCcapp_RecvOk(at_str,context,next_req,next_len);
		}
#endif		
    }
    return AT_END;
}

int  clck_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_PIN_MANAGE_REQ:
        {
            if(psinfo.clck_state==CLCK_SET_CMD)
            {
                cfg_set(NV_PIN_MANAGE_RESULT, "1");
                *next_req=normal_getzrapread();
				if(*next_req){
                *next_len=strlen(*next_req);
                psinfo.clck_state=CLCK_OTHER_CMD;
                return AT_CONTINUE;
				}
            }
            else if(psinfo.clck_state==CLCK_REQ_CMD)
            {
                cfg_set(NV_PIN_MANAGE_RESULT, "1");
                *next_req=normal_getzrapread();
				if(*next_req){
                *next_len=strlen(*next_req);
                psinfo.clck_state=CLCK_OTHER_CMD;
                return AT_CONTINUE;
				}
            }
            return AT_END;
        }
        
        case MSG_CMD_VERIFY_PUK_REQ:
        {
            MSG_BUF *buf=NULL;
            buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,ATCTL_INNER_ZMSRI_MSG,0,NULL);
            rcv_clt_req_msg_proc(buf);
            return AT_END;
        }
#if (APP_OS_TYPE == APP_OS_LINUX)
		case MSG_CMD_CLCK_REQ:
		{
// for porting at_ctrl 
// 			return atCcapp_RecvErr(at_str,context,next_req,next_len);
		}
#endif		
    }
    return AT_END;
}

int  cpwd_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_PIN_MANAGE_REQ:
        {
            cfg_set(NV_PIN_MANAGE_RESULT, "0");
            *next_req=normal_getzrapread();
			if(*next_req){
            *next_len=strlen(*next_req);
            return AT_CONTINUE;
			}
        }
    }
    return AT_END;
}

int  cpwd_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    switch(context->msg_id)
    {
        case MSG_CMD_PIN_MANAGE_REQ:
        {
            cfg_set(NV_PIN_MANAGE_RESULT, "1");
            *next_req=normal_getzrapread();
			if(*next_req){
            *next_len=strlen(*next_req);
            return AT_CONTINUE;
			}	
        }
    }
    return AT_END;
}

int mode_auto_act(char *at_paras ,int is_query_report)
{
    T_zUfi_ModeInfoInd   tAtRes      = {0};
    char strTemp[10]    = {0};
    char cfunState[50] = {0};
	char versionmode[2] = {0};
    MSG_BUF *buf=NULL;
    
    at_print(AT_DEBUG,"atBase_RecvModeRsp MODE \n");
    cfg_get_item(NV_CFUN_STATE,cfunState,sizeof(cfunState));
    if(!strcmp(cfunState,"0"))
    {
        return AT_END;
    }
	
    void *p[2] = {&tAtRes.sys_mode, &tAtRes.sys_submode};
    at_print(AT_DEBUG,"mode_auto_act at_paras=%s!\n",at_paras);
    parse_param2("%d,%d", at_paras, p);
    at_print(AT_DEBUG,"mode_auto_act sys_mode=%ld,sys_submode=%ld!\n",tAtRes.sys_mode,tAtRes.sys_submode);  
    
    snprintf(strTemp, sizeof(strTemp),"%ld", tAtRes.sys_mode);
    cfg_set(NV_SYS_MODE, strTemp);
    memset(strTemp, 0, 10);
    snprintf(strTemp, sizeof(strTemp),"%ld", tAtRes.sys_submode);
    cfg_set(NV_SYS_SUBMODE, strTemp);
    
    at_print(AT_DEBUG,"atBase_RecvModeRsp per=%d,cur=%ld!\n",g_zUfi_Mode,tAtRes.sys_mode);
	//оƬ��֤����
	cfg_get_item("version_mode", versionmode, sizeof(versionmode));
	if(0 == strcmp(versionmode, "0"))
	{
		if(g_zUfi_Mode != tAtRes.sys_mode || g_zUfi_SubMode != tAtRes.sys_submode)
	    {
	        g_zUfi_Mode = tAtRes.sys_mode;
			g_zUfi_SubMode = tAtRes.sys_submode;
			if(sysinfo_flag == 0)
			{
		        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
		        rcv_clt_req_msg_proc(buf);
		        free(buf);
				sysinfo_flag = 1;//sysinfoֻ��һ��
			}
	    }
	}
    return AT_END;
}

int cereg_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_CeregRes tAtRes = {0};
    MSG_BUF *buf=NULL;
	int tac = 0, ci = 0;
    char strBuf[20] = {0};
	char versionmode[2] = {0};

    void *p[5] = {&tAtRes.stat, tAtRes.tac, tAtRes.ci, &tAtRes.act,&tAtRes.subact};
    at_print(AT_DEBUG,"atBase_RecvCeregRsp at_paras=%s!\n",at_paras);
    parse_param2("%d,%s,%s,%d,%d", at_paras, p);
    at_print(AT_DEBUG,"atBase_RecvCeregRsp stat=%d,tac=%s,ci=%s,res=%d,sub=%d!\n",
    tAtRes.stat, tAtRes.tac, tAtRes.ci, tAtRes.act,tAtRes.subact);  
    sprintf(strBuf, "%d", tAtRes.stat);
    cfg_set("cereg_stat", strBuf);

	if(tAtRes.act == 7)
	{
		trans_Str2Value(tAtRes.tac, FALSE, &tac);
		memset(strBuf, 0x00, 20);
		sprintf(strBuf, "%d", tac);
		cfg_set(NV_TAC_CODE, strBuf);
		
		trans_Str2Value(tAtRes.ci, FALSE, &ci);
		memset(strBuf, 0x00, 20);
		sprintf(strBuf, "%d", ci);
		cfg_set(NV_CELL_ID, strBuf);
	}
	
    /*ע������������򱾵�����*/
    if( (5 == tAtRes.stat ) || ( 1 == tAtRes.stat ))
    {
		buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_CGCONTRDP_REQ,0,NULL);
		rcv_clt_req_msg_proc(buf);
		free(buf);
        cfg_set(NV_REG_STATUS,"ok");
		//zhangfen 1106 EPS����ע��ɹ�����PDP�Զ�����
		if(g_modem_model)
    	{
			start_pdp_auto_dial();//������ʱ������pdp����
    	}
    }
    else
    {
        cfg_set(NV_REG_STATUS,"no");
    }
    at_print(AT_DEBUG,"atBase_RecvCeregRsp per=%d,cur=%d!\n",g_zUfi_Stat,tAtRes.stat);

	//оƬ��֤����
	cfg_get_item("version_mode", versionmode, sizeof(versionmode));
	if(0 == strcmp(versionmode, "0"))
	{
		if(g_zUfi_Stat != tAtRes.stat)
		{
	        g_zUfi_Stat = tAtRes.stat;
			if(sysinfo_flag == 0)
			{
		        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
		        rcv_clt_req_msg_proc(buf);
		        free(buf);
				sysinfo_flag = 1;//sysinfoֻ��һ��
			}
	    }
		else
		{
			char networkType[50] = {0};
        	cfg_get_item(NV_NETWORK_TYPE,networkType,sizeof(networkType));
			if((5 == tAtRes.stat || 1 == tAtRes.stat) && (0 == strcmp("No Service",networkType) || 0 == strcmp("Limited Service",networkType)))
			{
				if(sysinfo_flag == 0)
				{
			        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
			        rcv_clt_req_msg_proc(buf);
			        free(buf);
					sysinfo_flag = 1;//sysinfoֻ��һ��
				}
			}
		}
	}
    return AT_END;
}

int can_poweron_dail = 0;
extern int apn_is_ims;
int cgreg_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_CgregRes   tAtRes  = {0};
    MSG_BUF *buf=NULL;
    char strBuf[10] = {0};
	char versionmode[2] = {0};

    void *p[6] = {&tAtRes.stat, tAtRes.lac, tAtRes.rac, &tAtRes.act, tAtRes.ci, &tAtRes.subact};
    at_print(AT_DEBUG,"cgreg_auto_act at_paras=%s!\n",at_paras);
    parse_param2("%d,%s,%s,%d,%s,%d", at_paras, p);
    at_print(AT_DEBUG,"cgreg_auto_act stat=%d,lac=%s,rac=%s,act=%d,ci=%s,subact=%d!\n",
    tAtRes.stat, tAtRes.lac, tAtRes.rac, tAtRes.act, tAtRes.ci, tAtRes.subact);  

    snprintf(strBuf,sizeof(strBuf),"%d", tAtRes.stat);
    cfg_set("cgreg_stat", strBuf);
	
    if(cfun_state==0)
    {
        at_print(AT_ERR,"query cgreg err!\n");
        return AT_END;
    }
    /*ע������������򱾵�����*/
    if( (5 == tAtRes.stat ) || ( 1 == tAtRes.stat ))
    {
		MSG_BUF *msg_buf = NULL;
		char ppp_status[20] = {0};
		cfg_get_item(NV_PPP_STATUS, ppp_status, sizeof(ppp_status));
		//ֻ�е��״ο�������������������������Ų���û�в��ųɹ�����ʱ���������
		//at_ctlģ����������·���pdp����
		if((can_poweron_dail == 0) && (g_defcid_mng.modid == MODULE_ID_MAIN_CTRL) && (0 == strcmp(PPP_DISCONNECTED, ppp_status)))
		{
			can_poweron_dail = 1;
			at_print(AT_ERR,"cgreg_auto_act begin dial!!!\n");
			msg_buf=normal_getmsg(g_defcid_mng.modid ,MODULE_ID_AT_CTL,MSG_CMD_PDP_ACT_REQ, 0, NULL);
			rcv_msg_proc(msg_buf);
			free(msg_buf);
			msg_buf = NULL;
		}
    	if(g_modem_model)
    	{
			start_pdp_auto_dial();
    	}
        cfg_set(NV_TDREG_STATUS,"ok");
		//apn_is_ims=-1;//4G��2-3G����ͬʱ����EC:616000620973
    }
    else
    {
        cfg_set(NV_TDREG_STATUS,"no");
    }
    at_print(AT_DEBUG,"atBase_RecvCgregRsp per=%d,cur=%d!\n",g_zUfi_Stat,tAtRes.stat);

	//оƬ��֤����
	cfg_get_item("version_mode", versionmode, sizeof(versionmode));
	if(0 == strcmp(versionmode, "0"))
	{
	    if(g_zUfi_Stat != tAtRes.stat && g_modem_model)
	    {
	        g_zUfi_Stat = tAtRes.stat;
			if(sysinfo_flag == 0)
			{
		        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
		        rcv_clt_req_msg_proc(buf);
		        free(buf);
				sysinfo_flag = 1;//sysinfoֻ��һ��
			}
	    }
		else
		{
			char networkType[50] = {0};
        	cfg_get_item(NV_NETWORK_TYPE,networkType,sizeof(networkType));
			if((5 == tAtRes.stat || 1 == tAtRes.stat) && (0 == strcmp("No Service",networkType) || 0 == strcmp("Limited Service",networkType)))
			{
				if(sysinfo_flag == 0)
				{
			        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
			        rcv_clt_req_msg_proc(buf);
			        free(buf);
					sysinfo_flag = 1;//sysinfoֻ��һ��
				}
			}
		}
	}
    return AT_END;
}


int creg_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_CregRes   tAtRes      = {0};
    MSG_BUF *buf=NULL;
	int lac = 0, ci = 0;
    char strBuf[20] = {0};
	char versionmode[2] = {0};
	
    void *p[5] = {&tAtRes.stat, tAtRes.tac, tAtRes.ci, &tAtRes.act,&tAtRes.subact};
    at_print(AT_DEBUG,"creg_auto_act at_paras=%s!\n",at_paras);
    parse_param2("%d,%s,%s,%d,%d", at_paras, p);
    at_print(AT_DEBUG,"creg_auto_act stat=%d,tac=%s,ci=%s,act=%d,subact=%d!\n",
    tAtRes.stat, tAtRes.tac, tAtRes.ci, tAtRes.act, tAtRes.subact);  

    sprintf(strBuf, "%d", tAtRes.stat);
    cfg_set("creg_stat", strBuf);

	if(tAtRes.act != 7)
	{
		trans_Str2Value(tAtRes.tac, FALSE, &lac);
		memset(strBuf, 0x00, 20);
		sprintf(strBuf, "%d", lac);
		cfg_set(NV_LAC_CODE, strBuf);
		
		trans_Str2Value(tAtRes.ci, FALSE, &ci);
		memset(strBuf, 0x00, 20);
		sprintf(strBuf, "%d", ci);
		cfg_set(NV_CELL_ID, strBuf);
	}
	
    /*ע������������򱾵�����*/
    if( (5 == tAtRes.stat ) || ( 1 == tAtRes.stat ))
    {
    	if(g_modem_model)
    	{
			start_pdp_auto_dial();
    	}
    	//zhangfen for PDP
		//ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MAIN_CTRL, MSG_CMD_NET_AUTO_DIAL, 0, NULL, 0);
        cfg_set(NV_REG_STATUS,"ok");

		ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_DMREG, MSG_CMD_CREG_IND, 0, NULL, 0);
    }
    else
    {
        cfg_set(NV_REG_STATUS,"no");
    }
    at_print(AT_DEBUG,"atBase_RecvCregRsp per=%d,cur=%d!\n",g_zUfi_Stat,tAtRes.stat);
	
    //оƬ��֤����
	cfg_get_item("version_mode", versionmode, sizeof(versionmode));
	if(0 == strcmp(versionmode, "0"))
	{
	    if(g_zUfi_Stat != tAtRes.stat)
	    {
			if(cfun_state==0 && g_modem_model)
		    {
		        at_print(AT_ERR,"query creg err!\n");
		        return AT_END;
		    }
	        g_zUfi_Stat = tAtRes.stat;
			if(sysinfo_flag == 0)
			{
		        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
		        rcv_clt_req_msg_proc(buf);
		        free(buf);
				sysinfo_flag = 1;//sysinfoֻ��һ��
			}
	    }
		else
		{
			char networkType[50] = {0};
        	cfg_get_item(NV_NETWORK_TYPE,networkType,sizeof(networkType));
			if((5 == tAtRes.stat || 1 == tAtRes.stat) && (0 == strcmp("No Service",networkType) || 0 == strcmp("Limited Service",networkType)))
			{
				if(sysinfo_flag == 0)
				{
			        buf=normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_QUERY_SYSINFO_REQ,0,NULL);
			        rcv_clt_req_msg_proc(buf);
			        free(buf);
					sysinfo_flag = 1;//sysinfoֻ��һ��
				}
			}
		}
	}
    return AT_END;
}

//��ȡ�����ź�״̬����NV
int sysinfo_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_SysinfoRes tPara                       = {0};
	UINT32 signalbar = 0;
	char needDispSearching[10] = {0};
	char modemMainState[50] = {0};
	char networkCategoryBasedOn[10] = {0};
	char strSignalbar[10] = {0};
	char perRoamStatus[10] = {0};
	char curRoamStatus[10] = {0};

    void *p[7] = {&tPara.srvStatus, &tPara.srvDomain,&tPara.roamStatus,&tPara.sysMode, &tPara.simState,&tPara.reserve,&tPara.sysSubmode};
    at_print(AT_DEBUG,"sysinfo_auto_act at_paras=%s!\n",at_paras);
    parse_param2("%d,%d,%d,%d,%d,%d,%d", at_paras, p);
    at_print(AT_DEBUG,"sysinfo_auto_act srvStatus=%ld,srvDomain=%ld,roamStatus=%ld,sysMode=%ld,simState=%ld,reserve=%ld,sysSubmode=%ld!\n",
    tPara.srvStatus,tPara.srvDomain,tPara.roamStatus,tPara.sysMode,tPara.simState,tPara.reserve,tPara.sysSubmode);  

	cfg_get_item("need_display_searching_status", needDispSearching, sizeof(needDispSearching));
	cfg_get_item("modem_main_state", modemMainState, sizeof(modemMainState));
	cfg_get_item("network_category_based_on", networkCategoryBasedOn, sizeof(networkCategoryBasedOn));
	if(0 == tPara.srvStatus)//�޷���
    {
	    if(0 == strcmp("yes", needDispSearching))
		{
			if(g_isSearchingNetwork == TRUE)
			{
	            if(strcmp(modemMainState,"modem_init_complete") == 0)
	            {
					cfg_set(NV_NETWORK_TYPE, NETWORK_TYPE_INIT);
					cfg_set(NV_SUB_NETWORK_TYPE, NETWORK_TYPE_INIT);
	            }
				else
				{
					cfg_set(NV_NETWORK_TYPE, "No Service");
					cfg_set(NV_SUB_NETWORK_TYPE, "No Service");
				}
			}
			else
			{
	        	cfg_set(NV_NETWORK_TYPE, "No Service");
				cfg_set(NV_SUB_NETWORK_TYPE, "No Service");
			}
		}		
		else
		{
			cfg_set(NV_NETWORK_TYPE, "No Service");
			cfg_set(NV_SUB_NETWORK_TYPE, "No Service");
		}  		
		signalbar = 0;
        ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI,MSG_CMD_OUT_REG_GET_SIGNAL_NUM, sizeof(signalbar), (UINT8 *)&signalbar,0);
    }
    else if( 1 == tPara.srvStatus )
    {
        if(0 == strcmp("yes", needDispSearching))
		{
			if(g_isSearchingNetwork == TRUE)
			{
				if(strcmp(modemMainState,"modem_init_complete") == 0)
	            {
					cfg_set(NV_NETWORK_TYPE, NETWORK_TYPE_INIT);
					cfg_set(NV_SUB_NETWORK_TYPE, NETWORK_TYPE_INIT);
	            }
				else
				{
					cfg_set(NV_NETWORK_TYPE, "Limited Service");
					cfg_set(NV_SUB_NETWORK_TYPE, "Limited Service");
                }
			}
			else
			{
	 	       cfg_set(NV_NETWORK_TYPE, "Limited Service");
			   cfg_set(NV_SUB_NETWORK_TYPE, "Limited Service");
 			}
		}		
		else
		{
			cfg_set(NV_NETWORK_TYPE, "Limited Service");
			cfg_set(NV_SUB_NETWORK_TYPE, "Limited Service");
		}		
		signalbar = 0;
        ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI,MSG_CMD_OUT_REG_GET_SIGNAL_NUM, sizeof(signalbar), (UINT8 *)&signalbar,0);
    }
    else
    {
    	if(0 == strcmp("yes", needDispSearching))
	    {
			g_isSearchingNetwork = FALSE;
        }
        char    netCategory[50]  = {0};
        char    connectLogic[50]  = {0};
        cfg_get_item(NV_NETWORK_CATEGORY_BASED_ON,netCategory,sizeof(netCategory));
        cfg_get_item(NV_CONNECT_LOGIC,connectLogic,sizeof(connectLogic));
        
        //normal_sysinfosetdomainstat(tPara.srvDomain);
        if(0 == strcmp("act", netCategory))
		{
			normal_sysinfosysmodecfgset(tPara.sysMode,tPara.sysSubmode);
		}
        else
        {
			normal_sysinfonetworktypeSet(tPara.sysMode,tPara.sysSubmode);
		}
        
        //if(0!=strcmp("cpe",connectLogic) && 0!=strcmp("five_times",connectLogic))
        //{
        //    normal_sysinfodomainpdpdail(tPara.srvDomain);
        //}
		cfg_get_item("signalbar", strSignalbar, sizeof(strSignalbar));
        signalbar = atoi(strSignalbar);
        ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI,MSG_CMD_OUT_REG_GET_SIGNAL_NUM, sizeof(signalbar), (UINT8 *)&signalbar,0);

		//zhangfen for PDP
		//ipc_send_message(MODULE_ID_AT_CTL,MODULE_ID_MAIN_CTRL, MSG_CMD_NET_AUTO_DIAL, 0, NULL, 0);
				
		if(g_modem_model)
		{
			//static int can_poweron_dail = 0;
			MSG_BUF *msg_buf = NULL;
			char ppp_status[20] = {0};
			cfg_get_item(NV_PPP_STATUS, ppp_status, sizeof(ppp_status));
			//ֻ�е��״ο�������������������������Ų���û�в��ųɹ�����ʱ���������
			//at_ctlģ����������·���pdp����
			if((can_poweron_dail == 0) && (g_defcid_mng.modid == MODULE_ID_MAIN_CTRL) && (0 == strcmp(PPP_DISCONNECTED, ppp_status)))
		    {
				char creg_stat[20] = {0};
				char cereg_stat[20] = {0};
				
				cfg_get_item("cgreg_stat", creg_stat, sizeof(creg_stat));
				cfg_get_item("cereg_stat", cereg_stat, sizeof(cereg_stat));
				at_print(AT_DEBUG,"@@@@@@@@@@@@@sysinfo_auto_act  creg stat=%s, cereg stat=%s!\n",creg_stat,cereg_stat);
				//zpr 1218 GPRSS&EPSûע��ã�������
				if(1 != atoi(creg_stat) && 5 != atoi(creg_stat) && 1 != atoi(cereg_stat) && 5 != atoi(cereg_stat))
				{
					//return 0;
				}
				else
				{
		        can_poweron_dail = 1;
				#if 0
		        struct pdp_act_req *pdpsetinfo=NULL;
		        pdpsetinfo=malloc(sizeof(struct pdp_act_req));
		        if(pdpsetinfo == NULL){softap_assert("");}
		        memset(pdpsetinfo,0,sizeof(struct pdp_act_req));
		        normal_getcurparam(pdpsetinfo);
		        cfg_set("auto_reconnect", "1");
				#endif
				
				at_print(AT_ERR,"sysinfo_auto_act begin dial!!!\n");
				msg_buf=normal_getmsg(g_defcid_mng.modid ,MODULE_ID_AT_CTL,MSG_CMD_PDP_ACT_REQ, 0, NULL);
				rcv_msg_proc(msg_buf);
				free(msg_buf);
				msg_buf = NULL;
				}
		    }
		}
	}

	cfg_get_item(NV_SIMCARD_ROAM, perRoamStatus, sizeof(perRoamStatus));
    if(0 == tPara.roamStatus)
    {
		cfg_set(NV_SIMCARD_ROAM,"Home");
	}
	else
	{
		if(TRUE == normal_checkroam(mccNum, mncNum))
		{
		    cfg_set(NV_SIMCARD_ROAM,"Home");
			tPara.roamStatus = 0;
		}
	    else
		{
		    cfg_set(NV_SIMCARD_ROAM,"Internal"); 
		}
    }

	cfg_get_item(NV_SIMCARD_ROAM, curRoamStatus, sizeof(curRoamStatus));
	if(strcmp(perRoamStatus, curRoamStatus) != 0)
	{
		ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_DMREG, MSG_CMD_ROAM_STATUS_IND, 0, NULL, 0);
	}
	
    if(0 == strcmp("yes", needDispSearching))
	{
	   if(g_isSearchingNetwork == FALSE)
	   {
			at_print(AT_DEBUG,"atBase_RecvSysInfoRsp, MSG_MMICHANNEL_NETWORK_MODE, tPara.srvStatus = %ld \n", tPara.srvStatus);
		    ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI,MSG_CMD_CHANNEL_NETWORK_MODE, sizeof(tPara), (UCHAR *)&tPara,0);
	   }
	}   
	else
    {
        ipc_send_message(MODULE_ID_AT_CTL, MODULE_ID_MMI,MSG_CMD_CHANNEL_NETWORK_MODE, sizeof(tPara), (UCHAR *)&tPara,0);
    }
    return AT_END;
}

int mac_auto_act(char *at_paras ,int is_query_report)
{
    mactozssid=malloc(128);
    if(mactozssid == NULL){
		softap_assert("");
		return AT_END;
	}
    memset(mactozssid, 0, 128);    
    snprintf(mactozssid,128,"+MAC:%s",at_paras);
    return AT_END;
}

int mac2_auto_act(char *at_paras ,int is_query_report)
{
    if(mactozssid == NULL || strlen(mactozssid) >= 128){
		softap_assert("");
		return AT_END;
	}
    snprintf(mactozssid+strlen(mactozssid),128-strlen(mactozssid),";%s",at_paras);
    return AT_END;
}

int clck_auto_act(char *at_paras ,int is_query_report)
{
    char strMode[10] = {0};
    T_zAt_ClckSet_RES clckRspPara = {0};
    int classVale = 0;
    int status = 0;
    sscanf(at_paras, "%d,%d",&status,&classVale);
    clckRspPara.uclass  = (unsigned char)classVale;
    clckRspPara.status  = (unsigned char)status;
    sprintf(strMode,"%d", clckRspPara.status);
    at_print(AT_DEBUG,"clck_auto_act uclass=%d,status=%d\n",status,classVale);
    /*0��δ����, 1������*/
    cfg_set(NV_PIN_STATUS, strMode);
    return AT_END;
}

int zpbic_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_ZpbicRes AtRes = {0};
    char ismsSwitch[2] = {0};
    sscanf(at_paras, "%ld,%ld", &AtRes.result, &AtRes.opertype);
    at_print(AT_DEBUG,"zpbic_auto_act str:%s,result:%ld,opertype:%ld\n",at_paras,AtRes.result, AtRes.opertype);

    if((1 == AtRes.result) && (1 == AtRes.opertype))
    {
    	cfg_set("zpbic_pb", "ready");
	    if(g_modem_model)
	    {
	    	g_smspb_init |= (1<<AtRes.opertype);
			at_print(AT_NORMAL,"1.sms_pb init flag: %d\n", g_smspb_init);
			if(((g_smspb_init & 0x03) == 0x03) && (g_need_smspb_init == 0))
			{
				char ppp_status[20] = {0};
				cfg_get_item(NV_PPP_STATUS, ppp_status, sizeof(ppp_status));
				if(0 == strcmp("ppp_connected", ppp_status))
					pbsms_init_msg_proc();//����Ѿ����ųɹ���ֱ�Ӹ�pb��smsӦ�÷����ʼ����Ϣ
				else
					pbsms_init_timerout(&AtRes);//pb��sms����ʼ����ɺ������ʱ��
			}
	    }
		
		char versionmode[2] = {0};
		MSG_BUF *buf = NULL;
        //оƬ��֤����
		cfg_get_item("version_mode", versionmode, sizeof(versionmode));
		if(0 == strcmp(versionmode, "0"))	
		{
	    	buf = normal_getmsg(MODULE_ID_AT_CTL,MODULE_ID_AT_CTL,MSG_CMD_MODEMREG_INFO_REQ,0,NULL);
	    	rcv_clt_req_msg_proc(buf);
	    	free(buf);
		}

        at_print(AT_DEBUG,"zpbic_auto_act cnum\n");
    }
    else if((1 == AtRes.result) && (0 == AtRes.opertype))
    {
        at_print(AT_DEBUG,"zpbic_auto_act sms\n");
#if (APP_OS_TYPE == APP_OS_LINUX)			
#if 1 
        cfg_get_item("isms_enable",ismsSwitch,sizeof(ismsSwitch));
        if(1 == AtRes.result && 0==strcmp(ismsSwitch, "1"))
        {
/*        	cfg_set("isms_state", "0");
            if(fork() == 0)
            {
        		cfg_set("isms_state", "1");
                if(execv("/bin/zte_isms", NULL) < 0)
                {
        			cfg_set("isms_state", "2");
                    at_print(AT_DEBUG,"execv error(%d)\n", errno);
                    exit(0);
                }
            }
            
        	cfg_set("isms_state", "3");*/
			if(system("/bin/zte_isms &") == 0)
			{
				cfg_set("isms_state", "3");
			}
			else
			{
				cfg_set("isms_state", "2");
			}
        }
#endif
#endif
	    if(g_modem_model)
	    {
	    	g_smspb_init |= (1<<AtRes.opertype);
	    	at_print(AT_NORMAL,"2.sms_pb init flag: %d\n", g_smspb_init);
	    	if(((g_smspb_init & 0x03) == 0x03) && (g_need_smspb_init == 0))
			{
				char ppp_status[20] = {0};
				cfg_get_item(NV_PPP_STATUS, ppp_status, sizeof(ppp_status));
				if(0 == strcmp("ppp_connected", ppp_status))
					pbsms_init_msg_proc();//����Ѿ����ųɹ���ֱ�Ӹ�pb��smsӦ�÷����ʼ����Ϣ
				else
					pbsms_init_timerout(&AtRes);//pb��sms����ʼ����ɺ������ʱ��
			}
	    }
    }
    return AT_END;
}
//add corem0620 begin
#define ZAT_CUSD_MAXLEN                     320
#define ZAT_MAX_LEN                 7168 //1024
#define AT_USSD_DATA_TO_WEB_LEN  900//ussd
#define ZAT_TAB_REPLACE                     ((unsigned char )(0xFC))    /* �Ʊ����滻��     */
#define ZAT_NULL_FILL                       ((unsigned char )(0xFD))    /* �մ�ռλ��       */
#define ZAT_SPACE_REPLACE                   ((unsigned char )(0xFE))    /* �ո��滻��       */
#define ZAT_LF_REPLACE                      ((unsigned char )(0xFB))    /* LF�滻��         */
#define ZAT_CR_REPLACE                      ((unsigned char )(0xFA))    /* CR�滻�� */

typedef struct
{
    SINT16  replyMsg;
    CHAR    cusdDataStr[ZAT_CUSD_MAXLEN + 1];
    SINT16  bDcs;
} T_zAt_CusdRes;

typedef enum
{
    ZAT_GSM7BIT = 0,
    ZAT_8BIT,
    ZAT_UCS2,
    ZAT_INVALID,
} T_zAt_CBSCodeType;
VOID atBase_PreProcRes(CHAR *pParaLine, int paraSize)
{
    SINT32  flg                                     = 0;
    UINT32  i                                       = 0;
    UINT32  length                                  = 0;
    CHAR    *pSource                                = pParaLine;
    CHAR    *pDest                                  = NULL;
    
    CHAR    *pStrDestMalloc = (CHAR *)malloc(ZAT_MAX_LEN);
    if(NULL == pStrDestMalloc)
    {
        return;
    }
    memset(pStrDestMalloc, 0, ZAT_MAX_LEN); 
    
    assert(pParaLine != NULL);
    pDest = pStrDestMalloc;
    length = strlen(pParaLine);
    if ((length == 0) || (length >= ZAT_MAX_LEN))
    {
        free(pStrDestMalloc);
        return;
    }
    for (i = 0; (i < length )&& (pDest - pStrDestMalloc < ZAT_MAX_LEN); i++)
    {
        if ('"' == *pSource)
        {
            flg = (0 == flg)?1:0;                           /* ˫���� ɾ��  */
            if ('"' == *(pSource + 1))                      /* �Ǻź�������Ǻţ�����մ�ռλ�� */
            {
                *pDest++ = (CHAR)ZAT_NULL_FILL;
            }
        }
        else if ((',' == *pSource) && (0 == flg))
        {
            *pDest++ = ' ';                                 /* ˫��������Ķ��ţ��滻�ɿո� */
            if(',' == *(pSource + 1))                       /* ���ź���������ţ������Զ��Ž�β������մ�ռλ�� */
            {
                *pDest++ = '9';                             //����������9��ʱ����
            }
            else if('\0' == *(pSource + 1))                 /* ���ź���������ţ������Զ��Ž�β������մ�ռλ�� */
            {
                *pDest++ = (CHAR)ZAT_NULL_FILL;
            }
        }
        else
        {
            //*pDest++ = ((' ' == *pSource) && (1 == flg))?(CHAR)ZAT_SPACE_REPLACE:((('\t' == *pSource) && (1 == flg))?(CHAR)ZAT_TAB_REPLACE:((('\n' == *pSource) && (1 == flg))?(CHAR)ZAT_LF_REPLACE:((('\r' == *pSource) && (1 == flg))?(CHAR)ZAT_CR_REPLACE:(*pSource))));
            if((' ' == *pSource) && (1 == flg))
			{
				*pDest++ = (CHAR)ZAT_SPACE_REPLACE;
			}
			else if(('\t' == *pSource) && (1 == flg))
			{
				*pDest++ = (CHAR)ZAT_TAB_REPLACE;
			}
			else if(('\n' == *pSource) && (1 == flg))
			{
				*pDest++ = (CHAR)ZAT_LF_REPLACE;
			}
			else if(('\r' == *pSource) && (1 == flg))
			{
				*pDest++ = (CHAR)ZAT_CR_REPLACE;
			}
			else
			{
				*pDest++ = *pSource;
			}
        }
        pSource++;
    }
    memset(pParaLine, 0, paraSize);                           /* ��Ԥ����������ݿ����ز������� */
    strncpy(pParaLine, pStrDestMalloc,paraSize-1);
    free(pStrDestMalloc);
}


VOID atBase_RestoreString(CHAR *pStringPara)
{
    SINT32  i           = 0;
    SINT32  length      = 0;
    CHAR*   pstrTemp    = 0;
    /* ������� �� ���ȼ��*/
    assert(pStringPara != NULL);
	
    length = (SINT32)strlen(pStringPara);
	if ( 0 == length)
    {
        return;
    }
	
    /* �մ�ռλ���ָ� */
    if ((1 == length) && ((CHAR) ZAT_NULL_FILL == *pStringPara))
    {
        *pStringPara = '\0';
        return;
    }
    /* ѭ���ָ��ո��滻�����Ʊ����滻�� */
    pstrTemp = pStringPara;
    for (i = 0; i < length; i++)
    {
        if ((CHAR) ZAT_SPACE_REPLACE == *pstrTemp)
        {
            *pstrTemp = ' ';
        }
        else if ((CHAR) ZAT_TAB_REPLACE == *pstrTemp)
        {
            *pstrTemp = '\t';
        }
        else if((CHAR) ZAT_LF_REPLACE == *pstrTemp)
        {
            *pstrTemp = '\n';
        }
        else if((CHAR) ZAT_CR_REPLACE == *pstrTemp)
        {
            *pstrTemp = '\r';
        }
        else
        {
            ;
        }
        pstrTemp++;
    }
}
//USSD
T_zAt_CBSCodeType atBase_CheckDataCodeType(SINT16 bDcs)
{
    UINT16 value76 = (bDcs & 0xC0) >> 6;
    UINT16 value74 = (bDcs & 0xF0) >> 4;
    UINT16 value30 = (bDcs & 0x0F);
    UINT16 value32 = (bDcs & 0x0C) >> 2;
    UINT16 value2  = (bDcs & 0x04) >> 2;

    if((value74 == 0) || (value74 == 2) || (value74 == 3) || (value74 == 8) ||
        (value74 == 10) || (value74 == 11) || (value74 == 12))
    {
        return ZAT_GSM7BIT;
    }
    else if((value74 == 1) && (value30 == 1))
    {
        return ZAT_UCS2;
    }
    else if((value74 == 1) && (value30 != 1))
    {
        return ZAT_GSM7BIT;
    }
    else if((value76 == 1) && ((value32 == 0) || (value32 == 3)))
    {
        return ZAT_GSM7BIT;
    }
    else if((value76 == 1) && (value32 == 1))
    {
        return ZAT_8BIT;
    }
    else if((value76 == 1) && (value32 == 2))
    {
        return ZAT_UCS2;
    }
    else if((value74 == 9) && ((value32 == 0) || (value32 == 3)))
    {
        return ZAT_GSM7BIT;
    }
    else if((value74 == 9) && (value32 == 1))
    {
        return ZAT_8BIT;
    }
    else if((value74 == 9) && (value32 == 2))
    {
        return ZAT_UCS2;
    }
    else if((value74 == 15) && (value2 == 0))
    {
        return ZAT_GSM7BIT;
    }
    else if((value74 == 15) && (value2 == 1))
    {
        return ZAT_8BIT;
    }
    return ZAT_INVALID;
}

VOID atBase_ConvertAsciiToUCS2(const CHAR *dataStr,CHAR *convertDataStr,UINT16 dataLen)
{
    UINT16 iDataLen = 0;
    
    if((dataStr == NULL) || (convertDataStr == NULL) || (dataLen <= 0))
    {
        return;
    }
    for(iDataLen = 0;iDataLen < dataLen;iDataLen++)
    {
        snprintf(convertDataStr+strlen(convertDataStr),AT_USSD_DATA_TO_WEB_LEN,"%04x",dataStr[iDataLen]);
    }
    printf("%s\n",convertDataStr);
}

VOID atBase_Convert8BitToUCS2(const CHAR *dataStr,CHAR *convertDataStr,UINT16 dataLen)
{
    UINT16 iDataLen = 0;
    
    if((dataStr == NULL) || (convertDataStr == NULL) || (dataLen <= 0))
    {
        return;
    }
    for(iDataLen = 0;iDataLen < dataLen;iDataLen++)
    {
        if(iDataLen%2 == 0)
	 {
	      snprintf(convertDataStr+strlen(convertDataStr),AT_USSD_DATA_TO_WEB_LEN,"00%c",dataStr[iDataLen]);
	 }
	 else
	 {
	      snprintf(convertDataStr+strlen(convertDataStr),AT_USSD_DATA_TO_WEB_LEN,"%c",dataStr[iDataLen]);
	 }
     }
     printf("%s\n",convertDataStr);
}


BOOL atBase_ConvertUCS2SetNV(T_zAt_CBSCodeType cbsCodeType,const CHAR *cusdDataStr,CHAR *convertDataStr,UINT16 dataLen)
{
    if((cbsCodeType == ZAT_INVALID) || (cusdDataStr == NULL) || (convertDataStr == NULL) || (dataLen <= 0))
    {
        return FALSE;
    }
    if(cbsCodeType == ZAT_GSM7BIT)
    {
        atBase_ConvertAsciiToUCS2(cusdDataStr,convertDataStr,dataLen);
        cfg_set("ussd_content",convertDataStr);
        return TRUE;
    }
    else if(cbsCodeType == ZAT_8BIT)
    {
        atBase_Convert8BitToUCS2(cusdDataStr,convertDataStr,dataLen);
        cfg_set("ussd_content",convertDataStr);
        return TRUE;
    }
    else if(cbsCodeType == ZAT_UCS2)
    {
        cfg_set("ussd_content",cusdDataStr);
        return TRUE;
    }
    else
    {
        printf("DCS Reported can't be processed!!!!\n");
        return FALSE;
    }
}

static int get_param_count_for_ussd(const char *str){//corem0418
    int fmt_param_count = 0;
    
    for(; *str != '\0'; str++){
        if(*str == ','){
            fmt_param_count++;
        }
    }
    return fmt_param_count;
}

int cusd_auto_act(char *at_paras ,int is_query_report)
{
    T_zAt_CusdRes                    cusdParam = {0};
    T_zAt_CBSCodeType            cbsCodeType = ZAT_INVALID;
    CHAR convertDataStr[AT_USSD_DATA_TO_WEB_LEN] = {0};
    //static int flag = 0;

	at_print(AT_ERR,"!!!!cusd_auto_act start.\n");
    if(at_paras == NULL)
    {
        return AT_END;
    }
	

    at_print(AT_ERR,"!!!!cusd_auto_act Gets %s.\n", at_paras);

//    void *p[3] = {&cusdParam.replyMsg,  cusdParam.cusdDataStr, &cusdParam.bDcs};

//    parse_param("%d %s %d", at_paras, p);
	
//    at_print(AT_DEBUG,"cusd_auto_act  replyMsg=%d, cusdDataStr=%s, bDcs=%d!\n",  cusdParam.replyMsg, cusdParam.cusdDataStr, cusdParam.bDcs);  	
    
    if(get_param_count_for_ussd(at_paras) > 0){//corem0418           
           atBase_PreProcRes(at_paras, strlen(at_paras));
	    at_print(AT_ERR,"!!!!cusd_auto_act Gets 3 para:%s\n", at_paras);
	    sscanf(at_paras, "%hd %320s %hd",&cusdParam.replyMsg,cusdParam.cusdDataStr,&cusdParam.bDcs);
	    
    }else{
           at_print(AT_ERR,"!!!!cusd_auto_act Gets 1 para:%s\n", at_paras);         
	   sscanf(at_paras, "%hd",&cusdParam.replyMsg);
    }

    at_print(AT_ERR,"cusd_auto_act before restore MSG  replyMsg=%d, cusdDataStr=%s, bDcs=%d!\n",  cusdParam.replyMsg, cusdParam.cusdDataStr, cusdParam.bDcs);  

    atBase_RestoreString(cusdParam.cusdDataStr);
	
    at_print(AT_ERR,"cusd_auto_act after restore MSG replyMsg=%d, cusdDataStr=%s, bDcs=%d!\n",  cusdParam.replyMsg, cusdParam.cusdDataStr, cusdParam.bDcs);
	
    //printf("atBase_RecvCusdRsp :%d,%s,%d",cusdParam.replyMsg,cusdParam.cusdDataStr,cusdParam.bDcs);
    if((cusdParam.replyMsg == 0) || (cusdParam.replyMsg == 1))
    {
        cbsCodeType = atBase_CheckDataCodeType((SINT16)cusdParam.bDcs);
        at_print(AT_ERR,"Code Type:%d\n",cbsCodeType);
        if(atBase_ConvertUCS2SetNV(cbsCodeType,cusdParam.cusdDataStr,convertDataStr,(UINT16)strlen(cusdParam.cusdDataStr)))
        {
            cfg_set("ussd_write_flag","16");
            cfg_set("ussd_dcs","72");
            if(cusdParam.replyMsg == 0)
            {
                cfg_set("ussd_mode","0");
                cfg_set("ussd_cancel_flag","no");
            }
            else
            {
                cfg_set("ussd_mode","1");
                cfg_set("ussd_cancel_flag","yes");
            }
			//�����ݴ洢�����ݿ�
			//store_ussd_data_to_db(convertDataStr);//corem0625
        }
        else
        {
            cfg_set("ussd_write_flag","99");
            cfg_set("ussd_cancel_flag","no");
			return AT_END;
        }
    }
    else if(cusdParam.replyMsg == 2)
    {
        cfg_set("ussd_write_flag","13");
        cfg_set("ussd_cancel_flag","no");
    }
    else if(cusdParam.replyMsg == 3)
    {
        cfg_set("ussd_write_flag","4");
        cfg_set("ussd_cancel_flag","no");
    }
    else if(cusdParam.replyMsg == 4)
    {
        cfg_set("ussd_write_flag","99");
        cfg_set("ussd_cancel_flag","no");
    }
    else if(cusdParam.replyMsg == 5)
    {
        cfg_set("ussd_write_flag","4");
        cfg_set("ussd_cancel_flag","no");
    }
    else
    {
        cfg_set("ussd_write_flag","99");
        cfg_set("ussd_cancel_flag","no");
    }
	return AT_END;
}

//add corem0620 end

int cnum_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
    return AT_END;
}
//const char *at_paras
int cnum_auto_act( char *at_paras ,int is_query_report)
{
    T_zAt_CnumRes   tAtRes  = {0};
    CHAR tcnum[256] = {0};
    CHAR *mslsdm = NULL;
    UINT32 len=0;
	
    void *p[3] = {tAtRes.alpha, tAtRes.number, &tAtRes.type};
    at_print(AT_DEBUG,"cnum_auto_act at_paras=%s!\n",at_paras);
    parse_param2("%s,%s,%d", at_paras, p);
    at_print(AT_DEBUG,"cnum_auto_act alpha=%s,number=%s,type=%d!\n",
    tAtRes.alpha, tAtRes.number, tAtRes.type); 
	
	if(strlen(tAtRes.number) == 0){//corem20201209
		return AT_END;
	}

    cfg_get_item(NV_SIM_ICCID,tcnum,sizeof(tcnum));
    if(0 == strcmp("", tcnum))
    {
        cfg_set(NV_SIM_ICCID, tAtRes.number);
		cfg_set(NV_MSISDN, tAtRes.number);
    }
    else
    {
	    char *pos = strstr(tcnum, tAtRes.number);//corem20201209
		if(pos != NULL && (*(pos + strlen(tAtRes.number)) == '\0' || *(pos + strlen(tAtRes.number)) == ',')){
		    return AT_END;
		}
		
        len = strlen(tcnum)+strlen(tAtRes.number)+strlen(", ")+1;
        mslsdm = malloc(len);
	    if(mslsdm == NULL)
	    {
	        return AT_END;
	    }
        memset(mslsdm, 0, len); 
        snprintf(mslsdm, len,"%s, %s", tcnum, tAtRes.number);
        cfg_set(NV_SIM_ICCID, mslsdm);
		cfg_set(NV_MSISDN, mslsdm);
        free(mslsdm);
	    mslsdm = NULL;
    }
    return AT_END;
}

char* start_query_cnum(void *msg,struct at_context *context)
{
    at_print(AT_DEBUG,"start_query_cnum\n");
    return normal_CnumRead();
}

/* Э��ջ�̶���Ϣ��ȡ�¼���ZICCID CIMI CNUM  */
char *start_getmodeminfo(void *msg,struct at_context *context)
{
	return normal_getziccid();
}


char* start_pdpact_cmd(void *msg,struct at_context *context)
{
	char creg_stat[20] = {0};
	MSG_BUF *p_msg = (MSG_BUF *)msg;
    struct pdp_act_req *pdpsetinfo = NULL;
	char *pstr = NULL;

	cfg_get_item("creg_stat", creg_stat, sizeof(creg_stat));
	at_print(AT_DEBUG,"@@@@@@@@@@@@@start_pdpact_cmd creg stat=%s!\n",creg_stat);

	/*����������Ӧ�ã�����webui�����ء�TR069�����������ͨ����ҵ���modem������Ͽ���
	��pdp������Ϣ�в�Я���κβ�����pdp�������ʹ�õ�apn��ip type���û������������Ϣ
	��Ҫ��at_ctl�Լ�ȷ��*/
	if(p_msg->usDataLen == 0)
	{	
		//zhangfen for PDP		
		cfg_set("auto_reconnect", "1");		
		
	 	pdpsetinfo=malloc(sizeof(struct pdp_act_req));
        if(pdpsetinfo == NULL){
			softap_assert("");
			return NULL;
		}
        memset(pdpsetinfo,0,sizeof(struct pdp_act_req));
		//at_ctl���ݱ����Ĭ��pdp������Ϣ����װpdp������Ϣ���ݣ�����䵽��Ϣ��
        normal_getcurparam(pdpsetinfo);
		
		set_default_apn(pdpsetinfo->comm_info.apn);
		pdpsetinfo->default_flag = 1;//����ʹ��Ĭ�ϳ���
		memcpy(p_msg->aucDataBuf, pdpsetinfo, sizeof(struct pdp_act_req));
		p_msg->usDataLen = sizeof(struct pdp_act_req);
		pstr = pdp_act_func(p_msg,context); 
		free(pdpsetinfo);
	#if 0
		if(1 == atoi(creg_stat) || 5 == atoi(creg_stat))
	    {      
	    	//set_pppstatus(PPP_CONNECTING);
	       
	        memcpy(p_msg->aucDataBuf, pdpsetinfo, sizeof(struct pdp_act_req));
			p_msg->usDataLen = sizeof(struct pdp_act_req);
			pstr = pdp_act_func(p_msg,context); 	       
	    }
		else
		{		  	
			/*Ϊ���ٲ���Ҫ�Ĳ���at�������̣������ǰ�����㲦����������ֱ�ӱ���������Ӧ�ã�
			֪ͨ�䵱ǰ�޷�����*/
			send_rsp_msg(context->source,context->msg_id,MSG_CMD_PDP_ACT_RSP,0,0);
		}
#endif
		
	}
	else
	{
		/*��������Ӧ�õ�pdp����뱾��ҵ���йأ��������ؿ��ƣ�ͬʱ��ָ��apn��ip type����Ϣ�����
		��pdp����ʱ��Ҫ��pdp������ϢЯ����������at_ctlֱ�ӷ���pdp����*/
		pstr = pdp_act_func(msg,context);	
	}		
	return pstr;   
}

extern struct defcid_mng_t g_defcid_mng;

char* start_pdpdeact_cmd(void *msg,struct at_context *context)
{
    char pppStatus[50] = {0};
	MSG_BUF *p_msg = (MSG_BUF *)msg;
	AT_PDP_DEACT_REQ_INFO req = {0};
	char *pstr = NULL;
	
	/*����������Ӧ�ã�����webui�����ء�TR069���������ͨ����ҵ���modem������Ͽ���
	��pdpȥ����ʱ����ָʾat_ctl�Ͽ�modem����������Ͽ���cid����at_ctl�����Ĭ��cid����*/
	if(p_msg->usDataLen == 0)
	{
		cfg_get_item("ppp_status", pppStatus, sizeof(pppStatus));
	    if((strcmp("ppp_disconnecting", pppStatus)== 0) || (strcmp("ppp_disconnected", pppStatus) == 0))
	    {
	    	AT_PDP_DEACT_RSP_INFO rsp = {0};
			rsp.result = AT_RSP_OK;
			send_rsp_msg(context->source,context->msg_id,MSG_CMD_PDP_DEACT_RSP,sizeof(AT_PDP_DEACT_RSP_INFO),&rsp);
	        return NULL;		
	    }
	    if(strcmp("ppp_connecting", pppStatus)== 0)
	    {
			at_print(AT_ERR,"start_pdpdeact_cmd is acting ,start timer to wait\n");
			add_one_delayed_msg(msg);
	        return NULL;		
	    }
		
		set_pppstatus(PPP_DISCONNECTING);
	
		req.c_id = g_defcid_mng.cid;
		memcpy(p_msg->aucDataBuf, &req, sizeof(AT_PDP_DEACT_REQ_INFO));
		p_msg->usDataLen = sizeof(AT_PDP_DEACT_REQ_INFO);
		pstr = pdp_deact_func(p_msg, context);
	}
	else
	{
		pstr = pdp_deact_func(msg, context);
	}
	return pstr;	
}

//������оƬ��֤����
char* start_zeact_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
	
	at_next=malloc(20);
	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	snprintf(at_next,20,"AT+ZEACT=%s\r\n",((MSG_BUF*)msg)->aucDataBuf); 
	return at_next;
	
}

char* start_zmmi_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
	
	at_next=malloc(20);
	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	snprintf(at_next,20,"AT+ZMMI=%s\r\n",((MSG_BUF*)msg)->aucDataBuf); 
	return at_next;
	
}

char* start_zimsamrw_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
	
	at_next=malloc(20);
	if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	snprintf(at_next,20,"AT+ZIMSAMRW=%s\r\n",((MSG_BUF*)msg)->aucDataBuf); 
	return at_next;
	
}

int  cmd_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
	*next_req=malloc(10);
	if(*next_req){
    *next_len=10;
    strcpy(*next_req,"1");
	}
    return AT_END;
}

int  cmd_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len)
{
	*next_req=malloc(10);
	if(*next_req){
    *next_len=10;
    strcpy(*next_req,"0");
	}
    return AT_END;
}

/**
 * @brief �������ܶ��Ž���
 * @param 
 * @return 
 * @note   
 * @warning 
 */
void at_ctl_startisms(void)
{
	at_print(AT_NORMAL,"[TEST]at_ctl_startisms\n");
#if (APP_OS_TYPE == APP_OS_LINUX)
/*
	if(fork() == 0)
	{
		if(execv("/bin/zte_isms", NULL) < 0)
		{
			at_print(AT_ERR,"[TEST]execv error(%d)\n", errno);
			exit(0);
		}
	}
*/
	if(system("/bin/zte_isms &") != 0)
	{
		at_print(AT_ERR,"[TEST]execv error(%d)\n", errno);
	}
#endif	
}

int atd_req_rcv_act( char *at_paras,int at_fd,struct at_context *context)
{
	if(is_ppp_dial_atd(at_paras, at_fd) == 0)
		return AT_END;
	else
		return AT_CONTINUE;//��ͨ��ATD���ֱ��ת��
}

char* start_cvmod_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CVMOD=0\r\n");
    
	return at_next;
}

char* start_zchnelset_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
    at_next=malloc(40);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,40);
	sprintf(at_next,"AT+ZCHNELSET=0,37,38,39,40,41\r\n");
    
	return at_next;
}

char *start_querycgdcont_cmd(void *msg,struct at_context *context)
{
	char *at_next=NULL;
    at_next=malloc(20);
    if(at_next == NULL){
		softap_assert("");
		return NULL;
	}
    memset(at_next,0,20);
	sprintf(at_next,"AT+CGDCONT?\r\n");
	return at_next;
}

//bsim
static int bs_aes_init_key(unsigned char *aes_key, int k_len)
{
	int efuse_fd = -1;
	T_ZDrvEfuse_Secure efuse = {0};
	
	memset(&efuse, 0, sizeof(efuse));
	efuse_fd = open("/dev/efuse", O_RDWR);
	if (efuse_fd < 0) {
		printf("wifi_aes_init_key efuse open errno=%d\n", errno);
		return 0;
	}
	if(ioctl(efuse_fd , EFUSE_GET_DATA, &efuse) != 0) {
		printf("wifi_aes_init_key efuse ioctl errno=%d\n", errno);
		close(efuse_fd);
		return 0;
	}
	close(efuse_fd);
	memcpy(aes_key, efuse.pubKeyHash, k_len);
	
	return 1;
}

static int bs_aes_encrypt(char* in, int len, char* out, unsigned char* key, int key_len)
{
    if (!in || !out || !key || len <=0 || (len%AES_BLOCK_SIZE)!=0 || (key_len!=16 && key_len!=24 && key_len!=32)) {
		printf("bs_aes_encrypt err in=%p out=%p key=%p len=%d key_len=%d\n",in,key,out,len,key_len);
        return 0;
    }
 
    AES_KEY aes = {0}; //cov h
    if (AES_set_encrypt_key(key, key_len*8, &aes) < 0) {
		printf("bs_aes_encrypt AES_set_encrypt_key err\n");
        return 0;
    }
 
    int en_len = 0;
    while (en_len < len) {
        AES_encrypt((unsigned char*)in, (unsigned char*)out, &aes);
        in	+= AES_BLOCK_SIZE;
        out += AES_BLOCK_SIZE;
        en_len += AES_BLOCK_SIZE;
    }
 
    return 1;
}

static int bs_aes_decrypt(char* in, int len, char* out, char* key, int key_len)
{
    if (!in || !out || !key || len <=0 || (len%AES_BLOCK_SIZE)!=0 || (key_len!=16 && key_len!=24 && key_len!=32)) {
		printf("bs_aes_decrypt err in=%p out=%p key=%p len=%d key_len=%d\n",in,key,out,len,key_len);
        return 0;
    }
 
    AES_KEY aes = {0}; //cov h
    if (AES_set_decrypt_key((unsigned char*)key, key_len*8, &aes) < 0) {
		printf("bs_aes_decrypt AES_set_decrypt_key err\n");
        return 0;
    }
 
    int en_len = 0;
    while (en_len < len) {
        AES_decrypt((unsigned char*)in, (unsigned char*)out, &aes);
        in	+= AES_BLOCK_SIZE;
        out += AES_BLOCK_SIZE;
        en_len += AES_BLOCK_SIZE;
    }
 
    return 1;
}

static int apn_encrypt_code(void)
{
	char w_code[PROFILE_APN_LEN] = {0};
	char b_aes[PROFILE_APN_LEN] = {0};
	char s_aes[PROFILE_APN_AES_LEN] = {0};

	cfg_get_item("ppp_passtmp", w_code, sizeof(w_code));
	bs_aes_encrypt(w_code, PROFILE_APN_LEN - 1, b_aes, atctl_aes_key, sizeof(atctl_aes_key));
	bytes2string(b_aes, s_aes, PROFILE_APN_LEN - 1); //libatutils
	cfg_set("ppp_passwd", s_aes);
	printf("apn_encrypt_code w_code=%s, s_aes=%s\n", w_code, s_aes);

	return 1;
}

static int ipv6apn_encrypt_code(void)
{
	char w_code[PROFILE_APN_LEN] = {0};
	char b_aes[PROFILE_APN_LEN] = {0};
	char s_aes[PROFILE_APN_AES_LEN] = {0};

	cfg_get_item("ipv6_ppp_passtmp", w_code, sizeof(w_code));
	bs_aes_encrypt(w_code, PROFILE_APN_LEN - 1, b_aes, atctl_aes_key, sizeof(atctl_aes_key));
	bytes2string(b_aes, s_aes, PROFILE_APN_LEN - 1);
	cfg_set("ipv6_ppp_passwd", s_aes);
	printf("ipv6apn_encrypt_code w_code=%s, s_aes=%s\n", w_code, s_aes);

	return 1;
}

//2��ʾfota���ϰ汾����������ppp_password������������룬�°汾������
static int apn_decrypt_code(void)
{
	char w_code[PROFILE_APN_LEN]= {0};
	char b_aes[PROFILE_APN_LEN] = {0};
	char s_aes[PROFILE_APN_AES_LEN] = {0};
	int flag = 0;

	cfg_get_item("ppp_passwd", s_aes, sizeof(s_aes));
	if (strlen(s_aes) == (PROFILE_APN_AES_LEN - 1)) {
		string2bytes(s_aes, b_aes, PROFILE_APN_AES_LEN - 1);
		bs_aes_decrypt(b_aes, PROFILE_APN_LEN - 1, w_code, atctl_aes_key, sizeof(atctl_aes_key));
		cfg_set("ppp_passtmp", w_code);
//		printf("apn_decrypt_code w_code=%s, s_aes=%s\n", w_code, s_aes);
	} else if (strlen(s_aes) > 0){
		cfg_set("ppp_passtmp", s_aes);
		return 2;
	}

	return 1;
}

static int ipv6apn_decrypt_code(void)
{
	char w_code[PROFILE_APN_LEN]= {0};
	char b_aes[PROFILE_APN_LEN] = {0};
	char s_aes[PROFILE_APN_AES_LEN] = {0};
	int flag = 0;

	cfg_get_item("ipv6_ppp_passwd", s_aes, sizeof(s_aes));
	if (strlen(s_aes) == (PROFILE_APN_AES_LEN - 1)) {
		string2bytes(s_aes, b_aes, PROFILE_APN_AES_LEN - 1);
		bs_aes_decrypt(b_aes, PROFILE_APN_LEN - 1, w_code, atctl_aes_key, sizeof(atctl_aes_key));
		cfg_set("ipv6_ppp_passtmp", w_code);
//		printf("apn_decrypt_code w_code=%s, s_aes=%s\n", w_code, s_aes);
	} else if (strlen(s_aes) > 0){
		cfg_set("ipv6_ppp_passtmp", s_aes);
		return 2;
	}

	return 1;
}

int atctl_aes_init(void)
{
	bs_aes_init_key(atctl_aes_key, sizeof(atctl_aes_key));
	
	if (2 == apn_decrypt_code())
		apn_encrypt_code();
	if (2 == ipv6apn_decrypt_code())
		ipv6apn_encrypt_code();

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
        cfg_get_item("sim_select",simSelect,sizeof(simSelect));
        cfg_get_item("prev_sim_select",prevSimSelect,sizeof(prevSimSelect));
        cfg_get_item("sim_pre_mode",simPreMode,sizeof(simPreMode));

        at_print(AT_DEBUG,"%s: salvikie prevSimSelect [%s] simSelect [%s] simPreMode [%s] \n", __func__, prevSimSelect, simSelect, simPreMode);
#endif
	return 1;
}


