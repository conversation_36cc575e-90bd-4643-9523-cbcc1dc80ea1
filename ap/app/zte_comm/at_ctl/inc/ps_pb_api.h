#ifndef __PS_PB_API_H__
#define __PS_PB_API_H__
#include "at_context.h"

char* atPb_SendScpbrReq(void *param,  struct at_context *context);

char* atPb_SendScpbwReq(void *param,  struct at_context *context);

char* atPb_SendScpbwDelReq(void *param,  struct at_context *context);

char* atPb_SendCpbsReq(void *param,  struct at_context *context);

int atPb_RecvCpbsOk(char *at_str,struct at_context *context,void **next_req,int *next_len);

int atPb_RecvCpbsQueryOk(char *at_str,struct at_context *context,void **next_req,int *next_len);

int atPb_RecvOk(char *at_str,struct at_context *context,void **next_req,int *next_len);

int atPb_RecvErr(char *at_str,struct at_context *context,void **next_req, int *next_len);

int atPb_RecvScpbrErr(char *at_str,struct at_context *context,void **next_req, int *next_len);

int atPb_RecvCpbsRsp(char *at_str ,int is_query_report);

int atPb_RecvScpbsRsp(char *at_str ,int is_query_report);

int atPb_RecvScpbsReadRsp(char *at_str ,int is_query_report);

int atPb_RecvZpbicRsp( char *at_str ,  void **res_msg,  int *res_msglen, struct at_context *context);

int atPb_RecvZuslotRsp( char *at_str ,  void **res_msg,  int *res_msglen, struct at_context *context);
#endif

