#ifndef __PS_WIFI_H__
#define __PS_WIFI_H__

#include "at_msg.h"
#include "at_com.h"
#include "at_context.h"
#include "ps_normal.h"

char* normal_getcsimset(char *pdu);

char* normal_getcardmode();

char *normal_getssidset();

char *normal_getwifikeyset();


char *start_csimauth_cmd(void *msg,struct at_context *context);

char *start_cardmode_cmd(void *msg,struct at_context *context);

char *start_pinstatus_cmd(void *msg,struct at_context *context);

char *start_pinnum_cmd(void *msg,struct at_context *context);

char *start_crsmreq_cmd(void *msg,struct at_context *context);

char *start_cimi_cmd(void *msg,struct at_context *context);

char *start_getmac_cmd(void *msg,struct at_context *context);

char *start_setssid_cmd(void *msg,struct at_context *context);

char *start_setwifikey_cmd(void *msg,struct at_context *context);

int  csim_set_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int csim_auto_act( char *at_paras ,int is_query_report);

int  cardmode_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int cardmode_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int cardmode_auto_act( char *at_paras ,int is_query_report);

int  ssid_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int  ssid_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int  wifikey_ok_act(char *at_str,struct at_context *context,void **next_req,int *next_len);

int  wifikey_err_act(char *at_str,struct at_context *context,void **next_req,int *next_len);


#endif
