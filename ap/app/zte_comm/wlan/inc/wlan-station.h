#ifndef _WLAN_STATION
#define _WLAN_STATION

#include <linux/rtc.h>
#include <sys/ioctl.h>
#include <stdio.h>
#include <errno.h>
#include <string.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <netinet/in.h>
#include <unistd.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <pthread.h>
#include <syslog.h>
#include <signal.h>
#include <sys/time.h>
#include <time.h>
#include "../include/softap_api.h"
//#include "../include/errorcode.h"
//#include "../include/netapi.h"

void   wlan_station_init(void);
BOOL wlan_station_is_station_msg(unsigned short msg);
void   wlan_station_msg_handle(MSG_BUF *pMsg);
void Delete_connect_timeout_timer(void);
void wifi_station_close();

void  wlan_statemachine_init();
void  wlan_station_open();

#endif

