#ifndef __WLAN_RTC_SLEEP__
#define __WLAN_RTC_SLEEP__




int disable_rtc_tsw_close_timer();
int disable_rtc_tsw_open_timer();
int create_rtc_tsw_close_timer (ULONG ulSec);
int create_rtc_tsw_open_timer (ULONG ulSec);
void handle_tsw_setting();
void wlan_reset_sleep_timer();
 void check_to_presleep (int wifi_status, int charging_status, int sta_num, int wps_status);

void cancel_all_timer();
void wlan_prepare_sleep();

 LONG create_rtc_wps_timer (int ulSec);
int  disable_rtc_sleep_timer();
int disable_rtc_wps_timer();
void handle_tsw_close();

#endif
