#ifndef RDA_SM_H
#define RDA_SM_H

#include "wifi_socket.h"
#include "wifi_drv_ko.h"

#define  WIRI_ROOT_DIR		"/etc_rw"
#define  SUPPLICANT_CONF		WIRI_ROOT_DIR"/wifi/wpa_supplicant.conf"
#define WPA_PID_FILE   		WIRI_ROOT_DIR"/wifi/wpa_file.pid"

struct   wlan_sta_manager {

	struct wlan_socket  sock;
	struct wlan_drv_proxy  drv_proxy;

	void 		(*init)(struct   wlan_sta_manager *sta_ctrl);
	int 		(*start_supplicant)(struct   wlan_sta_manager *sta_ctrl);
	int 		(*stop_supplciant)(struct   wlan_sta_manager *sta_ctrl);
	void 		 (*close_connection)(struct   wlan_sta_manager *sta_ctrl);
	void		(*scan)(struct   wlan_sta_manager *sta_ctrl);
};







#endif
