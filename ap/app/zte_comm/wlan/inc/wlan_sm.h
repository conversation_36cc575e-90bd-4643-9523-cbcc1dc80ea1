/******************************************************************************
 *
 *  Copyright (C) 2009-2012 Broadcom Corporation
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at:
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 ******************************************************************************/

/*****************************************************************************
 *
 *  Filename:      wlan_sm.h
 *
 *  Description:   Generic wlan state machine API
 *
 *****************************************************************************/

#ifndef WLAN_SM_H
#define WLAN_SM_H

/*****************************************************************************
**  Constants & Macros
******************************************************************************/

/* Generic Enter/Exit state machine events */
//#define WLAN_SM_ENTER_EVT 0xFFFF
//#define WLAN_SM_EXIT_EVT  0xFFFE

typedef enum{
	WF_STATUS_SUCCESS,
	WF_STATUS_FAIL,
	WF_STATUS_UNHANDLED,
}wf_status_t;

typedef enum{ 
	WF_FALSE, 
	WF_TRUE, 
} Boolean;

/*****************************************************************************
**  Type definitions and return values
******************************************************************************/
typedef enum{
	WLAN_SM_STATE_CLOSED,
	WLAN_SM_STATE_DISCONNECT,
	WLAN_SM_STATE_CONNECTING,
	WLAN_SM_STATE_DHCPING,
	WLAN_SM_STATE_CONNECTED,
	WLAN_SM_STATE_CLOSING,
}wlan_sm_state_t;

typedef enum{
	WLAN_SM_CMD_OPEN,   //0
	WLAN_SM_CMD_CLOSE,		//1
	WLAN_SM_CMD_CONNECT,//2
	WLAN_SM_CMD_DISCONNECT,	//3
	WLAN_SM_CMD_FORGET,//4
	WLAN_SM_CMD_SCAN,//5
	
	WLAN_SM_EVT_SCAN_RESULTS = 10,//5
	WLAN_SM_EVT_SCAN_FAILED,//6
	WLAN_SM_EVT_STATE_CHANGE,
//7
	WLAN_SM_EVT_ASSOCATING,//8
	WLAN_SM_EVT_BSS_ADDED,//9
	WLAN_SM_EVT_BSS_REMOVED,//10
	WLAN_SM_EVT_NETWORK_NOT_FOUND,//11
	WLAN_SM_EVT_SIGNAL_CHANGE,//12
	WLAN_SM_EVT_CHANNEL_SWITCH,//13
	WLAN_SM_EVT_SSID_TEMP_DISABLED,//14
	WLAN_SM_EVT_SSID_REENABLED,//15
	WLAN_SM_EVT_DHCP_FAIL,//16
	WLAN_SM_EVT_CONNECTED,//17
	WLAN_SM_EVT_DISCONNECTED,//18
	WLAN_SM_EVT_CONNECTING,//19
	WLAN_SM_EVT_IP_CONNECTED,//20
	WLAN_SM_EVT_ASSOC_REJECT,
	WLAN_SM_EVT_AUTH_REJECT,
	WLAN_SM_EVT_TERMINATING,
	WLAN_SM_ENTER_EVT=0xFFFE,
	WLAN_SM_EXIT_EVT=0xFFFF,
}wlan_sm_event_t;

//typedef unsigned int wlan_sm_state_t;
//typedef unsigned int wlan_sm_event_t;
typedef void* wlan_sm_handle_t;
typedef int (*wlan_sm_handler_t)(wlan_sm_event_t event, void *data);


/*****************************************************************************
**  Functions
**
**  NOTE: THESE APIs SHOULD BE INVOKED ONLY IN THE wlan CONTEXT
**
******************************************************************************/

/*****************************************************************************
**
** Function     wlan_sm_init
**
** Description  Initializes the state machine with the state handlers
**              The caller should ensure that the table and the corresponding
**              states match. The location that 'p_handlers' points to shall
**              be available until the wlan_sm_shutdown API is invoked.
**
** Returns      Returns a pointer to the initialized state machine handle.
**
******************************************************************************/
wlan_sm_handle_t wlan_sm_init(const wlan_sm_handler_t *p_handlers,
                               wlan_sm_state_t initial_state);

/*****************************************************************************
**
** Function     wlan_sm_shutdown
**
** Description  Tears down the state machine
**
** Returns      None
**
******************************************************************************/
void wlan_sm_shutdown(wlan_sm_handle_t handle);

/*****************************************************************************
**
** Function     wlan_sm_get_state
**
** Description  Fetches the current state of the state machine
**
** Returns      Current state
**
******************************************************************************/
wlan_sm_state_t wlan_sm_get_state(wlan_sm_handle_t handle);

/*****************************************************************************
**
** Function     wlan_sm_dispatch
**
** Description  Dispatches the 'event' along with 'data' to the current state handler
**
** Returns      Returns BT_STATUS_OK on success, BT_STATUS_FAIL otherwise
**
******************************************************************************/
wf_status_t wlan_sm_dispatch(wlan_sm_handle_t handle, wlan_sm_event_t event,
                                void *data);

/*****************************************************************************
**
** Function     wlan_sm_change_state
**
** Description  Make a transition to the new 'state'. The 'WLAN_SM_EXIT_EVT'
**              shall be invoked before exiting the current state. The
**              'WLAN_SM_ENTER_EVT' shall be invoked before entering the new state
**
**
** Returns      Returns BT_STATUS_OK on success, BT_STATUS_FAIL otherwise
**
******************************************************************************/
wf_status_t wlan_sm_change_state(wlan_sm_handle_t handle, wlan_sm_state_t state);

#endif /* WLAN_SM_H */
