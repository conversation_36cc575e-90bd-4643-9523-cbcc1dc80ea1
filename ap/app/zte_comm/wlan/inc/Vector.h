#ifndef __VECTOR_H__
#define __VECTOR_H__

#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include <errno.h>
#include <assert.h>

#define VECTOR_DEFAULT_CAPACITY (100)

typedef enum
{
    TYPE_UNDEFINED,
    IS_LONG,
    IS_FLOAT,
    IS_STRING
} Type;

typedef struct var
{
    Type type;
    union
    {
        long lval;
        float fval;
        struct
        {
            size_t len;
            char* sval;
        } str;
    } val;
}Var;

typedef struct vector
{
    size_t size;
    size_t capacity;
    Var** data;
} vector_t;

Var* var_long(long value);
Var* var_float(float value);
Var* var_string(char* value);
void var_print(Var* var);
void var_destroy(Var* var);
const char* const var_type_of_token(Type t);

vector_t* vector_init();
void vector_push_back(vector_t*, Var*);
size_t vector_size(vector_t*);
Var* vector_get(vector_t*, size_t);
void vector_free(vector_t*);

#endif /* __VECTOR_H__ */
