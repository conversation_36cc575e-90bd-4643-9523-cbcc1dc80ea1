#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/time.h>
#include <getopt.h>
#include <time.h>
#include <wifi_msg.h>
#include "softap_api.h"
#include <wifi_util.h>


#include "wifi_util.h"
/*
static char * g_short_string = "u:b:s:d:h";

static struct option g_long_options[] = {
	{"upgrade",   		required_argument,	NULL,	'u'},
	{"boot",    		required_argument,	NULL,	'b'},
	{"status",   		required_argument,	NULL,	's'},
	{"debug",   		required_argument,	NULL,	'd'},
	{"help",			no_argument,		NULL,	'h'},
};

static option_handle_t g_option_handle[] = {
	{'u',		excute_command_upgrade},
	{'b',		excute_command_boot},
	{'s',		excute_command_status},
	{'d',		excute_command_debug},
	{'h',		excute_command_help}
};

*/

int  msg_queue_id = 0;


void usage (void)
{
	printf ("cmd list: "
			"  open		open wifi\n"
			"  close	close wifi\n"
			"  results	display the scan results\n"
	         "  mmi		display scan list to simulate watch mmi\n"
	         "  help	dispaly this message\n"
             "  scan	send scan cmd\n"			 
             "  conn	connect the ap specified by the index of scan list ,and need input the password\n"			 
	         "  forget	disconnect and forget the ap\n"
	         "  dump	dump the content of the global variable in wlan_manager\n"
	         "  cmd 	you can send a wpa_supplicant cmd directly\n");
}

wlan_mac_info_list g_mac_list;

static void process_msg(MSG_BUF *pMsg)
{
	switch (pMsg->usMsgCmd) {
		case   MSG_CMD_WIFI_SET_ON_RSP:
		{
			printf("MSG_CMD_WIFI_SET_ON_RSP\n");
			break;
		}
		case  MSG_CMD_WIFI_SET_OFF_RSP:
		{
			printf("MSG_CMD_WIFI_SET_OFF_RSP\n");
			break;
		}

		case  MSG_CMD_WIFI_SET_SECURITY_RSP:
		{
			printf("MSG_CMD_WIFI_SET_SECURITY_RSP\n");

			break;
		}

		case  MSG_CMD_WIFI_GET_USER_LIST_RSP:
		{
			wlan_mac_info_list *list = (wlan_mac_info_list*)pMsg->aucDataBuf;
			printf("MSG_CMD_WIFI_GET_USER_LIST_RSP\n");
			printf("hostname=%s,mac=%s",list->mac_info[0].hostname,list->mac_info[0].mac );
			memcpy(&g_mac_list, &pMsg->aucDataBuf, sizeof(wlan_mac_info_list));
			break;
		}

		case  MSG_CMD_WIFI_GET_STATE_RSP:
		{
			printf("MSG_CMD_WIFI_GET_STATE_RSP\n");
			break;
		}
		case  MSG_CMD_WIFI_GET_INFO_RSP:
		{
			printf("MSG_CMD_WIFI_GET_INFO_RSP\n");
			break;
		}
		default:
			break;
	}

}

void dump_spot_list()
{
	int i = 0;
	printf("%-8s%-32s%-20s\n","id","hostname","mac");
	for(i = 0; i< g_mac_list.access_count; i++){
		wlan_mac_info  *info = &g_mac_list.mac_info[i];
		printf("%-8d%-32s%-20s\n",i, info->hostname, info->mac);
	}
}

int mmi_display_loop = 0;



static void *mmi_loop (void *param)
{
	MSG_BUF wlanMsg ={0};
	char name[32] = {0};
	int ret = -1;

	strcpy (name, (char*) param);
	prctl (PR_SET_NAME, name, 0, 0, 0);

	while (1) { 
		if(0 == mmi_display_loop)break;
		dump_spot_list();
		sleep(1);
		printf("\033[2J");//clear screen
		printf("\033[0;0H");//locate cursor to head
	}

	return NULL;
}
void mmi_display()
{
	mmi_display_loop = 1;

	wf_create_thread ("mmi_loop", mmi_loop);

	while(1){
		char cmd;
		scanf("%c", &cmd);
		if(cmd == 'q'){
			mmi_display_loop = 0;
			break;
		}
	}
	
	return;
}

static void *station_loop (void *param)
{
	MSG_BUF wlanMsg ={0};
	char name[32] = {0};
	int ret = -1;

	strcpy (name, (char*) param);
	prctl (PR_SET_NAME, name, 0, 0, 0);

	while (1) { 
		memset (&wlanMsg, 0, sizeof (MSG_BUF));
		ret = msgrcv (msg_queue_id, &wlanMsg, sizeof (MSG_BUF) - sizeof (LONG), 0, 0);
		if (ret == -1) {
			continue;
		}
		process_msg (&wlanMsg);
		
	}

	return NULL;
}


int main (int argc, char *argv[])
{
	int c=0;
	int iRet = -1;
	char cmd[128]={0};
	
	msg_queue_id = wf_create_msg_qid (MODULE_ID_WLAN_SERVER);
	wf_create_thread ("ap-mmi", station_loop);
	printf("rda_ap_mmi v1.0\n"
		"please use results, conn, disc three cmd to control it\n\n");
	
	for (;;) {

		printf(">>");
		scanf("%s", cmd);

		if(!strcmp(cmd, "open")){
			wf_msg_to_self(MSG_CMD_WIFI_SET_ON_REQ, 0, NULL);
		}
		else if(!strcmp(cmd, "close")){
			wf_msg_to_self(MSG_CMD_WIFI_SET_OFF_REQ, 0, NULL);
		}
		else if(!strcmp(cmd, "auth")){
			wlan_basic_info wbi={0};
			printf("ssid=");
			scanf("%s", wbi.ssid);
			printf("authmode=");
			scanf("%s", wbi.authmode);
			printf("pwd=");
			scanf("%s", wbi.pwd);
			
			wf_msg_to_self(MSG_CMD_WIFI_SET_SECURITY_REQ, sizeof(wlan_basic_info), &wbi);
		}
		else if(!strcmp(cmd, "list")){			
			wf_msg_to_self(MSG_CMD_WIFI_GET_USER_LIST_REQ, 0, NULL);
		}

		else if(!strcmp(cmd, "mmi")){
			mmi_display();
		}
		else if(!strcmp(cmd, "help")){
			usage();
		}
		else if(!strcmp(cmd, "exit") || !strcmp(cmd, "q")){
			exit(0);
		}

		printf("done\n");
	}


	return 0;
}
