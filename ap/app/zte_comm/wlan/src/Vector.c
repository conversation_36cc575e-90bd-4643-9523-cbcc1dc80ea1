/*
 *	Vector.c -- C vector implementation 
 *
 *	Copyright (c) ZTE Corporation All Rights Reserved.
 *
 *	$Id: Vector.c, v 0.1 2015-03-09 $
 *   Authors :	XUJIAN - <EMAIL>>
 */

#include "Vector.h"

/* PRIVATE, read only */
static const char const *type_string[] = {
    "IS_LONG",
    "IS_FLOAT",
    "IS_STRING"
};

Var* var_long(long value)
{
    Var* var = malloc(sizeof(Var));

    if(var == NULL) 
    {
        //fprintf(stderr, "%s\n", strerror(errno));//unsafe
        //fprintf(stderr, "%d\n", errno);
        //exit(EXIT_FAILURE);
        assert(var);
    } 
    else 
    {
        var->type = IS_LONG;
        var->val.lval = value;
    }

    return var;
}

Var* var_float(float value)
{
    Var* var = malloc(sizeof(Var));

    if(var == NULL)
    {
        //fprintf(stderr, "%s\n", strerror(errno));//unsafe
        //fprintf(stderr, "%d\n", errno);
        //exit(EXIT_FAILURE);
        assert(var);
    }
    else
    {
        var->type = IS_FLOAT;
        var->val.fval = value;
    }

    return var;
}

Var* var_string(char* value)
{
    if(value == NULL)
    {
        return NULL;
    }
    
    Var* var = malloc(sizeof(Var));

    if(var == NULL) 
    {
        //fprintf(stderr, "%s\n", strerror(errno));//unsafe
        //fprintf(stderr, "%d\n", errno);
        //exit(EXIT_FAILURE);
        assert(var);
    } 
    else 
    {
        size_t len = strlen(value);

        var->type = IS_STRING;
        var->val.str.len = len;
        var->val.str.sval = malloc(len + 1);

        if(var->val.str.sval == NULL) 
        {
            //fprintf(stderr, "%s\n", strerror(errno));//unsafe
            //fprintf(stderr, "%d\n", errno);
            //exit(EXIT_FAILURE);
            assert(var->val.str.sval);
        } 
        else 
        {
            memset(var->val.str.sval, 0, len + 1);
            memcpy(var->val.str.sval, value, len + 1);
        }
    }

    return var;
}

void var_print(Var* var)
{
    if(var == NULL)
    {
        return;
    }

    switch(var->type) 
    {
        case IS_LONG:
        {
            fprintf(stdout, "%ld", var->val.lval);
        }
        break;
        case IS_FLOAT:
        {
            fprintf(stdout, "%f", var->val.fval);
        }
        break;        
        case IS_STRING:
        {
            fprintf(stdout, "%s", var->val.str.sval);
        }
        break;      
        default:
        {
            fprintf(stdout, "%s\n", "Var not found. This should not be happening!");
        }
        break; 
    }
}

void var_destroy(Var* var)
{
    if(var == NULL)
    {
        return;
    }

    switch(var->type)
    {
        case IS_LONG:
        {
            free(var);
            var = NULL;
        }
        break; 
        case IS_FLOAT:
        {
            free(var);
            var = NULL;
        }
        break;
        case IS_STRING:
        {
            if(var->val.str.sval != NULL)
            {
                free(var->val.str.sval);
                var->val.str.sval = NULL;
            }
            free(var);
            var = NULL;
        }
        break;
        default:
        {
        	free(var);//kw 3
            var = NULL;
            fprintf(stdout, "%s\n", "Var not found. This should not be happening!");
        }
        break;
    }
}

const char* const var_type_of_token(Type t)
{
    if(t < sizeof(type_string) / sizeof(type_string[0])) 
    {
        return type_string[t];
    } 
    else 
    {
        return NULL;
    }
}




vector_t* vector_init()
{
    vector_t* v = malloc(sizeof(vector_t));

    if(v == NULL)
    {
        //fprintf(stdout, "%s\n", "vector_init: vector_t init memory allocation failure");
        //exit(EXIT_FAILURE);
        assert(v);
    }

    v->size = 0;
    v->capacity = VECTOR_DEFAULT_CAPACITY;
    v->data = malloc(v->capacity * sizeof(Var*));

    if(v->data == NULL)
    {
        //fprintf(stdout, "%s\n", "vector_init: v->data memory allocation error");
        //exit(EXIT_FAILURE);
        assert(v->data);
    }

    return v;		
}

/*
 * vector_push_back
 * Resize the array if needed, else increase the size
 * and push the element onto the back of the stack
 */
void vector_push_back(vector_t* v, Var* element)
{
    if(v == NULL || element == NULL)
    {
        return;
    }
    
    if(v->size == v->capacity)
    {
        v->capacity *= 2;
        v->data = realloc(v->data, v->capacity * sizeof(Var*));

        if(v->data == NULL) 
        {
            //fprintf(stdout, "%s \n", "vector_push_back: failed to resize array");
            //exit(EXIT_FAILURE);
            assert(v->data);
        }

        v->data[v->size] = element;
        v->size++;	
    } 
    else
    {
        v->data[v->size] = element;
        v->size++;
    }
}

/*
 * vector_size
 * Utility function to return the vector size
 */
size_t vector_size(vector_t* v)
{
    if(v == NULL)
    {
        return 0;
    }
    else
    {
        return v->size;
    }
}

/* 
 * vector_get
 * get an element at the index, but also check that it is possible
 * to retrieve by making sure the index is less than the size
 * the max index value is v->size - 1
 */
Var* vector_get(vector_t* v, size_t index)
{
    if(v == NULL)
    {
        return NULL;
    }
    
    if(index < v->size) 
    {
        return v->data[index];
    } 
    else 
    {
        //fprintf(stdout, "vector_get: fatal error: undefined offset %zu\n", index);
        //exit(EXIT_FAILURE);
        assert(index < v->size);
    }

    return NULL;
}

/*
 * vector_free
 * free all elements in the dynamic array of strings
 * and the struct itself
 */
void vector_free(vector_t* v)
{
    size_t i;

    if(v == NULL)
    {
        return;
    }

    for(i = 0; i < v->size; i++) 
    {
        var_destroy(v->data[i]);
    }

    if(v->data != NULL)
    {
        free(v->data);
        v->data = NULL;
    }

    free(v);
    v = NULL;
}
