/*
 *  wlan-station.c -- wifi station mode Deal module
 *
 *  Copyright (c) ZTE Corporation All Rights Reserved.
 *
 *  $Id: wlan-station.c, v 0.1 2015-03-09 $
 *   Authors :  XUJIAN - <EMAIL>>
 */
#include "wifi_util.h"
#include "wifi_socket.h"
#include "wifi_sta_ctrl.h"
#include "wifi_drv_ko.h"

#if (defined(USE_CAP_SUPPORT) || defined(WIFI_UNCOEXIST_5G))
extern int g_work_mode;
void reset_wpa_supplicant_conf (void)
{
	char conf_srt[360] = {0};
	char country_code[6] = {0};
	cfg_get_item ("CountryCode", country_code, sizeof(country_code) - 1);
	if (0 == strcmp(country_code, "NONE")) {
		strcpy(country_code, "00");
	}
	if (g_work_mode == IN_WIFI_WORK_MODE_AP1) {
		snprintf(conf_srt, sizeof(conf_srt), "##### wpa_supplicant configuration file template #####\n\
			ctrl_interface=/etc_rw/wifi/sockets\n\
			update_config=1\n\
			pmf=1\n\
			sae_pwe=2\n\
			eapol_version=1\n\
			ap_scan=1\n\
			country=%s\n\
			freq_list=5180 5200 5220 5240 5260 5280 5300 5320 5500 5520 5540 5560 5580 5600 5620 5640 5660 5680 5700 5720 5745 5765 5785 5805 5825\n",
			country_code);
	}
	else if (g_work_mode == IN_WIFI_WORK_MODE_STA) {
		snprintf(conf_srt, sizeof(conf_srt), "##### wpa_supplicant configuration file template #####\n\
			ctrl_interface=/etc_rw/wifi/sockets\n\
			update_config=1\n\
			pmf=1\n\
			sae_pwe=2\n\
			eapol_version=1\n\
			ap_scan=1\n\
			country=%s\n",
			country_code);
	}
	else {
		snprintf(conf_srt, sizeof(conf_srt), "##### wpa_supplicant configuration file template #####\n\
			ctrl_interface=/etc_rw/wifi/sockets\n\
			update_config=1\n\
			pmf=1\n\
			sae_pwe=2\n\
			eapol_version=1\n\
			ap_scan=1\n\
			country=%s\n\
			freq_list=2412 2417 2422 2427 2432 2437 2442 2447 2452 2457 2462 2467 2472 2484\n",
			country_code);
	}
	wlan_write_file(SUPPLICANT_CONF, conf_srt);
}
#endif
void check_wpa_supplicant_conf (void)
{
	//char  *file = SUPPLICANT_CONF;
/*	char * content = "##### wpa_supplicant configuration file template #####\n"
				"ctrl_interface=/etc_rw/wifi/sockets\n"
				"update_config=1\n"
				"eapol_version=1\n"
				"ap_scan=1\n";*/
				
	wf_log ("check_wpa_supplicant_conf");
	if (0 == get_file_size (SUPPLICANT_CONF)) {
		wf_log ("wpa_supplicant broken , size = 0");
#if defined(__AIC_8800DW_CHIP__)
		wlan_write_file (SUPPLICANT_CONF, "##### wpa_supplicant configuration file template #####\n\
			ctrl_interface=/etc_rw/wifi/sockets\n\
			update_config=1\n\
			pmf=1\n\
			eapol_version=1\n\
			ap_scan=1\n");
#else
		wlan_write_file (SUPPLICANT_CONF, "##### wpa_supplicant configuration file template #####\n\
			ctrl_interface=/etc_rw/wifi/sockets\n\
			update_config=1\n\
			eapol_version=1\n\
			ap_scan=1\n");
#endif
	}
}



char wpa_supplicant_pid[16] = {0};

pid_t  read_wpa_pid()
{
	memset(wpa_supplicant_pid,0,sizeof(wpa_supplicant_pid));
	wlan_readfile (WPA_PID_FILE, wpa_supplicant_pid, sizeof(wpa_supplicant_pid)-1);
	wpa_supplicant_pid[sizeof(wpa_supplicant_pid)-1] = '\0';//cov
	wf_log (" wpa_supplicant_pid= %s len=%d", wpa_supplicant_pid, strlen(wpa_supplicant_pid));

	if(wpa_supplicant_pid[strlen(wpa_supplicant_pid)-1] =='\n' )
		wpa_supplicant_pid[strlen(wpa_supplicant_pid)] = '\0';

	wf_log (" wpa_supplicant_pid= %s len=%d", wpa_supplicant_pid, strlen(wpa_supplicant_pid));

	return  (pid_t)atoi(wpa_supplicant_pid);
	
}


int wifi_start_supplicant (struct  wlan_sta_manager *sta_ctrl)
{

	int count = 200; /* wait at most 20 seconds for completion */
	char 	wifi_root_dir[32] = {0};
	char *buf = NULL;
	int ret = -1;

	char  mtu[8] = {0};
	char cmd[64]={0};
	
	cfg_get_item("mtu", mtu, sizeof(mtu));
	sprintf(cmd, "/sbin/ifconfig %s mtu %s", sta_ctrl->sock.iface_name, mtu);
	zxic_system(cmd); //kw 3
	wf_log ("wpa_supplicant_start %s", cmd);
	
	ret = sta_ctrl->drv_proxy.drv_init(&sta_ctrl->drv_proxy);
	if( -1 ==  ret)
		return -1;

	if (check_alive(&sta_ctrl->sock)) 
		return 0;

/* Started by AICoder, pid:u6328p3bbbp74d0143e60b0cb002cb023498ef04 */
#if (defined(USE_CAP_SUPPORT) || defined(WIFI_UNCOEXIST_5G))
	char support5g[10] = {0};
	cfg_get_item("wifi_sup_5g_band",support5g,sizeof(support5g));
	if(0 == strcmp(support5g, "1")) {
		reset_wpa_supplicant_conf();
	}
#endif
/* Ended by AICoder, pid:u6328p3bbbp74d0143e60b0cb002cb023498ef04 */

	/* Reset sockets used for exiting from hung state */
	sta_ctrl->sock.exit_sockets[0] = sta_ctrl->sock.exit_sockets[1] = -1;

	//system ("wpa_supplicant  -dddd  -iwlan0-vxd -Dwext  -c/wifi/wpa_supplicant.conf -B -P/wifi/wpa_file.pid");
	cfg_get_item("wifi_root_dir", wifi_root_dir, sizeof(wifi_root_dir));

#if defined(__REALTEK_8192_CHIP__)
	asprintf(&buf, "/bin/wpa_supplicant  -dddd  -iwlan0-vxd -Dwext  -c %s/wifi/wpa_supplicant.conf  -B -P %s", wifi_root_dir, WPA_PID_FILE);
#elif (defined(__SSV_6X5X_CHIP__) || defined(__AIC_8800DW_CHIP__))
	asprintf(&buf, "/bin/wpa_supplicant -dddd -i%s -Dnl80211 -c %s/wifi/wpa_supplicant.conf  -B -P %s", sta_ctrl->drv_proxy.iface_name, wifi_root_dir, WPA_PID_FILE);
#else
	asprintf(&buf, "/bin/wpa_supplicant -dddd -iwlan0 -Dnl80211 -c %s/wifi/wpa_supplicant.conf  -B -P %s", wifi_root_dir, WPA_PID_FILE);
#endif
	zxic_system(buf); //kw 3

	free(buf);

//	system("wpa_supplicant -dddd -iwlan0 -Dnl80211 -c"WPA_CONF_FILE" -B -P"WPA_PID_FILE);

	while (count-- > 0) {
		if (access(WPA_PID_FILE, F_OK) == 0){
			usleep (100000);
			sta_ctrl->sock.pid = read_wpa_pid();
			if(sta_ctrl->sock.pid == 0) {
				usleep (100000);
				continue;
			}
			return 0;
		}
		usleep (100000);
	}
	return -1;

}

void wifi_down_sta_iface(struct  wlan_sta_manager *sta_ctrl)
{
	char cmd[64] = {0};
	snprintf(cmd, sizeof(cmd), "/sbin/ifconfig %s down", sta_ctrl->drv_proxy.iface_name);
	wf_log("sta_down[%s]", cmd);
	zxic_system(cmd);
}

int wifi_stop_supplicant (struct  wlan_sta_manager *sta_ctrl)
{
	int count = 50; /* wait at most 5 seconds to ensure init has stopped stupplicant */
	int ret = -1;
	
	if (check_alive(&sta_ctrl->sock)==0){
		wf_log (" supplicant not exist");

		wifi_down_sta_iface(sta_ctrl);
		sta_ctrl->sock.pid = 0;
		ret = sta_ctrl->drv_proxy.drv_deinit(&sta_ctrl->drv_proxy);
		return ret; // can not find the file ,it has been killed
	}

	wf_log (" wpa_supplicant_stop");
	system("killall -9 wpa_supplicant");
	
	while (count-- > 0) {
		if (check_alive(&sta_ctrl->sock)==0) {
			wf_log ("wpa_supplicant=stop over\n");
			memset(wpa_supplicant_pid,0,sizeof(wpa_supplicant_pid));
			sta_ctrl->sock.pid = 0;
			ret = 0;
			break;
		}
		usleep (100000);
	}

	
	if(ret == 0) {// supplicant has stopped
		wifi_down_sta_iface(sta_ctrl);
		ret = sta_ctrl->drv_proxy.drv_deinit(&sta_ctrl->drv_proxy);
	}
	
	return ret;
}

void wifi_close_supplicant_connection(struct  wlan_sta_manager *sta_ctrl)
{
	wf_log (" enter ");
	wifi_close_sockets(&sta_ctrl->sock);
	wifi_stop_supplicant(sta_ctrl);
}

void  wlan_station_drv_init(struct  wlan_sta_manager * sta_ctrl)
{
	sta_ctrl->drv_proxy.drv_init(&sta_ctrl->drv_proxy); // not check return value, if init failed, check wlan function will assure the process

	check_wpa_supplicant_conf();

	unlink(WPA_PID_FILE);
//	unlink(WPA_SOCKETS);
//	unlink(WPA_SOCKETS_DIR);

}

struct  wlan_sta_manager   rda5995_sta =
{

	.sock    		={
		.iface_name	= 	"wlan0",
		.sockets_dir	= 	"/etc_rw/wifi/sockets",
	},
	.drv_proxy =
	{	
		.drv_init_flag = 0,
		.iface_name 	=	"wlan0",
		.insmod_cmd 	=  	RDA5995_INSMODE_STACMD,
		.rmmod_cmd 	=	RDA5995_RMMOD_CMD,
		.drv_init		= 	wlan_drv_init,
		.drv_deinit	=	wlan_drv_deinit,
	},
	.init 				=  	wlan_station_drv_init,
	.start_supplicant	= 	wifi_start_supplicant,
	.close_connection 	=  	wifi_close_supplicant_connection,
};



struct  wlan_sta_manager   esp8089_sta =
{

	.sock    		={
		.iface_name	= 	"wlan0",
		.sockets_dir	= 	"/etc_rw/wifi/sockets",
	},
	.drv_proxy =
	{
	
		.drv_init_flag = 0,
		.iface_name 	=	"wlan0",
		.insmod_cmd 	=  	ESP8089_INSMODE_STACMD,
		.rmmod_cmd 	=	ESP8089_RMMOD_STACMD,
		.drv_init		= 	wlan_drv_init,
		.drv_deinit	=	wlan_drv_deinit,

	},
	.init 				=  	wlan_station_drv_init,
	.start_supplicant	= 	wifi_start_supplicant,
	.close_connection 	=  wifi_close_supplicant_connection,
};


struct  wlan_sta_manager   realtek_sta =
{

	.sock    		={
		.iface_name	= 	"wlan0-vxd",
		.sockets_dir	= 	"/etc_rw/wifi/sockets",
	},
	.drv_proxy =
	{
	
		.drv_init_flag = 1, //realtek  do not use ko, so set it has been inited
		.iface_name 	=	"wlan0-vxd",
		.drv_init		= 	wlan_drv_init,
		.drv_deinit	=	wlan_drv_deinit,
	},
	.init 			=  	wlan_station_drv_init,
	.start_supplicant		= 	wifi_start_supplicant,
	.close_connection 	=  wifi_close_supplicant_connection,
};

struct  wlan_sta_manager   ssv6x5x_sta =
{

	.sock = {
		.iface_name 	= 	"wlan0-vxd",
		.sockets_dir 	= 	"/etc_rw/wifi/sockets",
	},
	.drv_proxy = {
		.drv_init_flag 	= 	2, //ap insmod ko, sta no need
		.iface_name 	=	"wlan0-vxd",
		.drv_init		= 	wlan_drv_init,
		.drv_deinit		=	wlan_drv_deinit,
	},
	.init	 			=  	wlan_station_drv_init,
	.start_supplicant	= 	wifi_start_supplicant,
	.close_connection 	=  	wifi_close_supplicant_connection,
};

struct  wlan_sta_manager   aic8800dw_sta =
{

	.sock = {
		.iface_name 	= 	"wlan0-vxd",
		.sockets_dir 	= 	"/etc_rw/wifi/sockets",
	},
	.drv_proxy = {
		.drv_init_flag 	= 	2, //ap insmod ko, sta no need
		.iface_name 	=	"wlan0-vxd",
		.drv_init		= 	wlan_drv_init,
		.drv_deinit		=	wlan_drv_deinit,
	},
	.init	 			=  	wlan_station_drv_init,
	.start_supplicant	= 	wifi_start_supplicant,
	.close_connection 	=  	wifi_close_supplicant_connection,
};

