
include $(COMMON_MK)

EXEC = rda_tools
OBJS = rda_tools.o

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) $(LDLIBS_$@) -Wl,--end-group
	@cp $@ $@.elf


romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)

clean:
	-@rm -f $(EXEC) *.elf *.gdb *.o
