#ifndef _MINI_UPNP_GLOBAL_H
#define _MINI_UPNP_GLOBAL_H

/* Server: HTTP header returned in all HTTP responses : */
#define MINIUPNPD_SERVER_STRING "OS 1.0 UPnP/1.0 Realtek/V1.3"

#define IP_ADDRLEN 				17
#define SID_LEN 					44
#define URL_MAX_LEN				200
#define IP_V4_DOT_COUNT		3
#define MAX_SUB_TIMEOUT		7200

#define BOOLEAN char
#define TRUE    (1==1)
#define FALSE   (1==0)

#define syslog(x, fmt, args...);
#endif
