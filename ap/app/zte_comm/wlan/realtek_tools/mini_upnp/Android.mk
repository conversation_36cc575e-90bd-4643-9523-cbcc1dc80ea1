LOCAL_PATH := $(call my-dir)

#########################

SOURCES = minixml.c \
          upnphttp.c \
          upnpreplyparse.c \
          upnpsoap.c \
          mini_upnp.c

#########################

include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_PRELINK_MODULE := false
LOCAL_SRC_FILES := $(SOURCES)
LOCAL_MODULE := lib_mini_upnp
LOCAL_CFLAGS := -Os -Wall -D__ANDROID__
LOCAL_C_INCLUDES:=$(LOCAL_PATH)/../include
include $(BUILD_SHARED_LIBRARY)

