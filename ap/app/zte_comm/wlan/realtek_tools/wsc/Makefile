include $(zte_app_mak)
CRYPTLIB = portingssl
WSCSRC = src
BIG_ENDIAN=n
CONFIG_PC=n

ifeq ($(CUR_USED_OS),LINUX)
all:
	make -C $(CRYPTLIB) CROSS_COMPILE=$(CROSS_COMPILE) CONFIG_PC=$(CONFIG_PC) BIG_ENDIAN=$(BIG_ENDIAN)
	make -C $(WSCSRC) CROSS_COMPILE=$(CROSS_COMPILE) CONFIG_PC=$(CONFIG_PC) BIG_ENDIAN=$(BIG_ENDIAN)

else
all:
	make -C $(CRYPTLIB)  CONFIG_PC=$(CONFIG_PC) BIG_ENDIAN=$(BIG_ENDIAN)
	make -C $(WSCSRC)  CONFIG_PC=$(CONFIG_PC) BIG_ENDIAN=$(BIG_ENDIAN)
endif

	
clean:
	make -C $(CRYPTLIB) clean
	make -C $(WSCSRC) clean
	

romfs:
	cp  $(WSCSRC)/wscd  $(WSCSRC)/wscd.elf
	$(ROMFSINST)  $(WSCSRC)/wscd /bin/wscd
	$(ROMFSINST)  $(WSCSRC)/wscd.conf /etc/wscd.conf
