#####################################################################
#																	 
#	Created by MIDE		17:06:54	07/30/02
#
#####################################################################
include $(zte_lib_mak)
#CROSS = rsdk-linux-
OUTDIR	= .
OUTLIBDIR	= .
OUTNAME	= libcrypto.a

ifeq ($(CUR_USED_OS),LINUX)
CC	= $(CROSS_COMPILE)gcc
AS	= $(CROSS_COMPILE)as
AR	= $(CROSS_COMPILE)ar
LD	= $(CROSS_COMPILE)ld
RM	= rm
STRIP = $(CROSS_COMPILE)strip
endif

TOOLLDFLAGS	= -n
#OPT	= -G 0
TEXT =
INCLUDES	= -I./crypto/include -I./crypto/include/openssl -I./
WARNING_FLAG = -Wall

CFLAGS	+= $(WARNING_FLAG) -Os $(OPT) \
			-DOPENSSL_FIPS -D__linux__ -DRSDK_BUILT -DOPENSSL_NO_SPEED -DOPENSSL_THREADS -D_REENTRANT \
			-DDSO_DLFCN -DHAVE_DLFCN_H -DOPENSSL_NO_KRB5 -DTERMIO \
			-fomit-frame-pointer \
			$(INCLUDES)

ifeq ($(BIG_ENDIAN),y)
	CFLAGS += -DB_ENDIAN
else
	CFLAGS += -DL_ENDIAN
endif

ifeq ($(SLINK),1)
CFLAGS  += -ffunction-sections -fdata-sections
LDFLAGS += --static -Wl,--gc-sections
endif

ifeq ($(CUR_USED_OS),UCLINUX)
CFLAGS += -D__ZTE_UCLINUX__
else
CFLAGS += -D__ZTE_LINUX__
endif


CRT	=

LIBS	=

all: $(OUTLIBDIR)/$(OUTNAME)

include Files.mk

$(OUTLIBDIR)/$(OUTNAME) : $(OBJFILES)
	$(AR) rcs $(OUTLIBDIR)/$(OUTNAME) $(OBJFILES)

compile : $(OBJFILES)
