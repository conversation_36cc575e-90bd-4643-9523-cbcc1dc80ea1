/*================================================
    Created by: <PERSON> Hsu
    DATE: 17 Aug. 2006
==================================================*/

//#define _M_AMD64


/*for bothl visual C++ (console) and RSDK*/
#define THIRTY_TWO_BIT
#define BN_LLONG   


#define OPENSSL_NO_ASM

#define OPENSSL_NO_ENGINE
#define GETPID_IS_MEANINGLESS

#define OPENSSL_NO_RSA
#define OPENSSL_NO_BIO
#define OPENSSL_NO_DSA
#define OPENSSL_NO_EC
#define OPENSSL_NO_ECDSA
#define OPENSSL_NO_MD2
#define OPENSSL_NO_MD4
#define OPENSSL_NO_SHA512
#define OPENSSL_NO_RIPEMD
#define OPENSSL_NO_DES
#define OPENSSL_NO_RC4
#define OPENSSL_NO_IDEA
#define OPENSSL_NO_RC2
#define OPENSSL_NO_BF
#define OPENSSL_NO_CAST
#define OPENSSL_NO_RC5
//#define OPENSSL_NO_AES
#define OPENSSL_NO_SHA0
#define OPENSSL_NO_FP_API
#define OPENSSL_NO_LOCKING

#define OPENSSL_NO_GMP
#define OPENSSL_NO_MDC2

