LOCAL_PATH := $(call my-dir)

#########################

# For mini_upnp stand alone
LIBUPNP=0
#STATIC_LIB=1

ifeq ($(CONFIG_RTL_COMAPI_CFGFILE),y)
CFLAGS += -DCONFIG_IWPRIV_INTF
endif

UPNPDIR  := $(LOCAL_PATH)/../../mini_upnp
UPNPINC  := $(UPNPDIR)
CFLAGS   += -DUSE_MINI_UPNP
SOURCES  = wsc.c txpkt.c rxpkt.c utils.c simplecfg_mini_upnp_main.c

#UPNPLIB  = $(UPNPDIR)/mini_upnp.a
#CFLAGS   += -DSTAND_ALONE_MINIUPNP

INCS := external/openssl/include $(UPNPINC) $(IXMLINC) 

CONFIG_RTL_8198 := y

ifeq ($(CONFIG_RTL_8198), y)
CFLAGS  += -DCONFIG_RTL8198
endif

ifeq ($(BIG_ENDIAN),y)
CFLAGS  += -DB_ENDIAN
else
CFLAGS  += -DL_ENDIAN
endif

$(shell echo  \#define BUILT_TIME \"`TZ=UTC date -u "+%Y.%m.%d-%H:%M%z" `\" > $(LOCAL_PATH)/built_time)

#########################
include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := $(SOURCES)
LOCAL_MODULE := wscd
LOCAL_SHARED_LIBRARIES := libc lib_mini_upnp libcrypto
LOCAL_CFLAGS := -Os -Wall $(CFLAGS) -D__ANDROID__
LOCAL_C_INCLUDES:= $(KERNEL_HEADERS) $(INCS) $(LOCAL_PATH)/../../include 
include $(BUILD_EXECUTABLE)


