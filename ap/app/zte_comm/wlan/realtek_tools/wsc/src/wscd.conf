#detail please reference config_file_README.txt
wlan_fifo0 ="/var/wscd-wlan0.fifo"
wlan_fifo1 ="/var/wscd-wlan1.fifo"

SSID_prefix = "Reaktek_AP_"

use_ie = 1

# AUTH_OPEN=1, AUTH_WPAPSK=2, AUTH_SHARED=4, AUTH_WPA=8, AUTH_WPA2=0x10, AUTH_WPA2PSK=0x20
auth_type_flags = 39

# ENCRYPT_NONE=1, ENCRYPT_WEP=2, ENCRYPT_TKIP=4, ENCRYPT_AES=8
encrypt_type_flags = 15

uuid = 63041253101920061228aabbccddeeff
device_name = "RTK_AP"
manufacturer = "Realtek Semiconductor Corp."
manufacturerURL = "http://www.realtek.com/"
modelURL = "http://www.realtek.com/"
model_name = "RTL8xxx"
model_num = "EV-2010-09-20"
serial_num = "123456789012347"
modelDescription = "WLAN Access Point"
device_attrib_id = 1
device_oui = 0050f204
device_category_id = 6
device_sub_category_id = 1

# PASS_ID_DEFAULT=0, PASS_ID_USER=1, PASS_ID_MACHINE=2, PASS_ID_REKEY=3,
# PASS_ID_PB=4, PASS_ID_REG=5, PASS_ID_RESERVED=6
device_password_id = 0

tx_timeout = 5
resent_limit = 2
reg_timeout = 120
block_timeout = 60

WPS_START_LED_GPIO_number = 2
WPS_END_LED_unconfig_GPIO_number = 0
WPS_END_LED_config_GPIO_number = 0
WPS_PBC_overlapping_GPIO_number = 1
PBC_overlapping_LED_time_out = 30


No_ifname_for_flash_set = 0

#disable_disconnect = 1
#disable_auto_gen_ssid = 1
#manual_key_type = 2
#manual_key = 1234567890
#random_key_len = 64
#PSK_LEN = 64

disable_hidden_ap = 1

#SSID_prefix = "RTKAP_"

button_hold_time = 1

# for WPS2;if wps1.0 don't define
# 0x2008|0x480|0x680(CONFIG_METHOD_VIRTUAL_PIN | CONFIG_METHOD_PHYSICAL_PBC | CONFIG_METHOD_VIRTUAL_PBC )

config_method =  9864
#if AP mode config by EAP-base ER or auto-generate
#but want not apply to both band then preset the paramater to 1
#ProfileDontBothApply = 1

# value=2:deny whatever config or unconfig state.
# value=1:deny under configured state, allow when unconfig state.
#disable_configured_by_exReg = 1

#when PIN failed number >= MaxPinFailThresHold AP will indefinitely auto-lock-down 
#until user intervenes to unlock ; vaild value 1~10
#MaxPinFailThresHold = 10

