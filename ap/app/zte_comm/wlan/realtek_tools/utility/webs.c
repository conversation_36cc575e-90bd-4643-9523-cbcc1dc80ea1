/* 
 */
#include <stdio.h>
#include <unistd.h>
#include <string.h>
#include <stdlib.h>
#include <signal.h>
#include <sys/stat.h>
#include "rtk_arch.h"

static int isDaemon=0;
static char	*pidfile=(TOP_CONFIG_DIR "/run/webs.pid");
#define REINIT_WEB_FILE	(WIFI_WPS_TMP_DIR "/reinit_web")

void get_wifi_script_dir(char *path, size_t size)
{
	FILE *fp;
	char *pstr;
	struct stat statbuf;
	int len = 0;

	fp = fopen(WIFI_CONFIG_ROOT_DIR "/wifi_script_dir", "r");
	if (fp) {
		pstr = fgets(path, size, fp);
		fclose(fp);
		if (pstr) {
			len = strlen(path);
			if ((len > 0) && ('\n' == path[len-1])) {
				--len;
				path[len] = '\0';
			}
			if (stat(path, &statbuf) == -1) { // dir not exist
				len = 0;
			}
		}
	}
	if (0 == len) {
		strncpy(path, WIFI_SCRIPT_DIR, size);
	}
}

void sigHandler_autoconf(int signo)
{
	char tmpbuf[256]={0};
	struct stat status;
	int reinit=1;
	
printf("<<Got Sig and Will Check Do init.sh:%d>>\n",signo);
	if (stat(REINIT_WEB_FILE, &status) == 0) { // file existed
        unlink(REINIT_WEB_FILE);
		reinit = 0;		
	}	
	if (reinit) { // re-init system
		printf("<<Got Sig and Do init.sh>>\n");
#ifdef CONFIG_NEW_SCRIPT             
		get_wifi_script_dir(tmpbuf, sizeof(tmpbuf));
		strncat(tmpbuf, "/../my_test.sh wps_restart", sizeof(tmpbuf)-strlen(tmpbuf)-1);
#else
		get_wifi_script_dir(tmpbuf, sizeof(tmpbuf));
		strncat(tmpbuf, "/init.sh", sizeof(tmpbuf)-strlen(tmpbuf)-1);
#endif	     
 		system(tmpbuf);
	}
}
void init_signals(void)
{
    struct sigaction sa;

    sa.sa_flags = SA_NODEFER;

    sigemptyset(&sa.sa_mask);
   
    sa.sa_handler = sigHandler_autoconf;

    sigaction(SIGUSR1, &sa, NULL);
    //sigaction(SIGTERM, &sa, NULL);
}
int main(int argc, char *argv[])
{
	int i;
	char line[20];
	FILE *fp;
	
	if( argc==2 && (!strcmp(argv[1], "version")) ) 
	{
		printf("version : %s\n",DAEMON_VERSION);
		return 0;
	}
	
	for(i=1; i<argc; i++)
	{
		if(argv[i][0]!='-')
		{
			fprintf(stderr, "%s: Unknown option\n", argv[i]);
		}
		else switch(argv[i][1])
		{
		case 'x':
			isDaemon = 1;
			break;
		default:
			fprintf(stderr, "%s: Unknown option\n", argv[i]);
		}
	}
	if(isDaemon==1){
		if (daemon(0, 1) == -1) {
			perror("webs fork error");
			return 0;
		}
	}
	init_signals();
	setsid();
	sprintf(line, "%d\n", getpid());
	if ((fp = fopen(pidfile, "w")) == NULL) {
		printf("open file error:%s\n",pidfile);
		return -1;
	}
	fwrite(line, strlen(line), 1, fp);
	fclose(fp);
	
	for (;;) {
		sleep(5);
	}
	return 0;
}



