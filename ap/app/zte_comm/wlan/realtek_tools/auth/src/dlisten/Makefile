#
# Makefile for WPA for the Linux OS
#
#
# $Id: Makefile,v ******* 2007/08/06 10:04:42 root Exp $
#

include $(zte_app_mak)
WPA_ROOT_DIR = ../..

ARCH	=
NAME	= dlisten
ifeq ($(CUR_USED_OS),LINUX)
###################
CC	= $(CROSS_COMPILE)gcc
STRIP	= $(CROSS_COMPILE)strip
LD	= $(CROSS_COMPILE)ld
AR	= $(CROSS_COMPILE)ar
###################
endif
dlisten_SRCS = iwcontrol.c iwconfig.c iwcommon.c iwreq.c
dlisten_OBJS = ${dlisten_SRCS:.c=.o}

RTL_WPA_IFLAGS 	= -I${WPA_ROOT_DIR}/include \
				-I${WPA_ROOT_DIR}/Package/Libnet-1.0.2a/include \
				-I${WPA_ROOT_DIR}/Package/libpcap-0.7.2 \
				-I${WPA_ROOT_DIR}/Package/openssl-0.9.7b/include \
				-I$(zte_app_path)/include

#RTL_WPA_LIBS = ${WPA_ROOT_DIR}/libnet.a \
#				${WPA_ROOT_DIR}/libpcap.a \
#				${WPA_ROOT_DIR}/libcrypto.a

RTL_WPA_DEFFLAGS += -DGLIBC22_HEADERS

RTL_WPA_CFLAG = -Wall -Os


#ifeq ($(CONFIG_RTL_92D_DMDP),y)
#RTL_WPA_CFLAG += -DFOR_DUAL_BAND
#endif

RTL_WPA_CFLAG += -DFOR_DUAL_BAND
#-DDEBUG -DALLOW_DBG_CONTROL

ifeq ($(CUR_USED_OS),UCLINUX)
CFLAGS += -D__ZTE_UCLINUX__
LDFLAGS  += -Wl,-elf2flt=-s65536
else
CFLAGS += -D__ZTE_LINUX__  -g
endif


ifeq ($(SLINK),1)
RTL_WPA_CFLAG += -ffunction-sections -fdata-sections
RTL_WPA_LDFLAG += --static -Wl,--gc-sections
endif

#
#	Transition rules (add -o to put object in right directory)
#
.c.o:
	$(CC) $(CFLAGS) $(RTL_WPA_CFLAG) $(RTL_WPA_IFLAGS) $(RTL_WPA_DEFFLAGS) -c -o $*.o $<


###################
all: iwcontrol

ifeq ($(CUR_USED_OS),LINUX)

iwcontrol: $(dlisten_OBJS)
	echo CUR_USED_OS=$(CUR_USED_OS)
	$(CC) ${RTL_WPA_LDFLAG} -o iwcontrol $(dlisten_OBJS) $(RTL_WPA_LIBS)
#	$(STRIP) $(STRIPFLAGS) iwcontrol
else

iwcontrol: $(dlisten_OBJS)
	echo CUR_USED_OS=$(CUR_USED_OS)
	$(CC)  $(LDFLAGS) ${RTL_WPA_LDFLAG} -o iwcontrol $(dlisten_OBJS) $(RTL_WPA_LIBS)
endif

##-------------------------------------------------

clean:
	rm -f *.o iwcontrol *.gdb *.elf

romfs:
	cp iwcontrol  iwcontrol.elf
	$(ROMFSINST)  /bin/iwcontrol
