LOCAL_PATH := $(call my-dir)

#########################

ARCH := rWPA.a
NAME := rWPA

WPA_ROOT_DIR = ..
RTL_WPA_TARGET = auth

###### WPA2 #######
ENABLE_WPA2 = true
ENABLE_WPA2_PREAUTH = true
ENABLE_WPA2_CLIENT = true
ENABLE_CLIENT_TLS = false

###### 2 set of RADIUS #######
ENABLE_RADIUS_2SET = true

###### Endian ######
ifeq ($(BIG_ENDIAN), y)
ENABLE_BIG_ENDIAN = true
else
ENABLE_BIG_ENDIAN = false
endif

ifeq ($(RTL8186_TR), 1)
RTL_WPA_CFLAG += -DCONFIG_RTL8186_TR
ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(RTL865X_AC), 1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_AC
ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(RTL865X_KLD), 1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_KLD
#ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(CONFIG_RTL865X_SC), 1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_SC
ENABLE_WPA2_CLIENT = false
#ENABLE_WPA2_PREAUTH = false
endif

DBG_FLAG := -DDEBUG_DISABLE
DOWN_SIZE := -DCOMPACK_SIZE
#INCS := -I$(TOP_USERS_DIR)/include

RTL_WPA_CFLAG += -Wall -Os $(DOWN_SIZE) $(DBG_FLAG) #$(INCS)

ifeq ($(SLINK), 1)
RTL_WPA_CFLAG += -ffunction-sections -fdata-sections
RTL_WPA_LDFLAG += --static -Wl,--gc-sections
endif

#RTL_WPA_IFLAGS  = -I${WPA_ROOT_DIR}/include

ifeq ($ENABLE_BIG_ENDIAN), true)
   RTL_WPA_DEFFLAGS = -D_ON_RTL8181_TARGET -DLIBNET_BIG_ENDIAN -DLIB1X_BIG_ENDIAN -D_DAEMON_SIDE -D_RTL_WPA_UNIX
else
   RTL_WPA_DEFFLAGS = -D_ON_RTL8181_TARGET -DLIBNET_LIL_ENDIAN -DLIB1X_LIL_ENDIAN -D_DAEMON_SIDE -D_RTL_WPA_UNIX
endif

ifeq ($(ASUS),1)
   RTL_WPA_DEFFLAGS += -D__ASUS_DVD__ -DPSK_ONLY -DAUTH_LITTLE_ENDIAN -DSTART_AUTH_IN_LIB
else
ifeq ($(ENABLE_BIG_ENDIAN), true)
   RTL_WPA_DEFFLAGS += -DAUTH_BIG_ENDIAN
else
   RTL_WPA_DEFFLAGS += -DAUTH_LITTLE_ENDIAN
endif
endif

ifeq ($(strip $(ENABLE_WPA2)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2
RTL_WPA_CFLAG   += -g
endif

ifeq ($(strip $(ENABLE_WPA2_PREAUTH)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2_PREAUTH
endif # ENABLE_WPA2_PREAUTH

ifeq ($(strip $(ENABLE_WPA2_CLIENT)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2_CLIENT
endif # ENABLE_WPA2_CLIENT

ifeq ($(strip $(ENABLE_RADIUS_2SET)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_RADIUS_2SET
endif #ENABLE_RADIUS_2SET

#------------------------------------------------------------------------
# source files
#------------------------------------------------------------------------
RTL_WPA_SRCS = auth.c \
               1x_auth_pae.c 1x_bauth_sm.c 1x_common.c 1x_krc_sm.c \
               1x_nal.c 1x_kxsm.c 1x_radius.c 1x_ptsm.c 1x_reauth_sm.c \
               1x_cdsm.c 1x_config.c 1x_ioctl.c 1x_parser.c \
               1x_kmsm_auth.c 1x_kmsm_supp.c 1x_kmsm_eapolkey.c \
               1x_info_auth.c 1x_kmsm_hmac.c 1x_kmsm_prf.c 1x_kmsm_aes.c 1x_acct_sm.c 1x_md5c.c 1x_rc4.c libnet.c

ifeq ($(strip $(ENABLE_WPA2)),true)
RTL_WPA_SRCS    += 1x_pmk_cache.c
endif # ENABLE_WPA2

#RTL_WPA_OBJS = ${RTL_WPA_SRCS:.c=.o}

ifeq ($(ASUS),1)
RTL_WPA_SRCS += iwcontrol.c
endif

INCLUDES += external/openssl/include

#########################
include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := $(RTL_WPA_SRCS)
LOCAL_MODULE := auth
OCAL_SHARED_LIBRARIES := libc libssl
LOCAL_CFLAGS := -Os -Wall $(RTL_WPA_DEFFLAGS) -D__ANDROID__
LOCAL_C_INCLUDES:= $(KERNEL_HEADERS) $(INCLUDES) $(LOCAL_PATH)/../include $(LOCAL_PATH)/../../include 
include $(BUILD_EXECUTABLE)

#########################
dlisten_SRCS = dlisten/iwcontrol.c dlisten/iwconfig.c dlisten/iwcommon.c dlisten/iwreq.c

DRL_WPA_DEFFLAGS += -DGLIBC22_HEADERS

DRTL_WPA_CFLAG = -Wall -Os
DRTL_WPA_CFLAG += -DFOR_DUAL_BAND

ifeq ($(SLINK),1)
DRTL_WPA_CFLAG += -ffunction-sections -fdata-sections
DRTL_WPA_LDFLAG += --static -Wl,--gc-sections
endif

include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := $(dlisten_SRCS)
LOCAL_MODULE := iwcontrol
LOCAL_SHARED_LIBRARIES := libc libssl
LOCAL_CFLAGS := -Os -Wall $(DRTL_WPA_DEFFLAGS) $(DRTL_WPA_CFLAG) -D__ANDROID__
LOCAL_C_INCLUDES:= $(KERNEL_HEADERS) $(INCLUDES) $(LOCAL_PATH)/../include $(LOCAL_PATH)/../../include
include $(BUILD_EXECUTABLE)

