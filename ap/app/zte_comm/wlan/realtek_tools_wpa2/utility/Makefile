
include $(zte_lib_mak)
INCS = -I$(zte_app_path)/include
CFLAGS += -Os -Wall $(INCS)
LDFLAGS	+= -g -s
ifeq ($(CUR_USED_OS),LINUX)
CC = $(CROSS_COMPILE)gcc
AR = $(CROSS_COMPILE)ar
endif

ifeq ($(CUR_USED_OS),UCLINUX)
CFLAGS += -D__ZTE_UCLINUX__
else
CFLAGS += -D__ZTE_LINUX__  -g
endif

#CFLAGS += -DCONFIG_IEEE80211W
#CFLAGS += -DCONFIG_NEW_SCRIPT

all: webs	flash

webs: webs.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)
	#cp -p ./webs ../bin/
	#cp -p ./webs $(ROOTFS_DIR)/bin
flash: flash.o
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS) 
	#cp -p ./flash ../bin/
	#cp -p ./flash $(ROOTFS_DIR)/bin


clean:
	rm -f *.o webs flash *.gdb *.elf


romfs:
	cp flash  flash.elf
	cp webs  webs.elf
	$(ROMFSINST)   /bin/flash
	$(ROMFSINST)   /bin/webs
