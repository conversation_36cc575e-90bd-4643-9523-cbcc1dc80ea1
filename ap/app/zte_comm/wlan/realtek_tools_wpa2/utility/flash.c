/*
 
 *
 */


/* System include files */
#include <string.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>
#include <stdio.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <stdlib.h>
#include <ctype.h>
#include <regex.h>
#include <linux/wireless.h>
#include <linux/sockios.h>
#include <sys/ioctl.h>

#include "rtk_arch.h"

#define WLAN_FAST_INIT
#define BR_SHORTCUT	
#define NUM_WLAN_INTERFACE 1
#define DEFAULT_CONFIG_PATH (WIFI_CONFIG_ROOT_DIR "/wlan0/")
#define PIN_LEN					8
typedef enum { BANDMODE2G=0, BANDMODE5G=1, BANDMODEBOTH=2, BANDMODESINGLE=3 } WLANBAND2G5GMODE_TYPE_T;
typedef enum { AP_MODE=0, CLIENT_MODE=1, WDS_MODE=2, AP_WDS_MODE=3	} WLAN_MODE_T;
typedef enum { ENCRYPT_DISABLED=0, ENCRYPT_WEP=1, ENCRYPT_WPA=2, ENCRYPT_WPA2=4, ENCRYPT_WPA2_MIXED=6 ,ENCRYPT_WAPI=7} ENCRYPT_T;
typedef enum { WEP_DISABLED=0, WEP64=1, WEP128=2 } WEP_T;
typedef enum { KEY_ASCII=0, KEY_HEX=1} KEY_TYPE_T;
typedef enum { WPA_AUTH_AUTO=1, WPA_AUTH_PSK=2 } WPA_AUTH_T;
typedef enum { WPA_CIPHER_TKIP=1, WPA_CIPHER_AES=2, WPA_CIPHER_MIXED=3 } WPA_CIPHER_T;
enum { WSC_AUTH_OPEN=1, WSC_AUTH_WPAPSK=2, WSC_AUTH_SHARED=4, WSC_AUTH_WPA=8, WSC_AUTH_WPA2=0x10, WSC_AUTH_WPA2PSK=0x20, WSC_AUTH_WPA2PSKMIXED=0x22 };
enum { WSC_ENCRYPT_NONE=1, WSC_ENCRYPT_WEP=2, WSC_ENCRYPT_TKIP=4, WSC_ENCRYPT_AES=8, WSC_ENCRYPT_TKIPAES=12 };
enum { CONFIG_METHOD_ETH=0x2, CONFIG_METHOD_PIN=0x4, CONFIG_METHOD_PBC=0x80 };
enum { CONFIG_BY_INTERNAL_REGISTRAR=1, CONFIG_BY_EXTERNAL_REGISTRAR=2};
/* Constand definitions */
#define DEC_FORMAT	("%d")
#define BYTE5_FORMAT	("%02x%02x%02x%02x%02x")
#define BYTE6_FORMAT	("%02x%02x%02x%02x%02x%02x")
#define BYTE13_FORMAT	("%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x")
#define STR_FORMAT	("%s")
#define HEX_FORMAT	("%02x")
#define SCHEDULE_FORMAT	("%d,%d,%d,%d")
#ifdef HOME_GATEWAY
#define PORTFW_FORMAT	("%s, %d, %d, %d")
#define PORTFILTER_FORMAT ("%d, %d, %d")
#define IPFILTER_FORMAT	("%s, %d")
#define TRIGGERPORT_FORMAT ("%d, %d, %d, %d, %d, %d")
#endif
#define MACFILTER_FORMAT ("%02x%02x%02x%02x%02x%02x")
#define MACFILTER_COLON_FORMAT ("%02x:%02x:%02x:%02x:%02x:%02x")
#define WDS_FORMAT	("%02x%02x%02x%02x%02x%02x,%d")
#define DHCPRSVDIP_FORMAT ("%02x%02x%02x%02x%02x%02x,%s,%s")
#define VLANCONFIG_FORMAT ("%s,%d,%d,%d,%d,%d,%d")
#ifdef HOME_GATEWAY
#ifdef VPN_SUPPORT
//#define IPSECTUNNEL_FORMAT ("%d, %d, %s, %d, %s, %d, %d, %s , %d, %s, %d, %d, %d, %d, %d, %d, %s, %d, %d, %d, %lu, %lu, %d, %s, %s, %s")
#define IPSECTUNNEL_FORMAT ("%d, %d, %s, %d, %s, %d, %d, %s , %d, %s, %d, %d,  %d, %d,  %s, %d, %d, %d, %lu, %lu, %d, %s, %s, %s, %d, %s, %s, %d, %d, %s")
#endif
#ifdef CONFIG_IPV6
#define RADVD_FORMAT ("%d, %s, %d, %d, %d, %d, %d, %d, %d, %d ,%d, %d, %s, %d, %d, %04x:%04x:%04x:%04x:%04x:%04x:%04x:%04x, %d, %d, %d, %d, %d, %d, %s, %d, %04x:%04x:%04x:%04x:%04x:%04x:%04x:%04x, %d, %d, %d, %d, %d, %d, %s, %d")
#define DNSV6_FORMAT ("%d, %s")
#define DHCPV6S_FORMAT ("%d, %s, %s, %s, %s")
#define ADDR6_FORMAT ("%d, %d, %d, %04x:%04x:%04x:%04x:%04x:%04x:%04x:%04x, %04x:%04x:%04x:%04x:%04x:%04x:%04x:%04x")
#endif
#endif

#define SPACE	(' ')
#define EOL	('\n')

#define LOCAL_ADMIN_BIT 0x02

#define READ_MIB_FROM_DEVICE 0



/* Macro definition */
static int _is_hex(char c)
{
    return (((c >= '0') && (c <= '9')) ||
            ((c >= 'A') && (c <= 'F')) ||
            ((c >= 'a') && (c <= 'f')));
}

static int string_to_hex(char *string, char *key, int len)
{
	char tmpBuf[4];
	int idx, ii=0;
	for (idx=0; idx<len; idx+=2) {
		tmpBuf[0] = string[idx];
		tmpBuf[1] = string[idx+1];
		tmpBuf[2] = 0;
		if ( !_is_hex(tmpBuf[0]) || !_is_hex(tmpBuf[1]))
			return 0;

		key[ii++] = (char) strtol(tmpBuf, (char**)NULL, 16);
	}
	return 1;
}

static void convert_lower(char *str)
{	int i;
	int len = strlen(str);
	for (i=0; i<len; i++)
		str[i] = tolower(str[i]);
}



static void generateWpaConf(char *outputFile, int isWds, char *iface);

static int updateWscConf(char *in, char *out, int genpin, char *iface);

int write_line_to_file(char *filename, int mode, char *line_data);
int get_file_content(char *file_name, char *dest_buffer);
//static void getVal2(char *value, char **p1, char **p2);

/////////////////////////////////////////////////////////////////////////////////////////
#if 0
static char __inline__ *getVal(char *value, char **p)
{
	int len=0;

	while (*value == ' ' ) value++;

	*p = value;

	while (*value && *value!=',') {
		value++;
		len++;
	}

	if ( !len ) {
		*p = NULL;
		return NULL;
	}

	if ( *value == 0)
		return NULL;

	*value = 0;
	value++;

	return value;
}
#endif

//////////////////////////////////////////////////////////////////////
static char *get_token(char *data, char *token)
{
	char *ptr=data;
	int len=0, idx=0;

	while (*ptr && *ptr != '\n' ) {
		if (*ptr == '=') {
			if (len <= 1)
				return NULL;
			memcpy(token, data, len);

			/* delete ending space */
			for (idx=len-1; idx>=0; idx--) {
				if (token[idx] !=  ' ')
					break;
			}
			token[idx+1] = '\0';
			
			return ptr+1;
		}
		len++;
		ptr++;
	}
	return NULL;
}

//////////////////////////////////////////////////////////////////////
static int get_value(char *data, char *value)
{
	char *ptr=data;	
	int len=0, idx, i;

	while (*ptr && *ptr != '\n' && *ptr != '\r') {
		len++;
		ptr++;
	}

	/* delete leading space */
	idx = 0;
	while (len-idx > 0) {
		if (data[idx] != ' ') 
			break;	
		idx++;
	}
	len -= idx;

	/* delete bracing '"' */
	if (data[idx] == '"') {
		for (i=idx+len-1; i>idx; i--) {
			if (data[i] == '"') {
				idx++;
				len = i - idx;
			}
			break;
		}
	}

	if (len > 0) {
		memcpy(value, &data[idx], len);
		value[len] = '\0';
	}
	return len;
}

//////////////////////////////////////////////////////////////////////
/*
		SSID=WPS05bdad67c1
		WSC_SSID=WPS05bdad67c1
		ENCRYPT=6
		WSC_AUTH=34
		WPA_AUTH=2
		WPA_PSK=5cde41ec70fd0dcf75a766
		PSK_FORMAT=0
		WSC_PSK=5cde41ec70fd0dcf75a766
		WPA_CIPHER_SUITE=3
		WPA2_CIPHER_SUITE=3
		WSC_ENC=12
		WSC_CONFIGBYEXTREG=1
		WSC_CONFIGURED=1
*/
static void readFileSetParam(char *file, char *wlanif_name)
{
	FILE *fp;
	char line[200], token[40], value[100], *ptr;
	char config_path[64]={0};
	char file_name[40];
	char full_path[100];

	fp = fopen(file, "r");
	if (fp == NULL) {
		printf("read file [%s] failed!\n", file);
		return;
	}

	sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s/", wlanif_name);

	while ( fgets(line, 200, fp) ) {
		ptr = get_token(line, token);
		if (ptr == NULL)
			continue;
		if (get_value(ptr, value)==0)
			continue;
		if(strstr(token, "WEP")){
			if(!strcmp(token, "WEP64_KEY1")){
				sprintf(file_name, "%s", "wepkey1_64_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP64_KEY2")){
				sprintf(file_name, "%s", "wepkey2_64_hex");
				sprintf(full_path, "%s%s",config_path,file_name);
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP64_KEY3")){
				sprintf(file_name, "%s", "wepkey3_64_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP64_KEY4")){
				sprintf(file_name, "%s", "wepkey4_64_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP128_KEY1")){
				sprintf(file_name, "%s", "wepkey1_128_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP128_KEY2")){
				sprintf(file_name, "%s", "wepkey2_128_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP128_KEY3")){
				sprintf(file_name, "%s", "wepkey3_128_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP128_KEY4")){
				sprintf(file_name, "%s", "wepkey4_128_hex");
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
			}else if(!strcmp(token, "WEP")){
				//set WEP 64 or 128
				sprintf(file_name, "%s", token);
				convert_lower(file_name);
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
				
				
			}else if(!strcmp(token, "WEP_KEY_TYPE")){
				//set HEX key type
				sprintf(file_name, "%s", token);
				convert_lower(file_name);
				sprintf(full_path, "%s%s",config_path,file_name); 
				write_line_to_file(full_path, 1,value);
				
			}
		}
		else{
			sprintf(file_name, "%s", token);
			convert_lower(file_name);
			sprintf(full_path, "%s%s",config_path,file_name); 
			write_line_to_file(full_path, 1,value);
		}
	}
	fclose(fp);
}

static void write_mib_content_to_file(char *mib_name, char *mib_value)
{
	char file_name[40];
	char full_path[100];
	
	sprintf(file_name, "%s", mib_name);
	convert_lower(file_name);
	sprintf(full_path, "%s%s",DEFAULT_CONFIG_PATH,file_name); 
	write_line_to_file(full_path, 1,mib_value);
}

static void read_mib_content_from_file(char *mib_name, char *mib_value)
{
	char file_name[40];
	char full_path[100];

	sprintf(file_name, "%s", mib_name);
	convert_lower(file_name);
	sprintf(full_path, "%s%s",DEFAULT_CONFIG_PATH,file_name); 
	get_file_content(full_path, mib_value);
}

//////////////////////////////////////////////////////////////////////
int main(int argc, char** argv)
{
	int argNum=1;
//	int i;
	
//	int wlan_idx=0;
	if ( argc > 1 ) {

		// for debugging
#if 0		
		printf("Flash Receive Command: ");
		for (i=0;i<argc;i++)
	{
			printf("%s ", argv[i]);
	}
		printf("\n");
#endif		
		if ( !strcmp(argv[argNum], "wpa") ) {
			if ((argNum+2) < argc) {
				if (memcmp(argv[++argNum], "wlan", 4)) {
					printf("Miss wlan_interface argument!\n");
					return 0;
				}
				generateWpaConf(argv[argNum+1], 0,argv[argNum+2]);
				return 0;
			}
			else {
				printf("Miss arguments [wlan_interface config_filename]!\n");
				return 0;
			}
		}
		// set flash parameters by reading from file
		else if ( !strcmp(argv[argNum], "-param_file") ) {
			if ((argNum+2) < argc) {
				if (memcmp(argv[++argNum], "wlan", 4)) {
					printf("Miss wlan_interface argument!\n");
					return 0;
				}
#if 0
				wlan_idx = atoi(&argv[argNum][4]);
				if (wlan_idx >= NUM_WLAN_INTERFACE) {
					printf("invalid wlan interface index number!\n");
					return 0;
				}
#endif
				readFileSetParam(argv[argNum+1], argv[argNum]);
			}
			else
				printf("Miss arguments [wlan_interface param_file]!\n");
			return 0;
		}

		else if ( !strcmp(argv[argNum], "upd-wsc-conf") ) {
			
			return updateWscConf(argv[argNum+1], argv[argNum+2], 0, argv[argNum+3]);			
		}
		else if ( !strcmp(argv[argNum], "gen-pin") ) {
			return updateWscConf(0, 0, 1, argv[argNum+1]);			
		}else  if ( !strcmp(argv[argNum], "set") ) {
			write_mib_content_to_file(argv[argNum+1], argv[argNum+2]);
		}else if ( !strcmp(argv[argNum], "sethw") ) {	//flash sethw
			unsigned char pwr;
			char mib[100]={0};
			char mibvalue[256+1]={0};

			argNum++;
			if (!memcmp((argv[argNum]+9), "TX_POWER", 8)) {
				sprintf(mib, "%s", (argv[argNum++]+9));
				while( (argNum < argc) &&  (argNum < 31) ) {
					pwr = atoi(argv[argNum++]);
					sprintf(mibvalue, "%s%02x", mibvalue, pwr);
				}
//				printf("Flash: write file %s:%s\n", mib, mibvalue);
				write_mib_content_to_file(mib, mibvalue);
			}
			else {
				while((argNum + 1) < argc)
				{
					if((!strcmp(argv[argNum], "HW_WLAN0_WLAN_ADDR")) ) {
						sprintf(mib, "wlan0_addr");
						argNum++;
					}
					else if (!memcmp(argv[argNum], "HW_WLAN", 7)) {
						sprintf(mib, "%s", (argv[argNum++]+9));
					}
					else {
						strcpy(mib, argv[argNum++]);
					}
					sscanf(argv[argNum++], "%s", mibvalue);

//					printf("Flash: write file %s:%s\n", mib, mibvalue);
					write_mib_content_to_file(mib, mibvalue);
				}
			}
		}
		else if ( !strcmp(argv[argNum], "gethw") ) {
			char buffer[512];
			char mib[100]={0};

			argNum++;
			while(argNum < argc)
			{
				if((!strcmp(argv[argNum], "HW_NIC0_ADDR"))	|| (!strcmp(argv[argNum], "HW_WLAN0_WLAN_ADDR")) ) {
					sprintf(mib, "wlan0_addr");
				}
				else if (!memcmp(argv[argNum], "HW_WLAN", 7)) {
					sprintf(mib, "%s", (argv[argNum]+9));
				}
				else {
					strcpy(mib, argv[argNum]);
				}
	
				read_mib_content_from_file(mib, buffer);
				printf("%s=%s",argv[argNum],buffer);
	
				argNum++;
			}
		} else {
			printf("flash: Unknown Command: %s\n", argv[argNum]);
		}		

	}
	
	return 0;
}
#if 0
////////////////////////////////////////////////////////////////////////////////
static void getVal2(char *value, char **p1, char **p2)
{
	value = getVal(value, p1);
	if ( value )
		getVal(value, p2);
	else
		*p2 = NULL;
}

////////////////////////////////////////////////////////////////////////////////
static int getdir(char *fullname, char *path, int loop)
{
	char tmpBuf[100], *p, *p1;

	strcpy(tmpBuf, fullname);
	path[0] = '\0';

	p1 = tmpBuf;
	while (1) {
		if ((p=strchr(p1, '/'))) {
			if (--loop == 0) {
				*p = '\0';
				strcpy(path, tmpBuf);
				return 0;
			}
			p1 = ++p;
		}
		else
			break;
	}
	return -1;
}
#endif
////////////////////////////////////////////////////////////////////////////////
static void __inline__ WRITE_WPA_FILE(int fh, char *buf)
{
	if ( write(fh, buf, strlen(buf)) != strlen(buf) ) {
		printf("Write WPA config file error!\n");
		close(fh);
		exit(1);
	}
}

#if READ_MIB_FROM_DEVICE
#else
////////////////////////////////////////////////////////////////////////////////
static void generateWpaConf(char *outputFile, int isWds, char *iface)
{
	int fh, intVal , auth_val , encrypt, enable1x, wep;
	char buf1[1024], buf2[1024];
	char config_path[100]={0};
	char config_value[100]={0};
	char tmp1[100], tmpbuf[100];
	int get_result=0;
	char default_config_path[64]={0};
	int repeater_enabled=0;
#ifdef CONFIG_IEEE80211W
	int pmf_enabled=0;
#endif /* CONFIG_IEEE80211W */
	
	sprintf(default_config_path, WIFI_CONFIG_ROOT_DIR "/%s/", iface);
	fh = open(outputFile, O_RDWR|O_CREAT|O_TRUNC, S_IRGRP|S_IWGRP);
	if (fh == -1) {
		printf("Create WPA config file error!\n");
		return;
	}
	if (!isWds) {
		
			//apmib_get( MIB_WLAN_ENCRYPT, (void *)&encrypt);
			sprintf(config_path, "%s%s", default_config_path, "encrypt");
			get_result = get_file_content(config_path, config_value);
			encrypt = atoi(config_value);
			
			sprintf(buf2, "encryption = %d\n", encrypt);
			WRITE_WPA_FILE(fh, buf2);
		
		
			//apmib_get( MIB_WLAN_SSID,  (void *)buf1);
			if(!strcmp(iface, "wlan0-vxd")){
				sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "repeater_enabled");
				get_result = get_file_content(config_path, config_value);
				repeater_enabled = atoi(config_value);
				if(repeater_enabled==1){
					sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "repeater_ssid");
					get_result = get_file_content(config_path, config_value);
				}else{
					sprintf(config_path, "%s%s", default_config_path, "ssid");
				}
			}else{
				sprintf(config_path, "%s%s", default_config_path, "ssid");
			}
			
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1,"%s", config_value);
			
			sprintf(buf2, "ssid = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ENABLE_1X, (void *)&enable1x);
			sprintf(config_path, "%s%s", default_config_path, "enable_1x");
			get_result = get_file_content(config_path, config_value);
			enable1x = atoi(config_value);
			
			sprintf(buf2, "enable1x = %d\n", enable1x);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_MAC_AUTH_ENABLED, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "mac_auth_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "enableMacAuth = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
			
			//apmib_get( MIB_WLAN_ENABLE_SUPP_NONWPA, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "enable_supp_nonwpa");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			
			if (intVal){
				//apmib_get( MIB_WLAN_SUPP_NONWPA, (void *)&intVal);
				sprintf(config_path, "%s%s", default_config_path, "supp_nonwpa");
				get_result = get_file_content(config_path, config_value);
				intVal = atoi(config_value);
			}
			sprintf(buf2, "supportNonWpaClient = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WEP, (void *)&wep);
			sprintf(config_path, "%s%s", default_config_path, "wep");
			get_result = get_file_content(config_path, config_value);
			wep = atoi(config_value);
			
			sprintf(buf2, "wepKey = %d\n", wep);
			WRITE_WPA_FILE(fh, buf2);
		
			if ( encrypt==1 && enable1x ) {
				if (wep == 1) {
					//apmib_get( MIB_WLAN_WEP64_KEY1, (void *)buf1);
					sprintf(config_path, "%s%s", default_config_path, "wepkey1_64_asc");
					if( get_file_content(config_path, config_value)<0 )
					{
						strcpy(buf2, "wepGroupKey = \"\"\n");
					}
					else
					{
						sprintf(tmpbuf, "%s", config_value);
						memcpy(tmp1, tmpbuf, 5);
						tmp1[5] = '\0';
						sprintf(buf2, "wepGroupKey = \"%02x%02x%02x%02x%02x\"\n", tmp1[0],tmp1[1],tmp1[2],tmp1[3],tmp1[4]);
					}
				}
				else {
					//apmib_get( MIB_WLAN_WEP128_KEY1, (void *)buf1);
					sprintf(config_path, "%s%s", default_config_path, "wepkey1_128_asc");
					if( get_file_content(config_path, config_value)<0 )
					{
						strcpy(buf2, "wepGroupKey = \"\"\n");
					}
					else
					{
						sprintf(tmpbuf, "%s", config_value);
						memcpy(tmp1, tmpbuf, 13);
						tmp1[13] = '\0';
						sprintf(buf2, "wepGroupKey = \"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x\"\n",
							tmp1[0],tmp1[1],tmp1[2],tmp1[3],tmp1[4],
							tmp1[5],tmp1[6],tmp1[7],tmp1[8],tmp1[9],
							tmp1[10],tmp1[11],tmp1[12]);
					}
				}
			}
			else
				strcpy(buf2, "wepGroupKey = \"\"\n");
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WPA_AUTH, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "wpa_auth");
			get_result = get_file_content(config_path, config_value);
			auth_val = atoi(config_value);

#ifndef CONFIG_IEEE80211W
			sprintf(buf2, "authentication = %d\n", auth_val );
			WRITE_WPA_FILE(fh, buf2);
#else
			sprintf(config_path, "%s%s", default_config_path, "wpa11w");
			get_result = get_file_content(config_path, config_value);
			pmf_enabled = atoi(config_value);
			sprintf((char *)buf2, "ieee80211w = %d\n", pmf_enabled);
			WRITE_WPA_FILE(fh, buf2);

			sprintf(config_path, "%s%s", default_config_path, "wpa2EnableSHA256");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			if (pmf_enabled == 0) {
				intVal = 0;
			} else if (pmf_enabled == 1) {
				if (intVal != 0 && intVal != 1) {
					intVal = 0;
				}
			} else if (pmf_enabled == 2) {
				intVal = 1;
			}
			sprintf((char *)buf2, "sha256 = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);

			if( (intVal==1) && (auth_val==1) )
			{
				sprintf(buf2, "authentication = 5\n" );
				WRITE_WPA_FILE(fh, buf2);
			}
			else
			{
				sprintf(buf2, "authentication = %d\n", auth_val );
				WRITE_WPA_FILE(fh, buf2);
			}


#endif

			//apmib_get( MIB_WLAN_WPA_CIPHER_SUITE, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "wpa_cipher");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "unicastCipher = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WPA2_CIPHER_SUITE, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "wpa2_cipher");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "wpa2UnicastCipher = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WPA2_PRE_AUTH, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "wpa2_pre_auth");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "enablePreAuth = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_PSK_FORMAT, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "psk_format");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			if (intVal==0)
				sprintf(buf2, "usePassphrase = 1\n");
			else
				sprintf(buf2, "usePassphrase = 0\n");
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WPA_PSK, (void *)buf1);
			
			sprintf(config_path, "%s%s", default_config_path, "wpa_psk");
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "psk = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_WPA_GROUP_REKEY_TIME, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "gk_rekey");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "groupRekeyTime = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_RS_PORT, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "rs_port");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "rsPort = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_RS_IP, (void *)buf1);
			sprintf(config_path, "%s%s", default_config_path, "rs_ip");
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "rsIP = %s\n",buf1);
			//sprintf(buf2, "rsIP = %s\n", inet_ntoa(*((struct in_addr *)buf1)));
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_RS_PASSWORD, (void *)buf1);
			sprintf(config_path, "%s%s", default_config_path, "rs_password");
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "rsPassword = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);

			sprintf(config_path, "%s%s", default_config_path, "rs2_port");
			if( get_result = get_file_content(config_path, config_value)>0 )
			{
				intVal = atoi(config_value);
				sprintf(buf2, "rs2Port = %d\n", intVal);
				WRITE_WPA_FILE(fh, buf2);
			}
			
			sprintf(config_path, "%s%s", default_config_path, "rs2_ip");
			if( get_result = get_file_content(config_path, config_value)>0 )
			{
				intVal = atoi(config_value);
				sprintf(buf2, "rs2IP = %d\n", intVal);
				WRITE_WPA_FILE(fh, buf2);
			}
			
			sprintf(config_path, "%s%s", default_config_path, "rs2_password");
			if( get_result = get_file_content(config_path, config_value)>0 )
			{
				intVal = atoi(config_value);
				sprintf(buf2, "rs2Password = %d\n", intVal);
				WRITE_WPA_FILE(fh, buf2);
			}
		
			//apmib_get( MIB_WLAN_RS_MAXRETRY, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "rs_maxretry");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "rsMaxReq = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
		
		//	apmib_get( MIB_WLAN_RS_INTERVAL_TIME, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "rs_interval_time");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "rsAWhile = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_ENABLED, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "accountRsEnabled = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_PORT, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_port");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "accountRsPort = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_IP, (void *)buf1);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_ip");
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "accountRsIP = %s\n", buf1);
			//sprintf(buf2, "accountRsIP = %s\n", inet_ntoa(*((struct in_addr *)buf1)));
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_PASSWORD, (void *)buf1);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_password");
			get_result = get_file_content(config_path, config_value);
			//config_value[strlen(config_value)-1]='\0';
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "accountRsPassword = \"%s\"\n", buf1);
			
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_UPDATE_ENABLED, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_update_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "accountRsUpdateEnabled = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_UPDATE_DELAY, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_update_delay");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "accountRsUpdateTime = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_MAXRETRY, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_maxretry");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "accountRsMaxReq = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			//apmib_get( MIB_WLAN_ACCOUNT_RS_INTERVAL_TIME, (void *)&intVal);
			sprintf(config_path, "%s%s", default_config_path, "account_rs_interval_time");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			sprintf(buf2, "accountRsAWhile = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);

	}
	close(fh);
}
#endif

////////////////////////////////////////////////////////////////////////////////
#if 0
void calc_incr(unsigned char *mac, int idx)
{
	if( (*mac+idx) == 0x0 )
		calc_incr(mac-1,1);
	else
		*mac += idx;
}

int get_root_mac(unsigned char *mac)
{
	int fd;
	struct ifreq ifr;
	unsigned char zero_mac[6]={0}, broadcat_mac[6]={0xff};

	fd = socket(AF_INET, SOCK_DGRAM, 0);

	ifr.ifr_addr.sa_family = AF_INET;
	strcpy(ifr.ifr_name, "wlan0");
	if( ioctl(fd, SIOCGIFHWADDR, &ifr) < 0 )
		return -1;

	close(fd);
	if( !memcmp(ifr.ifr_hwaddr.sa_data,zero_mac,6) || !memcmp(ifr.ifr_hwaddr.sa_data,broadcat_mac,6) )
		return -1;

	memcpy(mac,ifr.ifr_hwaddr.sa_data,6);
	return 0;
}

#endif
enum { 
	MODE_AP_UNCONFIG=1, 			// AP unconfigured (enrollee)
	MODE_CLIENT_UNCONFIG=2, 		// client unconfigured (enrollee) 
	MODE_CLIENT_CONFIG=3,			// client configured (registrar) 
	MODE_AP_PROXY=4, 			// AP configured (proxy)
	MODE_AP_PROXY_REGISTRAR=5,		// AP configured (proxy and registrar)
	MODE_CLIENT_UNCONFIG_REGISTRAR=6		// client unconfigured (registrar) 
};

#define WRITE_WSC_PARAM(dst, tmp, str, val) {	\
	sprintf(tmp, str, val); \
	memcpy(dst, tmp, strlen(tmp)); \
	dst += strlen(tmp); \
}

static void convert_bin_to_str(char *bin, int len, char *out)
{
	int i;
	char tmpbuf[10];

	out[0] = '\0';

	for (i=0; i<len; i++) {
		sprintf(tmpbuf, "%02x", bin[i]);
		strcat(out, tmpbuf);
	}
}

static void convert_hex_to_ascii(unsigned long code, char *out)
{
	*out++ = '0' + ((code / 10000000) % 10);  
	*out++ = '0' + ((code / 1000000) % 10);
	*out++ = '0' + ((code / 100000) % 10);
	*out++ = '0' + ((code / 10000) % 10);
	*out++ = '0' + ((code / 1000) % 10);
	*out++ = '0' + ((code / 100) % 10);
	*out++ = '0' + ((code / 10) % 10);
	*out++ = '0' + ((code / 1) % 10);
	*out = '\0';
}

static int compute_pin_checksum(unsigned long int PIN)
{
	unsigned long int accum = 0;
	int digit;
	
	PIN *= 10;
	accum += 3 * ((PIN / 10000000) % 10); 	
	accum += 1 * ((PIN / 1000000) % 10);
	accum += 3 * ((PIN / 100000) % 10);
	accum += 1 * ((PIN / 10000) % 10); 
	accum += 3 * ((PIN / 1000) % 10); 
	accum += 1 * ((PIN / 100) % 10); 
	accum += 3 * ((PIN / 10) % 10);

	digit = (accum % 10);
	return (10 - digit) % 10;
}
int write_line_to_file(char *filename, int mode, char *line_data)
{
	char tmpbuf[512];
	int fh=0;

	if(mode == 1) {/* write line data to file */
		
		fh = open(filename, O_RDWR|O_CREAT|O_TRUNC, S_IWGRP|S_IRGRP);
		
	}else if(mode == 2){/*append line data to file*/
		
		fh = open(filename, O_RDWR|O_APPEND);	
	}
	
	
	if (fh < 0) {
		fprintf(stderr, "Create %s error!\n", filename);
		return 0;
	}


	sprintf(tmpbuf, "%s", line_data);
	write(fh, tmpbuf, strlen(tmpbuf));



	close(fh);
	return 1;
}

int get_file_content(char *file_name, char *dest_buffer)
{
	FILE *fp;
	char buf[150];
	int check_end_char=0;
	fp= fopen(file_name, "r");
	if (!fp) {
    printf("can't open file:%s!\n",file_name);
    dest_buffer[0]=0;
		return -1;
  }
  fgets(buf,150,fp);
	fclose(fp);
	sprintf(dest_buffer, "%s", buf);
	for(check_end_char=0; check_end_char< strlen(dest_buffer);check_end_char++){
		if(dest_buffer[check_end_char]==0x0A){
			dest_buffer[check_end_char]='\0';
			break;
		}
	}
	return 1;
}




#if READ_MIB_FROM_DEVICE
#else
static int updateWscConf(char *in, char *out, int genpin, char *wlanif_name)
{
	int fh;
	struct stat status;
	char *buf, *ptr;
	int intVal, is_client, is_config, is_registrar, len, is_wep=0, wep_key_type=0, wep_transmit_key=0;
	char tmpbuf[100], tmp1[200];
	
	int isUpnpEnabled=0, wsc_method = 0, wsc_auth=0, wsc_enc=0;
	int wlan_network_type=0, wsc_manual_enabled=0, wlan_encrpty=0, wlan_wep=0;
	//char wlan_wep64_key1[100], wlan_wep64_key2[100], wlan_wep64_key3[100], wlan_wep64_key4[100];
	//char wlan_wep128_key1[100], wlan_wep128_key2[100], wlan_wep128_key3[100], wlan_wep128_key4[100];
#ifdef FOR_DUAL_BAND
	char wlan_wpa_psk[100];
#endif
	char wlan_ssid[100], device_name[100];//, wsc_pin[100];
	int wlan_chan_num=0, wsc_config_by_ext_reg=0;
//	int is_vxdif=0;
	char default_config_path[64]={0};
	char config_path[100]={0};
	char config_value[100]={0};
	char tmp_config_value[100]={0};
	char tmp_config_path[100]={0};
	int get_result=0;

	// WPS2DOTX
	int wlan0_wlan_disabled=0;	
	int wlan0_wsc_disabled=0;	
#ifdef FOR_DUAL_BAND
	int wlan1_wlan_disabled=0;
	int wlan1_wsc_disabled=0;		
#endif
	
	// WPS2DOTX
	
	int band_select_5g2g;  // 0:2.4g  ; 1:5G   ; 2:both
	
	printf("\r\n wlanif_name=[%s],__[%s-%u]\r\n",wlanif_name,__FILE__,__LINE__);	

	sprintf(default_config_path, WIFI_CONFIG_ROOT_DIR "/%s/", wlanif_name);
	
	//apmib_get(MIB_WLAN_WSC_REGISTRAR_ENABLED, (void *)&is_registrar);	
	sprintf(config_path, "%s%s", default_config_path, "wsc_registrar_enabled");
	get_result = get_file_content(config_path, config_value);
	is_registrar = atoi(config_value);

	//apmib_get(MIB_WLAN_WSC_UPNP_ENABLED, (void *)&isUpnpEnabled);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_upnp_enabled");
	get_result = get_file_content(config_path, config_value);
	isUpnpEnabled = atoi(config_value);
		
	
	//apmib_get(MIB_WLAN_WSC_METHOD, (void *)&wsc_method);
	sprintf(config_path, "%s%s", default_config_path, "wsc_method");
	get_result = get_file_content(config_path, config_value);
	wsc_method = atoi(config_value);

	//apmib_get(MIB_WLAN_NETWORK_TYPE, (void *)&wlan_network_type);
	sprintf(config_path, "%s%s", default_config_path, "network_type");
	get_result = get_file_content(config_path, config_value);
	wlan_network_type = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WSC_MANUAL_ENABLED, (void *)&wsc_manual_enabled);
	sprintf(config_path, "%s%s", default_config_path, "wsc_manual_enabled");
	get_result = get_file_content(config_path, config_value);
	wsc_manual_enabled = atoi(config_value);
	
	//apmib_get(MIB_WLAN_CHANNEL, (void *)&wlan_chan_num);
	sprintf(config_path, "%s%s", default_config_path, "channel");
	get_result = get_file_content(config_path, config_value);
	wlan_chan_num=atoi(config_value);
	
	//apmib_get(MIB_DEVICE_NAME, (void *)&device_name);
	sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "device_name");
	get_result = get_file_content(config_path, config_value);
	//config_value[strlen(config_value)-1]='\0';
	sprintf(device_name, "%s", config_value);
	
	
	//apmib_get(MIB_WLAN_BAND2G5G_SELECT, (void *)&band_select_5g2g);	
	sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "band2g5g_select");
	get_result = get_file_content(config_path, config_value);
	band_select_5g2g=atoi(config_value);
	
	
	
	

	//apmib_get(MIB_WLAN_WSC_AUTH, (void *)&wsc_auth);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_auth");
	get_result = get_file_content(config_path, config_value);
	wsc_auth = atoi(config_value);
	
	
	//apmib_get(MIB_WLAN_WSC_ENC, (void *)&wsc_enc);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_enc");
	get_result = get_file_content(config_path, config_value);
	wsc_enc = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WSC_CONFIGURED, (void *)&is_config);
	sprintf(config_path, "%s%s", default_config_path, "wsc_configured");
	get_result = get_file_content(config_path, config_value);
	is_config = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WSC_CONFIGBYEXTREG, (void *)&wsc_config_by_ext_reg);
	sprintf(config_path, "%s%s", default_config_path, "wsc_configbyextreg");
	get_result = get_file_content(config_path, config_value);
	wsc_config_by_ext_reg=atoi(config_value);
	
	//apmib_get(MIB_WLAN_SSID, (void *)&wlan_ssid);	
	
	sprintf(config_path, "%s%s", default_config_path, "ssid");
	get_result = get_file_content(config_path, config_value);
	sprintf(wlan_ssid, "%s", config_value);
	
	//apmib_get(MIB_WLAN_MODE, (void *)&is_client);
	sprintf(config_path, "%s%s", default_config_path, "wlan_mode");
	get_result = get_file_content(config_path, config_value);
	is_client = atoi(config_value);	
	
	//apmib_get(MIB_WLAN_ENCRYPT, (void *)&wlan_encrpty);
	
	sprintf(config_path, "%s%s", default_config_path, "encrypt");
	get_result = get_file_content(config_path, config_value);
	wlan_encrpty = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WEP, (void *)&wlan_wep);
	sprintf(config_path, "%s%s", default_config_path, "wep");
	get_result = get_file_content(config_path, config_value);
	wlan_wep = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WEP_KEY_TYPE, (void *)&wep_key_type);
	sprintf(config_path, "%s%s", default_config_path, "wep_key_type");
	get_result = get_file_content(config_path, config_value);
	wep_key_type = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WEP_DEFAULT_KEY, (void *)&wep_transmit_key);
	
	sprintf(config_path, "%s%s", default_config_path, "wep_default_key");
	get_result = get_file_content(config_path, config_value);
	wep_transmit_key = atoi(config_value);
	
	
	
	//apmib_get(MIB_WLAN_WPA_PSK, (void *)&wlan_wpa_psk);
	//apmib_get(MIB_WLAN_WSC_DISABLE, (void *)&wlan0_wsc_disabled);	// 1104
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan0_wsc_disabled = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WLAN_DISABLED, (void *)&wlan0_wlan_disabled);	// 0908	
	
	sprintf(config_path, "%s%s", default_config_path, "wlan_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan0_wlan_disabled = atoi(config_value);
	
	/* WPS2DOTX ; 2011-0524*/ 	
	if(wlan0_wlan_disabled)
		wlan0_wsc_disabled=1 ; // if wlan0 interface is disabled ;
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_pin");
	get_result = get_file_content(config_path, config_value);
	
	if (genpin || ((get_result != -1) && !memcmp(config_value, "\x0\x0\x0\x0\x0\x0\x0\x0", PIN_LEN))) {
		#include <sys/time.h>			
		struct timeval tod;
		unsigned long num;
		
		gettimeofday(&tod , NULL);

		sprintf(tmp_config_path, "%s%s", default_config_path, "nic0_addr");
		get_result = get_file_content(tmp_config_path, tmp_config_value);
		//apmib_get(MIB_HW_NIC0_ADDR, (void *)&tmp1);	
		if(get_result != -1){
			string_to_hex(tmp_config_value,tmp1, 12);
		
			tod.tv_sec += tmp1[4]+tmp1[5];		
			srand(tod.tv_sec);
			num = rand() % 10000000;
			num = num*10 + compute_pin_checksum(num);
			convert_hex_to_ascii((unsigned long)num, tmpbuf);
	
			//apmib_set(MIB_HW_WSC_PIN, (void *)tmpbuf);
			write_line_to_file(config_path, 1,tmpbuf);
			write_line_to_file(WIFI_CONFIG_ROOT_DIR "/wlan1/wsc_pin", 1,tmpbuf);
			printf("Generated PIN = %s\n", tmpbuf);
		}	
		if (genpin)
			return 0;
	}
	
	if (stat(in, &status) < 0) {
		printf("stat() error [%s]!\n", in);
		return -1;
	}

	buf = malloc(status.st_size+2048);
	if (buf == NULL) {
		printf("malloc() error [%d]!\n", (int)status.st_size+2048);
		return -1;		
	}

	ptr = buf;

	if (is_client == CLIENT_MODE) {
		{
			if (is_registrar) {
				if (!is_config)
					intVal = MODE_CLIENT_UNCONFIG_REGISTRAR;
				else
					intVal = MODE_CLIENT_CONFIG;			
			}
			else
				intVal = MODE_CLIENT_UNCONFIG;
		}
	}
	else {
		if (!is_config)
			intVal = MODE_AP_UNCONFIG;
		else
			intVal = MODE_AP_PROXY_REGISTRAR;
	}
	WRITE_WSC_PARAM(ptr, tmpbuf, "mode = %d\n", intVal);

	
	if (is_client == CLIENT_MODE) {
			intVal = 0;
	}
	else
		intVal = isUpnpEnabled;

	WRITE_WSC_PARAM(ptr, tmpbuf, "upnp = %d\n", intVal);

	intVal = 0;
	intVal = wsc_method;
	//Ethernet(0x2)+Label(0x4)+PushButton(0x80) Bitwise OR
	if (intVal == 1) //Pin+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PIN);
	else if (intVal == 2) //PBC+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PBC);
	if (intVal == 3) //Pin+PBC+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PIN | CONFIG_METHOD_PBC);
	WRITE_WSC_PARAM(ptr, tmpbuf, "config_method = %d\n", intVal);

	// 1104
	WRITE_WSC_PARAM(ptr, tmpbuf, "wlan0_wsc_disabled = %d\n", wlan0_wsc_disabled);
	
	WRITE_WSC_PARAM(ptr, tmpbuf, "auth_type = %d\n", wsc_auth);
	
	WRITE_WSC_PARAM(ptr, tmpbuf, "encrypt_type = %d\n", wsc_enc);

	if (wsc_enc == WSC_ENCRYPT_WEP)
		is_wep = 1;

	if(wlanif_name != NULL && (strcmp(wlanif_name,"wlan0") == 0 || strcmp(wlanif_name,"wlan1") == 0))
	{
		if (is_client == CLIENT_MODE) 
		{
			if (wlan_network_type == 0)
				intVal = 1;
			else
				intVal = 2;
		}
		else
			intVal = 1;
	}
	else
		intVal = 1;
		
	WRITE_WSC_PARAM(ptr, tmpbuf, "connection_type = %d\n", intVal);
	
	WRITE_WSC_PARAM(ptr, tmpbuf, "manual_config = %d\n", wsc_manual_enabled);


	if (is_wep) { // only allow WEP in none-MANUAL mode (configured by external registrar)		
		if (wlan_encrpty != ENCRYPT_WEP) {
			printf("WEP mismatched between WPS and host system\n");
			free(buf);
			return -1;
		}		
		if (wlan_wep <= WEP_DISABLED || wlan_wep > WEP128) {
			printf("WEP encrypt length error\n");
			free(buf);
			return -1;
		}
		
		wep_transmit_key++;
		WRITE_WSC_PARAM(ptr, tmpbuf, "wep_transmit_key = %d\n", wep_transmit_key);

		/*whatever key type is ASSIC or HEX always use String-By-Hex fromat
		;2011-0419,fixed,need patch with wscd daemon , search 2011-0419*/ 
		if (wlan_wep == WEP64) {
		//	apmib_get(MIB_WLAN_WEP64_KEY1, (void *)&wlan_wep64_key1);
						
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}		
			WRITE_WSC_PARAM(ptr, tmpbuf, "network_key = %s\n", tmp1);

//apmib_get(MIB_WLAN_WEP64_KEY2, (void *)&wlan_wep64_key2);
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			

			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key2 = %s\n", tmp1);

//apmib_get(MIB_WLAN_WEP64_KEY3, (void *)&wlan_wep64_key3);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			

			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key3 = %s\n", tmp1);

//apmib_get(MIB_WLAN_WEP64_KEY4, (void *)&wlan_wep64_key4);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			
			
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key4 = %s\n", tmp1);
		}
		else {
			//apmib_get(MIB_WLAN_WEP128_KEY1, (void *)&wlan_wep128_key1);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}


			WRITE_WSC_PARAM(ptr, tmpbuf, "network_key = %s\n", tmp1);
//apmib_get(MIB_WLAN_WEP128_KEY2, (void *)&wlan_wep128_key2);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}

			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key2 = %s\n", tmp1);
//apmib_get(MIB_WLAN_WEP128_KEY3, (void *)&wlan_wep128_key3);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}

			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key3 = %s\n", tmp1);
			
//apmib_get(MIB_WLAN_WEP128_KEY4, (void *)&wlan_wep128_key4);

			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key4 = %s\n", tmp1);
		}
	}
	else {
		sprintf(config_path, "%s%s", default_config_path, "wpa_psk");
		get_result = get_file_content(config_path, config_value);
		sprintf(tmp1, "%s", config_value);
		WRITE_WSC_PARAM(ptr, tmpbuf, "network_key = %s\n", tmp1);		
	}

	sprintf(config_path, "%s%s", default_config_path, "ssid");
	get_result = get_file_content(config_path, config_value);
	sprintf(tmp1, "%s", config_value);
	WRITE_WSC_PARAM(ptr, tmpbuf, "ssid = \"%s\"\n", tmp1);

	sprintf(config_path, "%s%s", default_config_path, "wsc_pin");
	get_result = get_file_content(config_path, config_value);
	//config_value[strlen(config_value)-1]='\0';
	sprintf(tmp1, "%s", config_value);
	WRITE_WSC_PARAM(ptr, tmpbuf, "pin_code = %s\n", tmp1);
	

	if (wlan_chan_num > 14)
		intVal = 2;
	else
		intVal = 1;
	WRITE_WSC_PARAM(ptr, tmpbuf, "rf_band = %d\n", intVal);

#ifdef FOR_DUAL_BAND
	/* switch to wlan1 */

	sprintf(default_config_path, WIFI_CONFIG_ROOT_DIR "/%s/", "wlan1");
	
	//apmib_get(MIB_WLAN_WSC_AUTH, (void *)&wsc_auth);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_auth");
	get_result = get_file_content(config_path, config_value);
	wsc_auth = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WSC_ENC, (void *)&wsc_enc);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_enc");
	get_result = get_file_content(config_path, config_value);
	wsc_enc = atoi(config_value);
	
//	apmib_get(MIB_WLAN_SSID, (void *)&wlan_ssid);
	
	sprintf(config_path, "%s%s", default_config_path, "ssid");
	get_result = get_file_content(config_path, config_value);
	sprintf(wlan_ssid, "%s", config_value);
	
		
//	apmib_get(MIB_WLAN_MODE, (void *)&is_client);	
	
	sprintf(config_path, "%s%s", default_config_path, "wlan_mode");
	get_result = get_file_content(config_path, config_value);
	is_client = atoi(config_value);	
	
	//apmib_get(MIB_WLAN_ENCRYPT, (void *)&wlan_encrpty);
	
	sprintf(config_path, "%s%s", default_config_path, "encrypt");
	get_result = get_file_content(config_path, config_value);
	wlan_encrpty = atoi(config_value);
	
	
	//apmib_get(MIB_WLAN_WEP, (void *)&wlan_wep);
	
	sprintf(config_path, "%s%s", default_config_path, "wep");
	get_result = get_file_content(config_path, config_value);
	wlan_wep = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WEP_KEY_TYPE, (void *)&wep_key_type);
	
	sprintf(config_path, "%s%s", default_config_path, "wep_key_type");
	get_result = get_file_content(config_path, config_value);
	wep_key_type = atoi(config_value);
	
	//apmib_get(MIB_WLAN_WEP_DEFAULT_KEY, (void *)&wep_transmit_key);
	
	sprintf(config_path, "%s%s", default_config_path, "wep_default_key");
	get_result = get_file_content(config_path, config_value);
	wep_transmit_key = atoi(config_value);
	
	
	
	
	
	
	//apmib_get(MIB_WLAN_WSC_DISABLE, (void *)&wlan1_wsc_disabled);	// 1104
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan1_wsc_disabled = atoi(config_value);
	
	
	//apmib_get(MIB_WLAN_WLAN_DISABLED, (void *)&wlan1_wlan_disabled);	// 0908	
	sprintf(config_path, "%s%s", default_config_path, "wlan_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan1_wlan_disabled = atoi(config_value);
	


	if(wlan1_wlan_disabled)
		wlan1_wsc_disabled = 1 ; // if wlan1 interface is disabled

	
	WRITE_WSC_PARAM(ptr, tmpbuf, "#=====wlan1 start==========%d\n",intVal);
	WRITE_WSC_PARAM(ptr, tmpbuf, "ssid2 = \"%s\"\n",wlan_ssid );	
	WRITE_WSC_PARAM(ptr, tmpbuf, "auth_type2 = %d\n", wsc_auth);
	WRITE_WSC_PARAM(ptr, tmpbuf, "encrypt_type2 = %d\n", wsc_enc);

	// 1104	
	if(band_select_5g2g!=2)		//  != dual band
	{
		intVal=1;
		WRITE_WSC_PARAM(ptr, tmpbuf, "wlan1_wsc_disabled = %d\n",intVal);
	}
	else	// else see if wlan1 is enabled
	{
		WRITE_WSC_PARAM(ptr, tmpbuf, "wlan1_wsc_disabled = %d\n", wlan1_wsc_disabled);		
	}

	is_wep = 0;
	
	if (wsc_enc == WSC_ENCRYPT_WEP)
		is_wep = 1;

	if (is_wep) { // only allow WEP in none-MANUAL mode (configured by external registrar)		
		if (wlan_encrpty != ENCRYPT_WEP) {
			printf("WEP mismatched between WPS and host system\n");
			free(buf);
			return -1;
		}		
		if (wlan_wep <= WEP_DISABLED || wlan_wep > WEP128) {
			printf("WEP encrypt length error\n");
			free(buf);
			return -1;
		}
		
		wep_transmit_key++;
		WRITE_WSC_PARAM(ptr, tmpbuf, "wep_transmit_key2 = %d\n", wep_transmit_key);


		/*whatever key type is ASSIC or HEX always use String-By-Hex fromat
		;2011-0419,fixed,need patch with wscd daemon , search 2011-0419*/ 
		if (wlan_wep == WEP64) {
			
			
			//apmib_get(MIB_WLAN_WEP64_KEY1, (void *)&wlan_wep64_key1);
			
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}		
				
			
			WRITE_WSC_PARAM(ptr, tmpbuf, "network_key2 = %s\n", tmp1);




			
			//apmib_get(MIB_WLAN_WEP64_KEY2, (void *)&wlan_wep64_key2);
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			
			
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key22 = %s\n", tmp1);

			
			//apmib_get(MIB_WLAN_WEP64_KEY3, (void *)&wlan_wep64_key3);
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key32 = %s\n", tmp1);


			
			//apmib_get(MIB_WLAN_WEP64_KEY4, (void *)&wlan_wep64_key4);	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_64_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 5);
				tmp1[5] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_64_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}			
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key42 = %s\n", tmp1);
		}
		else {
			

			//apmib_get(MIB_WLAN_WEP128_KEY1, (void *)&wlan_wep128_key1);
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey1_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}
			WRITE_WSC_PARAM(ptr, tmpbuf, "network_key2 = %s\n", tmp1);

			//apmib_get(MIB_WLAN_WEP128_KEY2, (void *)&wlan_wep128_key2);
	
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey2_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}
			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key22 = %s\n", tmp1);

			//apmib_get(MIB_WLAN_WEP128_KEY3, (void *)&wlan_wep128_key3);
			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey3_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}


			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key32 = %s\n", tmp1);

			//apmib_get(MIB_WLAN_WEP128_KEY4, (void *)&wlan_wep128_key4);

			if (wep_key_type == KEY_ASCII) {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_128_asc");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmpbuf, "%s", config_value);
				memcpy(tmp1, tmpbuf, 13);
				tmp1[13] = '\0';
			}
			else {
				sprintf(config_path, "%s%s", default_config_path, "wepkey4_128_hex");
				get_result = get_file_content(config_path, config_value);
				sprintf(tmp1, "%s", config_value);
			}

			WRITE_WSC_PARAM(ptr, tmpbuf, "wep_key42 = %s\n", tmp1);
		}
	}
	else {
		
		//apmib_get(MIB_WLAN_WPA_PSK, (void *)&wlan_wpa_psk);
		sprintf(config_path, "%s%s", default_config_path, "wpa_psk");
		get_result = get_file_content(config_path, config_value);
		sprintf(wlan_wpa_psk, "%s", config_value);
		WRITE_WSC_PARAM(ptr, tmpbuf, "network_key2 = %s\n", wlan_wpa_psk);		
	}



	intVal =2 ;
	WRITE_WSC_PARAM(ptr, tmpbuf, "#=====wlan1 end==========%d\n",intVal);

	/*sync the PIN code of wlan0 and wlan1*/
	//apmib_set(MIB_HW_WSC_PIN, (void *)wsc_pin);


	
	
#endif	// END of FOR_DUAL_BAND
	WRITE_WSC_PARAM(ptr, tmpbuf, "device_name = \"%s\"\n", device_name);
	
	WRITE_WSC_PARAM(ptr, tmpbuf, "config_by_ext_reg = %d\n", wsc_config_by_ext_reg);

	len = (int)(((long)ptr)-((long)buf));
	
	fh = open(in, O_RDONLY);
	if (fh == -1) {
		printf("open() error [%s]!\n", in);
		return -1;
	}

	lseek(fh, 0L, SEEK_SET);
	if (read(fh, ptr, status.st_size) != status.st_size) {		
		printf("read() error [%s]!\n", in);
		return -1;	
	}
	close(fh);

	// search UUID field, replace last 12 char with hw mac address
	ptr = strstr(ptr, "uuid =");
	if (ptr) {
		char tmp2[100];
//		apmib_get(MIB_HW_NIC0_ADDR, (void *)&tmp1);
		sprintf(tmp_config_path, "%s%s", default_config_path, "nic0_addr");
		get_result = get_file_content(tmp_config_path, tmp_config_value);
		if(get_result != -1){	
			string_to_hex(tmp_config_value,tmp1, 12);
			convert_bin_to_str(tmp1, 6, tmp2);
			memcpy(ptr+27, tmp2, 12);		
		}
	}

	fh = open(out, O_RDWR|O_CREAT|O_TRUNC, S_IWGRP|S_IRGRP);
	if (fh == -1) {
		printf("open() error [%s]!\n", out);
		return -1;
	}

	if (write(fh, buf, len+status.st_size) != len+status.st_size ) {
		printf("Write() file error [%s]!\n", out);
		return -1;
	}
	close(fh);
	free(buf);


	
	return 0;
}
#endif



#if READ_MIB_FROM_DEVICE

#define IW_MAX_PRIV_DEF	64
int get_priv_info_flag=0;
int priv_info_number;

int iw_get_priv_info(int skfd, char * ifname, struct iw_priv_args *	priv, int maxpriv )
{
  struct iwreq		wrq;
  /* Ask the driver */
  wrq.u.data.pointer = (caddr_t) priv;
  wrq.u.data.length = maxpriv;
  wrq.u.data.flags = 0;


	strncpy(wrq.ifr_name, ifname, IFNAMSIZ);
	if( ioctl(skfd, SIOCGIWPRIV , &wrq)<0 )
		return(-1);

  //if(iw_get_ext(skfd, ifname, SIOCGIWPRIV, &wrq) < 0)
  //  return(-1);


  return(wrq.u.data.length);
}

 struct iw_priv_args	priv[IW_MAX_PRIV_DEF];

static int
mib_cmd(	char *		ifname,		/* Dev name */
					char *		cmdname,	/* Command name */
					char *		args,		/* Command line args */ 
					char *		out_buf,
					int 			out_buf_max_len )
{
  struct iwreq	wrq,*pwrq;  
  u_char	buffer[4096];	/* Only that big in v25 and later */
  int		i = 0,j;		/* Start with first command arg */
  int		k;		/* Index in private description table */
  int		temp;
  int		subcmd = 0;	/* sub-ioctl index */


 
  int		number;		/* Max of private ioctl */
  int skfd;

	pwrq = &wrq;

  skfd = socket( AF_INET , SOCK_DGRAM, 0);
	if(skfd < 0)
	{
		printf("open socket failure skfd=%d\n",skfd);
		return skfd;
  }
 
 
  if( get_priv_info_flag==0 )
  {
		memset( priv , 0 , sizeof(priv) );
  	number = iw_get_priv_info(skfd, ifname, priv, IW_MAX_PRIV_DEF);
	  if(number <= 0)
	  {
	      /* Could skip this message ? */
	      fprintf(stderr, "%-8.8s  no private ioctls.\n\n",ifname);
	      close(skfd);
	      return(-1);
	  }
	  get_priv_info_flag=1;
	  priv_info_number = number;
	  
  }
  else
  {
  	number = priv_info_number;
  }
 
  /*for( j=0 ; j<number ; j++ )
  {
  	printf("args[%d]=%08x, set_args=%04x, get_args=%04x, name=%s\n", j , priv[j].cmd , priv[j].set_args , priv[j].get_args ,  priv[j].name );
  }
  printf("set_private number=%d\n", number );*/
  //printf("private_cmd, args[0]=%s, ifname=%s, cmdname=%s\n",args[0], ifname, cmdname );


  /* Search the correct ioctl */
  k = -1;
  while((++k < number) && strcmp(priv[k].name, cmdname));
  if(k == number)
	{
      fprintf(stderr, "Invalid command : %s\n", cmdname);
      close(skfd);
      return(-1);
	}

  /* If we have to set some data */
  if((priv[k].set_args & IW_PRIV_TYPE_MASK) && (priv[k].set_args & IW_PRIV_SIZE_MASK))
	{
		switch(priv[k].set_args & IW_PRIV_TYPE_MASK)
		{
			case IW_PRIV_TYPE_CHAR:
			  printf("IW_PRIV_TYPE_CHAR\n");
	      /* Size of the string to fetch */
	      pwrq->u.data.length = strlen(args) + 1;
	      if(pwrq->u.data.length > (priv[k].set_args & IW_PRIV_SIZE_MASK))
	      	pwrq->u.data.length = priv[k].set_args & IW_PRIV_SIZE_MASK;
		
		      /* Fetch string */
		      memcpy(buffer, args, pwrq->u.data.length);
		      buffer[sizeof(buffer) - 1] = '\0';
		      i++;
					break;
	
			default:
				printf("%s %d k=%d number=%d\n",__FUNCTION__,__LINE__,k,number);
				fprintf(stderr, "Not yet implemented...\n");
				close(skfd);
				return(-1);
		}

		if((priv[k].set_args & IW_PRIV_SIZE_FIXED) && (pwrq->u.data.length != (priv[k].set_args & IW_PRIV_SIZE_MASK)))
		{
			printf("The command %s need exactly %d argument...\n",cmdname, priv[k].set_args & IW_PRIV_SIZE_MASK);
			close(skfd);
		  return(-1);
		}
	}	/* if args to set */
  else
	{
		printf("ttt4\n");
		printf("length=0?\n"); 
		pwrq->u.data.length = 0L;
	}


  strncpy(pwrq->ifr_name, ifname, IFNAMSIZ);
	pwrq->u.data.pointer = (caddr_t) buffer;
	pwrq->u.data.flags = subcmd;

	printf("cmd=%d ifname=%s subcmd=%08x\n",priv[k].cmd,pwrq->ifr_name,subcmd);
  /* Perform the private ioctl */
  if(ioctl(skfd, priv[k].cmd , pwrq) < 0)
	{
		fprintf(stderr, "Interface doesn't accept private ioctl...\n");
		close(skfd);
		return(-1);
	}
	//printf("out_buf_max_len=%d\n",out_buf_max_len);
	
	if( pwrq->u.data.length < 4 )
	{
		memset( out_buf , 0 , 4 );
		memcpy( out_buf , pwrq->u.data.pointer , pwrq->u.data.length );
		pwrq->u.data.length=4;
	}
	else
	{
		if( pwrq->u.data.length > out_buf_max_len )
		{
			pwrq->u.data.length=out_buf_max_len;
		}
		memcpy( out_buf , pwrq->u.data.pointer , pwrq->u.data.length );
	}

	//printf("pwrq->u.data.length=%d\n",pwrq->u.data.length);

	printf("data=\n");
	for( i=0 ; i<pwrq->u.data.length ; i++ )
	{
		printf("%02x ",out_buf[i]);
	}
	printf("\n");

	close(skfd);
	return pwrq->u.data.length;
}


static void generateWpaConf(char *outputFile, int isWds, char *iface)
{
	int fh, intVal, encrypt=0, enable1x, wep,encmode,psk_enable;
	char buf1[1024], buf2[1024];
	char config_path[100]={0};
	char config_value[100]={0};
	char tmp1[100], tmpbuf[100];
	int get_result=0;
	char default_config_path[64]={0};
	int repeater_enabled=0;
	char buf[128];
	int len,i;
	
	sprintf(default_config_path, WIFI_CONFIG_ROOT_DIR "/%s/", iface);
	fh = open(outputFile, O_RDWR|O_CREAT|O_TRUNC, S_IRGRP|S_IWGRP);
	if (fh == -1) {
		printf("Create WPA config file error!\n");
		return;
	}
	
	
	if (!isWds)
	{
			len=mib_cmd(  iface , "get_mib" , "psk_enable" , buf , sizeof(buf) );
			if( len<0 )
			{
							printf("ERROR : Execute mib_cmd wepkey1 failure\n");
							return;
			}
			else
			{
						psk_enable = *(int *)buf;
						printf("psk_enable = %d\n", psk_enable );
			}

			
			
			len=mib_cmd(  iface , "get_mib" , "encmode" , buf , sizeof(buf) );
			if( len<0 )
			{
				printf("ERROR : Execute mib_cmd failure\n");
				return;
			}
			else
			{
				encmode = *(int *)buf;
				if( encmode==1 || encmode==5 )
				{
					encrypt = 1;
				}
				else
				{
					if( psk_enable==1 )
					{
						encrypt=2;
					}
					else if( psk_enable==2 )
					{
						encrypt=4;
					}
					else if( psk_enable==3 )
					{
						encrypt=6;
					}
					else
					{
						printf("unknow mode psk_enable=%d encmode=%d",psk_enable,encmode);
					}
					
				}
				printf("2.encrypt=%d\n",encrypt);
			}
			sprintf(buf2, "encryption = %d\n", encrypt);
			WRITE_WPA_FILE(fh, buf2);
			
			
			if(!strcmp(iface, "wlan0-vxd"))
			{
				sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "repeater_enabled");
				get_result = get_file_content(config_path, config_value);
				repeater_enabled = atoi(config_value);
				if(repeater_enabled==1)
				{
					sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "repeater_ssid");
					get_result = get_file_content(config_path, config_value);
				}
				else
				{
					//sprintf(config_path, "%s%s", default_config_path, "ssid");
					goto aa;
				}
			}
			else
			{
aa:
				//sprintf(config_path, "%s%s", default_config_path, "ssid");
				len=mib_cmd(  iface , "get_mib" , "ssid" , config_value , sizeof(config_value) );
				if( len<0 )
				{
							printf("ERROR : Execute mib_cmd ssid failure\n");
							return -1;
				}
				else
				{
							config_value[len]=0;
							printf("ssid=%s\n",config_value);
				}
			}
			sprintf(buf1,"%s", config_value);
			sprintf(buf2, "ssid = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);


			
			sprintf(config_path, "%s%s", default_config_path, "enable_1x");
			get_result = get_file_content(config_path, config_value);
			enable1x = atoi(config_value);
			sprintf(buf2, "enable1x = %d\n", enable1x);
			WRITE_WPA_FILE(fh, buf2);



			sprintf(config_path, "%s%s", default_config_path, "mac_auth_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "enableMacAuth = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
			
			
			sprintf(config_path, "%s%s", default_config_path, "enable_supp_nonwpa");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			if (intVal){
				sprintf(config_path, "%s%s", default_config_path, "supp_nonwpa");
				get_result = get_file_content(config_path, config_value);
				intVal = atoi(config_value);
			}
			sprintf(buf2, "supportNonWpaClient = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);


			if( encmode==1 )
			{
					sprintf(buf1,"1");
			}
			else if( encmode==5 )
			{
					sprintf(buf1,"2");
			}
			else
			{
					buf1[0]=0;
			}
			sprintf(buf2, "wepKey = %s\n", buf1);
			WRITE_WPA_FILE(fh, buf2);
		
		
			if ( encrypt==1 && enable1x ) {
				len=mib_cmd(  iface , "get_mib" , "wepkey1" , buf , sizeof(buf) );
				if( len<0 )
				{
						printf("ERROR : Execute mib_cmd wepkey1 failure\n");
						return;
				}
				else
				{
					if( encmode==1 )
					{
						len=5;
					}
					else
					{
						len=13;
					}
					
						memcpy( tmp1 , buf , len );
						tmp1[len] = '\0';
						if( len==5 )
						{
							sprintf(buf2, "wepGroupKey = \"%02x%02x%02x%02x%02x\"\n", tmp1[0],tmp1[1],tmp1[2],tmp1[3],tmp1[4]);
						}
						else if( len==13 )
						{
							sprintf(buf2, "wepGroupKey = \"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x\"\n",
							tmp1[0],tmp1[1],tmp1[2],tmp1[3],tmp1[4],
							tmp1[5],tmp1[6],tmp1[7],tmp1[8],tmp1[9],
							tmp1[10],tmp1[11],tmp1[12]);
						}
						else
						{
							strcpy(buf2, "wepGroupKey = \"\"\n");
							printf("ERROR : error wepkey1 len=%d\n",len);
						}
				}
			}
			else
				strcpy(buf2, "wepGroupKey = \"\"\n");
			WRITE_WPA_FILE(fh, buf2);




			sprintf(config_path, "%s%s", default_config_path, "wpa_auth");
			if( get_file_content(config_path, config_value) < 0 )
			{
				printf("ERROR : %s %d : can't open %s\n",__FUNCTION__,__LINE__,config_path);
				intVal=0;
			}
			else
			{
					intVal = atoi(config_value);
					printf("1.authentication = %d\n", intVal);
			}
			sprintf(buf2, "authentication = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);



			len=mib_cmd(  iface , "get_mib" , "wpa_cipher" , buf , sizeof(buf) );
			if( len<0 )
			{
					printf("ERROR : Execute mib_cmd wpa_cipher failure\n");
					return;
			}
			else
			{
				intVal=*(int *)buf;
				intVal=*(int *)buf;
				if( intVal==2  )
				{
					intVal=1;
				}
				else if( intVal==8  )
				{
					intVal=2;
				}
				else if( intVal==10  )
				{
					intVal=3;
				}
				printf("2.wpa_cipher=%d\n",intVal);
			}
			sprintf(buf2, "unicastCipher = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);


			len=mib_cmd(  iface , "get_mib" , "wpa2_cipher" , buf , sizeof(buf) );
			if( len<0 )
			{
					printf("ERROR : Execute mib_cmd wpa2_cipher failure\n");
					return;
			}
			else
			{
				intVal=*(int *)buf;
				if( intVal==2  )
				{
					intVal=1;
				}
				else if( intVal==8  )
				{
					intVal=2;
				}
				else if( intVal==10  )
				{
					intVal=3;
				}
				printf("2.wpa2_cipher=%d\n",intVal);
			}
			sprintf(buf2, "wpa2UnicastCipher = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
			

			sprintf(config_path, "%s%s", default_config_path, "wpa2_pre_auth");
			if( get_file_content(config_path, config_value) < 0 )
			{
				printf("%s %d\n",__FUNCTION__,__LINE__);
			}
			else
			{
					intVal = atoi(config_value);
					sprintf(buf2, "enablePreAuth = %d\n", intVal);
					printf("%s\n",buf2);
			}
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "psk_format");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			
			if (intVal==0)
				sprintf(buf2, "usePassphrase = 1\n");
			else
				sprintf(buf2, "usePassphrase = 0\n");
			WRITE_WPA_FILE(fh, buf2);
	
	
	
			sprintf(config_path, "%s%s", default_config_path, "wpa_psk");
			get_result = get_file_content(config_path, config_value);
			sprintf(buf1, "%s", config_value);


			sprintf(buf2, "psk = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);



			sprintf(config_path, "%s%s", default_config_path, "gk_rekey");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "groupRekeyTime = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
			sprintf(config_path, "%s%s", default_config_path, "rs_port");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "rsPort = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "rs_ip");
			get_result = get_file_content(config_path, config_value);
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "rsIP = %s\n",buf1);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "rs_password");
			get_result = get_file_content(config_path, config_value);
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "rsPassword = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);
		
		

			sprintf(config_path, "%s%s", default_config_path, "rs_maxretry");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "rsMaxReq = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		
		
			sprintf(config_path, "%s%s", default_config_path, "rs_interval_time");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "rsAWhile = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "accountRsEnabled = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_port");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "accountRsPort = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_ip");
			get_result = get_file_content(config_path, config_value);
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "accountRsIP = %s\n", buf1);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_password");
			get_result = get_file_content(config_path, config_value);
			sprintf(buf1, "%s", config_value);
			sprintf(buf2, "accountRsPassword = \"%s\"\n", buf1);
			WRITE_WPA_FILE(fh, buf2);


			sprintf(config_path, "%s%s", default_config_path, "account_rs_update_enabled");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "accountRsUpdateEnabled = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_update_delay");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);
			sprintf(buf2, "accountRsUpdateTime = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_maxretry");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "accountRsMaxReq = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
		

			sprintf(config_path, "%s%s", default_config_path, "account_rs_interval_time");
			get_result = get_file_content(config_path, config_value);
			intVal = atoi(config_value);			
			sprintf(buf2, "accountRsAWhile = %d\n", intVal);
			WRITE_WPA_FILE(fh, buf2);
	}
}

static int updateWscConf(char *in, char *out, int genpin, char *wlanif_name)
{
	int fh=0,ret=0;
	struct stat status;
	char *buf=0, *ptr;
	int intVal, is_client, is_config, is_registrar, len, is_wep=0, wep_key_type=0, wep_transmit_key=0;
	char tmpbuf[100], tmp1[100];
	char buf_1[128];
	
	int isUpnpEnabled=0, wsc_method = 0, wsc_auth=0, wsc_enc=0;
	int wlan_network_type=0, wsc_manual_enabled=0, wlan_encrpty=0, wlan_wep=0;
#ifdef FOR_DUAL_BAND
	char wlan_wpa_psk[100];
#endif
	char wlan_ssid[100], device_name[100];//, wsc_pin[100];
	int wlan_chan_num=0, wsc_config_by_ext_reg=0;
//	int is_vxdif=0;
	char default_config_path[64]={0};
	char config_path[100]={0};
	char config_value[100]={0};
	char wsc_pin[100]={0};
	char tmp_config_value[100]={0};
	char tmp_config_path[100]={0};
	int get_result=0,psk_en;

	// WPS2DOTX
	int wlan0_wlan_disabled=0;	
	int wlan0_wsc_disabled=0;	
#ifdef FOR_DUAL_BAND
	int wlan1_wlan_disabled=0;
	int wlan1_wsc_disabled=0;		
#endif
	
	// WPS2DOTX
	
	int band_select_5g2g;  // 0:2.4g  ; 1:5G   ; 2:both
	
	printf("\r\n wlanif_name=[%s],__[%s-%u]\r\n",wlanif_name,__FILE__,__LINE__);	

	sprintf(default_config_path, WIFI_CONFIG_ROOT_DIR "/%s/", wlanif_name);
	
	//apmib_get(MIB_WLAN_WSC_REGISTRAR_ENABLED, (void *)&is_registrar);	
	sprintf(config_path, "%s%s", default_config_path, "wsc_registrar_enabled");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	is_registrar = atoi(config_value);

	sprintf(config_path, "%s%s", default_config_path, "wsc_upnp_enabled");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	isUpnpEnabled = atoi(config_value);

	sprintf(config_path, "%s%s", default_config_path, "wsc_method");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	wsc_method = atoi(config_value);

	sprintf(config_path, "%s%s", default_config_path, "network_type");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	wlan_network_type = atoi(config_value);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_manual_enabled");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	wsc_manual_enabled = atoi(config_value);
	printf("1.wlan_chan_num=%08x\n",wlan_chan_num);

	sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "device_name");
	if( get_file_content(config_path, config_value) < 0 )
	{
		//return -1;
	}
	sprintf(device_name, "%s", config_value);
	
	
	sprintf(config_path, WIFI_CONFIG_ROOT_DIR "/%s", "band2g5g_select");
	if( get_file_content(config_path, config_value) < 0 )
	{
		//return -1;
	}
	band_select_5g2g=atoi(config_value);
	
	sprintf(config_path, "%s%s", default_config_path, "wsc_configured");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	is_config = atoi(config_value);

	sprintf(config_path, "%s%s", default_config_path, "wsc_configbyextreg");
	if( get_file_content(config_path, config_value) < 0 )
	{
		return -1;
	}
	wsc_config_by_ext_reg=atoi(config_value);

	sprintf(config_path, "%s%s", default_config_path, "wsc_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan0_wsc_disabled = atoi(config_value);
	
	sprintf(config_path, "%s%s", default_config_path, "wlan_disabled");
	get_result = get_file_content(config_path, config_value);
	wlan0_wlan_disabled = atoi(config_value);
	
	/* WPS2DOTX ; 2011-0524*/ 	
	if(wlan0_wlan_disabled)
		wlan0_wsc_disabled=1 ; // if wlan0 interface is disabled ;
	
	
	sprintf(config_path, "%s%s", default_config_path, "channel");
	get_result = get_file_content(config_path, config_value);
	wlan_chan_num=atoi(config_value);
	
	len=mib_cmd(  wlanif_name , "get_mib" , "channel" , buf_1 , sizeof(buf_1) );
	if( len<0 )
	{
				printf("ERROR : Execute mib_cmd failure\n");
				return -1;
	}
	else
	{
				wlan_chan_num = *(int *)buf_1;
				printf("2.wlan_chan_num=%08x\n",wlan_chan_num);
	}



	len=mib_cmd(  wlanif_name , "get_mib" , "psk_enable" , buf_1 , sizeof(buf_1) );
	if( len<0 )
	{
				printf("ERROR : Execute mib_cmd psk_enable failure\n");
				return -1;
	}
	else
	{
				psk_en = *(int *)buf_1;
				switch( psk_en )
				{
					case 1:
						//WPA
						wsc_auth=2;
						wlan_encrpty=2;
						break;
						
					case 2:
						//WPA2
						wsc_auth=32;
						wlan_encrpty=4;
						break;
						
					case 3:
						//wpa_wpa2_mixed
						wsc_auth=34;
						wlan_encrpty=6;
						break;
						
					default :
						printf("ERROR : unknow mode\n");
						return -1;
				}
				printf("2.wsc_auth=%08x wlan_encrpty=%d\n",wsc_auth,wlan_encrpty);
	}



	if( psk_en==1 )
	{
		len=mib_cmd(  wlanif_name , "get_mib" , "wpa_cipher" , buf_1 , sizeof(buf_1) );
	}
	else if( psk_en==2 )
	{
		len=mib_cmd(  wlanif_name , "get_mib" , "wpa2_cipher" , buf_1 , sizeof(buf_1) );
	}
	else if( psk_en==3 )
	{
		len=mib_cmd(  wlanif_name , "get_mib" , "wpa2_cipher" , buf_1 , sizeof(buf_1) );
	}
	else
	{
		printf("ERROR : Execute mode psk_en=%d\n",psk_en);
	}
	
	
	if( len<0 )
	{
				printf("ERROR : Execute mib_cmd failure\n");
				//close(fh);
	}
	else
	{
				wsc_enc = *(int *)buf_1;
				switch( wsc_enc )
				{
					case 2:
						//TKIP
						wsc_enc=4;
						break;
						
					case 8:
						//AES
						wsc_enc=8;
						break;
						
					case 10:
						//TKIP_AES_mixed
						wsc_enc=12;
						break;
						
					default :
						printf("ERROR : unknow mode wsc_enc=%d\n",wsc_enc);
						return -1;
				}
				printf("2.wsc_enc=%08x\n",wsc_enc);
	}



	len=mib_cmd(  wlanif_name , "get_mib" , "ssid" , buf_1 , sizeof(buf_1) );
	if( len<0 )
	{
				printf("ERROR : Execute mib_cmd failure\n");
				return -1;
	}
	else
	{
				strncpy( wlan_ssid , buf_1 , len );
				wlan_ssid[len]=0;
				printf("2.wlan_ssid=%s\n",wlan_ssid);
	}
	

	
	len=mib_cmd(  wlanif_name , "get_mib" , "opmode" , buf_1 , sizeof(buf_1) );
	if( len<0 )
	{
				printf("ERROR : Execute mib_cmd opmode failure\n");
				return -1;
	}
	else
	{
				get_result = *(int *)buf_1;
				if( get_result==16 )
				{
					is_client=0;
				}
				else if( get_result==8 )
				{
					is_client=1;
				}
				else
				{
					printf("ERROR : unknow opmode mode=%d\n",get_result);
					ret=-1;
		goto exit;
				}
				printf("2.is_client=%d\n",is_client);
	}
	
	
	
	


			len=mib_cmd(  wlanif_name , "get_mib" , "encmode" , buf_1 , sizeof(buf_1) );
			if( len<0 )
			{
				printf("ERROR : Execute mib_cmd encmode failure\n");
				ret=-1;
		goto exit;
			}
			else
			{
				wlan_wep = *(int *)buf_1;
				//printf("2.wlan_wep=%d\n",wlan_wep);
				if( wlan_wep==1 )
				{
					;
				}
				else if( wlan_wep==1 )
				{
					wlan_wep=5;
				}
				else
				{
					wlan_wep=0;
				}
				printf("2.wlan_wep=%d\n",wlan_wep);
			}

	sprintf(config_path, "%s%s", default_config_path, "wsc_pin");
	if( get_file_content(config_path, wsc_pin ) < 0 )
	{
		printf("ERROR : oen %s failure\n",config_path);
	}
	else
	{
		printf("wsc_pin=%s\n",wsc_pin);
	}
	

	if (stat(in, &status) < 0) {
		printf("stat() error [%s]!\n", in);
		return -1;
	}

	buf = malloc(status.st_size+2048);
	if (buf == NULL) {
		printf("malloc() error [%d]!\n", (int)status.st_size+2048);
		return -1;		
	}

	ptr = buf;

	if (is_client == CLIENT_MODE) {
		{
			if (is_registrar) {
				if (!is_config)
					intVal = MODE_CLIENT_UNCONFIG_REGISTRAR;
				else
					intVal = MODE_CLIENT_CONFIG;			
			}
			else
				intVal = MODE_CLIENT_UNCONFIG;
		}
	}
	else {
		if (!is_config)
			intVal = MODE_AP_UNCONFIG;
		else
			intVal = MODE_AP_PROXY_REGISTRAR;
	}
	WRITE_WSC_PARAM(ptr, tmpbuf, "mode = %d\n", intVal);





	
	if (is_client == CLIENT_MODE) {
			intVal = 0;
	}
	else
		intVal = isUpnpEnabled;

	WRITE_WSC_PARAM(ptr, tmpbuf, "upnp = %d\n", intVal);

	intVal = 0;
	intVal = wsc_method;
	//Ethernet(0x2)+Label(0x4)+PushButton(0x80) Bitwise OR
	if (intVal == 1) //Pin+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PIN);
	else if (intVal == 2) //PBC+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PBC);
	if (intVal == 3) //Pin+PBC+Ethernet
		intVal = (CONFIG_METHOD_ETH | CONFIG_METHOD_PIN | CONFIG_METHOD_PBC);
	WRITE_WSC_PARAM(ptr, tmpbuf, "config_method = %d\n", intVal);

	// 1104
	WRITE_WSC_PARAM(ptr, tmpbuf, "wlan0_wsc_disabled = %d\n", wlan0_wsc_disabled);
	WRITE_WSC_PARAM(ptr, tmpbuf, "auth_type = %d\n", wsc_auth);	
	WRITE_WSC_PARAM(ptr, tmpbuf, "encrypt_type = %d\n", wsc_enc);
	
	if (wsc_enc == WSC_ENCRYPT_WEP)
		is_wep = 1;

	if(wlanif_name != NULL && (strcmp(wlanif_name,"wlan0") == 0 || strcmp(wlanif_name,"wlan1") == 0))
	{
		if (is_client == CLIENT_MODE) 
		{
			if (wlan_network_type == 0)
				intVal = 1;
			else
				intVal = 2;
		}
		else
			intVal = 1;
	}
	else
		intVal = 1;
		
	WRITE_WSC_PARAM(ptr, tmpbuf, "connection_type = %d\n", intVal);
	WRITE_WSC_PARAM(ptr, tmpbuf, "manual_config = %d\n", wsc_manual_enabled);

	if (is_wep) { // only allow WEP in none-MANUAL mode (configured by external registrar)		
		//WPS2.0 don't support WEP
		goto exit;
	}
	else {
		len=mib_cmd(  wlanif_name , "get_mib" , "passphrase" , buf_1 , sizeof(buf_1) );
		if( len<0 )
		{
					printf("ERROR : Execute mib_cmd passphrase failure\n");
					ret=-1;
		goto exit;
		}
		else
		{
					printf("passphrase=%s\n",buf_1);
		}
		WRITE_WSC_PARAM( ptr, tmpbuf, "network_key = %s\n", buf_1 );		
	}

	WRITE_WSC_PARAM(ptr, tmpbuf, "ssid = \"%s\"\n", wlan_ssid);
	WRITE_WSC_PARAM(ptr, tmpbuf, "pin_code = %s\n", wsc_pin);
	

	if (wlan_chan_num > 14)
		intVal = 2;
	else
		intVal = 1;

	WRITE_WSC_PARAM(ptr, tmpbuf, "rf_band = %d\n", intVal);
	WRITE_WSC_PARAM(ptr, tmpbuf, "device_name = \"%s\"\n", device_name);
	WRITE_WSC_PARAM(ptr, tmpbuf, "config_by_ext_reg = %d\n", wsc_config_by_ext_reg);

	len = (int)(((long)ptr)-((long)buf));
	
	fh = open(in, O_RDONLY);
	if (fh == -1) {
		printf("open() error [%s]!\n", in);
		ret=-1;
		goto exit;
	}

	lseek(fh, 0L, SEEK_SET);
	if (read(fh, ptr, status.st_size) != status.st_size) {		
		printf("read() error [%s]!\n", in);
		ret=-1;
		goto exit;	
	}
	close(fh);

	// search UUID field, replace last 12 char with hw mac address
	ptr = strstr(ptr, "uuid =");
	if (ptr) {
		char tmp2[100];
//		apmib_get(MIB_HW_NIC0_ADDR, (void *)&tmp1);
		sprintf(tmp_config_path, "%s%s", default_config_path, "nic0_addr");
		get_result = get_file_content(tmp_config_path, tmp_config_value);
		if(get_result != -1){	
			string_to_hex(tmp_config_value,tmp1, 12);
			convert_bin_to_str(tmp1, 6, tmp2);
			memcpy(ptr+27, tmp2, 12);		
		}
	}

	fh = open(out, O_RDWR|O_CREAT|O_TRUNC, S_IRGRP|S_IWGRP);
	if (fh == -1) {
		printf("open() error [%s]!\n", out);
		goto exit;
	}

	if (write(fh, buf, len+status.st_size) != len+status.st_size ) {
		printf("Write() file error [%s]!\n", out);
		ret=-1;
		goto exit;
	}

exit:
	if( fh )
		close(fh);
	if( buf )
		free(buf);	
	return ret;
}




#endif


