LOCAL_PATH := $(call my-dir)

#########################

include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := webs.c
LOCAL_MODULE := webs 
LOCAL_CFLAGS := -Os -Wall $(CFLAGS) #-DCONFIG_NEW_SCRIPT
LOCAL_C_INCLUDES:=$(LOCAL_PATH)/../include
include $(BUILD_EXECUTABLE)

#########################

include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := flash.c
LOCAL_MODULE := flash
LOCAL_CFLAGS := -Os -Wall $(CFLAGS)
LOCAL_C_INCLUDES:=$(LOCAL_PATH)/../include

include $(BUILD_EXECUTABLE)

