/* $Id: minixml.h,v 1.1.1.1 2007/08/06 10:04:43 root Exp $ */
/* minimal xml parser
 *
 * Project : miniupnp
 * Website : http://miniupnp.free.fr/
 * Author : <PERSON>
 * Copyright (c) 2005 <PERSON>
 * This software is subject to the conditions detailed in the
 * LICENCE file provided in this distribution.
 * */
#ifndef __MINIXML_H__
#define __MINIXML_H__
#define IS_WHITE_SPACE(c) ((c==' ') || (c=='\t') || (c=='\r') || (c=='\n'))

/* if a callback function pointer is set to NULL,
 * the function is not called */
struct xmlparser {
	const char *xmlstart;
	const char *xmlend;
	const char *xml;	/* pointer to current character */
	int xmlsize;
	void * data;
	void (*starteltfunc) (void *, const char *, int);
	void (*endeltfunc) (void *, const char *, int);
	void (*datafunc) (void *, const char *, int);
	void (*attfunc) (void *, const char *, int, const char *, int);
};

/* parsexml()
 * the xmlparser structure must be initialized before the call
 * the following structure members have to be initialized :
 * xmlstart, xmlsize, data, *func
 * xml is for internal usage, xmlend is computed automatically */
void parsexml(struct xmlparser *);

#endif

