#
# Makefile for WPA for the Linux OS
#
#
# $Id: Makefile,v 1.8 2008/10/13 01:10:14 davidhsu Exp $
#
include $(zte_app_mak)

ARCH	= rWPA.a
NAME	= rWPA

WPA_ROOT_DIR = ..
RTL_WPA_TARGET = auth
BIG_ENDIAN=n

###### WPA2 #######
ENABLE_WPA2 = true
ENABLE_WPA2_PREAUTH = true
ENABLE_WPA2_CLIENT = true
ENABLE_CLIENT_TLS = false
ENABLE_PMF = true


###### 2 set of RADIUS #######
ENABLE_RADIUS_2SET = true

###### Endian ######
ifeq ($(BIG_ENDIAN),y)
ENABLE_BIG_ENDIAN = true
else
ENABLE_BIG_ENDIAN = false
endif

ifeq ($(CUR_USED_OS),LINUX)
CC	= $(CROSS_COMPILE)gcc
STRIP	= $(CROSS_COMPILE)strip
LD	= $(CROSS_COMPILE)ld
AR	= $(CROSS_COMPILE)ar
endif

ifeq ($(RTL8186_TR),1)
RTL_WPA_CFLAG += -DCONFIG_RTL8186_TR
ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(RTL865X_AC),1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_AC
ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(RTL865X_KLD),1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_KLD
#ENABLE_WPA2_CLIENT = false
ENABLE_WPA2_PREAUTH = false
endif

ifeq ($(CONFIG_RTL865X_SC),1)
RTL_WPA_CFLAG += -DCONFIG_RTL865X_SC
ENABLE_WPA2_CLIENT = false
#ENABLE_WPA2_PREAUTH = false
endif

#kenny
DBG_FLAG=-DDEBUG_DISABLE
DOWN_SIZE=-DCOMPACK_SIZE
INCS = -I$(zte_app_path)/include
RTL_WPA_CFLAG += -Wall -Os $(DOWN_SIZE) $(DBG_FLAG) $(INCS)

ifeq ($(SLINK),1)
RTL_WPA_CFLAG += -ffunction-sections -fdata-sections
RTL_WPA_LDFLAG += --static -Wl,--gc-sections
endif

#RTL_WPA_CFLAG	= -Wall -O2

#RTL_WPA_LDFLAG	= #-r

RTL_WPA_IFLAGS 	= -I${WPA_ROOT_DIR}/include 

#RTL_WPA_LIBS = ${WPA_ROOT_DIR}/Package/libnet.a \
#				${WPA_ROOT_DIR}/Package/libpcap.a \
#				${WPA_ROOT_DIR}/Package/libcrypto.a

ifeq ($(strip $(ENABLE_BIG_ENDIAN)),true)
RTL_WPA_DEFFLAGS = -D_ON_RTL8181_TARGET -DLIBNET_BIG_ENDIAN -DLIB1X_BIG_ENDIAN -D_DAEMON_SIDE -D_RTL_WPA_UNIX 
else
RTL_WPA_DEFFLAGS = -D_ON_RTL8181_TARGET -DLIBNET_LIL_ENDIAN -DLIB1X_LIL_ENDIAN -D_DAEMON_SIDE -D_RTL_WPA_UNIX 
endif

ifeq ($(ASUS),1)
	RTL_WPA_DEFFLAGS += -D__ASUS_DVD__ -DPSK_ONLY -DAUTH_LITTLE_ENDIAN -DSTART_AUTH_IN_LIB
else
ifeq ($(strip $(ENABLE_BIG_ENDIAN)),true)
	RTL_WPA_DEFFLAGS += -DAUTH_BIG_ENDIAN
else
	RTL_WPA_DEFFLAGS += -DAUTH_LITTLE_ENDIAN
endif
endif

ifeq ($(strip $(ENABLE_CLIENT_TLS)),true)
XSUPP_LIBS = xsup_src/xsup.a -ldl
openssl_dir = ../../openssl-0.9.8b
ifeq ($(strip $(USE_OPENSSL_DLIB)),true)
XSUPP_LIBS += ${openssl_dir}/libssl.so ${openssl_dir}/libcrypto.so
else
XSUPP_LIBS += ${openssl_dir}/libssl.a ${openssl_dir}/libcrypto.a
endif
RTL_WPA_DEFFLAGS += -DCLIENT_TLS
endif

ifeq ($(strip $(ENABLE_WPA2)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2
RTL_WPA_CFLAG	+= -g
endif # ENABLE_WPA2

ifeq ($(strip $(ENABLE_WPA2_PREAUTH)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2_PREAUTH
endif # ENABLE_WPA2_PREAUTH


ifeq ($(strip $(ENABLE_WPA2_CLIENT)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_WPA2_CLIENT
endif # ENABLE_WPA2_CLIENT

ifeq ($(strip $(ENABLE_PMF)),true)
RTL_WPA_DEFFLAGS +=  -DCONFIG_IEEE80211W
endif # ENABLE_PMF

ifeq ($(strip $(ENABLE_RADIUS_2SET)),true)
RTL_WPA_DEFFLAGS +=  -DRTL_RADIUS_2SET
endif #ENABLE_RADIUS_2SET

ifeq ($(CUR_USED_OS),UCLINUX)
CFLAGS += -D__ZTE_UCLINUX__
LDFLAGS  += -Wl,-elf2flt=-s65536
else
CFLAGS += -D__ZTE_LINUX__
endif
#------------------------------------------------------------------------
# source files
#------------------------------------------------------------------------
RTL_WPA_SRCS = auth.o \
			1x_auth_pae.o 1x_bauth_sm.o 1x_common.o 1x_krc_sm.o \
			1x_nal.o 1x_kxsm.o 1x_radius.o 1x_ptsm.o 1x_reauth_sm.o \
			1x_cdsm.o 1x_config.o 1x_ioctl.o 1x_parser.o \
			1x_kmsm_auth.o 1x_kmsm_supp.o 1x_kmsm_eapolkey.o \
			1x_info_auth.o 1x_kmsm_hmac.o 1x_kmsm_prf.o 1x_kmsm_aes.o 1x_acct_sm.o 1x_md5c.o 1x_rc4.o libnet.o
ifeq ($(strip $(ENABLE_PMF)),true)
RTL_WPA_SRCS	+= sha256.o
endif # ENABLE_PMF
ifeq ($(strip $(ENABLE_WPA2)),true)
RTL_WPA_SRCS	+= 1x_pmk_cache.o
endif # ENABLE_WPA2
RTL_WPA_OBJS = ${RTL_WPA_SRCS:.c=.o}

ifeq ($(ASUS),1)
RTL_WPA_SRCS += iwcontrol.o
endif

#------------------------------------------------------------------------
#	Transition rules (add -o to put object in right directory)
#------------------------------------------------------------------------
.c.o:
	$(CC) $(CFLAGS) $(RTL_WPA_CFLAG) $(RTL_WPA_IFLAGS) $(RTL_WPA_DEFFLAGS) -c -o $*.o $<

#ifeq ($(ASUS),1)
#iwcontrol.o: dlisten/iwcontrol.c
#	$(CC) $(RTL_WPA_CFLAG) $(RTL_WPA_IFLAGS) $(RTL_WPA_DEFFLAGS) -c -o $*.o $<
#endif

###################
#all: build_lib build_listen ${RTL_WPA_TARGET} wpa_bin pretest
#all: pretest build_listen ${RTL_WPA_TARGET} wpa_bin
all: build_listen ${RTL_WPA_TARGET}
ifeq ($(strip $(ENABLE_CLIENT_TLS)),true)
all:  xsup rwCert ${RTL_WPA_TARGET}
else

### ASUS library ###
ifeq ($(ASUS),1)
ASUS_WPA_LIB = auth.a

${ASUS_WPA_LIB}: ${RTL_WPA_OBJS}
	$(AR) r $@ $(RTL_WPA_OBJS)
	$(STRIP) -S $@

all: ${ASUS_WPA_LIB}

iwcontrol.o: dlisten/iwcontrol.c
	@echo "==========> iwcontrol <=========="
	$(CC) $(RTL_WPA_CFLAG) $(RTL_WPA_IFLAGS) $(RTL_WPA_DEFFLAGS) -c -o $*.o $<

else
### 8186 daemon ###
all:  ${RTL_WPA_TARGET}

endif
endif
#all: ${RTL_WPA_TARGET}  dlisten_build
dlisten_build:
	@echo "==========>build iwcontrol <=========="
	$(MAKE) -C ./dlisten

build_lib:
	cd ${WPA_ROOT_DIR}/Package/Libnet-1.0.2a; \
	${MAKE} clean; \
	${MAKE} -f Makefile-rtl8181 RTL_WPA_CFLAG='${RTL_WPA_CFLAG}' RTL_WPA_LDFLAG='${RTL_WPA_LDFLAG}' RTL_WPA_DEFFLAGS='${RTL_WPA_DEFFLAGS}';
	cd ${WPA_ROOT_DIR}/Package/libpcap-0.7.2; \
	${MAKE} clean; \
	${MAKE} -f Makefile-rtl8181 RTL_WPA_CFLAG='${RTL_WPA_CFLAG}' RTL_WPA_LDFLAG='${RTL_WPA_LDFLAG}' RTL_WPA_DEFFLAGS='${RTL_WPA_DEFFLAGS}';
	cd ${WPA_ROOT_DIR}/Package/openssl-0.9.7d; \
	${MAKE} clean; \
	${MAKE} -f Makefile-rtl8181 RTL_WPA_CFLAG='${RTL_WPA_CFLAG}' RTL_WPA_LDFLAG='${RTL_WPA_LDFLAG}' RTL_WPA_DEFFLAGS='${RTL_WPA_DEFFLAGS}';



build_listen:
	cd dlisten; ${MAKE} clean; \
	${MAKE} -f Makefile RTL_WPA_CFLAG='${RTL_WPA_CFLAG}' RTL_WPA_LDFLAG='${RTL_WPA_LDFLAG}' ;
ifeq ($(CUR_USED_OS),LINUX)

ifeq ($(strip $(ENABLE_CLIENT_TLS)),true)
${RTL_WPA_TARGET}: ${RTL_WPA_OBJS} ${RTL_WPA_LIBS} ${XSUPP_LIBS}
	$(CC) $(CFLAGS) $(LDFLAGS) $(RTL_WPA_LDFLAG) -o $@ $(RTL_WPA_OBJS) ${RTL_WPA_LIBS} ${XSUPP_LIBS}
#	$(STRIP) $@
else
${RTL_WPA_TARGET}: ${RTL_WPA_OBJS} ${RTL_WPA_LIBS}
	$(CC) $(CFLAGS) $(LDFLAGS) $(RTL_WPA_LDFLAG) -o $@ $(RTL_WPA_OBJS) ${RTL_WPA_LIBS}
#	$(STRIP) $@
#	cp ./auth ../../bin/auth
endif

else

ifeq ($(strip $(ENABLE_CLIENT_TLS)),true)
${RTL_WPA_TARGET}: ${RTL_WPA_OBJS} ${RTL_WPA_LIBS} ${XSUPP_LIBS}
	@echo "==========>build auth  ENABLE_CLIENT_TLS=true<=========="
	$(CC) $(CFLAGS) $(LDFLAGS) $(RTL_WPA_LDFLAG) -o $@ $(RTL_WPA_OBJS) ${RTL_WPA_LIBS} ${XSUPP_LIBS}
else
${RTL_WPA_TARGET}: ${RTL_WPA_OBJS} ${RTL_WPA_LIBS}
	@echo "==========>build auth  ENABLE_CLIENT_TLS=false<== go here========"
	$(CC) $(CFLAGS) $(LDFLAGS) $(RTL_WPA_LDFLAG) -o $@ $(RTL_WPA_OBJS) ${RTL_WPA_LIBS}
endif
endif






pretest: pretest.o
	$(CC) $(RTL_WPA_LDFLAG) -o $@ pretest.o 
	rm pretest.o -f

wpa_bin:
	chmod 744 ${WPA_ROOT_DIR}/bin/; \
	${STRIP} dlisten/iwcontrol; \
	cp dlisten/iwcontrol ${WPA_ROOT_DIR}/bin/; \
#	${STRIP} ${RTL_WPA_TARGET}; \
	cp ${RTL_WPA_TARGET} ${WPA_ROOT_DIR}/bin/; \
	cp pretest ${WPA_ROOT_DIR}/bin/; \
	chmod 777 ${WPA_ROOT_DIR}/bin/*;


SUBDIRS=dlisten
xsup:
	@for d in $(SUBDIRS); do (cd $$d && $(MAKE) DESTDIR=${DESTDIR}); done

SUBDIRS2=rwCertSrc
rwCert:
	@for d in $(SUBDIRS2); do (cd $$d && $(MAKE) DESTDIR=${DESTDIR}); done

##-------------------------------------------------

clean:
	cd dlisten;$(MAKE) clean
	rm -f *.o ${RTL_WPA_TARGET} *.a *.gdb *elf
#	@for d in $(SUBDIRS); do (cd $$d && $(MAKE) DESTDIR=${DESTDIR} clean); done

romfs:
	cp auth auth.elf;$(ROMFSINST) /bin/auth
	cd dlisten;cp iwcontrol iwcontrol.elf; $(ROMFSINST) /bin/iwcontrol
