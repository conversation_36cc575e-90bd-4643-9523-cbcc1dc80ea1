

#include "1x_pkt_xmit.h"
#include "1x_common.h"

//--------------------------------------------------
// IEEE 802.1x Implementation
//
// File		: 1x_pkt_xmit.c
// Programmer	: <PERSON><PERSON><PERSON>
//
// Copyright (c) <PERSON><PERSON><PERSON> 2002
// All rights reserved.
// Maryland Information and Systems Security Lab
// University of Maryland, College Park.
// Coding for calling libnet properly
//--------------------------------------------------




PKT_XMIT * lib1x_pktxmit_init( char *device, Global_Params * global )
{
	PKT_XMIT	* xmitter;

	xmitter

}
