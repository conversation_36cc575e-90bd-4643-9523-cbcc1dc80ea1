# use ie=1, not use=0, used ie & auto generated SSID in the format of ssid+last 2 bytes mac=2
use_ie = 1

# AUTH_OPEN=1, AUTH_WPAPSK=2, AUTH_SHARED=4, AUTH_WPA=8, AUTH_WPA2=0x10, AUTH_WPA2PSK=0x20
auth_type_flags = 39

# ENCRYPT_NONE=1, ENCRYPT_WEP=2, ENCRYPT_TKIP=4, ENCRYPT_AES=8
encrypt_type_flags = 15

uuid = 63041253101920061228aabbccddeeff
device_name = "JWNR2000(Wireless AP)"
manufacturer = "NETGEAR, Inc."
manufacturerURL = "http://www.netgear.com/products"
modelDescription = "JWNR2000 Gateway"
model_name = "JWNR2000(Wireless AP)"
model_num = "JWNR2000"
modelURL = "http://www.netgear.com/"
serial_num = "00000000"
device_attrib_id = 1
device_oui = 0050f204
device_category_id = 6
device_sub_category_id = 1

# PASS_ID_DEFAULT=0, PASS_ID_USER=1, PASS_ID_MACHINE=2, PASS_ID_REKEY=3,
# PASS_ID_PB=4, PASS_ID_REG=5, PASS_ID_RESERVED=6
device_password_id = 0

tx_timeout = 5
resent_limit = 2
reg_timeout = 120
block_timeout = 60

# Those parameters are supported by WPS daemon starting from V1.2.
# Need to patch /rtl8186/linux-2.4.18/drivers/char/rtl_gpio.c if
# you want to use wireless LED instead of WPS LED.
WPS_START_LED_GPIO_number = 2
WPS_END_LED_unconfig_GPIO_number = 0
WPS_END_LED_config_GPIO_number = 0
# when error occur,the value will echo > /proc/gpio
WPS_PBC_overlapping_GPIO_number = 3
WPS_ERROR_LED_GPIO_number = 3

#when error occur need blink how long time
PBC_overlapping_LED_time_out = 120
WPS_ERROR_LED_time_out = 30

# When 0, WPS daemon will issue command 'flash set wlan0 value' to update setting
# When 1, WPS daemon will issue command 'flash set value' to update setting
# When 2, WPS daemon will update setting to a file '/tmp/flash_param'
No_ifname_for_flash_set = 0

# Disable to send dis-association to STA after WPS is done. 1:disable, 0:enable
#disable_disconnect = 1

# Disable auto generate SSID in un-configured state
#disable_auto_gen_ssid = 1
	 
# Manual assigned encryption type. 0:disable, 1:WPA-TKIP, 2:WPA2-AES, 3:Mixed-AES-TKIP
#manual_key_type = 3

# Manual psk value with length from 8~64
#manual_key = 1234567890

# Disable hidden AP when wsc is activiated
disable_hidden_ap = 1

# Disable allow configured by external Regisrar (PIN disabled)
#disable_configured_by_exReg=1

# define PIN timeout
PIN_timeout = 240

#if SSID_prefix's string != NULL use this as prefix of SSID,else use "WPS"  as prefix of SSID
SSID_prefix = "NTGR_"

# auto-lock-down autj fail times threshold,vaild scope 3~30
Auth_Fail_Times = 30
button_hold_time = 3
