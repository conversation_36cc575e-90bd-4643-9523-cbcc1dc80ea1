# makefile for wscd
include $(zte_lib_mak)
Config_PC=$(Config_PC)

#ifeq ($(DIR_LINUX),)
#include ../../../linux-2.6.30/.config
#else
#include $(DIR_LINUX)/.config
#endif

#KVER  := $(shell uname -r)
#KSRC := /lib/modules/$(KVER)/build
include $(LINUX_DIR)/.config

#for INBAND_HOST
#include .config
#inband_lib = ../../rtk_inbandLib/inband.a

#CC			= rsdk-linux-gcc

ifeq ($(CUR_USED_OS),LINUX)
CC = $(CROSS_COMPILE)gcc
endif

#COPT		= -O2
COPT		= -Os
CDEF		=
CWARN		= -Wall
#CDBG		= -ggdb
CFLAGS		+= ${CWARN} ${COPT} ${CDEF} ${CDBG}
#CFLAGS		+= -DINBAND_WPS_OVER_HOST
#LDFLAGS		= --static -Wl,--gc-sections
#LDFLAGS		+=

ifeq ($(CONFIG_PC),y)
	CFLAGS += -D__PC__
endif

ifeq ($(CONFIG_RTL_8198_NFBI_RTK_INBAND_AP),y)
STATIC_LIB = 1
CFLAGS += -DCONFIG_RTL8196C_AP_HCM -DCONFIG_RTL8198_AP_HCM
LDFLAGS += --static
endif

CFLAGS += -DCONFIG_RTL_REPEATER_WPS_SUPPORT
# -DDEBUG 
#-DOUTPUT_LOG

ifeq ($(CUR_USED_OS),UCLINUX)
CFLAGS += -D__ZTE_UCLINUX__
else
CFLAGS += -D__ZTE_LINUX__
endif


ifeq ($(RTL8186_KB),1)
	LIBUPNP=1
endif

# For mini_upnp stand alone
LIBUPNP=0
STATIC_LIB=1

ifeq ($(CONFIG_RTL_COMAPI_CFGFILE),y)
CFLAGS		+= -DCONFIG_IWPRIV_INTF
endif

ifeq ($(OFFICIAL_OPENSSL),1)
########## Build with OPENSSL Library ##################
OPENSSLDIR	= ../../openssl-0.9.7i

else
########## Build with porting-SSL Library ##################
OPENSSLDIR	= ../portingssl
CFLAGS		+= -DUSE_PORTING_OPENSSL
endif


ifeq ($(LIBUPNP),1)
########## Build with libupnp Library ##################
UPNPDIR		= ../../upnpd/libupnp-1.4.1
UPNPINC		= $(UPNPDIR)/upnp/inc
IXMLINC		= $(UPNPDIR)/ixml/inc
UPNPLIB 	= -lpthread $(UPNPDIR)/ixml/.libs/libixml.so.2 \
	$(UPNPDIR)/threadutil/.libs/libthreadutil.so.2 \
	$(UPNPDIR)/upnp/.libs/libupnp.so.2
SOURCES = wsc.c txpkt.c rxpkt.c utils.c sample_util.c simplecfg_upnp_main.c

else
########## Build with MINI-UPNP Library ##################
UPNPDIR		= ../../mini_upnp
UPNPINC		= $(UPNPDIR)
IXMLINC		= ./
CFLAGS		+= -DUSE_MINI_UPNP
SOURCES 	= wsc.c txpkt.c rxpkt.c utils.c simplecfg_mini_upnp_main.c

	ifeq ($(STATIC_LIB),1)
	################# Static Library ##################
		UPNPLIB		= $(UPNPDIR)/mini_upnp.a
		CFLAGS		+= -DSTAND_ALONE_MINIUPNP

	else
	################# Shared Library ##################
	UPNPLIB = $(UPNPDIR)/mini_upnp.so

	endif
endif

ifeq ($(OFFICIAL_OPENSSL),1)
INCS		= -I$(OPENSSLDIR)/include/ -I$(UPNPINC) -I$(IXMLINC)
else
INCS		= -I$(OPENSSLDIR)/crypto/include -I$(UPNPINC) -I$(IXMLINC)
endif
INCS		+= -I$(zte_app_path)/include
INCS		+= -I$(zte_lib_path)/libnvram
LIBS =  $(OPENSSLDIR)/libcrypto.a 
LIBS += $(UPNPLIB)
LIBS += $(inband_lib)
LIBS += -lpthread
LIBS += -lsoftap
LIBS += -L$(zte_lib_path)/libsoftap
LIBS += -lsoft_timer
LIBS += -L$(zte_lib_path)/libsoft_timer
LIBS += -lnvram
LIBS += -L$(zte_lib_path)/libnvram
ifeq ($(RTL8186_KB),1)
CFLAGS		+= -DCONFIG_RTL8186_KB
endif

ifeq ($(RTL8186_TR),1)
CFLAGS		+= -DCONFIG_RTL8186_TR
endif

ifeq ($(RTL865X_AC),1)
CFLAGS		+= -DCONFIG_RTL865X_AC
endif

ifeq ($(CONFIG_RTL_8196C),y)
CFLAGS		+= -DCONFIG_RTL8196C
endif

ifeq ($(CONFIG_RTL_8198),y)
CFLAGS		+= -DCONFIG_RTL8198
endif

ifeq ($(CONFIG_RTL_819XD),y)
CFLAGS		+= -DCONFIG_RTL_819XD
endif

ifeq ($(CONFIG_RTL_8196E),y)
CFLAGS		+= -DCONFIG_RTL_8196E
endif

ifeq ($(RTL865X_KLD),1)
CFLAGS		+= -DCONFIG_RTL865X_KLD
endif

ifeq ($(CONFIG_RTL865X_SC),1)
CFLAGS		+= -DCONFIG_RTL865X_SC
endif

ifeq ($(OFFICIAL_OPENSSL),1)
CFLAGS		+= $(INCS)
else
CFLAGS		+= -D__linux__ -DRSDK_BUILT $(INCS)
endif

ifeq ($(BIG_ENDIAN),y)
CFLAGS		+= -DB_ENDIAN
else
CFLAGS		+= -DL_ENDIAN
endif

ifeq ($(NOIWCTL),1)
CFLAGS		+= -DNO_IWCONTROL
endif

ifeq ($(CMO),1)
CFLAGS		+= -DCONFIG_CMO
endif

ifeq ($(TLD),1)
CFLAGS		+= -DDET_WPS_SPEC
endif


ifeq ($(CONFIG_MESH_ENABLE),y)
CFLAGS		+= -DCONFIG_RTK_MESH
endif	

SHELL		= /bin/sh
STRIP		= $(CROSS_COMPILE)strip

PROGS=wscd

ifeq ($(SLINK),1)
CFLAGS += -ffunction-sections -fdata-sections
LDFLAGS += --static -Wl,--gc-sections
endif


ifeq ($(CUR_USED_OS),UCLINUX)
LDFLAGS  += -Wl,-elf2flt=-s65536
endif


#CONFIG_RTL_DUAL_PCIESLOT_BIWLAN_D
ifeq ($(CONFIG_RTL_DUAL_PCIESLOT_BIWLAN_D),y)
CFLAGS += -DFOR_DUAL_BAND
endif

ifeq ($(CONFIG_RTL_92D_DMDP),y)
CFLAGS += -DFOR_DUAL_BAND
endif

ifeq ($(CONFIG_RTL_WPS2_SUPPORT),y)
CFLAGS += -DWPS2DOTX
endif

ifeq ($(CONFIG_WIFI_SINGLEAP), yes)
CFLAGS	+=  -D__SINGLE_AP__
endif

all:	BUILT_TIME $(PROGS)

clean:
	rm -f $(PROGS) core tags ID *.o *.d *~ *.bak built_time *.gdb *.elf

sources = $(SOURCES)

#include $(sources:.c=.d)

BUILT_TIME:
	@echo  \#define BUILT_TIME \"`TZ=UTC date -u "+%Y.%m.%d-%H:%M%z" `\" > ./built_time	

ifeq ($(CUR_USED_OS),LINUX)
$(PROGS):	$(sources:.c=.o)
	@echo $(CFLAGS) $(LDFLAGS)
	$(CC) $(LDFLAGS) $^ -o $@ $(LIBS)
#	$(STRIP) $@
else
$(PROGS):	$(sources:.c=.o)
	@echo $(CFLAGS) $(LDFLAGS)
	$(CC) $(LDFLAGS) $^ -o $@ $(LIBS)
endif
	
%.d: %.c
	@$(SHELL) -ec '$(CC) -MM $(CFLAGS) $< \
		      | sed '\''s/\($*\)\.o[ :]*/\1.o $@ : /g'\'' > $@; \
		      [ -s $@ ] || rm -f $@'
romfs:
	cp $(PROGS) $(PROGS).elf
	$(ROMFSINST)  /bin/$(PROGS)
