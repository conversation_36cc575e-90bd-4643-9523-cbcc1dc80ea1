LOCAL_PATH := $(call my-dir)

#########################

LIBS= -lm
CFLAGS := -W -Wall -Wstrict-prototypes $(CFLAGS)
CFLAGS += $(EXTRA_CFLAGS)

ifeq ($(SLINK),1)
CFLAGS  += -ffunction-sections -fdata-sections
LDFLAGS += --static -s -Wl,--gc-sections
endif

########################
include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_PRELINK_MODULE := false
LOCAL_SRC_FILES := iwlib.c
LOCAL_MODULE := libiw
LOCAL_CFLAGS := -Os -Wall $(CFLAGS) -D__ANDROID__
LOCAL_SHARED_LIBRARIES := libc
include $(BUILD_SHARED_LIBRARY)

#########################
include $(CLEAR_VARS)

LOCAL_MODULE_TAGS := optional
LOCAL_SRC_FILES := iwpriv.c
LOCAL_MODULE := iwpriv
LOCAL_SHARED_LIBRARIES := libc libiw
LOCAL_CFLAGS := -Os -Wall $(CFLAGS) -D__ANDROID__
#LOCAL_C_INCLUDES:= $(KERNEL_HEADERS) $(INCS) $(LOCAL_PATH)/../../include 
include $(BUILD_EXECUTABLE)

