	Wireless Tools
	--------------

	This package contain the Wireless tools, used to manipulate
the Wireless Extensions. The Wireless Extension is an interface
allowing you to set Wireless LAN specific parameters and get the
specific stats.

web page
--------
	You'll find a lot of useful info on :
		http://www.hpl.hp.com/personal/<PERSON>_<PERSON>/Linux/Tools.html
		http://web.hpl.hp.com/personal/<PERSON>_<PERSON>/Linux/

INSTALL
-------
	This file contains installation instruction and requirements.
	A *must* read.

PCMCIA.txt
----------
	This file describes how to use Pcmcia init script to configure
Wireless Extensions and how to use Pcmcia schemes.

DISTRIBUTION
------------
	This file will document the specifics of Wireless Extensions
with a few different distributions. I need your help to complete this
file.

man pages (iwconfig.8, iwlist.8, iwpriv.8, iwspy.8)
---------
	VERY IMPORTANT : I try to keep the man page up to date, so
you'd better read them before asking questions.
	ALSO IMPORTANT : Those man pages describe the capacity of the
tools, no device implement the full range (and drivers usually
implement even less).

	As far as I know, the man pages are the most complete, up to
date and accurate documentation of the wireless tools. An update of
the web page related to Wireless Extension is long overdue. Send
feedback to me.
	The man pages can either be copied in a location where the
command "man" will find them, such as /usr/local/man/man8, or can be
read locally with the command :
		nroff -man xxx.8 | less

iwconfig.c
----------
	The main wireless tool. Used for device configuration and to see
the most common wireless parameters.

iwlist.c
--------
	Display some large chunk of information not displayed by iwconfig.
	For example, all bit rates, all frequencies, all keys...

iwspy.c
-------
	Mobile IP support test and allow get get stats per MAC address
(instead of globally). Also, for some driver/device, this is the only
way to get stats in Ad-Hoc mode.

iwpriv.c
--------
	Manipulate driver private ioctls : all parameters that are
specific to a driver or a device and therefore not part of iwconfig.

iwgetid.c
---------
	Output the ESSID or NWID of the specified device.
	Can also output it in a form that can be used as a Pcmcia Scheme.

iwevent.c
---------
	Display Wireless Events. This is new, so there is not much support
in drivers for it yet...

iwlib.c
-------
	The Wireless Tools helper library. May be usefull if you want
to create your own applications using Wireless Extensions.

Changelog, contributions
------------------------
	See CHANGELOG.h

wireless.h
----------
	Definition of the Wireless Extensions. Remember that the
definition used by the drivers and the tools must match, otherwise
funny things may happen. The tools try to check for that.
	Since Wireless Extensions v12, you can no longer drop this
file in your kernel headers to update the Wireless Extensions, you
need to use the full patches available on my web page. So, the use is
more if you plan to do some cross compile or something similar.
	Just for your enjoyement, there is various release of it. If
your kernel/drivers are old, you may want to try the older releases...

sample_xxx.c :
------------
	Various samples of code showing how to implement some of the
more tricky feature of Wireless Extensions in your driver.
	Note that there is no guarantee that this code compile, let
alone work, but it should point you in the proper direction.
	Also, have a look at existing drivers in the Linux kernel.

Other tools :
-----------
	My web page list many other tools using Wireless
Extensions that you may find useful...
	http://www.hpl.hp.com/personal/Jean_Tourrilhes/Linux/Tools.html#links

Other questions :
---------------
	You have the source, and it is documented. In 99% of the case,
you will find your answer there.

	Good luck...

	Jean <<EMAIL>>
