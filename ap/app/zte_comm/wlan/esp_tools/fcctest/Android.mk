#
#Copyright (C) 2008 The Espressif Eagle Project
#
# This software may be distributed under the terms of the ESpressif license.
# See README for more details.
# for Allwin platform, build this application in android4.x/external/eagle_test
#
LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS) 
#LOCAL_STATIC_LIBRARIES := libc
LOCAL_MODULE_TAGS := debug
LOCAL_MODULE := esp_fcc_tool_android
OBJS_c = esp_fcc_main.c esp_fcc_cmd.c esp_fcc_file.c
LOCAL_SRC_FILES := $(OBJS_c)
LOCAL_SRC_FILES += $(LOCAL_PATH)/libc.a
LCAL_CFLAGS := $(L_CFLAGS)
include $(BUILD_EXECUTABLE)

