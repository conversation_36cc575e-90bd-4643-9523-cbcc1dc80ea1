#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>
#include <unistd.h>

#include "esp_fcc.h"

#define VER "V1.4"

struct fcc_conf_s gl_fcc_conf = {
	.cmd_path = DEFAULT_CMD_PATH,
	.driver_path = DEFAULT_DRIVER_PATH,
};

void show_usage(void)
{
	printf("usage:\n"
		 "esp_fcc_tool reset\n"
		 "esp_fcc_tool selftest\n"
		 "esp_fcc_tool ate_mode\n"
		 "esp_fcc_tool product_test\n"
		 "esp_fcc_tool fcctx rate_desc channel power_decrease(*0.25dbm)\n"
		 "esp_fcc_tool nortx rate_desc channel power_decrease(*0.25dbm)\n"
		 "esp_fcc_tool tonetx channel power_decrease(*0.25dbm)\n"
		 "esp_fcc_tool sdio_noise\n"
		 "esp_fcc_tool sdio_stability [start|stop]\n"
		 "esp_fcc_tool sdio_ddr_noise [start|stop]\n"
		 "esp_fcc_tool sdio_speed\n");

	printf("\nrate_desc:\n" "11b: [ 1m   2ms   2ml   5.5ms   5.5ml   6m   9m  11ms  11ml]\n"
		  "11g: [ 12m  18m  24m  36m  48m  54m ]\n"
		  "11n: [ mcs0 mcs1 mcs2 mcs3 mcs4 mcs5 mcs6 mcs7 ]\n");

	printf("\nPower MAX:	20.5 dbm [11n: mcs0] [11b: all] [tone]\n"
		"	      	20.5 dbm [11n: mcs1] [11g: 12m]\n"
		"		19.5 dbm [11n: mcs2] [11g: 18m]\n"
		"		19.5 dbm [11n: mcs3] [11g: 24m]\n"
		"		18.5 dbm [11n: mcs4] [11g: 36m]\n"
		"	  	17 dbm [11n: mcs5] [11g: 48m]\n"
		"	  	16 dbm [11n: mcs6] [11g: 54m]\n"
		"	  	14 dbm [11n: mcs7] \n");
	printf("\nWarning: the power of test target rate will be limited  >= 10dbm\n");
	printf("\nVERSION: %s\n", VER);
}

int main(int argc, char *argv[])
{
	TEST_ITEM_t test_item = ITEM_ITEM_MAX;

	request_conf(FCC_CONF, DEFAULT_FCC_CONF_PATH, &gl_fcc_conf);

	if (argc <= 1) {
		show_usage();
		return -EINVAL;
	}

	if (strcmp(argv[1], "selftest") == 0) {
		printf("selftest...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		cmd_selftest();
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_SELF_TEST;
	} else if (strcmp(argv[1], "sdio_noise") == 0) {
		printf("sdio_noise...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		cmd_sdio_noise();
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_SDIO_NOISE;
	} else if (strcmp(argv[1], "sdio_stability") == 0) {
		if (argc != 3) {
			show_usage();
			return -EINVAL;
		}
		if (strcmp(argv[2], "start") == 0) {
			int pid;
			printf("sdio_stability start...\n");
			cmd_driver_down();
			usleep(10000);
			cmd_clear();
			usleep(10000);
			cmd_sdio_stability_start();
			usleep(10000);
			pid = fork();
			if (pid == 0) {
				cmd_driver_up();
			} else if (pid < 0)
				return -ECHILD;
			else
				return 0;
		} else if (strcmp(argv[2], "stop") == 0) {
			printf("sdio_stability stop...\n");
			cmd_sdio_stability_stop();
			usleep(1000000); //1s
			test_item = ITEM_SDIO_STABILITY;
        } else {
            show_usage();
            return -EINVAL;
        }
    }else if (strcmp(argv[1], "sdio_ddr_noise") == 0) {
        if (argc != 3) {
            show_usage();
            return -EINVAL;
        }   
        if (strcmp(argv[2], "start") == 0) {
            int pid;
            printf("add_ddr_noise start...\n");
            usleep(10000);
            cmd_sdio_ddr_noise_start();
            usleep(10000);
            pid = fork();
            if (pid == 0) {
                ;
            } else if (pid < 0)
                return -ECHILD;
            else
                return 0;
        } else if (strcmp(argv[2], "stop") == 0) {
            cmd_sdio_ddr_noise_stop();
            printf("add_ddr_noise stop...\n");
            usleep(1000000); //1s
            test_item = ITEM_SDIO_DDR_NOISE;
        } else {
            show_usage();
            return -EINVAL;
        }   
    } else if (strcmp(argv[1], "sdio_speed") == 0) {
		printf("sdio_speed...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		cmd_sdio_speed();
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_SDIO_SPEED;
	} else if (strcmp(argv[1], "product_test") == 0) {
		printf("product_test...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		result_file_clear();
		usleep(10000);
		cmd_sdio_product_test();
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_PRODUCT_TEST;
	}else if (strcmp(argv[1], "fcctx") == 0) {
		int ret;
		if (argc != 5) {
			show_usage();
			return -EINVAL;
		}
		printf("fcctx...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		ret = cmd_fcc_tx(argv[2], atoi(argv[3]), atoi(argv[4]));
		if (ret < 0)
			return ret;
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_FCC_TX;
	} else if (strcmp(argv[1], "nortx") == 0) {
		int ret;
		if (argc != 5) {
			show_usage();
			return -EINVAL;
		}
		printf("nortx...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		ret = cmd_nor_tx(argv[2], atoi(argv[3]), atoi(argv[4]));
		if (ret < 0)
			return ret;
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_NOR_TX;
	} else if (strcmp(argv[1], "tonetx") == 0) {
		int ret;
		if (argc != 4) {
			show_usage();
			return -EINVAL;
		}
		printf("tonetx...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		ret = cmd_tone_tx(atoi(argv[2]), atoi(argv[3]));
		if (ret < 0)
			return ret;
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_TONE_TX;
	} else if (strcmp(argv[1], "reset") == 0) {
		printf("reset...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_clear();
		usleep(10000);
		cmd_driver_up();
	} else if (strcmp(argv[1], "ate_mode") == 0) {
		printf("ate mode...\n");
		cmd_driver_down();
		usleep(10000);
		cmd_ate_mode();
		usleep(10000);
		cmd_driver_up();
		test_item = ITEM_ABOVE_HAS_RESULT;
	} else {
		show_usage();
		return -EINVAL;
	}

	if (test_item >= ITEM_ABOVE_HAS_RESULT)
		return 0;
        

	process_res(test_item, &gl_fcc_conf);
	
	return 0;
}
