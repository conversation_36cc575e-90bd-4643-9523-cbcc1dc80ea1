#include <string.h>
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <errno.h>

#include "esp_fcc.h"

extern struct fcc_conf_s gl_fcc_conf;

typedef struct rate_s {
	unsigned int offset;
	char rate[8];
} rate_s_t;

typedef enum FCC_MODE_ID {
    FCC_MODE_ILDE = 0,
    FCC_MODE_SELFTEST = 1,
    FCC_MODE_CONT_TX,
    FCC_MODE_NORM_TX,
    FCC_MODE_TONE_TX,
    FCC_MODE_SDIO_NOISE,
    FCC_MODE_MAX,
} FCC_MODE_ID_t;

rate_s_t rate_table[23] = {
	{ 0, "1m"},
	{ 5, "2ms"},
	{ 1, "2ml"},
	{ 6, "5.5ms"},
	{ 2, "5.5ml"},
	{11, "6m"},
	{15, "9m"},
// above is 11b
	{ 7, "11ms"},
	{ 3, "11ml"},
	{10, "12m"},
	{14, "18m"},
	{ 9, "24m"},
	{13, "36m"},
	{ 8, "48m"},
	{12, "54m"},
// above is 11b
	{16, "mcs0"},
	{17, "mcs1"},
	{18, "mcs2"},
	{19, "mcs3"},
	{20, "mcs4"},
	{21, "mcs5"},
	{22, "mcs6"},
	{23, "mcs7"},
// above is 11n
};

int cmd_selftest(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo %d >%s/%s", FCC_MODE_SELFTEST, gl_fcc_conf.cmd_path, FCCMODE);
	return system(abs_cmd);
}

int cmd_fcc_tx(char *rate, unsigned int channel, unsigned int power_dec)
{
	int i;
	unsigned int var;
	unsigned int offset;
	char abs_cmd[128];

	if (rate == NULL || channel >14)
		return -EINVAL;

	for (i = 0; i < 23; i++) {
		if (strncmp(rate_table[i].rate, rate, strlen(rate_table[i].rate)) == 0) {
			offset = rate_table[i].offset;
			break;
		}
	}

	if (i >= 23) {
		printf("rate error");
		return -EINVAL;
	}

	var = ((power_dec<<24)|(offset<<16)|(channel<<8)|FCC_MODE_CONT_TX);

	sprintf(abs_cmd, "echo 0x%08x >%s/%s", var, gl_fcc_conf.cmd_path, FCCMODE);
	return system(abs_cmd);
}

int cmd_nor_tx(char *rate, unsigned int channel, unsigned int power_dec)
{
	int i;
	unsigned int var;
	unsigned int offset;
	char abs_cmd[128];

	if (rate == NULL || channel >14)
		return -EINVAL;

	for (i = 0; i < 23; i++) {
		if (strncmp(rate_table[i].rate, rate, strlen(rate_table[i].rate)) == 0) {
			offset = rate_table[i].offset;
			break;
		}
	}

	if (i >= 23) {
		printf("rate error");
		return -EINVAL;
	}

	var = ((power_dec<<24)|(offset<<16)|(channel<<8)|FCC_MODE_NORM_TX);

	sprintf(abs_cmd, "echo 0x%08x >%s/%s", var, gl_fcc_conf.cmd_path, FCCMODE);
	return system(abs_cmd);
}

int cmd_tone_tx(unsigned int channel, unsigned int power_dec)
{
	unsigned int var;
	char abs_cmd[128];

	if (channel >14)
		return -EINVAL;

	var = ((power_dec<<16)|(channel<<8)|FCC_MODE_TONE_TX);

	sprintf(abs_cmd, "echo 0x%08x >%s/%s", var, gl_fcc_conf.cmd_path, FCCMODE);
	return system(abs_cmd);
}


int cmd_sdio_stability_start(void)
{
	char abs_cmd[128];
	sprintf(abs_cmd, "echo 0x02 >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);

	return system(abs_cmd);
}

int cmd_sdio_ddr_noise_start(void)
{
	char abs_cmd[128];
	sprintf(abs_cmd, "%s &",DO_MEMCPY);

	return system(abs_cmd);
}

static void store_ddr_noise_pid()
{
	char abs_cmd[128];
	sprintf(abs_cmd, "ps | grep do_memcpy > %s/%s", gl_fcc_conf.driver_path, TEST_RESULTS);
	system(abs_cmd);
}

int cmd_sdio_ddr_noise_stop(void)
{
    int pid = 0 ,ppid = 0, i = 0 , err = -1;
    char buf[128];
	char abs_cmd[128];

    memset(buf, '\0', 128);

    store_ddr_noise_pid();
    
    sprintf(buf,"%s/%s", DEFAULT_DRIVER_PATH, TEST_RESULTS);
    FILE *fp = fopen(buf, "r");
    memset(buf, '\0', 128);
    while(fgets(buf, 128, fp) != 0){
        if(strstr(buf,"do_memcpy") != 0){

            while(buf[i] == ' '){
                i++;
            }

            while(buf[i] != ' '){
                i++;
            }

            while(buf[i] == ' ' && buf[i+1] == ' '){
                i++;
            }

            i++;

            while(buf[i] != ' '){
                pid *= 10;
                pid += buf[i] - '0';
                i++;
            }

            printf("pid = %d\n", pid);
            sprintf(abs_cmd, "kill -9 %d", pid);

            err = system(abs_cmd);
            memset(buf, '\0', 128);
            i = 0;
            pid = 0;
        }
    }
    return err;
}

int cmd_sdio_stability_stop(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 0x00 >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);
	return system(abs_cmd);
}

int cmd_sdio_speed(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 0x04 >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);
	return system(abs_cmd);
}
int cmd_sdio_product_test(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 0xa >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);
	return system(abs_cmd);
}

int cmd_sdio_noise(void)
{
	int ret;
	char abs_cmd[128];

	sprintf(abs_cmd, "echo %d >%s/%s", FCC_MODE_SDIO_NOISE, gl_fcc_conf.cmd_path, FCCMODE);
	ret = system(abs_cmd);
	sprintf(abs_cmd, "echo 0x06 >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);
	ret = system(abs_cmd);

	return ret;
}

int cmd_driver_up(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 1 >%s/%s", gl_fcc_conf.cmd_path, NORMODE);
	return system(abs_cmd);
}

int cmd_driver_down(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 0 >%s/%s", gl_fcc_conf.cmd_path, NORMODE);
	return system(abs_cmd);
}

int cmd_ate_mode(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 1 >%s/%s", gl_fcc_conf.cmd_path, ATEMODE);
	return system(abs_cmd);
}

int cmd_clear(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo 2 >%s/%s", gl_fcc_conf.cmd_path, NORMODE);
	return system(abs_cmd);
}
int result_file_clear(void)
{
	char abs_cmd[128];

	sprintf(abs_cmd, "echo "" > %s/%s", gl_fcc_conf.driver_path, TEST_RESULTS);
	system(abs_cmd);

	//sprintf(abs_cmd, "mount -o remount,rw %s",gl_fcc_conf.driver_path);
	//system(abs_cmd);

	sprintf(abs_cmd, "chmod 555 %s/%s", gl_fcc_conf.driver_path, TEST_RESULTS);
	return system(abs_cmd);
}
