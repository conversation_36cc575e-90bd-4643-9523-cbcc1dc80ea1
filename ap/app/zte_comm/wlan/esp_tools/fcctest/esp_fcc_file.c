#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>
#include <unistd.h>

#include "esp_fcc.h"

#define  SELF_SHOW_DETAIL (1)

int request_conf(const char *name, const char *path, struct fcc_conf_s *fcc_conf)
{
	FILE *filep;
	int ret;
	char abs_path[128];
	char buf[256];

	if (name == NULL || path == NULL || fcc_conf == NULL) 	{
		printf("%s invalid param\n", __func__);
		return -EINVAL;
	}

	sprintf(abs_path, "%s/%s", path, name);
	printf("%s abs_path %s\n", __func__, abs_path);

	filep = fopen(abs_path, "r");
	if (filep == NULL) {
		printf("%s file open error\n", __func__);
		return -EACCES;
	}
	while (fgets(buf, 256, filep) != NULL) {
		char *p = buf;
		char *attr = buf;
		char *var;
		while (*p != '=') {p++;};
		*p = '\0';
		var = p+1;
		while (*p != '\n') {p++;};
		*p = '\0';
		if (strncmp(attr, CONF_CMD_PATH_NAME, strlen(CONF_CMD_PATH_NAME)) == 0){
			strncpy(fcc_conf->cmd_path, var, strlen(var) - 1);
        }
		else if (strncmp(attr, CONF_DRIVER_PATH_NAME, strlen(CONF_DRIVER_PATH_NAME)) == 0){
			strncpy(fcc_conf->driver_path, var, strlen(var) - 1);
        }
		else  {
			//printf("[%s], cannot recognize\n", attr);
			continue;
		}
		printf("%s: %s\n", attr, var);
	};
	return 0;	
}


static int process_line(char *line, TEST_ITEM_t test_item) {
	char *res = "";
	char *stmp;
	int len;

	if (line == NULL)
		return -EINVAL;

	if (strstr(line, "FT:") != NULL && test_item == ITEM_SELF_TEST){
		int rx_path_gain, ppm, snr;
		char *p;
		stmp = strstr(line, ":") + 1;
		p = strtok(stmp, ",");
		rx_path_gain = atoi(p);
		p = strtok(NULL, ",");
		ppm = atoi(p);
		p = strtok(NULL, ",");
		snr = atoi(p);
		printf(" ===================================\n\n");
		printf("Freq Test Result: \n\n");
		if (snr >= 20) {
			printf("RX_PATH_GAIN : %d dB \n", rx_path_gain);
			printf("Freq OFFSET  : %d ppm \n", ppm);
			printf("频偏         : %d ppm \n", ppm);
		 	printf("PATH SNR     : %d dB \n", snr);
			if (ppm > 20 || ppm < -20) {
				printf("注意： 频偏太大！\n");
			}
		} else {

			printf("WARNING : PATH SNR IS TOO LOW , \n");
			printf("          TRY to GET CLOSER TO THE TEST BOARD!\n");
			printf("          测试信号不佳，请检查测试板是否打开，调整相对测试板位置，确认天线是否安装。\n");
			printf("PATH SNR     : %d dB", snr);
			printf("RX_PATH_GAIN : %d dB", rx_path_gain);
			printf("PATH SNR     : %d dB", snr);
			printf("ppm          : %d ppm", ppm);
		}

		printf("\n\n==================================\n\n");

	} else if (strstr(line, "RN:") && test_item == ITEM_SELF_TEST) {
		int i, noise_max, noise_min, noise_max_disp, noise_min_disp;
		int max_sum = 0, max_num = 0;
		char *p[20];
		int pcnt = 0;
		float rx_best_val = 0;
		float rx_worst_val = -800;
		float rx_score = 0;

		stmp = strstr(line, ":") + 1;
		for (i = 0; i < 20; i++) {
			if (i == 0)
				p[i] = strtok(stmp, ";");
			else
				p[i] = strtok(NULL, ";");
			if (p[i] == NULL)
				break;
			pcnt++;
		}
			
		printf("===============================\n\n");
		printf("RX NOISEFLOOR TEST RESULT : \n\n\n");
		for (i = 0; i < pcnt; i++) {
			char *sp;
			sp = strtok(p[i], ",");
			noise_max = atoi(sp);
			sp = strtok(NULL, ",");
			noise_min = atoi(sp);
			noise_max_disp = (noise_max - 3 * 4 < -98 * 4 ? -98 * 4 : noise_max - 3 * 4);
			noise_min_disp = (noise_min - 3 * 4 < -98 * 4 ? -98 * 4 : noise_min - 3 * 4);
			if (SELF_SHOW_DETAIL) {
				printf("CH %d: MAX:", (i + 1));
				if (noise_max == -800) {
					printf("TIME OUT ; MIN: ");
				} else {
					printf("%f ; MIN: ", (float)noise_max_disp/4.0);
					max_sum += noise_max;
					max_num++;
				}
				if (noise_min == -800) {
					printf("TIME OUT \n");
				} else {
					printf("%f \n", (float)noise_min_disp/4.0);
				}
			}
			if (noise_min < rx_best_val && noise_min != -800) {
				rx_best_val = (float)noise_min;
			}
			if (noise_min > rx_worst_val && noise_min != -800) {
				rx_worst_val = (float)noise_min;
			}

		}
		rx_best_val /= 4.0;
		rx_worst_val /= 4.0;
		float dtmp = ((float)max_sum * 1.0 / (float)max_num);
		float dtmp_disp = 0;
		if ((dtmp - 3 * 4.0) > (-98 * 4.0))
			dtmp_disp = dtmp - 3 * 4.0;
		else
			dtmp_disp = -98 * 4.0;
		dtmp /= 4;
		dtmp_disp /= 4;

#if 0
		if (rmNoise_flg) {
			res += "\nDDR NOISE RUNNING \n系统ddr干扰已经打开： \n";
			// tv.append(":max_sum:"+max_sum+" max_num: "+max_num+"\n");

			// tv.append(":max_sum:"+max_sum+" max_num: "+max_num+"dtmp:"+dtmp+"\n");
			// dtmp=( ((dtmp-3*4)<(-98*4))?(-98*4):(dtmp-3*4));

			// tv.append("avg max: "+ dtmp
			// +"  max sum: "+max_sum+" max_num:"+max_num+"\n");
			if (dtmp < -94)
				rx_score = 100;
			else
				rx_score = (100 - (dtmp + 94) * 2);
			// tv.append("\n worst:"+rx_worst_val+"\n");
		} else {
#endif
			if (rx_best_val < -94)
				rx_score = 100;
			else
				rx_score = (100 - (rx_best_val + 94) * 4);
#if 0
		}
#endif
		printf("\nmaxOf(MIN) : %f \n", (rx_worst_val * 4 - 3 * 4 < -98 * 4 ? -98 * 4 : rx_worst_val * 4 - 3 * 4) / 4 );
		printf("minOf(MIN) : %f \n", (rx_best_val * 4 - 3 * 4 < -98 * 4 ? -98 * 4 : rx_best_val * 4 - 3 * 4) / 4);
		printf("avgOf(MAX) : %.2f \n\n", dtmp_disp);
		printf("\n RX 测试得分： %.2f \n", rx_score);

		printf("\n\n=================================================\n\n");


	} else if (strstr(line, "TB:") && test_item == ITEM_SELF_TEST) {
		int tb, j;
		int worst_tx_val = 0;
		int dtmp = 0;
		float tx_score;
		char *p;
		stmp = strstr(line, ":") + 1;
		
		printf("===============================\n\n");
		printf("TX BACKOFF TEST RESULT : \n\n\n");
		for (j = 0; j < 20; j++) {
			if (j == 0)
				p = strtok(stmp, ",");
			else
				p = strtok(NULL, ",");
			if (p == NULL)
				break;

			if (SELF_SHOW_DETAIL) {
				printf("CHANNEL %d :  TX BACKOFF CAL VALUE:   0x%02x\n", (j + 1), atoi(p));
			}
			dtmp = atoi(p);
			if (dtmp > worst_tx_val)
				worst_tx_val = dtmp;
		}
		tx_score = (100 - (float)worst_tx_val * 2.5);
		printf("\n TX测试得分 ：%.2f \n", tx_score);
		printf("\n\n=================================================\n\n");
	} else if (strstr(line, "rw_time")) {
		int idx = 0;
		int num = 0;
		int t = 0;
		char *p[10];
		int pcnt = 0; 

		for (idx = 0; idx < 10; idx++) {
			if (idx == 0)
				p[idx] = strtok(line, ",");
			else
				p[idx] = strtok(NULL, ",");
			if (p[idx] == NULL)
				break;
			pcnt++;
		}
	
		for (idx = 0; idx < pcnt; idx++) {
			if (strstr(p[idx], "rw")) {
				strtok(p[idx], "=");
				t = atoi(strtok(NULL, "="));
				printf("-------------------------\n");
				printf("r/w time: %d ms;\n", t);
				printf("speed : %f Mbit/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000)));
				printf("speed : %f MB/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000) / 8));
				printf("-------------------------\n\n");
			} else if (strstr(p[idx], "read")) {
				strtok(p[idx], "=");
				t = atoi(strtok(NULL, "="));

				printf("-------------------------\n");
				printf("read time: %d ms;\n", t);
				printf("speed : %f Mbit/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000)));
				printf("speed : %f MB/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000) / 8));
				printf("-------------------------\n\n");
			} else if (strstr(p[idx], "write")) {
				strtok(p[idx], "=");
				t = atoi(strtok(NULL, "="));
				printf("-------------------------\n");
				printf("write time: %d ms;\n", t);
				printf("speed : %f Mbit/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000)));
				printf("speed : %f MB/s \n", (float) (256 * 1.0 / ((float)t * 1.0 / 1000) / 8));
				printf("-------------------------\n\n");
			}
		}

	} else if (strstr(line, "count")) {
		int num = 0;
		char *tmp = strdup(line);
		strtok(tmp, " ");
		strtok(NULL, " ");
		strtok(NULL, " ");
		num = atoi(strtok(NULL, " "));
		free(tmp);
		printf("\n-------------------------\n\n");
		if (strstr(line, "ok")) {
			printf("no error :\n\ntotal length: %d bytes\n", num);
		} else if (strstr(line, "error")) {
			printf("error !!!:\n\ntotal length: %d bytes\n", num);
		}
		printf("-------------------------\n\n");
	} else if (strstr(line, "RN:") && test_item == ITEM_SDIO_NOISE) {
		int i;
		float noise_max, noise_min, noise_max_disp, noise_min_disp;
		float rx_best_val = 0;
		float rx_worst_val = -400;
		float rx_score = 0;
		int max_sum = 0;
		int max_num = 0;
		int pcnt = 0;
		char *p[20];

		stmp = strstr(line,":") + 1;
		for (i = 0; i < 20; i++) {
			if (i == 0)
				p[i] = strtok(stmp, ";");
			else
				p[i] = strtok(NULL, ";");
			if (p[i] == NULL)
				break;
			pcnt++;
		}
		printf("===============================\n");
		printf("SDIO TEST\n\n");
		printf("===============================\n\n");
		printf("SDIO NOISE (RX) TEST RESULT : \n\n\n");
		for (i = 0; i < pcnt - 1 ; i++) {
			noise_max = (float)atof(strtok(p[i], ","));
			noise_min = (float)atof(strtok(NULL, ","));
			noise_max_disp = (noise_max - 3 * 4 < -98 * 4 ? -98 * 4
					: noise_max - 3 * 4);
			noise_min_disp = (noise_min - 3 * 4 < -98 * 4 ? -98 * 4
					: noise_min - 3 * 4);
			if (SELF_SHOW_DETAIL) {
				if (noise_max != -150) {
					printf("CH %d: MAX: %f; MIN %f\n", (i + 1),  noise_max_disp/4.0, noise_min_disp/4.0);
					max_sum += noise_max;
					max_num++;
				} else {
					printf("CH %d: MAX: TIME OUT ;  MIN : %f \n", (i + 1),  noise_min_disp / 4.0);
				}
			}
			if (noise_min < rx_best_val && noise_min != -150) {
				rx_best_val = noise_min;
			}
			if (noise_min > rx_worst_val && noise_min != -150) {
				rx_worst_val = noise_min;
			}

		}
		rx_best_val /= 4.0;
		rx_worst_val /= 4.0;

		float tmp = ((float)max_sum * 1.0 / max_num);
		float tmp_disp = 0;
		if (tmp - 3 * 4 < -98 * 4)
			tmp_disp = -98 * 4;
		else
			tmp_disp = tmp - 3 * 4;
		tmp /= 4;
		tmp_disp /= 4;
/*
		if (rmNoise_flg) {
			res += "\nDDR NOISE RUNNING \n系统ddr干扰已经打开： \n";
			if (tmp < -94)
				rx_score = 100;
			else
				rx_score = (100 - (tmp + 94) * 2);
			// tv.append("\n worst:"+rx_worst_val+"\n");
		} else {
*/
			// res+="best : "+rx_best_val+"\n";
			if (rx_best_val < -94)
				rx_score = 100;
			else
				rx_score = (100 - (rx_best_val + 94) * 4);
//		}
		printf("\nmaxOf(MIN) : %f\n",(rx_worst_val * 4 - 3 * 4 < -98 * 4 ? -98 * 4 : rx_worst_val * 4 - 3 * 4) / 4);
		printf("minOf(MIN) : %f\n", (rx_best_val * 4 - 3 * 4 < -98 * 4 ? -98 * 4 : rx_best_val * 4 - 3 * 4) / 4);
		printf("avgOf(MAX) : %.2f\n\n", tmp_disp);
		printf("\n SDIO NOISE(RX) 测试得分： %.2f\n", rx_score);

		printf("\n\n=================================================\n\n");

	}else if (test_item == ITEM_PRODUCT_TEST) {
        //printf("%s\n",line);
    }
	return 0;
}


int process_res(TEST_ITEM_t test_item, struct fcc_conf_s *fcc_conf)
{
	char abs_path[128];
	char buf[512];
	FILE *filep;
	

	if (!fcc_conf)
		return -EINVAL;

	sprintf(abs_path, "%s/%s", fcc_conf->driver_path, TEST_RESULTS);
	
	filep = fopen(abs_path, "r");
	if (filep == NULL) {
		printf("cannot open test result\n");
		return -EACCES;
	}
#if 0
	if (test_item == ITEM_PRODUCT_TEST) {
        int count = 100;
		while (fgets(buf, 512, filep) != NULL && count-- > 0) {
            if(strstr(buf, "don't use rtc mem data")){
		        process_line(buf, test_item);
                break;
            }else{
                fclose(filep);
	            filep = fopen(abs_path, "r");
		        //printf("%s\n",buf);
	            if (filep == NULL) {
		            printf("cannot open test result\n");
		            return -EACCES;
	            }
                usleep(1000000);
            }
        }
    }
#endif
	while (fgets(buf, 512, filep) != NULL) {
		process_line(buf, test_item);
	}

    fclose(filep);
	return 0;
}

