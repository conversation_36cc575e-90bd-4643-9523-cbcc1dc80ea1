#ifndef __ESP_FCC_H__
#define __ESP_FCC_H__

#define FCCMODE "fccmode"
#define ATEMODE "atemode"
#define NORMODE "normode"
#define DEFAULT_CMD_PATH "/sys/class/esp_boot"

#define FCC_CONF "esp_fcc.conf"
#define DEFAULT_FCC_CONF_PATH "/etc_ro"

#define TEST_RESULTS "test_results"
#define DEFAULT_DRIVER_PATH "/sys/class/esp_boot"

#define DO_MEMCPY "do_memcpy"

#define CONF_CMD_PATH_NAME "cmd_path"
#define CONF_DRIVER_PATH_NAME "driver_path"


typedef enum TEST_ITEM {
	ITEM_SELF_TEST = 0,
	ITEM_SDIO_NOISE,
	ITEM_SDIO_STABILITY,
	ITEM_SDIO_SPEED,
	ITEM_PRODUCT_TEST,
	ITEM_ABOVE_HAS_RESULT,
	ITEM_SDIO_DDR_NOISE,
	ITEM_FCC_TX,
	ITEM_NOR_TX,
	ITEM_TONE_TX,
	ITEM_ITEM_MAX,
} TEST_ITEM_t;

struct fcc_conf_s {
	char cmd_path[128];
	char driver_path[128];
};

//int file_read(const char *name, const char *path, char *buf, int length);
//int file_write(const char *name, const char *path, const char *buf, int length);

int request_conf(const char *name, const char *path, struct fcc_conf_s *fcc_conf);
int process_res(TEST_ITEM_t test_item, struct fcc_conf_s *fcc_conf);

int cmd_selftest(void);
int cmd_fcc_tx(char *rate, unsigned int channel, unsigned int power_dec);
int cmd_nor_tx(char *rate, unsigned int channel, unsigned int power_dec);
int cmd_tone_tx(unsigned int channel, unsigned int power_dec);
int cmd_sdio_stability_start(void);
int cmd_sdio_stability_stop(void);
int cmd_sdio_ddr_noise_start(void);
int cmd_sdio_ddr_noise_stop(void);
int cmd_sdio_speed(void);
int cmd_sdio_noise(void);
int cmd_driver_up(void);
int cmd_driver_down(void);
int cmd_sdio_product_test(void);
int cmd_clear(void);
int cmd_ate_mode(void);
int result_file_clear(void);

#endif /* __ESP_FCC_H__ */
