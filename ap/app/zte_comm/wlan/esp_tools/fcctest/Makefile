#ESP_TARGET=esp_fcc_tool_linux_arm
#mycc=/cold_2/aw_series/uneiltek/tool-chain/external-toolchain/bin/arm-none-linux-gnueabi-gcc
#$(ESP_TARGET):
#	$(mycc) -o $(ESP_TARGET) esp_fcc_main.c esp_fcc_file.c esp_fcc_cmd.c
#clean:
#	rm -f *.o $(ESP_TARGET) esp_fcc_tool_linux_arm
include $(zte_app_mak)

ESP_TARGET=esp_fcc_tool_android
ESP_CONF=esp_fcc.conf
#mycc=gcc
$(ESP_TARGET):
	$(CC) -o $(ESP_TARGET) esp_fcc_main.c esp_fcc_file.c esp_fcc_cmd.c
clean:
	rm -f *.o $(ESP_TARGET) esp_fcc_tool_arm

romfs:
	$(ROMFSINST) $(ESP_TARGET) /bin/$(ESP_TARGET)
	$(ROMFSINST) $(ESP_CONF) /etc_ro/$(ESP_CONF)
