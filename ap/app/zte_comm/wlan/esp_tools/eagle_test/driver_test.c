/*
 * WPA Supplicant - driver interaction with Linux nl80211/cfg80211
 * Copyright (c) 2003-2008, <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 * Alternatively, this software may be distributed under the terms of BSD
 * license.
 *
 * See README and COPYING for more details.
 */
#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/socket.h>

#include <linux/string.h>
#include <linux/netlink.h>
#include <linux/genetlink.h>
#include <unistd.h>
#include "type.h"

#include "testmode.h"
#include "driver_test.h"
#include "test_cmd.h"

static struct test_driver_param driver_op;

void *os_zalloc(size_t size)
{
    void *n=malloc(size);
    if(n)
        memset(n,0,size);
    return n;
}

/**
 * test_driver_nl80211_init - Initialize nl80211 driver interface
 * @ctx: context to be used when calling wpa_supplicant functions,
 * e.g., wpa_supplicant_event()
 * @ifname: interface name, e.g., wlan0
 * Returns: Pointer to private data, %NULL on failure
 */

int test_driver_init()
{
    struct sockaddr_nl saddr;
    int sd;

    sd = socket(AF_NETLINK, SOCK_RAW,NETLINK_GENERIC);
    os_memset(&saddr, 0, sizeof(saddr));

    saddr.nl_family = AF_NETLINK;
    saddr.nl_pid = getpid();
    saddr.nl_groups = 0;
    ;
    if (bind(sd, (struct sockaddr*)&saddr, sizeof(saddr)) < 0) {
        perror("bind(netlink)");
        return -1;
    } else {
        driver_op.sd=sd;
        return 0;
    }
}

int test_driver_close()
{
    close(driver_op.sd);
    return 0;
}

void register_test_cmd(u32 cmd_id, test_cmd_cb func)
{
    driver_op.cmd_id=cmd_id;
    driver_op.cmd_cb=func;
}

int test_driver_send_cmd(int nlmsg_type, int nlmsg_seq, int cmd_type, int cmd_ver, char *cmd, int cmd_len)
{
    struct sockaddr_nl daddr;
    struct nlmsghdr *nlhdr = NULL;
    struct genlmsghdr *hdr;
    char *payload;

    struct msghdr msg;
    struct iovec iov;
    int ret = -1;

    os_memset(&daddr, 0, sizeof(daddr));
    daddr.nl_family = AF_NETLINK;
    daddr.nl_pid = 0;
    daddr.nl_groups = 0;

    nlhdr = (struct nlmsghdr *)os_zalloc(NLMSG_SPACE(cmd_len+sizeof(struct genlmsghdr)));
    if(!nlhdr)
        return ret;

    nlhdr->nlmsg_len = NLMSG_LENGTH(cmd_len);
    nlhdr->nlmsg_pid = getpid();
    nlhdr->nlmsg_flags = NLM_F_REQUEST;
    nlhdr->nlmsg_type=nlmsg_type;
    nlhdr->nlmsg_seq = nlmsg_seq;

    hdr = (struct genlmsghdr *)NLMSG_DATA(nlhdr);
    hdr->cmd = cmd_type;
    hdr->version = cmd_ver;
    hdr->reserved = 0;

    //fill request info
    payload=(char *)(hdr+1);
    os_memcpy(payload, cmd, cmd_len);

    iov.iov_base = (void *)nlhdr;
    iov.iov_len = nlhdr->nlmsg_len;

    os_memset(&msg, 0, sizeof(struct msghdr));
    msg.msg_name = (void *)&daddr;
    msg.msg_namelen = sizeof(daddr);
    msg.msg_iov = &iov;
    msg.msg_iovlen = 1;
    ret = sendmsg(driver_op.sd, &msg, 0);
    if(ret>0)
        os_free(nlhdr);
    return ret;
}

void nl_put(char **attr, u16 attr_type, char *attr_payload, u16 payload_len)
{
    struct nlattr   *nlattr_hdr;
    char*    nlattr_msg;

    nlattr_hdr= (struct nlattr   *)(*attr);
    nlattr_hdr->nla_type=attr_type;
    nlattr_hdr->nla_len=NLA_ATTR_SIZE(payload_len);

    nlattr_msg=(char *)(nlattr_hdr+1);
    os_memset(nlattr_msg, 0, NLMSG_ALIGN(payload_len));
    os_memcpy(nlattr_msg, attr_payload, payload_len);
    *attr=nlattr_msg+NLMSG_ALIGN(payload_len);
}

/*return address point of next attribute*/
int nl_get(char **attr, u16 attr_type, void *attr_payload, u32 payload_maxlen)
{
    struct nlattr   *nlattr_hdr;
    u16  payload_len;

    nlattr_hdr= (struct nlattr   *)(*attr);
    if (attr_type!=nlattr_hdr->nla_type)
        return -1;

    payload_len=nlattr_hdr->nla_len-sizeof(nlattr_hdr);
    if(payload_len> payload_maxlen)
        return -1;
    os_memcpy((char *)attr_payload, (char *)(nlattr_hdr+1), payload_len);

    *attr=(*attr)+NLMSG_ALIGN(nlattr_hdr->nla_len);
    return 0;
}

int test_driver_get_nlid(char *fam_name)
{
    int ret = -1;
    struct ctrl_cmd_getfamily ctrl_cmd;
    char *cmd=(char *) &ctrl_cmd;

    if (sizeof(ctrl_cmd.family_name)<=strlen(fam_name))
        return -1;

    NL_STRING_PUT(&cmd, CTRL_ATTR_FAMILY_NAME, fam_name);

    ret=test_driver_send_cmd(GENL_ID_CTRL, 0, CTRL_CMD_GETFAMILY, 0x2,(char *) &ctrl_cmd, sizeof(ctrl_cmd));
    if(ret<0) {
        printf("fail to send cmd!\n");
        goto proc_end;
    }

    /*get reply from driver*/
    if(0>=(ret=test_driver_recv_reply(GENL_CTRL)))
        printf("fail to get reply!\n");

proc_end:
    return ret;
}

static int proc_ctrl_reply(struct genlmsghdr * hdr)
{
    char *ctrl_cmd;

    if(hdr->cmd==CTRL_CMD_NEWFAMILY) {
        ctrl_cmd=(char *)(hdr+1);

        /*get family name*/
        if (0> nl_get(&ctrl_cmd, CTRL_ATTR_FAMILY_NAME, &driver_op.fam.name, 16))
            goto error;

        /*get family id*/
        if (0> NL_GET16(&ctrl_cmd, CTRL_ATTR_FAMILY_ID, &driver_op.fam.id))
            goto error;

        //printf("%s fam.id %d \n", __func__, driver_op.fam.id);

        /*get version id*/
        if (0> NL_GET32(&ctrl_cmd, CTRL_ATTR_VERSION, &driver_op.fam.version))
            goto error;
#ifdef __DEBUG_TEST_SDIO
        printf("fam_name:%s, fam_id:%d,ver:%d\n", driver_op.fam.name, driver_op.fam.id, driver_op.fam.version);
#endif
        return 1;
    }

error:
    printf("reply atrr parse error!");
    return -1;
}

static int proc_cmd_reply_err(struct genlmsghdr * hdr)
{
    char *cmd;

    if(hdr->cmd!=driver_op.cmd_id) {
        printf("reply cmd_id:%d,not %d error!\n",hdr->cmd,driver_op.cmd_id);
    }

    cmd=(char *)(hdr+1);

    return -1;
}


static int proc_cmd_reply(struct genlmsghdr * hdr)
{
    char *cmd;

    if(hdr->cmd!=driver_op.cmd_id) {
        printf("reply cmd_id:%d,not %d error!\n",hdr->cmd,driver_op.cmd_id);
        goto error;
    }
    cmd=(char *)(hdr+1);

    if(0> driver_op.cmd_cb(cmd)) {
        printf("reply atrr parse error!");
        goto error;
    }
    return 1;
error:
    return -1;
}

int test_driver_recv_reply(u8 reply_type)
{
    struct sockaddr_nl daddr;
    struct nlmsghdr *nlhdr = NULL;
    struct msghdr msg;
    struct iovec iov;
    int ret = -1;

    memset(&daddr, 0, sizeof(daddr));
    daddr.nl_family = AF_NETLINK;
    daddr.nl_pid = 0;
    daddr.nl_groups = 0;
    nlhdr = (struct nlmsghdr *)os_zalloc(NLMSG_SPACE(MAX_MSGSIZE));
    if(!nlhdr) {
        printf("fail to alloc recv buff!\n");
        return ret;
    }

    memset(nlhdr, 0, NLMSG_SPACE(MAX_MSGSIZE));
    iov.iov_base = (void *)nlhdr;
    iov.iov_len = NLMSG_SPACE(MAX_MSGSIZE);

    memset(&daddr, 0, sizeof(daddr));
    msg.msg_name = (void *)&daddr;
    msg.msg_namelen = sizeof(daddr);
    msg.msg_iov = &iov;
    msg.msg_iovlen = 1;

    ret = recvmsg(driver_op.sd, &msg, 0);
    if (ret == 0) {
        printf("Exit.\n");
        exit(0);
    } else if (ret == -1) {
        perror("recvmsg:");
        exit(1);
    }

    if(reply_type==GENL_CTRL) {
        /*deal with gen_ctrl frame*/
        if(nlhdr->nlmsg_type==GENL_ID_CTRL) {
            driver_op.ctrl_seq=nlhdr->nlmsg_seq;
            ret=proc_ctrl_reply((struct genlmsghdr *)NLMSG_DATA(nlhdr));
        } else {
            printf("%s ID_CTRL err nlmsg_type %d expect %d \n", __func__, nlhdr->nlmsg_type, driver_op.fam.id);
            goto error;
        }
    } else if(reply_type==GENL_CMD) {
        /*deal with gen_cmd frame*/
        if(nlhdr->nlmsg_type==driver_op.fam.id) {
            ret=proc_cmd_reply((struct genlmsghdr *)NLMSG_DATA(nlhdr));
        } else {
            printf("%s err nlmsg_type %d expect %d \n", __func__, nlhdr->nlmsg_type, driver_op.fam.id);
            printf("%s nlmsg_len %d  pid %d flag %x\n", __func__, nlhdr->nlmsg_len, nlhdr->nlmsg_pid, nlhdr->nlmsg_flags);
            ret=proc_cmd_reply_err((struct genlmsghdr *)NLMSG_DATA(nlhdr));
            goto error;
	}
    } else {
	    printf("%s wrong reply_type! %d \n", __func__, reply_type);
        goto error;
    }

    goto end;

error:
    ret=-1;
    printf("Get wrong nlmsg_type! %d \n", reply_type);
end:
    os_free(nlhdr);
    return ret;
}

int test_cmd_send(int cmd_type, char *cmd, int cmd_len)
{
    int ret = -1;
    //cmd_len plus 4bytes as pad for nlmsg requirement
    ret=test_driver_send_cmd(driver_op.fam.id, 0, cmd_type, 1, cmd, cmd_len+4);
    return ret;
}

