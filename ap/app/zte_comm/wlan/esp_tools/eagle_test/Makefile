#makefile for wapi_test
include $(zte_app_mak)

#CC = /cold/oooo/prebuilt/gcc/linux-x86/arm/toolchain-sunxi-musl/toolchain/bin/arm-openwrt-linux-gcc
CFLAGS = -Wall -O -g  
TARGET = eagle_test_ate

DEPS = $(wildcard *.h)
%.o: %.c $(DEPS)
	$(CC) $(CFLAGS) -c $< -o $@

SOURCES = $(wildcard *.c)
OBJS = $(patsubst %.c,%.o,$(SOURCES))

$(TARGET) : $(OBJS)
	$(CC) $(OBJS) -o $(TARGET)
	chmod a+x $(TARGET)
.PHONY : clean
clean:
	rm -rf *.o esp $(TARGET)

romfs:
	$(ROMFSINST) $(TARGET) /bin/$(TARGET)
