
#ifndef _DRIVER_TEST_
#define _DRIVER_TEST_

#define MAX_MSGSIZE 1024

#define os_free          free
#define os_memset    memset
#define os_memcpy   memcpy
void * os_zalloc(size_t size);

#ifndef __packed
#define __packed __attribute__ ((packed))
#endif /* __packed */

#define NLA_ATTR_SIZE(payload) (NLA_HDRLEN+payload)

struct genl_family {
    u32  id;
    u32  hdrsize;
    char name[16];
    u32  version;
    u32  maxattr;
};

struct test_driver_param {
    int sd;
    u32 ctrl_seq;
    struct genl_family fam;
    u32 cmd_id;
    test_cmd_cb cmd_cb;
};


struct ctrl_cmd_getfamily {
    struct nlattr   nlattr_hdr;
    char    family_name[16];
} __packed;

void nl_put(char **attr, u16 attr_type, char *attr_payload, u16 payload_len);

#define NL_STRING_PUT(attr, attr_type, attr_payload) \
            nl_put(attr, attr_type, attr_payload, strlen(attr_payload)+1)

#define NL_PUT(attr, attr_type, attr_value, type) \
	do { \
	      type __tmp_##attr_type = attr_value; \
              nl_put(attr, attr_type, (char *)&__tmp_##attr_type , sizeof(type)); \
	} while(0)

static inline void NL_PUT32(char **attr, u16 attr_type, u32 attr_value)
{
    u32 tmp=attr_value;
    nl_put(attr, attr_type, (char *)&tmp, sizeof(tmp));
}

int nl_get(char **attr, u16 attr_type, void *attr_payload, u32 payload_len);

#define NL_GET32(attr, attr_type, attr_value) nl_get(attr, attr_type, attr_value, sizeof(u32))
#define NL_GET16(attr, attr_type, attr_value) nl_get(attr, attr_type, attr_value, sizeof(u16))
#define NL_GET8(attr, attr_type, attr_value) nl_get(attr, attr_type, attr_value, sizeof(u8))


void register_test_cmd(u32 cmd_id, test_cmd_cb func);
int test_driver_init();
int test_driver_close();
int test_driver_send_cmd(int nlmsg_type, int nlmsg_seq, int cmd_type, int cmd_ver, char *cmd, int cmd_len);
int test_driver_get_nlid(char *fam_name);
int test_driver_recv_reply(u8 reply_type);

int test_cmd_send(int cmd_type, char *cmd, int cmd_len);

#endif
