
#ifndef _TEST_CMD_
#define _TEST_CMD_

enum {
    GENL_CTRL,
    GENL_CMD,
};

enum {
    END_FAIL=-1,
    END_OK =0
};

enum {
    RD_REG=0,
    WR_REG,
    SET_SENSE,
    SET_TX_RATE,
    SET_TX_FREQ,
    TKIP_MIC_ERROR,
    RIFS_CTRL,
    BACKOFF,
    SET_RXSENSE,
    CONFIGURE_TRC,
    RDPER,
    RDRSSI,
    DBGTRC,
    WRMEM,
    RDMEM
};

enum {
    PHY_RATE_1_LONG = 0x0,
    PHY_RATE_2_LONG  = 0x1,
    PHY_RATE_5_LONG  = 0x2,
    PHY_RATE_11_LONG = 0x3,

    PHY_RATE_RESERVED   = 0x4,
    PHY_RATE_2_SHORT   = 0x5,
    PHY_RATE_5_SHORT   = 0x6,
    PHY_RATE_11_SHORT  = 0x7,

    PHY_RATE_48       = 0x8,
    PHY_RATE_24       = 0x9,
    PHY_RATE_12       = 0xA,
    PHY_RATE_6        = 0xB,
    PHY_RATE_54       = 0xC,
    PHY_RATE_36       = 0xD,
    PHY_RATE_18       = 0xE,
    PHY_RATE_9        = 0xF,

    PHY_RATE_MAX,
    PHY_RATE_ERROR,
};

struct cmd_echo {
    struct nlattr   nlattr_hdr;
    char    echo_info[64];
} __packed;

struct cmd_ate {
    struct nlattr   nlattr_hdr;
    char    ate_info[128];
} __packed;

struct cmd_sdiospeed {
    struct nlattr   nlattr_hdr;
    char    sdiospeed_info[32];
} __packed;


struct cmd_sdiotest {
    struct nlattr   nlattr_hdr0;
    u32 start;
    struct nlattr   nlattr_hdr1;
    u32 mode;
    struct nlattr   nlattr_hdr2;
    u32 addr;
    struct nlattr   nlattr_hdr3;
    u32 idle_period;
} __packed;

struct cmd_ask {
    struct nlattr   nlattr_hdr;
    char    echo_info[256];
} __packed;

/*sleep_mode
    0: sleep and wakeup fix time as sleep_tm_ms and wakeup_tm_ms
    1: sleep rand time(from 0 to sleep_tm_ms) and wakeup fix time as wakeup_tm_ms
    2: sleep fix time as sleep_tm_ms and wakeup rand time from 0 to wakeup_tm_ms
    3: sleep rand time (from 0 to sleep_tm_ms) and wakeup rand time(from 0 to wakeup_tm_ms)
*/
struct cmd_sleep {
    struct nlattr   nlattr_hdr0;
    u32     sleep_mode;
    struct nlattr   nlattr_hdr1;
    u32     sleep_tm_ms;
    struct nlattr   nlattr_hdr2;
    u32     wakeup_tm_ms;  //zero: after receive bcn, then sleep, nozero: delay nozero ms to sleep
    struct nlattr   nlattr_hdr3;
    u32    sleep_times;         //zero: always sleep, nozero: after nozero number sleep/wakeup, then end up sleep
} __packed;

struct cmd_wakeup {
    struct nlattr   nlattr_hdr;
    u32     check_data;        //use as check if reply is ok, target need copy this data back in reply
} __packed;

struct cmd_loopback {
    struct nlattr   nlattr_hdr0;
    u32     tx_packet_len;        //host to target packet_len, 0: means no packet from host to target
    struct nlattr   nlattr_hdr1;
    u32     rx_packet_len;        //target to host packet_len, 0: means no packet from target to host
    struct nlattr   nlattr_hdr2;
    u32     packet_num;      //transaction number, one transaction includes one tx packet and one rx packet
} __packed;

struct cmd_tx {
    struct nlattr   nlattr_hdr0;
    u32     txpacket_len;        //packetlen as byte nunber
} __packed;
struct cmd_rx {
    struct nlattr   nlattr_hdr0;
    u32     rxpacket_len;        //packetlen as byte nunber
    struct nlattr   nlattr_hdr1;
    u32     rxpacket_num;      //packetlen as byte nunber
} __packed;


struct cmd_para_t {
    struct nlattr   nlattr_hdr;
    u32     para_data;        //use as check if reply is ok, target need copy this data back in reply
} __packed;

struct cmd_debug {
    struct nlattr   nlattr_hdr0;
    u32  cmd_type;
    struct nlattr   nlattr_hdr1;
    u32  para_num;
    struct cmd_para_t cmd_para[8];
} __packed;

struct cmd_sdwr {
    struct nlattr   nlattr_hdr0;
    u32     func_no;
    struct nlattr   nlattr_hdr1;
    u32     addr;
    struct nlattr   nlattr_hdr2;
    u32     value;
} __packed;

struct cmd_sdrd {
    struct nlattr   nlattr_hdr0;
    u32     func_no;
    struct nlattr   nlattr_hdr1;
    u32     addr;
} __packed;

typedef struct test_cmd_t {
    u32      cmd_id;
    char     cmd_name[16];
    int (* cmd_func)(u32 cmd_id, int argc, char * argv[]);
    test_cmd_cb cmd_cb;
    char     help_str[64];
} CmdMethodDef;

#define TEST_CMD_SEND_REPLY(cmd_type, cmd) \
            test_cmd_send_reply(cmd_type, (char *)&cmd, sizeof(cmd))

#define IS_HELP_CMD(cmd_str)     ((strcmp(cmd_str,"-h")==0)\
                                                     ||(strcmp(cmd_str,"-help")==0)\
                                                     ||(strcmp(cmd_str,"--help")==0)\
                                                     ||(strcmp(cmd_str,"help")==0))

#define SHOW_CMD(no)  \
            printf("%d: %s --%s\n", Test_CmdMethods[(no)].cmd_id, Test_CmdMethods[(no)].cmd_name, Test_CmdMethods[(no)].help_str)

int find_cmd_by_name(char *cmd_name);
int run_test_cmd(u32 cmd_id, int argc, char * argv[]);
void print_cmd_list(u32 no);

#endif
