
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <linux/netlink.h>
#include <linux/genetlink.h>
#include "type.h"
#include "testmode.h"
#include "driver_test.h"
#include "test_cmd.h"

int main(int argc, char * argv[])
{
    int res;

    /*no param append, as help cmd*/
    if((argc==1)||((argc==2)&&IS_HELP_CMD(argv[1]))) {
        print_cmd_list(0);
        goto end;
    }

    if(0>(res=test_driver_init())) {
        printf("error occur!\n");
        goto end;
    }

    if (test_driver_get_nlid("esp_sdio") == -1) {
        printf("Can not get nlid of esp_sdio!\n");
        goto end_txrx;
    }
#ifdef __DEBUG_TEST_SDIO
    printf("Got nlid for esp_sdio!\n");
#endif

    if (0>(res=find_cmd_by_name(argv[1]))) {
        printf("Can not find required cmd!\n");
        goto end_txrx;
    }

    if (run_test_cmd(res, argc, argv) <0) {
        printf("fail to run command!\n");
        goto end_txrx;
    }

end_txrx:
    test_driver_close();
end:
    printf("\n");
    return 0;
}

