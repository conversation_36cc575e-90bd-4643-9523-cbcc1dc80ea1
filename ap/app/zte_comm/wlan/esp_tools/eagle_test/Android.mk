#
#Copyright (C) 2008 The Espressif Eagle Project
#
# This software may be distributed under the terms of the ESpressif license.
# See README for more details.
# for Allwin platform, build this application in android4.x/external/eagle_test
#

LOCAL_PATH := $(call my-dir)
include $(CLEAR_VARS)
LOCAL_MODULE := eagle_test_ate
LOCAL_MODULE_TAGS := debug
OBJS_c = main.c
OBJS_c += test_cmd.c
OBJS_c += driver_test.c
LOCAL_SRC_FILES := $(OBJS_c) 
LCAL_CFLAGS := $(L_CFLAGS)
LOCAL_C_INCLUDES := $(INCLUDES)
include $(BUILD_EXECUTABLE)
