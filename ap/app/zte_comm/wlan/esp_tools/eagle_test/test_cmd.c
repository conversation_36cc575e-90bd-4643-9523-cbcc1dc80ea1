/*
 * WPA Supplicant - driver interaction with Linux nl80211/cfg80211
 * Copyright (c) 2003-2008, <PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 * Alternatively, this software may be distributed under the terms of BSD
 * license.
 *
 * See README and COPYING for more details.
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <linux/string.h>
#include <linux/netlink.h>
#include <linux/genetlink.h>
#include <unistd.h>
#include "type.h"
#include "testmode.h"
#include "driver_test.h"
#include "test_cmd.h"

static int test_cmd_echo(u32 cmd_id,int argc, char * argv[]);
static int test_cmd_echo_cb(char * cmd);
static int test_cmd_ask(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_ask_cb(char * cmd);
static int test_cmd_sleep(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_sleep_cb(char * cmd);
static int test_cmd_wakeup(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_wakeup_cb(char * cmd);
static int test_cmd_loopback(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_loopback_cb(char * cmd);
static int test_cmd_tx(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rx(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_tx_cb(char * cmd);
static int test_cmd_rx_cb(char * cmd);
static int test_cmd_debug(u32 cmd_type, u32 para_num, char * para[]);
static int test_cmd_debug_cb(char * cmd);
static int test_cmd_rd(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_wr(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_sense(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rxsense(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_txrate(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_setfreq(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_tkipmicerr(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rifs(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_backoff(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rdper(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rdrssi(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_dbgtrc(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_wrmem(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_rdmem(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_sdwr(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_sdwr_cb(char * cmd);
static int test_cmd_sdrd(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_sdrd_cb(char * cmd);
static int test_cmd_trc(u32 cmd_id, int argc, char * argv[]);
static int test_cmd_ate(u32 cmd_id,int argc, char * argv[]);
int test_cmd_ate_cb(char * cmd);
static int test_cmd_sdiotest(u32 cmd_id,int argc, char * argv[]);
static int test_cmd_sdiotest_cb(char *cmd);
static int test_cmd_sdiospeed(u32 cmd_id,int argc, char * argv[]);
static int test_cmd_sdiospeed_cb(char * cmd);

static CmdMethodDef Test_CmdMethods[] = {
    {TEST_CMD_UNSPEC, "", NULL, NULL,""},
    {TEST_CMD_ECHO, "echo", test_cmd_echo, test_cmd_echo_cb, "send str, and get its echo"},
    {TEST_CMD_ASK, "ask", test_cmd_ask, test_cmd_ask_cb,"send string cmd to target"},
    {TEST_CMD_SLEEP,"sleep", test_cmd_sleep, test_cmd_sleep_cb,"request target to sleep"},
    {TEST_CMD_WAKEUP, "wakeup", test_cmd_wakeup, test_cmd_wakeup_cb,"wakeup target"},
    {TEST_CMD_LOOPBACK, "loopback", test_cmd_loopback, test_cmd_loopback_cb,"test sdio throughput"},
    {TEST_CMD_TX,     "tx", test_cmd_tx, test_cmd_tx_cb,"send packet to target to transmit"},
    {TEST_CMD_RX,     "rx", test_cmd_rx, test_cmd_rx_cb,"receive packets from target"},
    {TEST_CMD_DEBUG, "rd", test_cmd_rd, test_cmd_debug_cb,"read target registers"},
    {TEST_CMD_DEBUG, "wr", test_cmd_wr, test_cmd_debug_cb,"write target registers"},
    {TEST_CMD_DEBUG, "sense", test_cmd_sense, test_cmd_debug_cb,"set rx sensitivity mode, 0, dec 10dB, dec 20dB"},
    {TEST_CMD_DEBUG, "txrate", test_cmd_txrate, test_cmd_debug_cb,"set tx rate to fix value"},
    {TEST_CMD_DEBUG, "setfreq", test_cmd_setfreq, test_cmd_debug_cb,"set tx freq to 1~14 chn"},
    {TEST_CMD_DEBUG, "micerr", test_cmd_tkipmicerr, test_cmd_debug_cb,"set ptk/gtk tkip mic error"},
    {TEST_CMD_DEBUG, "rifs", test_cmd_rifs, test_cmd_debug_cb,"set/unset rifs"},
    {TEST_CMD_DEBUG, "backoff", test_cmd_backoff, test_cmd_debug_cb,"set backoff"},
    {TEST_CMD_DEBUG, "rxsense", test_cmd_rxsense, test_cmd_debug_cb,"set rxsense"},
    {TEST_CMD_DEBUG, "trc", test_cmd_trc, test_cmd_debug_cb,"config trc, ratecode is to fix the rate, maxrate disable fixrate, 0x100 enable rate control, 0x101 disable it"},
    {TEST_CMD_DEBUG, "rdper", test_cmd_rdper, test_cmd_debug_cb,"read per"},
    {TEST_CMD_DEBUG, "rdrssi", test_cmd_rdrssi, test_cmd_debug_cb,"read rssi"},
    {TEST_CMD_DEBUG, "dbgtrc", test_cmd_dbgtrc, test_cmd_debug_cb,"dbg trc"},
    {TEST_CMD_DEBUG, "wrmem", test_cmd_wrmem, test_cmd_debug_cb,"write memory"},
    {TEST_CMD_DEBUG, "rdmem", test_cmd_rdmem, test_cmd_debug_cb,"read memory"},
    {TEST_CMD_SDIO_WR, "sdwr", test_cmd_sdwr, test_cmd_sdwr_cb,"write sdio register"},
    {TEST_CMD_SDIO_RD, "sdrd", test_cmd_sdrd, test_cmd_sdrd_cb,"read sdio register"},
    {TEST_CMD_ATE, "ate", test_cmd_ate, test_cmd_ate_cb, "ate cmd"},
    {TEST_CMD_SDIOTEST, "sdt", test_cmd_sdiotest, test_cmd_sdiotest_cb, "sdio test on/off, mode, addr, idle_time(msec)"},
    {TEST_CMD_SDIOSPEED, "sdiospeed", test_cmd_sdiospeed, test_cmd_sdiospeed_cb, "high or low"},
    {TEST_CMD_MAX, "", NULL, NULL,""}
};

void print_cmd_list(u32 no)
{
    u32 i, cmd_num=sizeof(Test_CmdMethods)/sizeof(CmdMethodDef);
    if(!no) {
        printf("\n*******CMD LIST*****************\n");
        for(i=1; i<cmd_num-1; i++)
            SHOW_CMD(i);
    } else if(no<cmd_num-1)
        SHOW_CMD(no);
    else
        printf("Cmd No out of max range!\n");
}

int find_cmd_by_name(char *cmd_name)
{
    u32 i, ret=END_FAIL;
    u32 cmd_num=sizeof(Test_CmdMethods)/sizeof(CmdMethodDef);

    for(i=1; i<cmd_num-1; i++) {
        if(strcmp(Test_CmdMethods[i].cmd_name, cmd_name) == 0) {
            register_test_cmd(Test_CmdMethods[i].cmd_id, Test_CmdMethods[i].cmd_cb);
            ret=i;
            break;
        }
    }
    return ret;
}

int run_test_cmd(u32 cmd_id, int argc, char * argv[])
{
    return Test_CmdMethods[cmd_id].cmd_func(cmd_id, argc, argv);
}

static int test_cmd_send_reply(int cmd_type, char *cmd, int cmd_len)
{
    int ret = END_FAIL;

    if (0> (ret=test_cmd_send(cmd_type, cmd, cmd_len))) {
        printf("fail to send cmd!\n");
        goto proc_end;
    }

    /*get reply from driver*/
    if(0>=(ret=test_driver_recv_reply(GENL_CMD)))
        printf("fail to get reply!\n");

proc_end:
    return ret;
}

static int get_para_frm_arg(char * argv, unsigned int * para)
{
    char* stopstr;
    *para=strtoul(argv, &stopstr, 0);
    if(strlen(stopstr)) {
        printf("input param error! %s\n",argv);
        return END_FAIL;
    } else
        return END_OK;
}

static int test_cmd_sdiotest(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_sdiotest sdt_cmd;
    char * cmd = (char *)&sdt_cmd; //it's necessary, cmd pointer will be moved fwd after nl_put()
    u8 paranum = 0;
    int i = 0;
    u32 v = 0;

    if (argc <= 2) {
        printf("Incorrect usage, miss sdio test cmd!\n");
        return END_FAIL;
    }
    paranum = argc - 2;


    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("Usage: esp sdt [on|off] mode(1:wr;2:rd;3:rd&wr) addr. idle_period(in msec)\n");
        return END_OK;
    }

    memset(&sdt_cmd, 0, sizeof(struct cmd_sdiotest));

    if (!strcmp("on", argv[2])) {
	    v = 0x80000000 + paranum;
    } else if (!strcmp("off", argv[2])) {
	    v = paranum;
    } else {
        printf("Usage: esp sdt [on|off] mode(1:wr;2:rd;3:rd&wr) addr. idle_period(in msec)\n");
	return END_OK;
    }
    NL_PUT32(&cmd, TEST_ATTR_PARA0, v);
    printf("%s start 0x%08x\n", __func__, v);

    if (paranum > 1) {
	    if (get_para_frm_arg(argv[3], &v) == END_FAIL)
		    return END_FAIL;

	    if (!v || v > 3) {
		    printf("Usage: esp sdt [on|off] mode(1:wr;2:rd;3:rd&wr) addr. idle_period(in msec)\n");
		    return END_OK;
	    }
	    NL_PUT32(&cmd, TEST_ATTR_PARA1, v);
	    printf("%s mode %d\n", __func__, v);
    }

    if (paranum > 2) {
	    if (get_para_frm_arg(argv[4], &v) == END_FAIL)
		    return END_FAIL;

	    NL_PUT32(&cmd, TEST_ATTR_PARA2, v);
	    printf("%s addr %d\n", __func__, v);
    }

    if (paranum == 4) {
	    if (get_para_frm_arg(argv[5], &v) == END_FAIL)
		    return END_FAIL;

	    NL_PUT32(&cmd, TEST_ATTR_PARA3, v);
	    printf("%s idle_period %d\n", __func__, v);
    }

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_SDIOTEST, sdt_cmd);
}

int test_cmd_sdiotest_cb(char * cmd)
{
    char cb_info[32];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &cb_info, 32-1))
        goto error;

    printf("sdiotest:%s\n",cb_info);

    return END_OK;

error:
    printf("fail to get echo info!\n");
    return END_FAIL;
}

static int test_cmd_ate(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_ate ate_cmd;
    char *cmd=(char *)&ate_cmd;
    u8 paranum = 0;
    char cmdstr[1024];
    int i = 0;

    if (argc <= 2) {
        printf("Incorrect usage, miss ate cmd!\n");
        return END_FAIL;
    }
    paranum = argc - 3;

    if (strlen(argv[2]) >= 128) {
        printf("Cmd para error, cmd string too long!\n");
        return END_FAIL;
    }

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("Usage: esp ate cmd_string\n");
        printf("cmd_string length is less than 128\n");
        return END_OK;
    }

    memset(cmdstr, 0, sizeof(cmdstr));
    strcpy(cmdstr, argv[2]);
    for (i = 0; i < paranum; i++) {
	    strcat(cmdstr, " ");
	    strcat(cmdstr, argv[3+i]);
    }

    printf("ate cmdstr %s \n", cmdstr);
    /*fill  command content*/
    NL_STRING_PUT(&cmd, TEST_ATTR_STR, cmdstr);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_ATE, ate_cmd);
}



int test_cmd_ate_cb(char * cmd)
{
    char ate_info[128];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &ate_info, 128-1))
        goto error;

    printf("ATE from esp_sdio:%s\n",ate_info);

    return END_OK;

error:
    printf("fail to get echo info!\n");
    return END_FAIL;
}


static int test_cmd_echo(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_echo echo_cmd;
    char *cmd=(char *)&echo_cmd;

    if (argc<=2) {
        printf("Incorrect usage, miss echo string!\n");
        return END_FAIL;
    }
    if(strlen(argv[2])>=64) {
        printf("Cmd para error, echo string too long!\n");
        return END_FAIL;
    }

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("Usage: esp echo cmd_string\n");
        printf("cmd_string length is less than 64\n");
        return END_OK;
    }

    /*fill  command content*/
    NL_STRING_PUT(&cmd, TEST_ATTR_STR, argv[2]);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_ECHO, echo_cmd);
}

static int test_cmd_echo_cb(char * cmd)
{
    char echo_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &echo_info, 99))
        goto error;
    printf("Echo from esp_sdio:%s\n",echo_info);
    return END_OK;
error:
    printf("fail to get echo info!\n");
    return END_FAIL;
}

static int test_cmd_sdiospeed(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_sdiospeed sdiospeed_cmd;
    char *cmd=(char *)&sdiospeed_cmd;

    if (argc<=2) {
        printf("Incorrect usage, miss echo string!\n");
        return END_FAIL;
    }
    if(strlen(argv[2])>=64) {
        printf("Cmd para error, echo string too long!\n");
        return END_FAIL;
    }

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("Usage: eagle_test sdiospeed [high | low]\n");
        return END_OK;
    }

    /*fill  command content*/
    NL_STRING_PUT(&cmd, TEST_ATTR_STR, argv[2]);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_SDIOSPEED, sdiospeed_cmd);
}

static int test_cmd_sdiospeed_cb(char * cmd)
{
    char sdiospeed_info[32];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &sdiospeed_info, 31))
        goto error;
    printf("sdiospeed:%s\n",sdiospeed_info);
    return END_OK;

error:
    printf("fail to get sdiospeed info!\n");

    return END_FAIL;
}

static int test_cmd_ask(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_ask ask_cmd;
    char *cmd=(char *)&ask_cmd;
    char cmd_string[256];
    int i;

    if (argc<=2) {
        printf("Incorrect usage, miss ask string!\n");
        return END_FAIL;
    }
    if(strlen(argv[2])>=16) {
        printf("Cmd para error, asked cmd_name too long!\n");
        return END_FAIL;
    }

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("Usage: esp ask cmd_name p1 p2 ...\n");
        printf("cmd_name length is less than 16, px is int type data\n");
        return END_OK;
    }

    /*fill  command content*/
    os_memset(cmd_string, 0, 256);
    for(i=2; i<argc; i++) {
        strcat((char *)&cmd_string, argv[i]);
        strcat((char *)&cmd_string, " ");
    }
    NL_STRING_PUT(&cmd, TEST_ATTR_STR, (char *)&cmd_string);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_ASK, ask_cmd);
}

static int test_cmd_ask_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("reply:%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_sleep(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_sleep sleep_cmd;
    char *cmd=(char *)&sleep_cmd;
    unsigned int para;
    char usage_str[]="Usage: esp sleep slp_mode slp_tm_ms wakeup_tm_ms slp_times\n slp_mode 0~all fix, 1~slp rand&wk fix,2~slp fix&wk rand,3:all rand\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc<6 ) {
        printf("Incorrect usage, miss sleep params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*sleep_mode
      0: sleep and wakeup fix time as sleep_tm_ms and wakeup_tm_ms
      1: sleep rand time(from 0 to sleep_tm_ms) and wakeup fix time as wakeup_tm_ms
      2: sleep fix time as sleep_tm_ms and wakeup rand time from 0 to wakeup_tm_ms
      3: sleep rand time (from 0 to sleep_tm_ms) and wakeup rand time(from 0 to wakeup_tm_ms)
    */
    if(END_FAIL==get_para_frm_arg(argv[2], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, para);


    /*sleep_tm_ms*/
    if(END_FAIL==get_para_frm_arg(argv[3], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA1, para);

    /*wakeup_tm_ms*/
    if(END_FAIL==get_para_frm_arg(argv[4], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA2, para);

    /*sleep_times*/
    if(END_FAIL==get_para_frm_arg(argv[5], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA3, para);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_SLEEP, sleep_cmd);

error:
    return END_FAIL;
}

static int test_cmd_sleep_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("reply:%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_wakeup(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_wakeup wakeup_cmd;
    char *cmd=(char *)&wakeup_cmd;
    unsigned int para;
    char usage_str[]="Usage: esp wakeup check_data\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("check_data: to check if data in reply is same as it\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss wakeup params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content*/
    if(END_FAIL==get_para_frm_arg(argv[2], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, para);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_WAKEUP, wakeup_cmd);

error:
    return END_FAIL;

}

static int test_cmd_wakeup_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int tim_subtract(struct timeval *result, struct timeval *x, struct timeval *y)
{
    if(x->tv_sec > y->tv_sec)
        return -1;

    if((x->tv_sec==y->tv_sec)&&(x->tv_usec>y->tv_usec))
        return -1;

    result->tv_sec =(y->tv_sec - x->tv_sec);
    result->tv_usec=(y->tv_usec - x->tv_usec);
    if(result->tv_usec<0) {
        result->tv_sec--;
        result->tv_usec+=1e6;
    }
    return 0;
}

static int test_cmd_loopback(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_loopback loopback_cmd;
    char *cmd;
    unsigned int pack_num, txpack_len, rxpack_len;
    char usage_str[]="Usage: esp loopback txpack_len rxpack_len pack_num\n";
    struct timeval start,stop,diff;
    diff.tv_sec=0;
    diff.tv_usec=0;

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("txpack_len: byte num in a packet frm host to tgt, rxpack_len: byte num in a packet frm tgt to host, pack_num: pack num for test\n");
        return END_OK;
    }

    if (argc<5 ) {
        printf("Incorrect usage, miss params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content*/
    if(END_FAIL==get_para_frm_arg(argv[2], &txpack_len))
        goto error;

    if(END_FAIL==get_para_frm_arg(argv[3], &rxpack_len))
        goto error;

    if(END_FAIL==get_para_frm_arg(argv[4], &pack_num))
        goto error;

    cmd=(char *)&loopback_cmd;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, txpack_len);
    NL_PUT32(&cmd, TEST_ATTR_PARA1, rxpack_len);
    NL_PUT32(&cmd, TEST_ATTR_PARA2, pack_num);

    gettimeofday(&start, 0);
    /*contact with driver*/
    if (0>TEST_CMD_SEND_REPLY(TEST_CMD_LOOPBACK, loopback_cmd))
        goto error;
    gettimeofday(&stop, 0);
    tim_subtract(&diff, &start, &stop);
    printf("total elapse time:%u:%u us\n", (u32) diff.tv_sec, (u32)diff.tv_usec);
    printf("throughput rate:%3.2fMb/s\n", ((txpack_len+rxpack_len)*pack_num*8.0)/(diff.tv_sec*1e6+diff.tv_usec));
    return 0;
error:
    return END_FAIL;

}

static int test_cmd_loopback_cb(char * cmd)
{
    char reply_info[100];

    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;

    printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_tx(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_tx tx_cmd;
    char *cmd;
    unsigned int txpack_len, txpack_num , i;
    char usage_str[]="Usage: esp tx pack_len pack_num\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("txpack_len: byte num in a packet frm host to tgt\n");
        return END_OK;
    }

    if (argc<4 ) {
        printf("Incorrect usage, miss params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content*/
    if(END_FAIL==get_para_frm_arg(argv[2], &txpack_len))
        goto error;
    if(END_FAIL==get_para_frm_arg(argv[3], &txpack_num))
        goto error;

    i=(txpack_num)? txpack_num : 1;

    while(i) {
        cmd=(char *)&tx_cmd;
        NL_PUT32(&cmd, TEST_ATTR_PARA0, txpack_len);

        /*contact with driver*/
        if (0>TEST_CMD_SEND_REPLY(TEST_CMD_TX, tx_cmd))
            goto error;

        i=(txpack_num)? i-1 : 1;
    }
    return 0;
error:
    return END_FAIL;

}

static int test_cmd_rx(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_rx rx_cmd;
    char *cmd;
    unsigned int rxpack_len, rxpack_num;
    char usage_str[]="Usage: esp rx pack_len pack_num\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("pack_len: byte num in a packet frm tgt to host\n");
        printf("pack_num: packet num frm tgt to host\n");
        return END_OK;
    }

    if (argc<4 ) {
        printf("Incorrect usage, miss params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content*/
    if(END_FAIL==get_para_frm_arg(argv[2], &rxpack_len))
        goto error;
    if(END_FAIL==get_para_frm_arg(argv[3], &rxpack_num))
        goto error;

    cmd=(char *)&rx_cmd;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, rxpack_len);
    NL_PUT32(&cmd, TEST_ATTR_PARA1, rxpack_num);

    /*contact with driver*/
    if (0>TEST_CMD_SEND_REPLY(TEST_CMD_RX, rx_cmd))
        goto error;

    return 0;
error:
    return END_FAIL;
}


static int test_cmd_tx_cb(char * cmd)
{
    char reply_info[100];

    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;

    //printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_rx_cb(char * cmd)
{
    char reply_info[100];

    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;

    //printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_debug(u32 cmd_type, u32 para_num, char * para[])
{
    struct cmd_debug debug_cmd;
    char *cmd=(char *)&debug_cmd;
    unsigned int  para_value, i;


    /*fill  command content*/
    NL_PUT32(&cmd, TEST_ATTR_CMD_TYPE, cmd_type);
    NL_PUT32(&cmd, TEST_ATTR_PARA_NUM, para_num);
    for(i=0; i<para_num; i++) {
        if(END_FAIL==get_para_frm_arg(para[2+i], &para_value))
            goto error;
        NL_PUT32(&cmd, TEST_ATTR_PARA(i), para_value);
    }

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_DEBUG, debug_cmd);

error:
    return END_FAIL;
}

static int test_cmd_debug_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_rd(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rd addr\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("addr: reg addr in target\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss address!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(RD_REG, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_wr(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp wr addr value\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("addr: reg addr in target, value: data to write\n");
        return END_OK;
    }

    if (argc<4 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(WR_REG, 2, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_sense(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp sense mode \n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("mode: 0-normal, 1: dec 10dB, 2: dec 20dB\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(SET_SENSE, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_rxsense(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rxsense mode \n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("mode: 0x100, 0x101\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(SET_RXSENSE, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_trc(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp config rate control\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("mode: 0x100 disable rc, 0x101 enable\n");
        printf("mode: 0-0x1f, fix rate 0x20, release fix rate\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(CONFIGURE_TRC, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}



static int get_rate_value(char * rate_name)
{
    u32 rate_value;

    if(strcmp("1m", rate_name) == 0) {
        rate_value= PHY_RATE_1_LONG;
    } else if(strcmp("2ml", rate_name) == 0) {
        rate_value= PHY_RATE_2_LONG;
    } else if(strcmp("2ms", rate_name) == 0) {
        rate_value= PHY_RATE_2_SHORT;
    } else if(strcmp("5ml", rate_name) == 0) {
        rate_value= PHY_RATE_5_LONG;
    } else if(strcmp("5ms", rate_name) == 0) {
        rate_value= PHY_RATE_5_SHORT;
    } else if(strcmp("11ml", rate_name) == 0) {
        rate_value= PHY_RATE_11_LONG;
    } else if(strcmp("11ms", rate_name) == 0) {
        rate_value= PHY_RATE_11_SHORT;
    } else if(strcmp("6m", rate_name) == 0) {
        rate_value= PHY_RATE_6;
    } else if(strcmp("9m", rate_name) == 0) {
        rate_value= PHY_RATE_9;
    }  else if(strcmp("12m", rate_name) == 0) {
        rate_value= PHY_RATE_12;
    } else if(strcmp("18m", rate_name) == 0) {
        rate_value= PHY_RATE_18;
    } else if(strcmp("24m", rate_name) == 0) {
        rate_value= PHY_RATE_24;
    } else if(strcmp("36m", rate_name) == 0) {
        rate_value= PHY_RATE_36;
    } else if(strcmp("48m", rate_name) == 0) {
        rate_value= PHY_RATE_48;
    } else if(strcmp("54m", rate_name) == 0) {
        rate_value= PHY_RATE_54;
    } else if(strcmp("free", rate_name) == 0) {
        rate_value= PHY_RATE_MAX;
    } else {
        rate_value=PHY_RATE_ERROR;
    }
    return rate_value;
}
static int test_cmd_txrate(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp txrate rate_str \n";
    u32 rate_value;
    char rate_str[5];

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("rate_str: free,1m,2ml,2ms,5ml,5ms,11ml,11ms\n");
        printf("             6m,9m, 12m, 18m, 24m, 36m, 48m, 54m\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(PHY_RATE_ERROR==(rate_value=get_rate_value(argv[2]))) {
        printf("rate error!\n");
        goto error;
    }

    sprintf(rate_str, "%d", rate_value);
    strcpy(argv[2], rate_str);
    if(END_FAIL != test_cmd_debug(SET_TX_RATE, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_setfreq(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp setfreq chan_no\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("chan_no: 1~14\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(SET_TX_FREQ, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_tkipmicerr(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp micerr key_type\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("key_type: 0~ptk, 1:gtk\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(TKIP_MIC_ERROR, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_rifs(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rifs enable\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("enable: 0~dis, 1:en\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(RIFS_CTRL, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_backoff(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp backoff factor\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("factor: 0~dis, 1~8:multi_factor\n");
        return END_OK;
    }

    if (argc<3 ) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(BACKOFF, 1, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_rdper(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rdper\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc < 2) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(RDPER, 0, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_rdrssi(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rdrssi\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc < 2) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(RDRSSI, 0, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_dbgtrc(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp dbgtrc\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc < 2) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if(END_FAIL != test_cmd_debug(DBGTRC, 0, argv))
        return END_OK;

error:
    return END_FAIL;

}

static int test_cmd_wrmem(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp wrmem reg val\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc < 3) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if (END_FAIL != test_cmd_debug(WRMEM, 2, argv))
        return END_OK;

error:
    return END_FAIL;
}

static int test_cmd_rdmem(u32 cmd_id, int argc, char * argv[])
{
    char usage_str[]="Usage: esp rdmem reg\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        return END_OK;
    }

    if (argc < 2) {
        printf("Incorrect usage, miss para!\n");
        printf("%s",usage_str);
        goto error;
    }

    if (END_FAIL != test_cmd_debug(RDMEM, 1, argv))
        return END_OK;

error:
    return END_FAIL;
}

static int test_cmd_sdwr(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_sdwr sdwr_cmd;
    char *cmd=(char *)&sdwr_cmd;
    unsigned int para;
    char usage_str[]="Usage: esp sdwr fun_no addr data\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("fun_no:0,1 add: address of sdio reg, data: value write to reg\n");
        return END_OK;
    }

    if (argc<5 ) {
        printf("Incorrect usage, miss params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content, add and data*/
    if(END_FAIL==get_para_frm_arg(argv[2], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, para);

    if(END_FAIL==get_para_frm_arg(argv[3], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA1, para);

    if(END_FAIL==get_para_frm_arg(argv[4], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA2, para);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_SDIO_WR, sdwr_cmd);

error:
    return END_FAIL;

}

static int test_cmd_sdwr_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

static int test_cmd_sdrd(u32 cmd_id, int argc, char * argv[])
{
    struct cmd_sdrd sdrd_cmd;
    char *cmd=(char *)&sdrd_cmd;
    unsigned int para;
    char usage_str[]="Usage: esp sdrd func_no addr\n";

    if ((argc==3)&&IS_HELP_CMD(argv[2])) {
        print_cmd_list(cmd_id);
        printf("%s",usage_str);
        printf("fun_no:0,1 addr: sdio address\n");
        return END_OK;
    }

    if (argc<4 ) {
        printf("Incorrect usage, miss sdio params!\n");
        printf("%s",usage_str);
        goto error;
    }

    /*fill  command content*/
    if(END_FAIL==get_para_frm_arg(argv[2], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA0, para);

    if(END_FAIL==get_para_frm_arg(argv[3], &para))
        goto error;
    NL_PUT32(&cmd, TEST_ATTR_PARA1, para);

    /*contact with driver*/
    return TEST_CMD_SEND_REPLY(TEST_CMD_SDIO_RD, sdrd_cmd);

error:
    return END_FAIL;

}

static int test_cmd_sdrd_cb(char * cmd)
{
    char reply_info[100];
    /*get echo information*/
    if (0> nl_get(&cmd, TEST_ATTR_STR, &reply_info, 99))
        goto error;
    printf("%s\n",reply_info);
    return END_OK;
error:
    printf("fail to get reply!\n");
    return END_FAIL;
}

