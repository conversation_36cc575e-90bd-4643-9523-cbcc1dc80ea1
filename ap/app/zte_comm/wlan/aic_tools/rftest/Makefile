#*******************************************************************************
# include build/common makefile
include $(COMMON_MK)

EXEC = wifi_test
OBJS = wifi_test.o

#CFLAGS += -I$(zte_app_path)/include

#LDLIBS += -lpthread

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) -Wl,--end-group
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)

clean:
	-rm -f $(EXEC) *.elf *.gdb *.o



