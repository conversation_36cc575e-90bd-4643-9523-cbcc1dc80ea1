#*******************************************************************************
# include build/common makefile
include $(COMMON_MK)

EXEC = wifi_test
OBJS = wifi_test.o

EXEC1 = bt_test
OBJS1 = bt_test.o

#CFLAGS += -I$(zte_app_path)/include

LDLIBS += -lpthread

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC) $(EXEC1)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) -Wl,--end-group
	@cp $@ $@.elf
	
$(EXEC1): $(OBJS1)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) -Wl,--end-group
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)
	$(ROMFSINST) $(EXEC1) /bin/$(EXEC1)

clean:
	-rm -f $(EXEC) $(EXEC1) *.elf *.gdb *.o



