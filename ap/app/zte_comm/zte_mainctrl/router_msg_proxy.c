
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <sys/msg.h>
#include "message.h"
#include "netapi.h"
#include "softap_api.h"



int main(int argc, char* argv[])
{
	//char  wan_name[64] = {0};
	char  wan_opt[64] = {0};
	char  tmp[64] = {0};
	if (argc != 2 && argc != 3) {
		slog(NET_PRINT, SLOG_ERR, "ZTE_wanctl parameter num error ");
		return -1;
	}
	//strcpy(wan_name,argv[1]); //get wan name:pswan;usbwan;ethwan;wifiwan
	strncpy(wan_opt, argv[1], sizeof(wan_opt)-1); //v4 or v6
	if (argc == 3) {
		strncpy(tmp, argv[2], sizeof(tmp)-1);//klocwork
	}

	if (0 == strcmp(wan_opt, "ipv4"))
		ipc_send_message(MODULE_ID_MAIN_CTRL, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_WAN4_CHANGE, strlen(tmp), (UCHAR *)tmp, 0);
	else if (0 == strcmp(wan_opt, "ipv6"))
		ipc_send_message(MODULE_ID_MAIN_CTRL, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_WAN6_CHANGE, strlen(tmp), (UCHAR *)tmp, 0);
	else if (0 == strcmp(wan_opt, "del_timer"))
		ipc_send_message(MODULE_ID_MAIN_CTRL, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_TIMER_DEL, strlen(tmp), (UCHAR *)tmp, 0);

	return 0;

}




