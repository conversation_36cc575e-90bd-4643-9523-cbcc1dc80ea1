# /*****************************************************************************
#* 版权所有 (C)2015, 中兴通讯股份有限公司。
#* 
#* 文件名称:     Makefile
#* 文件标识:     Makefile
#* 内容摘要:     Makefile of ZTE applications
#* 使用方法:     void
#* 
#* 修改日期        版本号      修改标记        修改人          修改内容
#* -----------------------------------------------------------------------------
#* 2015/02/10      V1.0        Create          张楠          创建
#* 
# ******************************************************************************/

#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************
include $(COMMON_MK)
WORKPATH = $(zte_lib_path)
SOFT_TIMER_PATH = $(WORKPATH)/libsoft_timer

#*******************************************************************************
# execute
#*******************************************************************************
EXEC    = zte_watchdog

#*******************************************************************************
# objects
#*******************************************************************************
OBJS    = watchdog.o watchdog_adapter.o watchdog_battery.o
#*******************************************************************************
# include path
#*******************************************************************************                
INCLUE_PATH = -I. -I./../../include
CFLAGS += -Wextra -Wall $(INCLUE_PATH) $(CUSTOM_MACRO)
CFLAGS  += -I$(SOFT_TIMER_PATH)
CFLAGS  += -I$(SOFT_TIMER_PATH) \
		   -L$(SOFT_TIMER_PATH)
CFLAGS += -g
CFLAGS += -I$(WORKPATH)/libnvram
CFLAGS += -g -Werror=implicit-function-declaration
ifeq ($(LINUX_TYPE),uClinux)
CFLAGS += -g -O0
endif

#*******************************************************************************
# macro definition
#*******************************************************************************


#*******************************************************************************
# library
#*******************************************************************************
LDLIBS += -lpthread -lm -lrt 
CFLAGS += -I$(SOFT_TIMER_PATH)
CFLAGS += -I$(zte_app_path)/zte_comm/rtc-service
LDLIBS += -lnvram -lpthread -lsoft_timer -lsoftap
#LDFLAGS += -L../soft_timer -lsofttimer 
#*******************************************************************************
# library path
#*******************************************************************************
LDLIBS  += -L$(WORKPATH)/libnvram
LDLIBS  += -L$(WORKPATH)/libsoft_timer
LDLIBS  += -L$(zte_lib_path)/libsoftap
#*******************************************************************************
# targets
#*******************************************************************************
lib: $(OBJS)
	@echo Compiling zte_watchdog libraries.
	
clean:
	-rm -f $(OBJS)
	

	
