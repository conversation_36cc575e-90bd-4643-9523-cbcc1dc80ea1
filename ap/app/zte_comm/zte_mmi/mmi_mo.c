
/*******************************************************************************
    Copyright(c) 1998 - 2005 DaTang Mobile Communications Equipment CO.,LTD.
    All Rights Reserved. By using this module you agree to the terms of the
    DaTang Mobile Communications Equipment CO.,LTD License Agreement for it.
*******************************************************************************/

/*******************************************************************************
Module Name     : i18n
Version         : 0.1
Description     : This file contains the initialization codes.
Performance     : Code Size   :  Bytes
                  Data Size   :  Bytes
Change History  : Version   Date            Author          Comments
                  0.1       2005-03-01                      Original

*******************************************************************************/
#ifndef DISABLE_LCD
#include "fw_i18n.h"
/* -------------------------------------------------------------------------- */
extern struct msg_domain En_domain;
extern struct msg_domain Zh_domain;

struct msg_domain *language_domains[] = {
	&En_domain,
	&Zh_domain,
	NULL,
};
#endif
