/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#include "mmi_common.h"

/***********************************LCDͳһ����********************************/

/*��Ļģʽ����*/
E_zMmiShowMode g_showMode = 0;//led,1;lcd,2;lcd+lcd,3


/***********************************LEDͳһ����********************************/

T_zMmiSmsLedConfig g_mmi_smsled_config_tab[] = {
	{{0, SMS_RECVBOX_STATUS_FULL}, {LED_SMS, LED_STATE_BLINK, LED_STATE_SMS_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{0, SMS_RECVBOX_STATUS_NEW}, {LED_SMS, LED_STATE_BLINK, LED_STATE_SMS_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{0, SMS_RECVBOX_STATUS_UNREAD}, {LED_SMS, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{0, SMS_RECVBOX_STATUS_NOR}, {LED_SMS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},

};

T_zMmiVoipLedConfig g_mmi_voipled_config_tab[] = {
	{{VOIP_STATUS_IN_CALL}, {LED_VOIP, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{VOIP_STATUS_IN_CONNECTION}, {LED_VOIP, LED_STATE_BLINK, LED_STATE_VOIP_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{VOIP_STATUS_HANG_UP}, {LED_VOIP, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{VOIP_STATUS_NOR}, {LED_VOIP, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},

};

T_zMmiBatteryLedConfig g_mmi_batled_config_tab[] = {
#if defined(QRZL_UE) && defined(JCV_HW_MZ803_V3_2) && defined(JCV_HW_MZ901_V1_0) 
	//MZ901
	{{STATE_CHARGING, VOLT_25PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_1}},
	{{STATE_CHARGING, VOLT_50PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_2}},
	{{STATE_CHARGING, VOLT_75PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_3}},
	{{STATE_CHARGING, VOLT_NORMALLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_4}},
	{{STATE_CHARGING, VOLT_FULLLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_4}},

	{{STATE_FULL, 0, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_4}},
	{{STATE_DISCHARGE, VOLT_5PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_1}},
	{{STATE_DISCHARGE, VOLT_10PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_1}},
	{{STATE_DISCHARGE, VOLT_20PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_1}},
	{{STATE_DISCHARGE, VOLT_25PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_1}},
	{{STATE_DISCHARGE, VOLT_50PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_2}},
	{{STATE_DISCHARGE, VOLT_75PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_3}},
	{{STATE_DISCHARGE, VOLT_NORMALLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_4}},
	{{STATE_DISCHARGE, VOLT_FULLLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_4}},

#elif defined(JCV_HW_MZ803_V3_2) && !defined(JCV_HW_POWERBANK_DZ801)
	//MZ804 MZ803
	{{STATE_CHARGING, 0, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{{STATE_FULL, 0, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_5PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_10PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_BLINK, LED_STATE_BAT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_20PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_25PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_50PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_75PERCENTLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{{STATE_DISCHARGE, VOLT_NORMALLEVEL, 0, 0}, {LED_BATTERY, LED_STATE_ON, LED_STATE_BAT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
#endif
};

T_zMmiNetLedConfig g_mmi_netled_config_tab[] = {
	{CUSTOMER_SDK, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	//16M flash
	
#if defined(QRZL_APP_CUSTOMIZATION_MY) || defined(QRZL_NET_CONNECTED_ALWAYS_GREEN)
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#elif defined(QRZL_APP_CUSTOMIZATION_XY_LED)
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#elif defined(QRZL_NET_READCARDREDON_CONNECTEDGREENON_DISABLENETGREENBLINK)
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#elif defined(QRZL_CUSTOM_TIANMU_LOGIC)
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_CONNECTING, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_LIMIT_SPEED, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},

	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_CONNECTING, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_LIMIT_SPEED, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#else
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#if defined(JCV_HW_MZ803_V3_2) || defined(JCV_HW_UZ901_V1_4)
	{CUSTOMER_SDK_MIN, {NET_MODE_3G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_4G, NET_STATE_WITHOUT_BALANCE, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
#endif
#endif

	//16M flash, yaoyuan cpe
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_RED, TRAFFIC_LED_MAX}},
	//{CUSTOMER_YAOYUAN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	//{CUSTOMER_YAOYUAN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	//{CUSTOMER_YAOYUAN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_CPE_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	//GUODIAN led config
	{CUSTOMER_GUODIAN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_GUODIAN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	//NANDIAN led config
	{CUSTOMER_NANDIAN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_FAST_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_2G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_3G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_4G, 1, 0, 0, 0, NET_SOCKET_ACTIVE}, {LED_WAN, LED_STATE_BLINK, LED_STATE_WAN_SLOW_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_2G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_3G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_4G, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_NANDIAN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_WAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
};


#if 0
T_zMmiWifiLedConfig g_mmi_wifiled_config_tab[] = {
	{{TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_BLINK, LED_STATE_LAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{TRUE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{TRUE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{TRUE, 0, 0, WPS_ACTIVE_MAX}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{FALSE, 0, 0, WPS_ACTIVE_MAX}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
};
#endif

T_zMmiWifiLedConfig g_mmi_wifiled_config_tab[] = {
	{CUSTOMER_SDK, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_BLINK, LED_STATE_LAN_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},

	//16M flash
//#ifdef QRZL_APP_CUSTOMIZATION_MY
// #if 0
// 	{CUSTOMER_SDK_MIN, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
// 	{CUSTOMER_SDK_MIN, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
// 	{CUSTOMER_SDK_MIN, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_BLINK, LED_STATE_LAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
// 	{CUSTOMER_SDK_MIN, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_BLINK, LED_STATE_LAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
// #else
#ifdef QRZL_WIFI_CONNECTED_ALWAYS_GREEN
	{CUSTOMER_SDK_MIN, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
#elif QRZL_WIFI_LED_NOT_LIGHT
	{CUSTOMER_SDK_MIN, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_OFF, LED_STATE_LAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
#else
	{CUSTOMER_SDK_MIN, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_LAN, LED_STATE_BLINK, LED_STATE_LAN_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_LAN, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK_MIN, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_LAN, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_GREEN, TRAFFIC_LED_MAX}},
#endif
};

//wps�� ҢԶcpe
T_zMmiWifiLedConfig g_mmi_wpsled_config_tab[] = {
	{CUSTOMER_SDK, {TRUE, TRUE, 0, 0, WPS_ACTIVING}, {LED_WPS, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {TRUE, FALSE, 0, 0, WPS_ACTIVED}, {LED_WPS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, TRUE, 0, 0, WPS_DEACTIVED}, {LED_WPS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, FALSE, 0, 0, WPS_DEACTIVING}, {LED_WPS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, FALSE, 0, 0, WPS_FAIL}, {LED_WPS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_SDK, {FALSE, FALSE, 0, 0, WPS_ACTIVE_MAX}, {LED_WPS, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
};

//5�ŵ� ����TRAFFIC_LED  ҢԶcpe
T_zMmiNetLedConfig g_mmi_signalled_config_tab[] = {
	//16M flash, yaoyuan cpe
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 1, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_1}},
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 2, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_2}},
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 3, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_3}},
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 4, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_4}},
	{CUSTOMER_YAOYUAN, {NET_MODE_2G, 0, 5, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_5}},

	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 1, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_1}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 2, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_2}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 3, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_3}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 4, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_4}},
	{CUSTOMER_YAOYUAN, {NET_MODE_3G, 0, 5, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_5}},

	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 1, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_1}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 2, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_2}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 3, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_3}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 4, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_4}},
	{CUSTOMER_YAOYUAN, {NET_MODE_4G, 0, 5, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_5}},

	{CUSTOMER_YAOYUAN, {NET_MODE_NOSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_LIMITSERVICE, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{CUSTOMER_YAOYUAN, {NET_MODE_NOTREADY, 0, 0, 0, 0, NET_SOCKET_INACTIVE}, {LED_SIGNAL, LED_STATE_BLINK, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
};

//rj11�� ҢԶcpe
T_zMmiRj11LedConfig g_mmi_rj11led_config_tab[] = {
	{{RJ11_STATUS_IN}, {LED_RJ11, LED_STATE_ON, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{RJ11_STATUS_OUT}, {LED_RJ11, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
	{{RJ11_STATUS_NOR}, {LED_RJ11, LED_STATE_OFF, LED_STATE_DEFAULT_BLINK, {0}, LED_COLOR_BLUE, TRAFFIC_LED_MAX}},
};

unsigned int mmi_get_config_tab_size(MMI_LED_NAME led)
{
	if (led == LED_BATTERY) {
		return sizeof(g_mmi_batled_config_tab);
	} else if (led == LED_LAN) {
		return sizeof(g_mmi_wifiled_config_tab);
	} else if (led == LED_WAN) {
		return sizeof(g_mmi_netled_config_tab);
	} else if (led == LED_SMS) {
		return sizeof(g_mmi_smsled_config_tab);
	} else if (led == LED_VOIP) {
		return sizeof(g_mmi_voipled_config_tab);
	} else if (led == LED_SIGNAL) {
		return sizeof(g_mmi_signalled_config_tab);
	} else if (led == LED_WPS) {
		return sizeof(g_mmi_wpsled_config_tab);
	} else if (led == LED_RJ11) {
		return sizeof(g_mmi_rj11led_config_tab);
	}
	return 0;
}
