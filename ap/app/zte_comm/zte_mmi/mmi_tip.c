/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd_tip.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��MMI��ȡ��ʾ��Ϣ
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#include <limits.h>
#include "mmi_common.h"

static BOOL gMultiSSIDSwitchFlag = FALSE;//���webui�Ƿ������˶�SSID

static SINT32 s_mmi_ssid_show_timer_status = 0;//ssid������ʾʱ�õ��Ķ�ʱ��״̬���
E_zMmi_Sim_Tip s_mmi_sim_tip = SIM_MAX;//sim��״̬
static E_zMmi_Sim_Tip s_mmi_sim_tipEx = SIM_MAX;//ÿs��ѯʱ������sim����һ�ε�״̬
static E_zMmi_NetCon_Tip s_mmi_netcon_tip = NET_MAX;//��������װ��
//static E_zMmi_WpsAct_Tip s_mmi_wpsact_tip = WPS_ACTIVE_MAX;
static CHAR s_mmi_net_pro [64] = {0};//����������Ӫ����Ϣ
static CHAR s_mmi_ssid [36] = {"--"};//������ssid
static CHAR s_mmi_wifikey [64] = {"--"};//������wifikey
static CHAR s_mmi_ssid2 [36] = {"--"};//���渱ssid
static CHAR s_mmi_wifikey2 [64] = {"--"};//���渱wifikey
static CHAR s_mmi_msisdn[16] = {"--"};//�Ƿ�ɾ��?
static CHAR s_mmi_pci[8] = {"__"};
static CHAR s_mmi_rapr_dBm[16] = {"--"};
static CHAR s_mmi_sinr_dB[12] = {"--"};
static CHAR s_mmi_acess_duration[16] = {"--"};
static CHAR s_mmi_current_uasge[8] = {"--"};
static CHAR s_mmi_update_result[16] = {0};
static SINT32 s_mmi_update_tip = 0;
static SINT32 s_mmi_update_tipEx = 0;
extern E_zMmiShowMode g_showMode;
//static SINT32 i_mmi_check_fota_result_times = 120;
//static SINT32 i_mmi_show_fota_result_times = 10;
//static BOOL b_mmi_show_fota_result = FALSE;

/**********************************************************************************
��������:��ȡSIM��״̬��Ϣ
***********************************************************************************/
E_zMmi_Sim_Tip mmi_getSIMStateInfo(VOID)
{
	return s_mmi_sim_tip;
}

E_zMmi_NetCon_Tip mmi_getNetConInfo(VOID)
{
	return s_mmi_netcon_tip;
}


/**********************************************************************************
��������:��ȡ��ʾ��Ϣ
***********************************************************************************/
SINT32 mmi_get_tipinfo(UINT32 tipinfo)
{
	if (tipinfo != 0) {
		T_zMMITipInfo * pTipInfo = (T_zMMITipInfo *)tipinfo;
		pTipInfo->sim_tip = s_mmi_sim_tip;
		pTipInfo->net_tip = s_mmi_netcon_tip;
		pTipInfo->net_pro = s_mmi_net_pro;
		//pTipInfo->update_tip = s_mmi_update_tip;
		//pTipInfo->update_result = s_mmi_update_result;
	}
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:ע����ʾģ��
***********************************************************************************/
SINT32 mmi_RegisterTipTaskInfoItem()
{
	T_zMMITaskInfoItem tipInfoItem = {0};
	tipInfoItem.task = MMI_TASK_TIP;
	tipInfoItem.taskinfo = (VOID *)malloc(sizeof(T_zMMITipInfo));
	tipInfoItem.get_taskinfo_fun = mmi_get_tipinfo;
#ifndef DISABLE_LCD
	tipInfoItem.get_lcdinfo_fun = mmi_getLcdTipInfo;
#endif
	//tipInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&tipInfoItem);
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:ע����ʾģ��
***********************************************************************************/
SINT32 mmi_RegisterTipNetConnectTaskInfoItem()
{
	T_zMMITaskInfoItem tipInfoItem = {0};
	tipInfoItem.task = MMI_TASK_TIP_NET_CONNECT;
	tipInfoItem.taskinfo = (VOID *)malloc(sizeof(T_zMMITipInfo));
	tipInfoItem.get_taskinfo_fun = mmi_get_tipinfo;
#ifndef DISABLE_LCD
	tipInfoItem.get_lcdinfo_fun = mmi_getLcdTipNetConnInfo;
#endif
	//tipInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&tipInfoItem);
	return MMI_SUCCESS;
}
#if 0
/**********************************************************************************
��������:ע��FOTA ������ʾģ��
***********************************************************************************/
SINT32 mmi_RegisterTipFotaTaskInfoItem()
{
	T_zMMITaskInfoItem tipInfoItem = {0};
	tipInfoItem.flag = MMI_TASK_TIP_FOTA;
	tipInfoItem.taskinfo = malloc(sizeof(T_zMMITipInfo));
	tipInfoItem.get_taskinfo_fun = mmi_get_tipinfo;
	tipInfoItem.get_lcdinfo_fun = mmi_getLcdTipFotaInfo;
	tipInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&tipInfoItem);
	return MMI_SUCCESS;
}
#endif
/**********************************************************************************
��������:ע��/��ȡSSID/WIFIKEY
***********************************************************************************/
SINT32 mmi_get_ssidinfo(UINT32 ssidinfo)
{
	if (ssidinfo != 0) {
		T_zMMISSIDInfo * pSSIDInfo = (T_zMMISSIDInfo *)ssidinfo;
		pSSIDInfo->ssid = s_mmi_ssid;
		pSSIDInfo->wifi_key = s_mmi_wifikey;
		pSSIDInfo->ssid2 = s_mmi_ssid2;
		pSSIDInfo->wifi_key2 = s_mmi_wifikey2;
	}
	return MMI_SUCCESS;
}
SINT32 mmi_RegisterSSIDInfoTaskInfoItem()
{
	T_zMMITaskInfoItem ssidInfoItem = {0};
	ssidInfoItem.task = MMI_TASK_SSID;
	ssidInfoItem.taskinfo = (VOID *)malloc(sizeof(T_zMMISSIDInfo));
	ssidInfoItem.get_taskinfo_fun = mmi_get_ssidinfo;
#ifndef DISABLE_LCD
	ssidInfoItem.get_lcdinfo_fun = mmi_getLcdSSIDKeyInfo;
#endif
	//ssidInfoItem.show_mode = 2;
	mmi_register_taskinfo_item(&ssidInfoItem);
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:��ȡ�����ź���Ϣ
***********************************************************************************/
SINT32 mmi_get_netsignalinfo(UINT32 netsiginfo)
{
	if (netsiginfo != 0) {
		T_zMMINetSignalInfo * pNetSigInfo = (T_zMMINetSignalInfo *)netsiginfo;
		pNetSigInfo->msisdn = s_mmi_msisdn;
		pNetSigInfo->pci = s_mmi_pci;
		pNetSigInfo->rapr_dBm = s_mmi_rapr_dBm;
		pNetSigInfo->sinr_dB = s_mmi_sinr_dB;
	}
	return MMI_SUCCESS;
}


/**********************************************************************************
��������:��ȡ��Ӫ������
***********************************************************************************/
static VOID mmi_get_network_provider(VOID)
{
	char nv_net_pro [64] = {0};
	cfg_get_item(NV_NETWORK_PROVIDER, nv_net_pro, 64);
	if (strlen(nv_net_pro) == 0) {
		strcpy(s_mmi_net_pro, "UnKnown");
	} else {
		if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CMCC)) {
			strcpy(s_mmi_net_pro, "CMCC");
		} else if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CUCC)) {
			strcpy(s_mmi_net_pro, "CUCC");
		} else if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CTCC)) {
			strcpy(s_mmi_net_pro, "CTCC");
		} else if (!strcasecmp(nv_net_pro, NV_NET_BOLT_4G_1) || !strcasecmp(nv_net_pro, NV_NET_BOLT_4G_2) || !strcasecmp(nv_net_pro, NV_NET_BOLT_4G_3) || !strcasecmp(nv_net_pro, NV_NET_BOLT_4G_4) || !strcasecmp(nv_net_pro, NV_NET_BOLT_4G_5)) {
			strcpy(s_mmi_net_pro, "BOLT! SUPER 4G");
		} else {
			strcpy(s_mmi_net_pro, nv_net_pro);
		}
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_get_network_provider s_mmi_net_pro_tip = %s!!\n", s_mmi_net_pro);
}

VOID mmi_update_netprovider(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_update_netprovider !!!\n");
	mmi_get_network_provider();
	mmi_set_update_flag(MMI_TASK_TIP);
}

VOID mmi_update_net_connect_tip(SINT32 con_state, E_zMmi_Net_Mode net_mode)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_update_net_connect_tip con_state = %d, net_mod = %d !!\n", con_state, net_mode);
	if (net_mode == NET_MODE_NOSERVICE) {
		s_mmi_netcon_tip = NET_NOSERVICE;
		mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
	} else if (net_mode == NET_MODE_LIMITSERVICE) {
		s_mmi_netcon_tip = NET_LIMITSERVICE;
		mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
	} else {
		s_mmi_netcon_tip = (E_zMmi_NetCon_Tip)con_state;
		if (s_mmi_netcon_tip != NET_DISCONNECTED && s_mmi_netcon_tip != NET_CONNECTED) {
			mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
		}
	}
}

VOID mmi_update_net_tip(SINT32 con_state, E_zMmi_Net_Mode net_mode)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_update_net_tip !!!\n");
	mmi_update_net_connect_tip(con_state, net_mode);
	mmi_update_netprovider();
}
SINT32 zMMI_Handle_Msg_Get_Provider(VOID *data)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI svr_Handle_Msg_Get_Provider !!!\n");
	mmi_update_netprovider();
	return 0;
}

/**********************************************************************************
��������:�����ص������з����Լ�����Ϣ
***********************************************************************************/
VOID zMMI_Handle_Msg_Get_Connecting_Tip(VOID *arg)
{
	SINT32 tip_conntent = 0;
	tip_conntent  = *(SINT32*)arg;
	s_mmi_netcon_tip = (E_zMmi_NetCon_Tip)tip_conntent;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI zMMI_Handle_Msg_Get_Connecting_Tip s_mmi_netcon_tip = %d!!!\n", s_mmi_netcon_tip);

	if (s_mmi_netcon_tip != NET_DISCONNECTED && s_mmi_netcon_tip != NET_CONNECTED) {
		mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
	} else {
		if (s_mmi_netcon_tip == NET_CONNECTED) {
			mmi_get_network_provider();
		}
		mmi_set_update_flag(MMI_TASK_TIP);
	}
}

/**********************************************************************************
��������:SIM��״̬��ⶨʱ���ص�
***********************************************************************************/
static VOID mmi_check_sim_state(VOID)
{
	char nv_sim_state[32] = {0};
	static BOOL run_once = FALSE;
	cfg_get_item(NV_MODEM_MAIN_STATE, nv_sim_state, 32);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_check_sim_state nv_sim_state = %s,s_mmi_sim_tipEx = %d !!\n", nv_sim_state, s_mmi_sim_tipEx);
	if (!strcmp(nv_sim_state, NV_SIM_STATE_PUK_WATIT)) {
		s_mmi_sim_tip = PUK_LOCK;
	} else if (!strcmp(nv_sim_state, NV_SIM_STATE_PIN_WATIT)) {
		s_mmi_sim_tip = PIN_LOCK;
	} else if (!strcmp(nv_sim_state, NV_SIM_STATE_UNDETECTED)) {
		s_mmi_sim_tip = INSERT_SIM;
	} else if (!strcmp(nv_sim_state, NV_SIM_STATE_NCK_WAIT)) {
		s_mmi_sim_tip = SIM_LOCK;
	} else if (!strcmp(nv_sim_state, NV_SIM_STATE_DESTROY)) {
		s_mmi_sim_tip = INVALID_SIM;
	} else {
		s_mmi_sim_tip = SIM_MAX;
	}

	if ((!strcmp(nv_sim_state, NV_SIM_STATE_INIT_COMPLETE) || !strcmp(nv_sim_state, NV_SIM_STATE_INIT_EXCPTION))) {
		if (!run_once) {
			s_mmi_netcon_tip = NET_SEARCHING;
			mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
			run_once = TRUE;
		}
	}
	if (s_mmi_sim_tip != s_mmi_sim_tipEx) {
		if (s_mmi_sim_tip == SIM_MAX) {
			mmi_set_update_flag(MMI_TASK_TIP_NET_CONNECT);
		} else if (s_mmi_sim_tip == INSERT_SIM) {
			mmi_clean_net_state();
		}
		mmi_set_update_flag(MMI_TASK_TIP);
		s_mmi_sim_tipEx = s_mmi_sim_tip;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_check_sim_state s_mmi_sim_tip = %d,s_mmi_sim_tipEx = %d !!\n", s_mmi_sim_tip, s_mmi_sim_tipEx);
}

#if 0
static VOID mmi_check_lte_sinr(VOID)
{
	CHAR sinr_nv[12] = {0};
	cfg_get_item(NV_SINR, sinr_nv, 12);
	if (sinr_nv != NULL && strlen(sinr_nv) > 0) {
		memset(s_mmi_sinr_dB, 0, sizeof(s_mmi_sinr_dB));
		snprintf(s_mmi_sinr_dB, 9, "%sdB", sinr_nv);
	} else {
		strcpy(s_mmi_sinr_dB, INVALID_STR);
	}
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI mmi_check_lte_sinr s_mmi_sinr_dB  %s!!!\n",s_mmi_sinr_dB);
	mmi_set_update_flag(MMI_CONTENT_FLAG_SIGNAL_INFO);
}
static VOID mmi_check_lte_pci(VOID)
{
	CHAR pci_nv[8] = {0};
	cfg_get_item(NV_PCI, pci_nv, 8);
	if (pci_nv != NULL) {
		strncpy(s_mmi_pci, pci_nv, 8);
	} else {
		strcpy(s_mmi_pci, INVALID_STR);
	}
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI mmi_check_lte_pci s_mmi_pci  %s!!!\n",s_mmi_pci);
	mmi_set_update_flag(MMI_CONTENT_FLAG_SIGNAL_INFO);
}
#endif
/**********************************************************************************
��������:��ⶨʱ���ص�
***********************************************************************************/
static VOID * mmi_tip_check_timer_cb(VOID *arg)
{
	ipc_send_message(MODULE_ID_MMI, MODULE_ID_MMI, (USHORT)MSG_CMD_MMICHECK_TIP_INFO, 0, NULL, 0);
	return NULL;
}

SINT32 zMMI_Handle_Msg_Check_Tip_Info(VOID *data)
{
	mmi_check_sim_state();
	mmi_check_new_version_state();
	//mmi_check_lte_sinr();
	//mmi_check_lte_pci();
	mmi_check_fota_upgrade_result();
	mmi_check_fota_download_state();
	return 0;
}

/**********************************************************************************
��������:SIM��״̬��ⶨʱ�� 1S
***********************************************************************************/
static VOID mmi_tip_check_timer_create(VOID)
{
	int ret = -1;
	ret = CreateSoftTimer(SET_TIP_CHECK_TIMER, TIMER_FLAG_RESTART, SET_TIP_CHECK_TIME, &mmi_tip_check_timer_cb, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_tip_check_timer_create FAILED !!");
	}
}

static VOID mmi_tip_check_timer_stop(VOID)
{
	DeleteSoftTimer(SET_TIP_CHECK_TIMER);
}

/**********************************************************************************
��������:��ȡ��WIFIKEY
***********************************************************************************/
static VOID mmi_get_multi_wifikey(VOID)
{
	char nv_multi_authmode[16] = {0};
	char nv_multi_encryp_type[16] = {0};
	char nv_multi_key[2] = {0};
	SINT32 key_multi_index = -1;
	char m_keystr[16] = {0};
	cfg_get_item("m_AuthMode", nv_multi_authmode, sizeof(nv_multi_authmode));
	cfg_get_item("m_EncrypType", nv_multi_encryp_type, sizeof(nv_multi_encryp_type));

	if (((!strcmp("WEP", nv_multi_encryp_type)) && (!strcmp("OPEN", nv_multi_authmode))) || (!strcmp("SHARED", nv_multi_authmode)) || (!strcmp("WEPAUTO", nv_multi_authmode))) { //WEP
		cfg_get_item("m_DefaultKeyID", nv_multi_key, sizeof(nv_multi_key));
		key_multi_index = atoi(nv_multi_key); //kw 3
		if(key_multi_index < 0 || key_multi_index > INT_MAX-1) {// 0~3
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_get_multi_wifikey %d!!\n", key_multi_index);
			key_multi_index = 0;
		}
		key_multi_index = key_multi_index + 1;
		snprintf(m_keystr, sizeof(m_keystr), "m_Key%dStr1", key_multi_index);//klocwork
		cfg_get_item(m_keystr, s_mmi_wifikey2, sizeof(s_mmi_wifikey2));
	} else if (!strcmp("NONE", nv_multi_encryp_type) && !strcmp("OPEN", nv_multi_authmode)) {
		strcpy(s_mmi_wifikey2, "");
	} else {
		cfg_get_item(NV_WIFIKEY2, s_mmi_wifikey2, sizeof(s_mmi_wifikey2));
	}
}


/**********************************************************************************
��������:��ȡ��WIFIKEY
***********************************************************************************/
static VOID mmi_get_main_wifikey(VOID)
{
	char nv_authmode[16] = {0};
	char nv_encryp_type[16] = {0};
	char nv_key[2] = {0};
	SINT32 key_index = -1;
	char keystr[16] = {0};
	cfg_get_item("AuthMode", nv_authmode, sizeof(nv_authmode));
	cfg_get_item("EncrypType", nv_encryp_type, sizeof(nv_encryp_type));

	if (((!strcmp("WEP", nv_encryp_type)) && (!strcmp("OPEN", nv_authmode))) || (!strcmp("SHARED", nv_authmode)) || (!strcmp("WEPAUTO", nv_authmode))) { //WEP
		cfg_get_item("DefaultKeyID", nv_key, sizeof(nv_key));
		key_index = atoi(nv_key); //kw 3
		if(key_index < 0 || key_index > INT_MAX-1) {// 0~3
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_get_main_wifikey %d!!\n", key_index);
			key_index = 0;
		}
		key_index = key_index + 1;
		snprintf(keystr, sizeof(keystr), "Key%dStr1", key_index);//klocwork
		cfg_get_item(keystr, s_mmi_wifikey, sizeof(s_mmi_wifikey));
	} else if (!strcmp("NONE", nv_encryp_type) && !strcmp("OPEN", nv_authmode)) {
		strcpy(s_mmi_wifikey, "");
	} else {
		cfg_get_item(NV_WPAPSK1, s_mmi_wifikey, sizeof(s_mmi_wifikey));
	}
}

/**********************************************************************************
��������:��ȡSSID
***********************************************************************************/
static VOID mmi_get_ssid_wifikey(VOID)
{
	char nv_multi_ssid_switch[2] = {0};
	char nv_ssid_write_flag[2] = {0};
	cfg_get_item(NV_SSID_WRITE_FLAG, nv_ssid_write_flag, sizeof(nv_ssid_write_flag));
	if (!strcmp(nv_ssid_write_flag, "1")) {
		cfg_get_item(NV_SSID1, s_mmi_ssid, sizeof(s_mmi_ssid));
		mmi_get_main_wifikey();
	} else {
		strcpy(s_mmi_ssid, "--");
		strcpy(s_mmi_wifikey, "--");
	}
	cfg_get_item(NV_MULTI_SSID_SWITCH, nv_multi_ssid_switch, sizeof(nv_multi_ssid_switch));
	if (!strcmp(nv_multi_ssid_switch, "1")) {
		gMultiSSIDSwitchFlag = TRUE;
		cfg_get_item(NV_SSID2, s_mmi_ssid2, sizeof(s_mmi_ssid2));
		mmi_get_multi_wifikey();
	} else {
		gMultiSSIDSwitchFlag = FALSE;
	}

	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_get_ssid_wifikey s_mmi_ssid = %s s_mmi_wifikey= %s s_multi_mmi_ssid = %s s_multi_mmi_wifikey= %s!!\n",
	     s_mmi_ssid, s_mmi_wifikey, s_mmi_ssid2, s_mmi_wifikey2);
}

BOOL mmi_get_multi_ssid_switch_flag(VOID)
{
	return gMultiSSIDSwitchFlag;
}

SINT32 zMMI_Handle_Msg_Get_SSID_Key(VOID *data)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI zMMI_Handle_Msg_Get_SSID_Key !!\n");
	mmi_get_ssid_wifikey();
#ifndef DISABLE_LCD
	if (!mmi_get_multi_ssid_switch_flag()) {
#ifndef QRZL_CUSTOMER_XIANJI
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
#endif
	}
#endif
	mmi_set_update_flag(MMI_TASK_SSID);
	return 0;
}

/**********************************************************************************
��������:SSID&WIFIKEY��ʾʱ�䶨ʱ����ʱ��20�룬һ���Զ�ʱ��
***********************************************************************************/
static VOID * mmi_ssid_show_timer_cb(VOID *arg)
{
	ipc_send_message(MODULE_ID_MMI, MODULE_ID_MMI, (USHORT)MSG_CMD_MMISHOW_SSID_INFO, 0, NULL, 0);
	return NULL;
}

SINT32 zMMI_Handle_Msg_SSID_Show_Info(VOID *data)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_ssid_show_timer_cb !!\n");
	s_mmi_ssid_show_timer_status = 0;
#if defined(QRZL_UE) && defined(JCV_HW_MZ801_V1_2)
	mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
	mmi_set_update_flag(MMI_TASK_SSID);
#else
#ifndef DISABLE_LCD
	mmi_set_lcd_page_index(MMI_SHOW_PAGE_FIRST);
#endif
	mmi_set_update_flag(MMI_TASK_TRAFFIC);
#endif
	return 0;
}

BOOL mmi_getSSIDFirstShowInfo(VOID)
{
	return s_mmi_ssid_show_timer_status ;
}

static VOID mmi_ssid_show_timer_create(VOID)
{
	if (s_mmi_ssid_show_timer_status == 0) {
		int ret = -1;
		ret = CreateSoftTimer(SET_SSID_SHOW_TIMER, TIMER_FLAG_ONCE, SET_SSID_SHOW_TIME, &mmi_ssid_show_timer_cb, NULL);
		if (ret != 0) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_ssid_show_timer_create FAILED !!");
		}
		s_mmi_ssid_show_timer_status = 1;
	}
}
VOID mmi_ssid_show_timer_stop(VOID)
{
	if (s_mmi_ssid_show_timer_status == 1) {
		DeleteSoftTimer(SET_SSID_SHOW_TIMER);
		s_mmi_ssid_show_timer_status = 0;
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_ssid_show_timer_stop !!\n");
	}
}
static VOID mmi_show_ssid_poweron()
{
#if defined(QRZL_UE) && defined(JCV_HW_MZ801_V1_2)
	mmi_get_ssid_wifikey();
	//mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
	mmi_set_update_flag(MMI_TASK_SSID);

	// 先显示NET 比如SIM CONNECTING 再等20s 跳PAGE2  显示SSID --- 暂时没启用
	//mmi_ssid_show_timer_create();
	// char *nv_task_tab[NV_CONTENT_LEN] = {0};

	// cfg_get_item("mmi_task_tab", nv_task_tab, sizeof(nv_task_tab));
	// if (strstr(nv_task_tab, "traffic_task")) {
	// 	mmi_ssid_show_timer_create();
	// }
#else
	mmi_get_ssid_wifikey();
	mmi_set_update_flag(MMI_TASK_SSID);
#ifndef DISABLE_LCD
	mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
#endif

	mmi_ssid_show_timer_create();
#endif
}


/**********************************************************************************
��������:MMI SIM��ⶨʱ��KILL��  �ػ�������ʱ����
***********************************************************************************/
VOID mmi_kill_tip_timer(VOID)
{
	if (g_showMode == MMI_MODE_LED || g_showMode == MMI_MODE_ALL) {
		mmi_tip_check_timer_stop();
	}
}

/**********************************************************************************
��������:��ʼ����ʾģ��
***********************************************************************************/
VOID mmi_init_lcd_tip(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_init_lcd_tip !!!\n");
	mmi_check_sim_state();
	mmi_show_ssid_poweron();
	mmi_get_network_provider();
	mmi_tip_check_timer_create();
}
