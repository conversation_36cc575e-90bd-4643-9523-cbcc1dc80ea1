/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmiledadapter.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��LED����
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
******************************************************************************/

/*****************************************************************************
 ͷ�ļ�
******************************************************************************/
#include "mmi_common.h"

#define itoa(i,a,b) (((b) == 16) ? sprintf((a), "%x", (i)) : sprintf((a), "%d", (i)))

MMI_LED_LASTSTATE g_mmi_wanled_state = LED_ALL_OFF;//�������Ƶĵ�ǰ״̬
MMI_LED_BLINK_SPEED g_mmi_wanled_speed = LED_STATE_DEFAULT_BLINK;//�������Ƶ���˸Ƶ��
MMI_LED_LASTSTATE g_mmi_lanled_state = LED_ALL_OFF;//���wifi�Ƶĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_batteryled_state = LED_ALL_OFF;//��ǵ�صƵĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_smsled_state = LED_ALL_OFF;//��Ƕ��ŵƵĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_voipled_state = LED_ALL_OFF;//��������Ƶĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_signalled_state = LED_ALL_OFF;//���ҢԶcpe�����źŵƵĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_wpsled_state = LED_ALL_OFF;//���ҢԶcpe wps�Ƶĵ�ǰ״̬
MMI_LED_LASTSTATE g_mmi_rj11led_state = LED_ALL_OFF;//���ҢԶcpe rj11�Ƶĵ�ǰ״̬


/**********************************************************************************
��������:д�ļ���������
***********************************************************************************/
SINT32 mmi_file_operate(char *filepath, char *buf)
{
#if 1
	SINT32 ret_fd = 0;
	SINT32 len = 0;

	ret_fd = open(filepath, O_RDWR);
	if (ret_fd == -1) {
		slog(MMI_PRINT, SLOG_ERR,"ZTE_MMI mmi_file_operate buf [%s] open file fail: %s!\n", buf, filepath);
		return MMI_ERROR;
	}

	len = strlen(buf);
	if (write(ret_fd, buf, len) != len) {
		slog(MMI_PRINT, SLOG_ERR,"ZTE_MMI mmi_file_operate buf [%s] write file fail: %s!\n", buf, filepath);
		close((int)ret_fd);
		return MMI_ERROR;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_file_operate write [%s] file: %s success!!\n", buf, filepath);
	close((int)ret_fd);
#endif
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:����
***********************************************************************************/
static SINT32 mmi_led_opt_on(char *filepath)
{
	return mmi_file_operate(filepath, "1");
}


/**********************************************************************************
��������:����
***********************************************************************************/
static SINT32 mmi_led_opt_off(char *filebrightness)
{
	return mmi_file_operate(filebrightness, "0");
}

/**********************************************************************************
��������:��˸
***********************************************************************************/
static SINT32 mmi_led_opt_blinkon(T_zMmi_LedBlink_Info *info)
{
	char buf1[12] = {0};
	char buf2[12] = {0};
	SINT32 ret1, ret2, ret3 = 0;

	ret1 = mmi_file_operate(info->fileblinkSwitch, LED_BLINKON_STATE);


	itoa((int)(info->timeon), buf1, 10);
	ret2 = mmi_file_operate(info->fileblinktimeon, buf1);

	itoa((int)(info->timoff), buf2, 10);
	ret3 = mmi_file_operate(info->fileblinktimeoff, buf2);
	if (ret1 == MMI_ERROR || ret2 == MMI_ERROR || ret3 == MMI_ERROR) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI led_setFastBlinkOn fail ret1 = %d ret2 = %d ret3 = %d!\n", ret1, ret2, ret3);
		return MMI_ERROR;
	}
	return MMI_SUCCESS;

}

/**********************************************************************************
��������:��˸��
***********************************************************************************/
static SINT32 mmi_led_opt_blinkoff(char *fileblinkSwitch)
{
	return mmi_file_operate(fileblinkSwitch, LED_BLINKOFF_STATE);
}

/**********************************************************************************
��������:����/��ȡ�Ƶ��ϴ�״̬
***********************************************************************************/
static VOID mmi_setBatLedState(MMI_LED_LASTSTATE bat_state)
{
	g_mmi_batteryled_state = bat_state;
}

static VOID mmi_setWanLedState(MMI_LED_LASTSTATE wan_state)
{
	g_mmi_wanled_state = wan_state;
}

static VOID mmi_setWanLedSpeed(MMI_LED_BLINK_SPEED wan_speed)
{
	g_mmi_wanled_speed = wan_speed;
}

static VOID mmi_setLanLedState(MMI_LED_LASTSTATE lan_state)
{
	g_mmi_lanled_state = lan_state;
}
static VOID mmi_setSmsLedState(MMI_LED_LASTSTATE sms_state)
{
	g_mmi_smsled_state = sms_state;
}
static VOID mmi_setVoipLedState(MMI_LED_LASTSTATE voip_state)
{
	g_mmi_voipled_state = voip_state;
}

static MMI_LED_LASTSTATE mmi_getBatLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_batteryled_state;
	return state;
}

static MMI_LED_LASTSTATE mmi_getWanLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_wanled_state;
	return state;
}
static MMI_LED_BLINK_SPEED mmi_getWanLedSpeed(VOID)
{
	MMI_LED_BLINK_SPEED speed;
	speed = g_mmi_wanled_speed;
	return speed;
}

static MMI_LED_LASTSTATE mmi_getLanLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_lanled_state;
	return state;
}

static MMI_LED_LASTSTATE mmi_getSmsLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_smsled_state;
	return state;
}

static MMI_LED_LASTSTATE mmi_getVoipLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_voipled_state;
	return state;
}

/********************************************************************************
��صƲ���
**********************************************************************************/
VOID  mmi_BatLedOffOpt()
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedOffOpt !\n");
	mmi_led_opt_blinkoff(LED_BATTERY_GREEN_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_RED_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_RED_BRIGHTNESS);
	mmi_setBatLedState(LED_ALL_OFF);
}

#ifdef QRZL_UE
VOID  mmi_OffKernelLedOpt(int charger_mode)
{
	if(!charger_mode) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI salvikie normal mode close boot or kernel led\n");
#if defined(JCV_HW_POWERBANK_DZ801) && defined(QRZL_APP_CUSTOMIZATION_HMM)
		mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_WAN_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_BLUE_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);

		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI salvikie start poweron_animation\n");
		T_zMmi_LedBlink_Info info = {0};
		info.fileblinkSwitch = LED_WAN_RED_BLINKSWITCH;
		info.fileblinktimeoff = LED_WAN_RED_BLINKTIMEOFF;
		info.fileblinktimeon = LED_WAN_RED_BLINKTIMEON;
		info.timeon = LED_WAN_CPE_FAST_BLINK_ON_TIME;
		info.timoff = LED_WAN_CPE_FAST_BLINK_OFF_TIME;
		mmi_led_opt_on(LED_WAN_RED_BRIGHTNESS);
#elif defined(JCV_HW_POWERBANK_DZ801)
		mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_WAN_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_BLUE_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);

#elif defined(JCV_HW_MZ901_V1_0)
		mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);

		mmi_led_opt_blinkoff(LED_BATTERY_1_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_1_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_2_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_2_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_3_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_3_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_4_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_4_BRIGHTNESS);
#else
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedOffOpt !\n");
		mmi_led_opt_blinkoff(LED_BATTERY_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_RED_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_RED_BRIGHTNESS);
#endif
	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI salvikie charger_mode close boot or all kernel led\n");
#if defined(JCV_HW_POWERBANK_DZ801)
		mmi_led_opt_blinkoff(LED_WAN_RED_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_RED_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_WAN_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_BLUE_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);

#elif defined(JCV_HW_MZ901_V1_0)
		mmi_led_opt_blinkoff(LED_WAN_RED_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_RED_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);

		mmi_led_opt_blinkoff(LED_BATTERY_1_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_1_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_2_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_2_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_3_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_3_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_4_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_4_BRIGHTNESS);
#else
		mmi_led_opt_blinkoff(LED_BATTERY_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_GREEN_BRIGHTNESS);
		mmi_led_opt_blinkoff(LED_BATTERY_RED_BLINKSWITCH);
		mmi_led_opt_off(LED_BATTERY_RED_BRIGHTNESS);
#endif
	}

	mmi_setBatLedState(LED_ALL_OFF);
}
#endif

static VOID mmi_BatLedRedOn()
{
	mmi_led_opt_on(LED_BATTERY_RED_BRIGHTNESS);
}

static VOID mmi_BatLedRedBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_BATTERY_RED_BLINKSWITCH;
	info.fileblinktimeoff = LED_BATTERY_RED_BLINKTIMEOFF;
	info.fileblinktimeon = LED_BATTERY_RED_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_BATTERY_RED_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_BatLedGreenOn()
{
	mmi_led_opt_on(LED_BATTERY_GREEN_BRIGHTNESS);
}

static VOID mmi_BatLedGreenBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_BATTERY_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_BATTERY_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_BATTERY_GREEN_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_BATTERY_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_BatLedYellowOn()
{
	mmi_led_opt_on(LED_BATTERY_RED_BRIGHTNESS);
	mmi_led_opt_on(LED_BATTERY_GREEN_BRIGHTNESS);
}

static VOID mmi_BatLedYellowBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_BATTERY_RED_BLINKSWITCH;
	info.fileblinktimeoff = LED_BATTERY_RED_BLINKTIMEOFF;
	info.fileblinktimeon = LED_BATTERY_RED_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_BATTERY_RED_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);

	info.fileblinkSwitch = LED_BATTERY_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_BATTERY_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_BATTERY_GREEN_BLINKTIMEON;
	mmi_led_opt_on(LED_BATTERY_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
static VOID mmi_BatLedTrafficOn(MMI_TRAFFIC_LED traffic)
{
	//off
	mmi_led_opt_blinkoff(LED_BATTERY_1_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_1_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_2_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_2_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_3_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_3_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_4_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_4_BRIGHTNESS);

	//on
	switch(traffic)
	{
		case TRAFFIC_LED_1:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			break;
		case TRAFFIC_LED_2:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			break;
		case TRAFFIC_LED_3:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_3_BRIGHTNESS);
			break;
		case TRAFFIC_LED_4:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_3_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_4_BRIGHTNESS);
			break;
		default:
			break;
	}
}

static VOID mmi_BatLedTrafficBlink(T_zMmi_Led_Blink_Time time, MMI_TRAFFIC_LED traffic)
{
	//off
	mmi_led_opt_blinkoff(LED_BATTERY_1_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_1_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_2_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_2_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_3_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_3_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_BATTERY_4_BLINKSWITCH);
	mmi_led_opt_off(LED_BATTERY_4_BRIGHTNESS);

	T_zMmi_LedBlink_Info info = {0};
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;

	//blink
	switch(traffic)
	{
		case TRAFFIC_LED_1:
			info.fileblinkSwitch = LED_BATTERY_1_BLINKSWITCH;
			info.fileblinktimeoff = LED_BATTERY_1_BLINKTIMEON;
			info.fileblinktimeon = LED_BATTERY_1_BLINKTIMEOFF;
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_blinkon(&info);
			break;
		case TRAFFIC_LED_2:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			info.fileblinkSwitch = LED_BATTERY_2_BLINKSWITCH;
			info.fileblinktimeoff = LED_BATTERY_2_BLINKTIMEON;
			info.fileblinktimeon = LED_BATTERY_2_BLINKTIMEOFF;
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			mmi_led_opt_blinkon(&info);
			break;
		case TRAFFIC_LED_3:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			info.fileblinkSwitch = LED_BATTERY_3_BLINKSWITCH;
			info.fileblinktimeoff = LED_BATTERY_3_BLINKTIMEON;
			info.fileblinktimeon = LED_BATTERY_3_BLINKTIMEOFF;
			mmi_led_opt_on(LED_BATTERY_3_BRIGHTNESS);
			mmi_led_opt_blinkon(&info);
			break;
		case TRAFFIC_LED_4:
			mmi_led_opt_on(LED_BATTERY_1_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_2_BRIGHTNESS);
			mmi_led_opt_on(LED_BATTERY_3_BRIGHTNESS);
			info.fileblinkSwitch = LED_BATTERY_4_BLINKSWITCH;
			info.fileblinktimeoff = LED_BATTERY_4_BLINKTIMEON;
			info.fileblinktimeon = LED_BATTERY_4_BLINKTIMEOFF;
			mmi_led_opt_on(LED_BATTERY_4_BRIGHTNESS);
			mmi_led_opt_blinkon(&info);
			break;
		default:
			break;
	}
}

static VOID mmi_BatLedOnOpt(MMI_LED_COLOR color, MMI_TRAFFIC_LED traffic)
{
	MMI_LED_LASTSTATE bat_sta = mmi_getBatLedState();
	static MMI_TRAFFIC_LED traffic_state = TRAFFIC_LED_MAX;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedOnOpt  color=%d, bat_sta = %d traffic_state = %d!\n", color, bat_sta, traffic_state);
	if (color == LED_COLOR_RED) {
		if (bat_sta == LED_RED_ON) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedRedOn();
		}
		mmi_setBatLedState(LED_RED_ON);
	} else if (color == LED_COLOR_GREEN) {
		if (bat_sta == LED_GREEN_ON && (traffic != TRAFFIC_LED_MAX && traffic_state == traffic)) {
			return;
		} else if (traffic == TRAFFIC_LED_MAX) {
			mmi_BatLedOffOpt();
			mmi_BatLedGreenOn();
		} else {
			traffic_state = traffic;
			mmi_BatLedTrafficOn(traffic);
		}
		mmi_setBatLedState(LED_GREEN_ON);
	} else if (color == LED_COLOR_YELLOW) {
		if (bat_sta == LED_YELLOW_ON) {
			return;
		} else if (bat_sta == LED_GREEN_ON) {
			mmi_BatLedRedOn();
		} else if (bat_sta == LED_RED_ON) {
			mmi_BatLedGreenOn();
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedYellowOn();
		}
		mmi_setBatLedState(LED_YELLOW_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_BatLedOnOpt invalid led color!\n");
	}
}

static VOID mmi_BatLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time, MMI_TRAFFIC_LED traffic)
{
	MMI_LED_LASTSTATE bat_sta = mmi_getBatLedState();
	static MMI_TRAFFIC_LED prev_traffic = TRAFFIC_LED_MAX;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedFastBlinkOpt  color=%d bat_sta = %d prev_traffic=%d!\n", color, bat_sta, prev_traffic);
	if (color == LED_COLOR_RED) {
		if (bat_sta == LED_RED_BLINK) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedRedBlink(time);
		}
		mmi_setBatLedState(LED_RED_BLINK);
	} else if (color == LED_COLOR_GREEN) {
		if (bat_sta == LED_GREEN_BLINK && (traffic != TRAFFIC_LED_MAX && prev_traffic == traffic)) {
			return;
		} else if(traffic == TRAFFIC_LED_MAX) {
			mmi_BatLedOffOpt();
			mmi_BatLedGreenBlink(time);
		} else {
			mmi_BatLedTrafficBlink(time, traffic);
			prev_traffic = traffic;
		}
		mmi_setBatLedState(LED_GREEN_BLINK);
		
	} else if (color == LED_COLOR_YELLOW) {
		if (bat_sta == LED_YELLOW_BLINK) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedYellowBlink(time);
		}
		mmi_setBatLedState(LED_YELLOW_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_BatLedFastBlinkOpt invalid led color!\n");
	}
}

static VOID mmi_processbBatteryLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time, MMI_TRAFFIC_LED traffic)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processbBatteryLed state = %d color = %d traffic = %d !\n",  state, color, traffic);
	switch (state) {
		case LED_STATE_ON: {
			mmi_BatLedOnOpt(color, traffic);
			break;
		}
		case LED_STATE_OFF: {
			mmi_BatLedOffOpt();
			break;
		}
		case LED_STATE_BLINK: {
			mmi_BatLedBlinkOpt(color, time, traffic);
			break;
		}
		default: {
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processbBatteryLed invalid state!\n");
			break;
		}
	}
}
#else
static VOID mmi_BatLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE bat_sta = mmi_getBatLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedOnOpt  color=%d, bat_sta = %d!\n", color, bat_sta);
	if (color == LED_COLOR_RED) {

		if (bat_sta == LED_RED_ON) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedRedOn();
		}
		mmi_setBatLedState(LED_RED_ON);
	} else if (color == LED_COLOR_GREEN) {
		if (bat_sta == LED_GREEN_ON) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedGreenOn();
		}
		mmi_setBatLedState(LED_GREEN_ON);
	} else if (color == LED_COLOR_YELLOW) {
		if (bat_sta == LED_YELLOW_ON) {
			return;
		} else if (bat_sta == LED_GREEN_ON) {
			mmi_BatLedRedOn();
		} else if (bat_sta == LED_RED_ON) {
			mmi_BatLedGreenOn();
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedYellowOn();
		}
		mmi_setBatLedState(LED_YELLOW_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_BatLedOnOpt invalid led color!\n");
	}
}

static VOID mmi_BatLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE bat_sta = mmi_getBatLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_BatLedFastBlinkOpt  color=%d bat_sta = %d!\n", color, bat_sta);
	if (color == LED_COLOR_RED) {
		if (bat_sta == LED_RED_BLINK) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedRedBlink(time);
		}
		mmi_setBatLedState(LED_RED_BLINK);
	} else if (color == LED_COLOR_GREEN) {
		if (bat_sta == LED_GREEN_BLINK) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedGreenBlink(time);
		}
		mmi_setBatLedState(LED_GREEN_BLINK);
	} else if (color == LED_COLOR_YELLOW) {
		if (bat_sta == LED_YELLOW_BLINK) {
			return;
		} else {
			mmi_BatLedOffOpt();
			mmi_BatLedYellowBlink(time);
		}
		mmi_setBatLedState(LED_YELLOW_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_BatLedFastBlinkOpt invalid led color!\n");
	}
}

static VOID mmi_processbBatteryLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processbBatteryLed state = %d color = %d !\n",  state, color);
	switch (state) {
	case LED_STATE_ON: {
		mmi_BatLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_BatLedOffOpt();
		break;
	}
	case LED_STATE_BLINK: {
		mmi_BatLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processbBatteryLed invalid state!\n");
		break;
	}
	}
}
#endif

/********************************************************************************
  ����Ʋ���
**********************************************************************************/
static VOID mmi_WanLedOffOpt()
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_WanLedOffOpt !\n");
	mmi_led_opt_blinkoff(LED_WAN_GREEN_BLINKSWITCH);
	mmi_led_opt_off(LED_WAN_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_WAN_RED_BLINKSWITCH);
	mmi_led_opt_off(LED_WAN_RED_BRIGHTNESS);
	mmi_led_opt_blinkoff(LED_WAN_BLUE_BLINKSWITCH);
	mmi_led_opt_off(LED_WAN_BLUE_BRIGHTNESS);
	mmi_setWanLedState(LED_ALL_OFF);
}

static VOID mmi_WanLedRedOn()
{
	mmi_led_opt_on(LED_WAN_RED_BRIGHTNESS);
}

static VOID mmi_WanLedRedBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_WAN_RED_BLINKSWITCH;
	info.fileblinktimeoff = LED_WAN_RED_BLINKTIMEOFF;
	info.fileblinktimeon = LED_WAN_RED_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_WAN_RED_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_WanLedGreenOn()
{
	mmi_led_opt_on(LED_WAN_GREEN_BRIGHTNESS);
}

static VOID mmi_WanLedGreenBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_WAN_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_WAN_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_WAN_GREEN_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_WAN_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_WanLedYellowOn()
{
	mmi_led_opt_on(LED_WAN_RED_BRIGHTNESS);
	mmi_led_opt_on(LED_WAN_GREEN_BRIGHTNESS);
}

static VOID mmi_WanLedYellowBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_WAN_RED_BLINKSWITCH;
	info.fileblinktimeoff = LED_WAN_RED_BLINKTIMEOFF;
	info.fileblinktimeon = LED_WAN_RED_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_WAN_RED_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);

	info.fileblinkSwitch = LED_WAN_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_WAN_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_WAN_GREEN_BLINKTIMEON;
	mmi_led_opt_on(LED_WAN_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_WanLedBlueOn()
{
	mmi_led_opt_on(LED_WAN_BLUE_BRIGHTNESS);
}

static VOID mmi_WanLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_WAN_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_WAN_BLUE_BLINKTIMEOFF;
	info.fileblinktimeon = LED_WAN_BLUE_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_WAN_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_WanLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE wan_sta = mmi_getWanLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_WanLedOnOpt  color=%d, wan_sta = %d!\n", color, wan_sta);
	if (color == LED_COLOR_RED) {
		if (wan_sta == LED_RED_ON) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedRedOn();
		}
		mmi_setWanLedState(LED_RED_ON);
	} else if (color == LED_COLOR_GREEN) {
		if (wan_sta == LED_GREEN_ON) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedGreenOn();
		}
		mmi_setWanLedState(LED_GREEN_ON);
	} else if (color == LED_COLOR_YELLOW) {
		if (wan_sta == LED_YELLOW_ON) {
			return;
		} else if (wan_sta == LED_GREEN_ON) {
			mmi_WanLedRedOn();
		} else if (wan_sta == LED_RED_ON) {
			mmi_WanLedGreenOn();
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedYellowOn();
		}
		mmi_setWanLedState(LED_YELLOW_ON);
	} else if (color == LED_COLOR_BLUE) {
		if (wan_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedBlueOn();
		}
		mmi_setWanLedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_WanLedOnOpt invalid led color!\n");
	}
}

static VOID mmi_WanLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time,MMI_LED_BLINK_SPEED speed)
{
	MMI_LED_LASTSTATE wan_sta = mmi_getWanLedState();
	MMI_LED_BLINK_SPEED wan_speed = mmi_getWanLedSpeed();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_WanLedBlinkOpt  color=%d, wan_sta = %d wan_speed_last=%d!\n", color, wan_sta, wan_speed);
	if (color == LED_COLOR_RED) {
		if (wan_sta == LED_RED_BLINK) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedRedBlink(time);
		}
		mmi_setWanLedState(LED_RED_BLINK);
	} else if (color == LED_COLOR_GREEN) {
		if (wan_sta == LED_GREEN_BLINK && (speed == wan_speed)) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedGreenBlink(time);
		}
		mmi_setWanLedState(LED_GREEN_BLINK);
		mmi_setWanLedSpeed(speed);
	} else if (color == LED_COLOR_YELLOW) {
		if (wan_sta == LED_YELLOW_BLINK) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedYellowBlink(time);
		}
		mmi_setWanLedState(LED_YELLOW_BLINK);
	} else if (color == LED_COLOR_BLUE) {
		if (wan_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_WanLedOffOpt();
			mmi_WanLedBlueBlink(time);
		}
		mmi_setWanLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_WanLedFastBlinkOpt invalid led color!\n");
	}
}

static VOID mmi_processWanLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time, MMI_LED_BLINK_SPEED speed)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processWanLed state = %d color=%d speed=%d!\n",  state, color, speed);
	switch (state) {
	case LED_STATE_ON: {
		mmi_WanLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_WanLedOffOpt();
		break;
	}
	case LED_STATE_BLINK: {
		mmi_WanLedBlinkOpt(color, time, speed);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processNetLed invalid state!\n");
		break;
	}
	}
}

/********************************************************************************
  WIFI �Ʋ���
**********************************************************************************/
static VOID mmi_LanLedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_LanLedOffOpt!\n");
	if (color == LED_COLOR_GREEN) {
		mmi_led_opt_blinkoff(LED_LAN_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_GREEN_BRIGHTNESS);
	#ifdef JCV_HW_MZ803_V3_2
		mmi_led_opt_blinkoff(LED_LAN_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_BLUE_BRIGHTNESS);
	#endif
	} else if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_LAN_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_LAN_BLUE_BRIGHTNESS);
	}
	mmi_setLanLedState(LED_ALL_OFF);
}
static VOID mmi_LanLedGreenOn()
{
	mmi_led_opt_on(LED_LAN_GREEN_BRIGHTNESS);
#ifdef JCV_HW_MZ803_V3_2
	mmi_led_opt_on(LED_LAN_BLUE_BRIGHTNESS);
#endif
}
static VOID mmi_LanLedGreenBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_LAN_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_LAN_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_LAN_GREEN_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_LAN_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);

#ifdef JCV_HW_MZ803_V3_2
	info.fileblinkSwitch = LED_LAN_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_LAN_BLUE_BLINKTIMEOFF;
	info.fileblinktimeon = LED_LAN_BLUE_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_LAN_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
#endif
}

static VOID mmi_LanLedBlueOn()
{
	mmi_led_opt_on(LED_LAN_BLUE_BRIGHTNESS);
}

static VOID mmi_LanLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_LAN_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_LAN_BLUE_BLINKTIMEOFF;
	info.fileblinktimeon = LED_LAN_BLUE_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_LAN_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_LanLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE lan_sta = mmi_getLanLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_LanLedOnOpt lan_sta = %d!\n", lan_sta);
	if (color == LED_COLOR_GREEN) {
		if (lan_sta == LED_GREEN_ON) {
			return;
		} else {
			mmi_LanLedOffOpt(color);
			mmi_LanLedGreenOn();
		}
		mmi_setLanLedState(LED_GREEN_ON);
	} else if (color == LED_COLOR_BLUE) {
		if (lan_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_LanLedOffOpt(color);
			mmi_LanLedBlueOn();
		}
		mmi_setLanLedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_LanLedOnOpt invalid color!!\n");
	}
}

static VOID mmi_LanLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE lan_sta = mmi_getLanLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_LanLedBlinkOpt lan_sta = %d!\n", lan_sta);
	if (color == LED_COLOR_GREEN) {
		if (lan_sta == LED_GREEN_BLINK) {
			return;
		} else {
			mmi_LanLedOffOpt(color);
			mmi_LanLedGreenBlink(time);
		}
		mmi_setLanLedState(LED_GREEN_BLINK);
	} else if (color == LED_COLOR_BLUE) {
		if (lan_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_LanLedOffOpt(color);
			mmi_LanLedBlueBlink(time);
		}
		mmi_setLanLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_LanLedBlinkOpt invalid color!!\n");
	}
}
static VOID mmi_processLanLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processLanLed state=%d!\n", state);
	switch (state) {
	case LED_STATE_ON: {
		mmi_LanLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_LanLedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_LanLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processLanLed invalid state!\n");
		break;
	}
	}
}


/********************************************************************************
  ���ŵƲ���
**********************************************************************************/
static VOID mmi_SmsLedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SmsLedOffOpt!\n");
	if (color == LED_COLOR_GREEN) {
		mmi_led_opt_blinkoff(LED_SMS_GREEN_BLINKSWITCH);
		mmi_led_opt_off(LED_SMS_GREEN_BRIGHTNESS);
	} else if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_SMS_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_SMS_BLUE_BRIGHTNESS);
	}
	mmi_setSmsLedState(LED_ALL_OFF);
}
static VOID mmi_SmsLedGreenOn()
{
	mmi_led_opt_on(LED_SMS_GREEN_BRIGHTNESS);
}

static VOID mmi_SmsLedGreenBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_SMS_GREEN_BLINKSWITCH;
	info.fileblinktimeoff = LED_SMS_GREEN_BLINKTIMEOFF;
	info.fileblinktimeon = LED_SMS_GREEN_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_SMS_GREEN_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_SmsLedBlueOn()
{
	mmi_led_opt_on(LED_SMS_BLUE_BRIGHTNESS);
}

static VOID mmi_SmsLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_SMS_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_SMS_BLUE_BLINKTIMEOFF;
	info.fileblinktimeon = LED_SMS_BLUE_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_SMS_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_SmsLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE sms_sta = mmi_getSmsLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SmsLedOnOpt  sms_sta = %d!\n", sms_sta);
	if (color == LED_COLOR_GREEN) {
		if (sms_sta == LED_GREEN_ON) {
			return;
		} else {
			mmi_SmsLedOffOpt(color);
			mmi_SmsLedGreenOn();
		}
		mmi_setSmsLedState(LED_GREEN_ON);
	} else if (color == LED_COLOR_BLUE) {
		if (sms_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_SmsLedOffOpt(color);
			mmi_SmsLedBlueOn();
		}
		mmi_setSmsLedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SmsLedOnOpt invalid color!!\n");
	}
}
static VOID mmi_SmsLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE sms_sta = mmi_getSmsLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SmsLedBlinkOpt sms_sta = %d!\n", sms_sta);
	if (color == LED_COLOR_GREEN) {
		if (sms_sta == LED_GREEN_BLINK) {
			return;
		} else {
			mmi_SmsLedOffOpt(color);
			mmi_SmsLedGreenBlink(time);
		}
		mmi_setSmsLedState(LED_GREEN_BLINK);
	} else if (color == LED_COLOR_BLUE) {
		if (sms_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_SmsLedOffOpt(color);
			mmi_SmsLedBlueBlink(time);
		}
		mmi_setSmsLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SmsLedBlinkOpt invalid color!!\n");
	}

}
static VOID mmi_processSmsLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processSmsLed state=%d\n", state);
	switch (state) {
	case LED_STATE_ON: {
		mmi_SmsLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_SmsLedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_SmsLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processSmsLed invalid state!\n");
		break;
	}
	}
}

/**********************************************************************************
��������:�����Ʋ���
***********************************************************************************/
static VOID mmi_TrafficLedOnOpt(MMI_TRAFFIC_LED traffic)
{
	switch (traffic) {
	case TRAFFIC_LED_1:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_1_ON);
		break;
	case TRAFFIC_LED_2:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_2_ON);
		break;
	case TRAFFIC_LED_3:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_3_ON);
		break;
	case TRAFFIC_LED_4:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_4_ON);
		break;
	case TRAFFIC_LED_5:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_5_ON);
		break;
	case TRAFFIC_LED_6:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_6_ON);
		break;
	case TRAFFIC_LED_7:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_7_ON);
		break;
	case TRAFFIC_LED_8:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_8_ON);
		break;
	case TRAFFIC_LED_9:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_9_ON);
		break;
	case TRAFFIC_LED_10:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_10_ON);
		break;
	default:
		break;
	}
}

static VOID mmi_TrafficLedBlinkOpt(MMI_TRAFFIC_LED traffic)
{
	switch (traffic) {
	case TRAFFIC_LED_1:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_1_BLINK);
		break;
	case TRAFFIC_LED_2:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_2_BLINK);
		break;
	case TRAFFIC_LED_3:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_3_BLINK);
		break;
	case TRAFFIC_LED_4:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_4_BLINK);
		break;
	case TRAFFIC_LED_5:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_5_BLINK);
		break;
	case TRAFFIC_LED_6:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_6_BLINK);
		break;
	case TRAFFIC_LED_7:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_7_BLINK);
		break;
	case TRAFFIC_LED_8:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_8_BLINK);
		break;
	case TRAFFIC_LED_9:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_9_BLINK);
		break;
	case TRAFFIC_LED_10:
		mmi_file_operate(TRAFFIC_LED_PATH, TRAFIIC_LED_10_BLINK);
		break;
	default:
		break;
	}
}

static VOID mmi_TrafficLedOffOpt()
{
	mmi_file_operate(TRAFFIC_LED_PATH, LED_TRAFFIC_ALL_OFF);
}
static VOID mmi_processTrafficLed(MMI_LED_STATE state, MMI_TRAFFIC_LED traffic)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processTrafficLed state = %d traffic = %d\n", state, traffic);
	switch (state) {
	case LED_STATE_ON:
		mmi_TrafficLedOnOpt(traffic);
		break;
	case LED_STATE_BLINK:
		mmi_TrafficLedBlinkOpt(traffic);
		break;
	case LED_STATE_OFF:
		mmi_TrafficLedOffOpt();
		break;
	default:
		break;

	}
}

/********************************************************************************
  ������ʾ�Ʋ���
**********************************************************************************/
static VOID mmi_VoipLedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_VoipLedOffOpt!\n");
	if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_VOIP_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_VOIP_BLUE_BRIGHTNESS);
	}
	mmi_setVoipLedState(LED_ALL_OFF);
}

static VOID mmi_VoipLedBlueOn()
{
	mmi_led_opt_on(LED_VOIP_BLUE_BRIGHTNESS);
}

static VOID mmi_VoipLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_VOIP_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_VOIP_BLUE_BLINKTIMEOFF;
	info.fileblinktimeon = LED_VOIP_BLUE_BLINKTIMEON;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_VOIP_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_VoipLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE voip_sta = mmi_getVoipLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_VoipLedOnOpt  voip_sta = %d!\n", voip_sta);
	if (color == LED_COLOR_BLUE) {
		if (voip_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_VoipLedOffOpt(color);
			mmi_VoipLedBlueOn();
		}
		mmi_setVoipLedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_VoipLedOnOpt invalid color!!\n");
	}
}
static VOID mmi_VoipLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE voip_sta = mmi_getVoipLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_VoipLedBlinkOpt voip_sta = %d!\n", voip_sta);
	if (color == LED_COLOR_BLUE) {
		if (voip_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_VoipLedOffOpt(color);
			mmi_VoipLedBlueBlink(time);
		}
		mmi_setVoipLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_VoipLedBlinkOpt invalid color!!\n");
	}

}
static VOID mmi_processVoipLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processVoipLed state=%d\n", state);
	switch (state) {
	case LED_STATE_ON: {
		mmi_VoipLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_VoipLedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_VoipLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processVoipLed invalid state!\n");
		break;
	}
	}
}

/********************************************************************************
  �ź�ǿ����ʾ�Ʋ��� ҢԶcpe
**********************************************************************************/
static MMI_LED_LASTSTATE mmi_getSignalLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_signalled_state;
	return state;
}

static VOID mmi_setSignalLedState(MMI_LED_LASTSTATE sig_state)
{
	g_mmi_signalled_state = sig_state;
}

static MMI_LED_LASTSTATE mmi_transSignalLedState(MMI_TRAFFIC_LED traffic)
{
	MMI_LED_LASTSTATE state;
    switch (traffic)
    {
    	case TRAFFIC_LED_1:
	        state = LED_BLUE1_ON;
	        break;

    	case TRAFFIC_LED_2:
	        state = LED_BLUE2_ON;
	        break;

		case TRAFFIC_LED_3:
	        state = LED_BLUE3_ON;
	        break;

		case TRAFFIC_LED_4:
	        state = LED_BLUE4_ON;
	        break;

		case TRAFFIC_LED_5:
	        state = LED_BLUE5_ON;
	        break;

		case TRAFFIC_LED_MAX:
			state = LED_BLUE_BLINK;
			break;

    	default:
	        state = LED_ALL_OFF;
	        break;
    }

	return state;
}


static VOID mmi_SignalLedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SignalLedOffOpt!\n");
	if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_SIGNAL1_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_SIGNAL1_BLUE_BRIGHTNESS);
		mmi_led_opt_off(LED_SIGNAL2_BLUE_BRIGHTNESS);
		mmi_led_opt_off(LED_SIGNAL3_BLUE_BRIGHTNESS);
		mmi_led_opt_off(LED_SIGNAL4_BLUE_BRIGHTNESS);
		mmi_led_opt_off(LED_SIGNAL5_BLUE_BRIGHTNESS);
	}
	mmi_setSignalLedState(LED_ALL_OFF);
}

static VOID mmi_SignalLedBlueOn(MMI_LED_LASTSTATE state)
{
	switch (state)
    {
    	case LED_BLUE1_ON:
			mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
	        break;

    	case LED_BLUE2_ON:
	        mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL2_BLUE_BRIGHTNESS);
	        break;

		case LED_BLUE3_ON:
	        mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL2_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL3_BLUE_BRIGHTNESS);
	        break;

		case LED_BLUE4_ON:
	        mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL2_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL3_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL4_BLUE_BRIGHTNESS);
	        break;

		case LED_BLUE5_ON:
	        mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL2_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL3_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL4_BLUE_BRIGHTNESS);
			mmi_led_opt_on(LED_SIGNAL5_BLUE_BRIGHTNESS);
	        break;

    	default:
	        slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SignalLedBlueOn  state = %d!\n", state);
	        break;
    }
}

static VOID mmi_SignalLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_SIGNAL1_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_SIGNAL1_BLUE_BLINKTIMEON;
	info.fileblinktimeon = LED_SIGNAL1_BLUE_BLINKTIMEOFF;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_SIGNAL1_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_SignalLedOnOpt(MMI_LED_COLOR color, MMI_TRAFFIC_LED traffic)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getSignalLedState();
	MMI_LED_LASTSTATE traffic_trans = mmi_transSignalLedState(traffic);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SignalLedOnOpt  sig_sta = %d,%d!\n", sig_sta, traffic_trans);
	if (color == LED_COLOR_BLUE) {
		if (sig_sta == traffic_trans) {
			return;
		} else {
			mmi_SignalLedOffOpt(color);
			mmi_SignalLedBlueOn(traffic_trans);
		}
		mmi_setSignalLedState(traffic_trans);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SignalLedOnOpt invalid color!!\n");
	}
}
static VOID mmi_SignalLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getSignalLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SignalLedBlinkOpt voip_sta = %d!\n", sig_sta);
	if (color == LED_COLOR_BLUE) {
		if (sig_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_SignalLedOffOpt(color);
			mmi_SignalLedBlueBlink(time);
		}
		mmi_setSignalLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SignalLedBlinkOpt invalid color!!\n");
	}
}

static VOID mmi_processSignalLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time, MMI_TRAFFIC_LED traffic)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processSignalLed state=%d\n", state);
	switch (state) {
	case LED_STATE_ON: {
		mmi_SignalLedOnOpt(color, traffic);
		break;
	}
	case LED_STATE_OFF: {
		mmi_SignalLedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_SignalLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processSignalLed invalid state!\n");
		break;
	}
	}
}


/********************************************************************************
  WPS�Ʋ��� ҢԶcpe
**********************************************************************************/
static MMI_LED_LASTSTATE mmi_getWpsLedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_wpsled_state;
	return state;
}

static VOID mmi_setWpsLedState(MMI_LED_LASTSTATE sig_state)
{
	g_mmi_wpsled_state = sig_state;
}

static VOID mmi_WpsLedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_WpsLedOffOpt!\n");
	if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_WPS_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_WPS_BLUE_BRIGHTNESS);
	}
	mmi_setWpsLedState(LED_ALL_OFF);
}

static VOID mmi_WpsLedBlueOn()
{
	mmi_led_opt_on(LED_WPS_BLUE_BRIGHTNESS);
}

static VOID mmi_WpsLedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_WPS_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_WPS_BLUE_BLINKTIMEON;
	info.fileblinktimeon = LED_WPS_BLUE_BLINKTIMEOFF;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_WPS_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_WpsLedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getWpsLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_WpsLedOnOpt  sig_sta = %d!\n", sig_sta);

	if (color == LED_COLOR_BLUE) {
		if (sig_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_WpsLedOffOpt(color);
			mmi_WpsLedBlueOn();
		}
		mmi_setWpsLedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_WpsLedOnOpt invalid color!!\n");
	}
}
static VOID mmi_WpsLedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getWpsLedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_SignalLedBlinkOpt voip_sta = %d!\n", sig_sta);
	if (color == LED_COLOR_BLUE) {
		if (sig_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_WpsLedOffOpt(color);
			mmi_WpsLedBlueBlink(time);
		}
		mmi_setWpsLedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_SignalLedBlinkOpt invalid color!!\n");
	}
}

static VOID mmi_processWpsLed(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processWpsLed state=%d\n", state);

	switch (state) {
	case LED_STATE_ON: {
		mmi_WpsLedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_WpsLedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_WpsLedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processWpsLed invalid state!\n");
		break;
	}
	}
}

/********************************************************************************
  rj11�Ʋ��� ҢԶcpe
**********************************************************************************/
static MMI_LED_LASTSTATE mmi_getRj11LedState(VOID)
{
	MMI_LED_LASTSTATE state;
	state = g_mmi_rj11led_state;
	return state;
}

static VOID mmi_setRj11LedState(MMI_LED_LASTSTATE sig_state)
{
	g_mmi_rj11led_state = sig_state;
}

static VOID mmi_Rj11LedOffOpt(MMI_LED_COLOR color)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_Rj11LedOffOpt!\n");
	if (color == LED_COLOR_BLUE) {
		mmi_led_opt_blinkoff(LED_RJ11_BLUE_BLINKSWITCH);
		mmi_led_opt_off(LED_RJ11_BLUE_BRIGHTNESS);
	}
	mmi_setRj11LedState(LED_ALL_OFF);
}

static VOID mmi_Rj11LedBlueOn()
{
	mmi_led_opt_on(LED_RJ11_BLUE_BRIGHTNESS);
}

static VOID mmi_Rj11LedBlueBlink(T_zMmi_Led_Blink_Time time)
{
	T_zMmi_LedBlink_Info info = {0};
	info.fileblinkSwitch = LED_RJ11_BLUE_BLINKSWITCH;
	info.fileblinktimeoff = LED_RJ11_BLUE_BLINKTIMEON;
	info.fileblinktimeon = LED_RJ11_BLUE_BLINKTIMEOFF;
	info.timeon = (char *)time.uBlinkOnTime;
	info.timoff = (char *)time.uBlinkOffTime;
	mmi_led_opt_on(LED_RJ11_BLUE_BRIGHTNESS);
	mmi_led_opt_blinkon(&info);
}

static VOID mmi_Rj11LedOnOpt(MMI_LED_COLOR color)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getRj11LedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_Rj11LedOnOpt  sig_sta = %d!\n", sig_sta);

	if (color == LED_COLOR_BLUE) {
		if (sig_sta == LED_BLUE_ON) {
			return;
		} else {
			mmi_Rj11LedOffOpt(color);
			mmi_Rj11LedBlueOn();
		}
		mmi_setRj11LedState(LED_BLUE_ON);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_Rj11LedOnOpt invalid color!!\n");
	}
}
static VOID mmi_Rj11LedBlinkOpt(MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	MMI_LED_LASTSTATE sig_sta = mmi_getRj11LedState();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_Rj11LedBlinkOpt voip_sta = %d!\n", sig_sta);
	if (color == LED_COLOR_BLUE) {
		if (sig_sta == LED_BLUE_BLINK) {
			return;
		} else {
			mmi_Rj11LedOffOpt(color);
			mmi_Rj11LedBlueBlink(time);
		}
		mmi_setRj11LedState(LED_BLUE_BLINK);
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_Rj11LedBlinkOpt invalid color!!\n");
	}
}

static VOID mmi_processRj11Led(MMI_LED_STATE state, MMI_LED_COLOR color, T_zMmi_Led_Blink_Time time)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_processRj11Led state=%d\n", state);
printf("ZTE_MMI mmi_processRj11Led state=%d\n", state);

	switch (state) {
	case LED_STATE_ON: {
		mmi_Rj11LedOnOpt(color);
		break;
	}
	case LED_STATE_OFF: {
		mmi_Rj11LedOffOpt(color);
		break;
	}
	case LED_STATE_BLINK: {
		mmi_Rj11LedBlinkOpt(color, time);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_processRj11Led invalid state!\n");
		break;
	}
	}
}

/**********************************************************************************
��������:���ػ�ʱ���е�ȫ��ȫ�����
***********************************************************************************/
static VOID mmi_processAllLed(MMI_LED_STATE state)
{
	switch (state) {
	case LED_STATE_ON: {
		mmi_file_operate(ALL_LED_PATH, LED_ALL_POWER_ON);
		break;
	}
	case LED_STATE_OFF: {
		mmi_file_operate(ALL_LED_PATH, LED_ALL_POWER_OFF);
		break;
	}
	default:
		break;
	}
}

/**********************************************************************************
��������:��Ʋ�������
***********************************************************************************/
VOID mmi_led_operate(T_zMmi_Led_Info *ledinfo)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_led_operate ledname = %d state = %d color=%d,traffic=%d\n", ledinfo->led_name, ledinfo->led_state, ledinfo->led_color, ledinfo->traffic);
	switch (ledinfo->led_name) {
	case LED_BATTERY: {
#if defined(QRZL_UE) && defined(JCV_HW_MZ901_V1_0)
		mmi_processbBatteryLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time, ledinfo->traffic);
#else
		mmi_processbBatteryLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
#endif
		break;
	}
	case LED_WAN: {
		mmi_processWanLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time, ledinfo->ledBlink_speed);
		break;
	}
	case LED_LAN: {
		mmi_processLanLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
		break;
	}
	case LED_SMS: {
		mmi_processSmsLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
		break;
	}
	case LED_TRAFFIC: {
		mmi_processTrafficLed(ledinfo->led_state, ledinfo->traffic);
		break;
	}
	case LED_VOIP: {
		mmi_processVoipLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
		break;
	}
	//yao yuan
	case LED_SIGNAL: {
		mmi_processSignalLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time, ledinfo->traffic);
		break;
	}
	case LED_WPS: {
		mmi_processWpsLed(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
		break;
	}
	case LED_RJ11: {
		mmi_processRj11Led(ledinfo->led_state, ledinfo->led_color, ledinfo->ledBlink_time);
		break;
	}
	
	case LED_ALL: {
		mmi_processAllLed(ledinfo->led_state);
		break;
	}
	default: {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_led_operate invalid ledname!\n");
		break;
	}
	}
}

