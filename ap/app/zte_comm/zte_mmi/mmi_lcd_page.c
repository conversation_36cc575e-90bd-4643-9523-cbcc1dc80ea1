/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd_page.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#ifndef DISABLE_LCD
#include "mmi_lcd.h"

extern T_LcdShowInfoItem g_LcdShowInfoTab[];
extern E_zMmi_Sim_Tip s_mmi_sim_tip;
extern pthread_mutex_t g_mmi_refresh_lcd_mutex;

extern UINT32 g_show_pagefirst;
extern UINT32 g_show_pagesecond;
extern UINT32 g_show_pagethird;

static E_zMMI_Lcd_Page_Index g_mmi_lcd_page_index = MMI_SHOW_PAGE_FIRST;
/***********************************************************************************
   ��������:�Ƿ���ʾ����ͼ��, visible: TRUE����ʾ;FALSE, ����
***********************************************************************************/
static VOID mmi_set_top_bar_visibility(BOOL visible)
{
	g_LcdShowInfoTab[LCD_SHOW_NET_SIGNAL].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].needShowFL = visible;
#ifdef QRZL_UE
	g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].needShowFL = visible;
#endif
	g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_SMS].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_WIFI].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_BATTERY].needShowFL = visible;
}

static VOID mmi_set_middle_visibility(BOOL visible)
{
#if defined(QRZL_UE) && defined(QRZL_CUSTOMER_XIANJI)
	g_LcdShowInfoTab[LCD_SHOW_CMCC].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].needShowFL = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_NET_PROVIDER_LOGO].needShowFL = visible;
#else
	g_LcdShowInfoTab[LCD_SHOW_CMCC].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].needShowFL = visible;
#endif
}

static VOID mmi_set_ssid_page_visibility(BOOL visible)
{
	g_LcdShowInfoTab[LCD_SHOW_WIFI_PASSWORD].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_WIFI_SSID].needShowFL = visible;
}

static VOID mmi_set_ssid2_page_visibility(BOOL visible)
{
	g_LcdShowInfoTab[LCD_SHOW_WIFI_PASSWORD2].needShowFL = visible;
	g_LcdShowInfoTab[LCD_SHOW_WIFI_SSID2].needShowFL = visible;
}

static VOID mmi_set_first_page_visibility(BOOL visible)
{
	if (g_show_pagefirst) {
	#ifdef QRZL_UE
		char *nv_task_tab[NV_CONTENT_LEN] = {0};
		cfg_get_item("mmi_task_tab", nv_task_tab, sizeof(nv_task_tab));
		if (strstr(nv_task_tab, "traffic_task")) {
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].needShowFL = visible;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShowFL = visible;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShowFL = visible;
		} else {
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].needShowFL = FALSE;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShowFL = FALSE;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShowFL = FALSE;
		}
	#ifdef QRZL_CUSTOMER_XIANJI
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].needShowFL = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShowFL = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShowFL = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_NET_PROVIDER_LOGO].needShowFL = visible;
	#endif
	#else
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].needShowFL = visible;
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShowFL = visible;
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShowFL = visible;
	#endif
	}
}

static VOID mmi_set_second_page_visibility(BOOL visible)
{
	if (g_show_pagesecond) {
	#ifdef QRZL_UE
		mmi_set_middle_visibility(FALSE);
	#else
		mmi_set_middle_visibility(visible);
	#endif
		mmi_set_ssid_page_visibility(visible);
	}
}

static VOID mmi_set_third_page_visibility(BOOL visible)
{
	if (g_show_pagethird) {
		g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE].needShowFL = visible;
	}
}

static VOID mmi_set_four_page_visibility(BOOL visible)
{
//���������ssidʱҪ��ʾ
	mmi_set_middle_visibility(visible);
	mmi_set_ssid2_page_visibility(visible);
}

static VOID mmi_set_five_page_visibility(BOOL visible)
{
//���������ssidʱҪ��ʾ
	g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE2].needShowFL = visible;
}

static VOID mmi_set_all_page_invisibility()
{
	mmi_set_first_page_visibility(FALSE);
	mmi_set_second_page_visibility(FALSE);
	mmi_set_third_page_visibility(FALSE);
	mmi_set_ssid2_page_visibility(FALSE);
	mmi_set_five_page_visibility(FALSE);
}


BOOL mmi_is_the_last_page()
{
	int lastpage = 0;

	if (g_show_pagethird)
		lastpage = MMI_SHOW_PAGE_THIRD;

	else if (g_show_pagefirst)
		lastpage = MMI_SHOW_PAGE_SECOND;

	return (g_mmi_lcd_page_index == lastpage);
}

E_zMMI_Lcd_Page_Index mmi_get_lcd_page_index(VOID)
{
	return g_mmi_lcd_page_index;
}

static E_zMMI_Lcd_Page_Index mmi_get_lcd_next_page_index(VOID)
{
	E_zMMI_Lcd_Page_Index index = mmi_get_lcd_page_index();
	E_zMMI_Lcd_Page_Index next_index = index;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  mmi_get_lcd_next_page_index index = %d\n", index);
#ifdef QRZL_UE
	while (index <= MMI_SHOW_PAGE_THIRD) {
		if (index == MMI_SHOW_PAGE_THIRD) {
			index = MMI_SHOW_PAGE_FIRST;
		} else {
			index++;
		}

		if (index == MMI_SHOW_PAGE_FIRST) {
			if (g_show_pagefirst) {
				next_index = index;
				break;
			}
		} else if (index == MMI_SHOW_PAGE_SECOND) {
			if (g_show_pagesecond) {
				next_index = index;
				break;
			}
		} else if ((index == MMI_SHOW_PAGE_THIRD) && (mmi_get_qrcode_state() == TRUE)) {
			if (g_show_pagethird) {
				next_index = index;
				break;
			}
		}
	}
#else
	while (index < MMI_SHOW_PAGE_MAX) {
		if (index == MMI_SHOW_PAGE_FIVE) {
			index = MMI_SHOW_PAGE_FIRST;
		} else {
			index++;
		}

		if (index == MMI_SHOW_PAGE_FIRST) {
			if (g_show_pagesecond) {
				next_index = index;
				break;
			}
		} else if (index == MMI_SHOW_PAGE_SECOND) {
			if (g_show_pagefirst) {
				next_index = index;
				break;
			}
		} else if ((index == MMI_SHOW_PAGE_THIRD) && (mmi_get_qrcode_state() == TRUE)) {
			if (g_show_pagethird) {
				next_index = index;
				break;
			}
		}
		if (mmi_get_multi_ssid_switch_flag()) {
			if (index == MMI_SHOW_PAGE_FOUR) {
				next_index = index;
				break;
			} else if ((index == MMI_SHOW_PAGE_FIVE) && (mmi_get_qrcode_state() == TRUE)) {
				next_index = index;
				break;
			}
		}
	}
#endif

	return next_index;
}


VOID mmi_set_lcd_page_index(E_zMMI_Lcd_Page_Index index)
{
	mmi_getMutex(&g_mmi_refresh_lcd_mutex);
	g_mmi_lcd_page_index = index;
	switch (index) {
	case MMI_SHOW_PAGE_FIRST:
		mmi_set_all_page_invisibility();
		mmi_set_top_bar_visibility(TRUE);
		mmi_set_middle_visibility(TRUE);
		mmi_set_first_page_visibility(TRUE);
		break;
	case MMI_SHOW_PAGE_SECOND:
		mmi_set_top_bar_visibility(TRUE);
		mmi_set_all_page_invisibility();
		mmi_set_second_page_visibility(TRUE);//��һҳ����������ҳ��ֱ���л�������Ҫ������ҳ��ȫ����false
		break;
	case MMI_SHOW_PAGE_THIRD:
		mmi_set_top_bar_visibility(FALSE);
		mmi_set_all_page_invisibility();
		mmi_set_third_page_visibility(TRUE);
		break;
	case MMI_SHOW_PAGE_FOUR:
		mmi_set_top_bar_visibility(TRUE);
		mmi_set_all_page_invisibility();
		mmi_set_four_page_visibility(TRUE);
		break;
	case MMI_SHOW_PAGE_FIVE:
		mmi_set_top_bar_visibility(FALSE);
		mmi_set_all_page_invisibility();
		mmi_set_five_page_visibility(TRUE);
		break;
	default:
		break;
	}
	mmi_putMutex(&g_mmi_refresh_lcd_mutex);

}


VOID mmi_handle_lcd_key_switch_page()
{
	int page_index = mmi_get_lcd_next_page_index();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_handle_lcd_key_switch_page salvikie page_index = %d\n", page_index);

	//������ʾSSID��ʱ��stop ��λ����Ϊ0
	mmi_stopLcdShowScrollSSIDTimer();
	mmi_set_wificode_show_flag(FALSE);
	if (page_index == MMI_SHOW_PAGE_FIRST) {
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_FIRST);
#ifdef QRZL_UE
		//第一页显示 middle NET 和 流量
		mmi_set_update_flag(MMI_TASK_NET);
		
		//流量禁用了不显示
		//mmi_set_update_flag(MMI_TASK_TRAFFIC);
#else
		mmi_set_update_flag(MMI_TASK_SSID);
#endif
	} else if (page_index == MMI_SHOW_PAGE_SECOND) {
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
#ifdef QRZ_UE
		// 第二页显示 ssid
		mmi_set_update_flag(MMI_TASK_SSID);
#else
		mmi_set_update_flag(MMI_TASK_TRAFFIC);
#endif
	} else if (page_index == MMI_SHOW_PAGE_THIRD) {
		mmi_set_wificode_show_flag(TRUE);
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_THIRD);
		mmi_set_update_flag(MMI_TASK_WIFICODE);
	} else if (page_index == MMI_SHOW_PAGE_FOUR) {
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_FOUR);
		mmi_set_update_flag(MMI_TASK_SSID);
	} else if (page_index == MMI_SHOW_PAGE_FIVE) {
		mmi_set_wificode_show_flag(TRUE);
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_FIVE);
		mmi_set_update_flag(MMI_TASK_WIFICODE);
	}

}
#endif
