/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd_info.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ����ʾʱ�䡢���š������źš�WIFI����������������Ϣ
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2015-1-4
*  ����˵��  ��
*
******************************************************************************/

/*****************************************************************************
 ͷ�ļ�
******************************************************************************/
#ifndef DISABLE_LCD
#include "mmi_common.h"
#include "os_type.h"
#include "app_infos.h"
#include "app_global_defines.h"

/****************************************************************************/

#define MMI_LCD_WIDTH					128
#define MMI_LCD_HEIGHT   				128

/*
���ػ�����
*/
typedef enum {
	E_POWERONOFF_FRAME_0 = 0,
	E_POWERONOFF_FRAME_1,
	E_POWERONOFF_FRAME_2,
	E_POWERONOFF_FRAME_3,
	E_POWERONOFF_FRAME_4,
	E_POWERONOFF_FRAME_5,
	E_POWERONOFF_FRAME_6,
	E_POWERONOFF_FRAME_7,
	E_POWERONOFF_FRAME_8,
	E_POWERONOFF_FRAME_9,
	E_POWERONOFF_FRAME_10,
	E_POWERONOFF_FRAME_11,
	E_POWERONOFF_FRAME_12
} E_POWONOFF_FRAME_ID;


typedef struct {
	E_zMmi_Work_Mode workMode;
	CHAR* tipString;
} T_PowerOnOffTipStringItem;

T_PowerOnOffTipStringItem g_PowerOnOffTipStringTab[] = {
	{MMI_POWERON_MODE, "Welcome"},
	{MMI_POWEROFF_MODE, "Poweroff"},
	{MMI_RESET_MODE, "Resetting"},
	{MMI_RESTART_MODE, "Restarting"}
};

typedef struct {
	E_POWONOFF_FRAME_ID frameID;
	CHAR* path;
} T_PowerOnOffFrameItem;

#ifdef QRZL_UE
T_PowerOnOffFrameItem g_powerOnFramTab[] = {
	{E_POWERONOFF_FRAME_0, "/etc_ro/mmi/image_poweron_0.png"},
	{E_POWERONOFF_FRAME_1, "/etc_ro/mmi/image_poweron_1.png"},
	{E_POWERONOFF_FRAME_2, "/etc_ro/mmi/image_poweron_2.png"},
	{E_POWERONOFF_FRAME_3, "/etc_ro/mmi/image_poweron_3.png"},
	{E_POWERONOFF_FRAME_4, "/etc_ro/mmi/image_poweron_4.png"},
	{E_POWERONOFF_FRAME_5, "/etc_ro/mmi/image_poweron_5.png"},
	{E_POWERONOFF_FRAME_6, "/etc_ro/mmi/image_poweron_6.png"},
	{E_POWERONOFF_FRAME_7, "/etc_ro/mmi/image_poweron_7.png"},
	{E_POWERONOFF_FRAME_8, "/etc_ro/mmi/image_poweron_8.png"},
	{E_POWERONOFF_FRAME_9, "/etc_ro/mmi/image_poweron_9.png"},
	{E_POWERONOFF_FRAME_10, "/etc_ro/mmi/image_poweron_10.png"}
};

T_PowerOnOffFrameItem g_powerOffFramTab[] = {
	{E_POWERONOFF_FRAME_0, "/etc_ro/mmi/image_poweroff_0.png"},
	{E_POWERONOFF_FRAME_1, "/etc_ro/mmi/image_poweroff_1.png"},
	{E_POWERONOFF_FRAME_2, "/etc_ro/mmi/image_poweroff_2.png"},
	{E_POWERONOFF_FRAME_3, "/etc_ro/mmi/image_poweroff_3.png"},
	{E_POWERONOFF_FRAME_4, "/etc_ro/mmi/image_poweroff_4.png"},
	{E_POWERONOFF_FRAME_5, "/etc_ro/mmi/image_poweroff_5.png"},
	{E_POWERONOFF_FRAME_6, "/etc_ro/mmi/image_poweroff_6.png"},
	{E_POWERONOFF_FRAME_7, "/etc_ro/mmi/image_poweroff_7.png"},
	{E_POWERONOFF_FRAME_8, "/etc_ro/mmi/image_poweroff_8.png"},
	{E_POWERONOFF_FRAME_9, "/etc_ro/mmi/image_poweroff_9.png"}
};

T_PowerOnOffFrameItem g_powerOnoffFramTab[] = {
	{E_POWERONOFF_FRAME_0, "/etc_ro/mmi/cartoon_0.png"},
	{E_POWERONOFF_FRAME_1, "/etc_ro/mmi/cartoon_1.png"},
	{E_POWERONOFF_FRAME_2, "/etc_ro/mmi/cartoon_2.png"},
	{E_POWERONOFF_FRAME_3, "/etc_ro/mmi/cartoon_3.png"},
	{E_POWERONOFF_FRAME_4, "/etc_ro/mmi/cartoon_4.png"},
	{E_POWERONOFF_FRAME_5, "/etc_ro/mmi/cartoon_5.png"},
	{E_POWERONOFF_FRAME_6, "/etc_ro/mmi/cartoon_6.png"},
	{E_POWERONOFF_FRAME_7, "/etc_ro/mmi/cartoon_7.png"},
	{E_POWERONOFF_FRAME_8, "/etc_ro/mmi/cartoon_8.png"},
	{E_POWERONOFF_FRAME_9, "/etc_ro/mmi/cartoon_9.png"},
	{E_POWERONOFF_FRAME_10, "/etc_ro/mmi/cartoon_10.png"},
	{E_POWERONOFF_FRAME_11, "welcome"}
};
#else
T_PowerOnOffFrameItem g_powerOnoffFramTab[] = {
	{E_POWERONOFF_FRAME_0, "/etc_ro/mmi/cartoon_0.png"},
	{E_POWERONOFF_FRAME_1, "/etc_ro/mmi/cartoon_1.png"},
	{E_POWERONOFF_FRAME_2, "/etc_ro/mmi/cartoon_2.png"},
	{E_POWERONOFF_FRAME_3, "/etc_ro/mmi/cartoon_3.png"},
	{E_POWERONOFF_FRAME_4, "/etc_ro/mmi/cartoon_4.png"},
	{E_POWERONOFF_FRAME_5, "/etc_ro/mmi/cartoon_5.png"},
	{E_POWERONOFF_FRAME_6, "/etc_ro/mmi/cartoon_6.png"},
	{E_POWERONOFF_FRAME_7, "/etc_ro/mmi/cartoon_7.png"},
	{E_POWERONOFF_FRAME_8, "/etc_ro/mmi/cartoon_8.png"},
	{E_POWERONOFF_FRAME_9, "/etc_ro/mmi/cartoon_9.png"},
	{E_POWERONOFF_FRAME_10, "/etc_ro/mmi/cartoon_10.png"},
	{E_POWERONOFF_FRAME_11, "welcome"}
};
#endif

/********************************************************************************
  ȫ�ֱ�������
**********************************************************************************/
extern UINT32 g_smstask_enable;
extern UINT32 g_voicetask_enable;
extern E_zMmiShowMode g_showMode;


/********************************************************************************
  ȫ�ֱ�������
**********************************************************************************/
CHAR* g_mmiMainWinBgPath = "/etc_ro/mmi/background.png";
HWND  g_mmiMainWin = HWND_INVALID;
BITMAP g_mmiMainBg = {0};

PLOGFONT mmi_smallest_font = NULL;
PLOGFONT mmi_small_font = NULL;
PLOGFONT mmi_middle_twelve_font = NULL;
//PLOGFONT mmi_middle_fourteen_font = NULL;
PLOGFONT mmi_middle_sixteen_font = NULL;
/*PLOGFONT mmi_middle_twenty_font = NULL;*/
//PLOGFONT mmi_big_font = NULL;

/**********************************************************************************
  ��ʼ������
***********************************************************************************/
void mmi_InitFont()
{
#ifdef ENABLE_TTF_FONT	
	mmi_smallest_font = CreateLogFont(FONT_TYPE_NAME_SCALE_TTF, "song", "ISO8859-1",
	                                  FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                  FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 10, 0);
	mmi_small_font = CreateLogFont(FONT_TYPE_NAME_SCALE_TTF, "song", "ISO8859-1",
	                               FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                               FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 11, 0);
	mmi_middle_twelve_font = CreateLogFont(FONT_TYPE_NAME_SCALE_TTF, "song", "ISO8859-1",
	                                       FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                       FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 12, 0);


	mmi_middle_sixteen_font = CreateLogFont(FONT_TYPE_NAME_SCALE_TTF, "song", "ISO8859-1",
	                                        FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                        FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE,  16, 0);
#else
	mmi_smallest_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
	                                  FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                  FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 10, 0);
	mmi_small_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
	                               FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                               FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 11, 0);
	mmi_middle_twelve_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
	                                       FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                       FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 12, 0);
	mmi_middle_sixteen_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
	                                        FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
	                                        FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE,  16, 0);
#endif
	/*mmi_middle_twenty_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
		  FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
		  FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 20, 0);*/
	/*mmi_big_font = CreateLogFont(FONT_TYPE_NAME_BITMAP_RAW, "song", "GB2312",
		  FONT_WEIGHT_REGULAR, FONT_SLANT_ROMAN, FONT_SETWIDTH_NORMAL,
		  FONT_SPACING_CHARCELL, FONT_UNDERLINE_NONE, FONT_STRUCKOUT_NONE, 24, 0);*/

	slog(MMI_PRINT, SLOG_NORMAL, "zcore ZTE_MMI_InitFont finish!!!!!\n");
}

/* power on off*/
static SINT32 g_mmiCurrentFrameID = E_POWERONOFF_FRAME_0;
static BOOL showingPowerOn = TRUE;
static BOOL showingPowerOff = FALSE;
static BOOL showingPowerOffCharger = FALSE;

POWER_ON_OFF_CALLBACK_FUN g_PowerOnOffFun = NULL;

BOOL mmi_Ispoweron_state(VOID)
{
	return showingPowerOn;
}

VOID mmi_registerLcdPowerOnOff(POWER_ON_OFF_CALLBACK_FUN fun)
{
	g_PowerOnOffFun = fun;
}

VOID mmi_changePowerOnOffFrame(VOID)
{
	if (showingPowerOn) {
		g_mmiCurrentFrameID ++ ;
	}
	if (showingPowerOff) {
		g_mmiCurrentFrameID -- ;
	}
}

VOID mmi_showPowerOnOffFrame(HDC hdc)
{
	BITMAP bitmap = {0};
	SINT32 ret = -1;

#ifdef QRZL_UE
	if (showingPowerOn) {
		 if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_0 && g_mmiCurrentFrameID < E_POWERONOFF_FRAME_11) { //show poweron ani
			ret = LoadBitmapFromFile(hdc, &bitmap, g_powerOnFramTab[g_mmiCurrentFrameID].path);
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_showPowerOnOffFrame load bitmap path:%s!!!\n", g_powerOnFramTab[g_mmiCurrentFrameID].path);
			if (ret != 0) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI load bitmap failed path:%s!!!\n", g_powerOnFramTab[g_mmiCurrentFrameID].path);
				return;
			}
			FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &bitmap);
			UnloadBitmap(&bitmap);
		}

		if (g_mmiCurrentFrameID <= E_POWERONOFF_FRAME_10) {
			mmi_startLcdPowerOnOffTimer();
		} else {
			showingPowerOn = FALSE;
			mmi_set_update_flag(MMI_TASK_BATTERY);//update all app show
			mmi_set_update_flag(MMI_TASK_POWER);
			mmi_set_update_flag(MMI_TASK_WIFI);
			mmi_set_update_flag(MMI_TASK_LED_WPS);

			if (g_voicetask_enable)
				mmi_set_update_flag(MMI_TASK_VOIP);
			else if (g_smstask_enable)
				mmi_set_update_flag(MMI_TASK_SMS);

			mmi_set_update_flag(MMI_TASK_NET);
			mmi_set_update_flag(MMI_TASK_TIP);
			mmi_set_lcd_mode(MMI_ACTIVE_MODE);
		}
	}

	if (showingPowerOff) {

		//开机动画有11张图片 开机完成后 g_mmiCurrentFrameID 值为11, 而关机动画只有10张图片,从最后一张图片开始即 E_POWERONOFF_FRAME_9
		if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_10)
		{
			g_mmiCurrentFrameID = E_POWERONOFF_FRAME_9;
		}

		if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_0 && g_mmiCurrentFrameID < E_POWERONOFF_FRAME_10) { //show poweron ani
			ret = LoadBitmapFromFile(hdc, &bitmap, g_powerOffFramTab[g_mmiCurrentFrameID].path);
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_showPowerOnOffFrame load bitmap path:%s!!!\n", g_powerOffFramTab[g_mmiCurrentFrameID].path);
			if (ret != 0) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI load bitmap failed path:%s!!!\n", g_powerOffFramTab[g_mmiCurrentFrameID].path);
				return;
			}
			FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &bitmap);
			UnloadBitmap(&bitmap);
		}

		if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_0) {
			mmi_startLcdPowerOnOffTimer();
		} else {
			mmi_set_poweroff_charge_show(TRUE);
			showingPowerOff = FALSE;
			if (g_PowerOnOffFun != NULL) {
				slog(MMI_PRINT, SLOG_DEBUG, "mmi_showPowerOnOffFrame  g_PowerOnOffFun!\n");
				g_PowerOnOffFun();
			}
		}
	}

#else
	if (g_mmiCurrentFrameID == E_POWERONOFF_FRAME_11) { //show text "welcome"
		SINT32 tempID = g_mmiCurrentFrameID;
		RECT rect = {0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT};
		FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &g_mmiMainBg);
		SetBkMode(hdc, BM_TRANSPARENT);
		SelectFont(hdc, mmi_middle_sixteen_font);
		SetTextColor(hdc, PIXEL_lightwhite);
		DrawText(hdc, g_powerOnoffFramTab[tempID].path, strlen(g_powerOnoffFramTab[tempID].path), &rect, DT_SINGLELINE | DT_VCENTER | DT_CENTER);
		//DrawText(hdc, g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path, strlen(g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path), &rect, DT_SINGLELINE|DT_VCENTER|DT_CENTER);
	}
#if 1
	else if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_0 && g_mmiCurrentFrameID < E_POWERONOFF_FRAME_11) { //show poweronoff ani
		ret = LoadBitmapFromFile(hdc, &bitmap, g_powerOnoffFramTab[g_mmiCurrentFrameID].path);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_showPowerOnOffFrame load bitmap path:%s!!!\n", g_powerOnoffFramTab[g_mmiCurrentFrameID].path);
		if (ret != 0) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI load bitmap failed path:%s!!!\n", g_powerOnoffFramTab[g_mmiCurrentFrameID].path);
			return;
		}
		FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &bitmap);
		UnloadBitmap(&bitmap);
	}
	if (showingPowerOn) {
		if (g_mmiCurrentFrameID <= E_POWERONOFF_FRAME_10) {
			mmi_startLcdPowerOnOffTimer();
		} else if (g_mmiCurrentFrameID == E_POWERONOFF_FRAME_11) {
			mmi_startLcdPowerOnOffTextFrameTimer();
		} else {
			showingPowerOn = FALSE;
			mmi_set_update_flag(MMI_TASK_BATTERY);//update all app show
			mmi_set_update_flag(MMI_TASK_POWER);
			mmi_set_update_flag(MMI_TASK_WIFI);
			mmi_set_update_flag(MMI_TASK_LED_WPS);

			if (g_voicetask_enable)
				mmi_set_update_flag(MMI_TASK_VOIP);
			else if (g_smstask_enable)
				mmi_set_update_flag(MMI_TASK_SMS);

			mmi_set_update_flag(MMI_TASK_NET);
			mmi_set_update_flag(MMI_TASK_TIP);
			mmi_set_lcd_mode(MMI_ACTIVE_MODE);
		}
	}
	if (showingPowerOff) {
		if (g_mmiCurrentFrameID == E_POWERONOFF_FRAME_11) {
			mmi_startLcdPowerOnOffTextFrameTimer();
		} else if (g_mmiCurrentFrameID >= E_POWERONOFF_FRAME_0) {
			mmi_startLcdPowerOnOffTimer();
		} else {
			mmi_set_poweroff_charge_show(TRUE);
			showingPowerOff = FALSE;
			if (g_PowerOnOffFun != NULL) {
				slog(MMI_PRINT, SLOG_DEBUG, "mmi_showPowerOnOffFrame  g_PowerOnOffFun!\n");
				g_PowerOnOffFun();
			}
		}
	}
#endif
#endif

}

BOOL mmi_getShowingPowerOnInfo(VOID)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getShowingPowerOnInfo showingPowerOn:%d!!!\n", showingPowerOn);
	return showingPowerOn;
}

static CHAR* mmi_findStringFromMode(E_zMmi_Work_Mode workMode)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_PowerOnOffTipStringTab) / sizeof(T_PowerOnOffTipStringItem); ++ i) {
		if (g_PowerOnOffTipStringTab[i].workMode == workMode) {
			//return g_PowerOnOffTipStringTab[i].tipString;
			return (CHAR*)tp_i18n_get_text(g_PowerOnOffTipStringTab[i].tipString);
		}
	}
	return NULL;
}

VOID mmi_startPowerOnFrame()
{
	showingPowerOn = TRUE;
	showingPowerOff = FALSE;
	g_mmiCurrentFrameID = E_POWERONOFF_FRAME_0;
	g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path = mmi_findStringFromMode(MMI_POWERON_MODE);
}

VOID mmi_startFastPowerOnFrame()
{
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_startFastPowerOnFrame!! \n\n");
	mmi_set_poweroff_charge_show(FALSE);
	showingPowerOn = TRUE;
	showingPowerOff = FALSE;
	g_mmiCurrentFrameID = E_POWERONOFF_FRAME_0;
	g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path = mmi_findStringFromMode(MMI_POWERON_MODE);
	InvalidateRect(g_mmiMainWin, NULL, FALSE);
}

VOID mmi_startPowerOffFrame()
{
	showingPowerOn = FALSE;
	showingPowerOff = TRUE;
	g_mmiCurrentFrameID = E_POWERONOFF_FRAME_10;
	g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path = mmi_findStringFromMode(MMI_POWEROFF_MODE);
}


VOID mmi_startPowerResetFrame()
{
	showingPowerOn = FALSE;
	showingPowerOff = TRUE;
	g_mmiCurrentFrameID = E_POWERONOFF_FRAME_11;
	g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path = mmi_findStringFromMode(MMI_RESET_MODE);
}

VOID mmi_startPowerRestartFrame()
{
	showingPowerOn = FALSE;
	showingPowerOff = TRUE;
	g_mmiCurrentFrameID = E_POWERONOFF_FRAME_11;
	g_powerOnoffFramTab[E_POWERONOFF_FRAME_11].path = mmi_findStringFromMode(MMI_RESTART_MODE);
}
VOID mmi_startPowerOffChagerFrame()
{
	showingPowerOn = FALSE;
	showingPowerOff = FALSE;
	showingPowerOffCharger = TRUE;
}

//BITMAP testbitmap = {0};
/**********************************************************************************
  ʱ����洰�ڹ��̺���
***********************************************************************************/
SINT32 mmi_InfoWinProc(HWND hWnd, SINT32 message, WPARAM wParam, LPARAM lParam)
{
	//HDC hdc = HDC_INVALID;
	//slog(MMI_PRINT,SLOG_DEBUG,"zcore ZTE_MMI_InfoWinProc !!!!!message=0x%x\n",message);

	switch (message) {
	case MSG_CREATE: {
		break;
	}
	case MSG_PAINT: {
		HDC hdc = BeginPaint(g_mmiMainWin);

		//FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &testbitmap);

#if 1
		if (showingPowerOn || showingPowerOff) {
			mmi_showPowerOnOffFrame(hdc);
		} else {
			FillBoxWithBitmap(hdc, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &g_mmiMainBg);
			zCore_Set_SkipUpdateflag(TRUE);
			mmi_showLcd(hdc);
		}
#endif
		EndPaint(g_mmiMainWin, hdc);
		return 0;
	}
	case MSG_TIMER: {
		break;
	}
	case MSG_KEYDOWN:
	case MSG_KEYUP: {
		break;
	}
	case MSG_CLOSE: {
		DestroyMainWindowIndirect(hWnd);
		g_mmiMainWin = HWND_INVALID;
		break;
	}
	default: {
		break;
	}
	}
	return DefaultMainWinProc(hWnd, message, wParam, lParam);
}


/**********************************************************************************
��������
***********************************************************************************/

VOID mmi_MainWinCreate()
{
	MAINWINCREATE CreateInfo = {0};
	CreateInfo.dwStyle = WS_VISIBLE;
	CreateInfo.dwExStyle = WS_EX_NONE;
	CreateInfo.spCaption = "";
	CreateInfo.hHosting = HWND_DESKTOP;
	CreateInfo.MainWindowProc = mmi_InfoWinProc;
	CreateInfo.lx = 0;
	CreateInfo.ty = 0;
	CreateInfo.rx = MMI_LCD_WIDTH;
	CreateInfo.by = MMI_LCD_HEIGHT;
	CreateInfo.iBkColor = PIXEL_black;
	CreateInfo.dwAddData = 0;
	CreateInfo.dwReserved = 0;
	CreateInfo.hHosting = HWND_DESKTOP;
	g_mmiMainWin = CreateMainWindow(&CreateInfo);

	// ShowWindow(mmi_info_win, SW_SHOWNORMAL);
	slog(MMI_PRINT, SLOG_NORMAL, "zcore ZTE_MMI_InfoWinCreate finish!!!!!mmi_test_Win=0x%x\n", g_mmiMainWin);


}

extern OS_SEMA_ID g_mmi_init_sem_id;
extern OS_SEMA_ID g_mmi_gui_init_sem_id;

/**********************************************************************************
  LCD��ʼ����ں���
***********************************************************************************/
static VOID *lcd_main_entry(VOID *arg)
{
	MSG msg = {0};

	slog(MMI_PRINT, SLOG_DEBUG, "zcore ZTE_MMI_task_main_entry!!!!\n");
	prctl(PR_SET_NAME, "mmi_lcd_main", 0, 0, 0);
	InitGUI();
	//zOss_Sleep(15000);
	mmi_InitFont();
	LoadBitmapFromFile(HDC_SCREEN, &g_mmiMainBg, g_mmiMainWinBgPath);
	//LoadBitmapFromFile(HDC_SCREEN, &testbitmap, "c:\\mmi\\RGB.bmp");
	mmi_initLcdShowInfoTab();
	mmi_MainWinCreate();
	mmi_PutSemaphore(&g_mmi_init_sem_id);

	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
		mmi_PutSemaphore(&g_mmi_gui_init_sem_id);
	}

	while (GetMessage(&msg, g_mmiMainWin)) {
		TranslateMessage(&msg);
		DispatchMessage(&msg);
	} 
	return NULL;
}

VOID mmi_setMainWindToBg()
{
	RECT tmpRect = {0, 0, 128, 128};
	FillBoxWithBitmap(HDC_SCREEN, 0, 0, MMI_LCD_WIDTH, MMI_LCD_HEIGHT, &g_mmiMainBg);
	SDev_LcdDirectRefresh(&tmpRect);
}

HWND mmi_getMainWnd()
{
	return g_mmiMainWin;
}

VOID mmi_initLcd(BOOL isPowerOffChager)
{
	int ret = -1;
	pthread_t mmi_lcdmain_thread;
	if (isPowerOffChager) {
#ifdef QRZL_UE
		g_mmiMainWinBgPath = "/etc_ro/mmi/background-poweroff.png";
#endif
		mmi_startPowerOffChagerFrame();
	} else {
#ifdef QRZL_UE
		g_mmiMainWinBgPath = "/etc_ro/mmi/background.png";
#endif
		mmi_startPowerOnFrame();
	}

	ret = pthread_create(&mmi_lcdmain_thread, NULL, &lcd_main_entry, NULL);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMIcorem mmi_initLcd end ret = %d\n", ret);
}
#endif
