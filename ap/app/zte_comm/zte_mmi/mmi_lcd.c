/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#ifndef DISABLE_LCD
#include "mmi_lcd.h"

extern pthread_mutex_t g_mmi_refresh_lcd_mutex;
extern pthread_mutex_t g_mmi_poweron_mutex;
extern UINT32 g_mmi_poweroff_turnon_flag;

extern UINT32 g_show_pagefirst;
extern UINT32 g_show_pagethird;
extern BOOL mmi_is_offchg_poweroff;

extern SINT32 g_mmi_power_mode;//0:charge;1:poweron

/*****************************************************************************
 ȫ�ֱ�������
******************************************************************************/
static T_LcdConfigInfo g_LcdConfigInfoTab[] = {
#ifdef QRZL_UE
	//===========top bar==================================
	{LCD_SHOW_NET_SIGNAL, {0, 0, 44, 31}, LCD_SHOW_PICTURE},
	{LCD_SHOW_NET_CONNECT, {23, 15, 44, 31}, LCD_SHOW_PICTURE},
	{LCD_SHOW_LTE_HOTSPOT, {48, 0, 128, 31}, LCD_SHOW_PICTURE},
	{LCD_SHOW_WIFISTATION_CONNECT, {28, 2, 45, 25}, LCD_SHOW_PICTURE},
	{LCD_SHOW_SMS, {89, 33, 128, 64}, LCD_SHOW_PICTURE},
	{LCD_SHOW_SMS_NUM, {110, 50, 124, 64}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI, {48, 33, 87, 64}, LCD_SHOW_PICTURE},
	{LCD_SHOW_TIP_NEW_VERSION, {89, 0, 106, 23}, LCD_SHOW_PICTURE},
	{LCD_SHOW_POWER, {89, 4, 106, 20}, LCD_SHOW_TEXT},
	{LCD_SHOW_BATTERY, {0, 33, 44, 64}, LCD_SHOW_PICTURE},
	//===================== ==PAGE1=======================
	//================MIDDLE==============================
	{LCD_SHOW_CMCC, {0, 72, 128, 106}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_WIFISTA_SSID, {0, 72, 128, 106}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_SIM_STATE, {0, 72, 128, 106}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_WPS_ACTIVE, {0, 72, 128, 106}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_NET_CONNECTING, {0, 72, 128, 106}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_NET_PROVIDER, {0, 72, 128, 106}, LCD_SHOW_TEXT},

	{LCD_SHOW_TIP_UPDATE_INFO, {0, 72, 128, 106}, LCD_SHOW_TEXT}, //zk add for fotaupdate result
#ifdef QRZL_CUSTOMER_XIANJI
	{LCD_SHOW_NET_PROVIDER_LOGO, {0, 64, 128, 128}, LCD_SHOW_PICTURE},
#endif

	//================TEMP================================
	{LCD_SHOW_WIFI_SSID, {0, 72, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI_PASSWORD, {0, 100, 128, 118}, LCD_SHOW_TEXT},
	//===================PAGE SSID2 WIFI KEY==============================
	{LCD_SHOW_WIFI_SSID2, {0, 72, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI_PASSWORD2, {0, 100, 128, 118}, LCD_SHOW_TEXT},
	//===================PAGE CODE2==============================
	{LCD_SHOW_WIFI_CODE2, {0, 0, 128, 128}, LCD_SHOW_PICTURE},


	//==================PAGE4================================
	{LCD_SHOW_TRAFFIC, {1, 74, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_TRAFFIC_BAR, {3, 94, 125, 103}, LCD_SHOW_RECT},
	{LCD_SHOW_TRAFFIC_SLIDER, {3, 94, 3, 103}, LCD_SHOW_BOX},

	{LCD_SHOW_TRAFFIC_WARING, {0, 94, 0, 103}, LCD_SHOW_BOX},
	//===================PAGE3==============================
	{LCD_SHOW_WIFI_CODE, {0, 0, 128, 128}, LCD_SHOW_PICTURE},

	{LCD_SHOW_POWER_OFF_CHARGER, {0, 0, 128, 128}, LCD_SHOW_PICTURE},
#else
	//===========top bar==================================
	{LCD_SHOW_NET_SIGNAL, {1, 0, 26, 24}, LCD_SHOW_PICTURE},
	{LCD_SHOW_NET_CONNECT, {28, 2, 45, 25}, LCD_SHOW_PICTURE},
	{LCD_SHOW_WIFISTATION_CONNECT, {28, 2, 45, 25}, LCD_SHOW_PICTURE},
	{LCD_SHOW_SMS, {48, 0, 65, 25}, LCD_SHOW_PICTURE}, //{48,2,65,25}
	{LCD_SHOW_SMS_NUM, {53, 11, 67, 25}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI, {67, 2, 87, 23}, LCD_SHOW_PICTURE},
	{LCD_SHOW_TIP_NEW_VERSION, {89, 0, 106, 23}, LCD_SHOW_PICTURE},
	{LCD_SHOW_POWER, {89, 4, 106, 20}, LCD_SHOW_TEXT},
	{LCD_SHOW_BATTERY, {108, 0, 126, 23}, LCD_SHOW_PICTURE},
	//===================== ==PAGE1=======================
	//================MIDDLE==============================
	{LCD_SHOW_CMCC, {0, 38, 128, 72}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_WIFISTA_SSID, {0, 38, 128, 72}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_SIM_STATE, {0, 38, 128, 72}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_WPS_ACTIVE, {0, 38, 128, 72}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_NET_CONNECTING, {0, 38, 128, 72}, LCD_SHOW_TEXT},
	{LCD_SHOW_TIP_NET_PROVIDER, {0, 38, 128, 72}, LCD_SHOW_TEXT},

	{LCD_SHOW_TIP_UPDATE_INFO, {0, 38, 128, 72}, LCD_SHOW_TEXT}, //zk add for fotaupdate result


	//================TEMP================================
	{LCD_SHOW_WIFI_SSID, {0, 72, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI_PASSWORD, {0, 100, 128, 118}, LCD_SHOW_TEXT},
	//===================PAGE SSID2 WIFI KEY==============================
	{LCD_SHOW_WIFI_SSID2, {0, 72, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_WIFI_PASSWORD2, {0, 100, 128, 118}, LCD_SHOW_TEXT},
	//===================PAGE CODE2==============================
	{LCD_SHOW_WIFI_CODE2, {0, 0, 128, 128}, LCD_SHOW_PICTURE},


	//==================PAGE4================================
	{LCD_SHOW_TRAFFIC, {1, 74, 128, 90}, LCD_SHOW_TEXT},
	{LCD_SHOW_TRAFFIC_BAR, {3, 94, 125, 103}, LCD_SHOW_RECT},
	{LCD_SHOW_TRAFFIC_SLIDER, {3, 94, 3, 103}, LCD_SHOW_BOX},

	{LCD_SHOW_TRAFFIC_WARING, {0, 94, 0, 103}, LCD_SHOW_BOX},
	//===================PAGE3==============================
	{LCD_SHOW_WIFI_CODE, {0, 0, 128, 128}, LCD_SHOW_PICTURE},

	{LCD_SHOW_POWER_OFF_CHARGER, {0, 0, 128, 128}, LCD_SHOW_PICTURE},
#endif
};

/*��ʾ�ܿ���Ϣ�ṹ��*/
T_LcdShowInfoItem g_LcdShowInfoTab[] = {
	//===============================top bar=================================================
	{LCD_SHOW_NET_SIGNAL, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_NET_CONNECT, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
#ifdef QRZL_UE
	{LCD_SHOW_LTE_HOTSPOT, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
#endif
	{LCD_SHOW_WIFISTATION_CONNECT, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_SMS, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_SMS_NUM, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_WIFI, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_NEW_VERSION, NULL, NULL, NULL, DT_CENTER, 0, TRUE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_POWER, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_BATTERY, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	//===============================middle=================================================
	{LCD_SHOW_CMCC, NULL, NULL, NULL, DT_CENTER | DT_VCENTER, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_WIFISTA_SSID, NULL, NULL, NULL, DT_CENTER | DT_VCENTER, 0, FALSE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_SIM_STATE, NULL, NULL, NULL, DT_CENTER, 0, FALSE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_WPS_ACTIVE, NULL, NULL, NULL, DT_CENTER, 0, FALSE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_NET_CONNECTING, NULL, NULL, NULL, DT_CENTER, 0, FALSE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_TIP_NET_PROVIDER, NULL, NULL, NULL, DT_CENTER, 0, FALSE, FALSE, LCD_SHOW_INVLAID, 0xffffffff, 0},

	{LCD_SHOW_TIP_UPDATE_INFO, NULL, NULL, NULL, DT_CENTER | DT_VCENTER, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},//zk add for fotaupdate result
#if defined(QRZL_UE) && defined(QRZL_CUSTOMER_XIANJI)
	{LCD_SHOW_NET_PROVIDER_LOGO, NULL, NULL, NULL, DT_CENTER, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
#endif

	//===============================PAGE2=================================================
	{LCD_SHOW_WIFI_SSID, NULL, NULL, NULL, DT_LEFT, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_WIFI_PASSWORD, NULL, NULL, NULL, DT_LEFT, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	//===============================PAGE4 SSID2 WIFI KEY=================================================
	{LCD_SHOW_WIFI_SSID2, NULL, NULL, NULL, DT_LEFT, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	{LCD_SHOW_WIFI_PASSWORD2, NULL, NULL, NULL, DT_LEFT, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},
	//=================================PAGE5 CODE2=======================================================
	{LCD_SHOW_WIFI_CODE2, NULL, NULL, NULL, DT_CENTER, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},

	//================================PAGE1====================================================
	{LCD_SHOW_TRAFFIC, NULL, NULL, NULL, DT_RIGHT, 0, FALSE, TRUE, LCD_SHOW_TEXT, 0xffffffff, 0},
	{LCD_SHOW_TRAFFIC_BAR, NULL, NULL, NULL, DT_CENTER, 0, FALSE, TRUE, LCD_SHOW_BOX, 0xffffffff, 0},
	{LCD_SHOW_TRAFFIC_SLIDER, NULL, NULL, NULL, DT_CENTER, 0, FALSE, TRUE, LCD_SHOW_RECT, 0xffffffff, 0},
	{LCD_SHOW_TRAFFIC_WARING, NULL, NULL, NULL, DT_CENTER, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},

	//=================================PAGE3=======================================================
	{LCD_SHOW_WIFI_CODE, NULL, NULL, NULL, DT_CENTER, 0, FALSE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},

	{LCD_SHOW_POWER_OFF_CHARGER, NULL, NULL, NULL, DT_CENTER | DT_VCENTER | DT_SINGLELINE, 0, TRUE, TRUE, LCD_SHOW_INVLAID, 0xffffffff, 0},

};

/*����Ϣ���ñ�*/
typedef struct {
	E_zMmi_Sms_Recvbox_Status status;
	CHAR* path;
	BOOL  isNeedReload;
	BITMAP bmp;

} T_SmsConfigInfo;

static T_SmsConfigInfo g_SmsConfigInfoTab[] = {
	{SMS_RECVBOX_STATUS_UNREAD, "sms_unread_2.png", FALSE, {0}},
	{SMS_RECVBOX_STATUS_NEW, "sms_unread_2.png", FALSE, {0}},
#ifdef QRZL_UE
	{SMS_RECVBOX_STATUS_FULL, "sms_full.png", FALSE, {0}},
	{SMS_RECVBOX_STATUS_NOR, "sms_unread.png", FALSE, {0}}
#else
	{SMS_RECVBOX_STATUS_FULL, "sms_full.png", FALSE, {0}}
#endif
};

/*��ص������ñ�*/
typedef enum {
	E_BATTERY_LEVEL_NULL,
	E_BATTERY_LEVEL_LOWER,
	E_BATTERY_LEVEL_0,
	E_BATTERY_LEVEL_1,
	E_BATTERY_LEVEL_2,
	E_BATTERY_LEVEL_3,
	E_BATTERY_LEVEL_4,
#ifdef QRZL_UE
	E_BATTERY_LEVEL_5,
#endif
	E_BATTERY_LEVEL_OVERVOL,
	E_BATTERY_LEVEL_CHARGER
} E_BATTERY_LEVER;

typedef struct {
	E_BATTERY_LEVER status;
	SINT32 timer_period;
	CHAR* path;
	BOOL  isNeedReload;
	BITMAP bmp;
} T_BatteryConfigInfo;

static T_BatteryConfigInfo g_lcdBatteryConfigInfo[] = {
	{E_BATTERY_LEVEL_NULL, 1000, "battery_null.png", FALSE, {0}},
	{E_BATTERY_LEVEL_LOWER, 0, "battery_low.png", FALSE, {0}},
	{E_BATTERY_LEVEL_0, 0, "battery_0.png", FALSE, {0}},
	{E_BATTERY_LEVEL_1, 0, "battery_1.png", FALSE, {0}},
	{E_BATTERY_LEVEL_2, 0, "battery_2.png", FALSE, {0}},
	{E_BATTERY_LEVEL_3, 0, "battery_3.png", FALSE, {0}},
	{E_BATTERY_LEVEL_4, 0, "battery_4.png", FALSE, {0}},
#ifdef QRZL_UE
	{E_BATTERY_LEVEL_5, 0, "battery_5.png", FALSE, {0}},
	{E_BATTERY_LEVEL_OVERVOL, 0, "overvoltage.bmp", FALSE, {0}},
#else
	{E_BATTERY_LEVEL_OVERVOL, 0, "overvoltage.png", FALSE, {0}},
#endif
	{E_BATTERY_LEVEL_CHARGER, 1000, NULL, FALSE, {0}}
};

static T_BatteryConfigInfo g_lcdPowerOffBatteryConfigInfo[] = {
	{E_BATTERY_LEVEL_NULL, 0, NULL, FALSE, {0}},
	{E_BATTERY_LEVEL_LOWER, 0, NULL, FALSE, {0}},
	{E_BATTERY_LEVEL_0, 0, "poweroff_charging0.png", TRUE, {0}},
	{E_BATTERY_LEVEL_1, 0, "poweroff_charging1.png", TRUE, {0}},
	{E_BATTERY_LEVEL_2, 0, "poweroff_charging2.png", TRUE, {0}},
	{E_BATTERY_LEVEL_3, 0, "poweroff_charging3.png", TRUE, {0}},
	{E_BATTERY_LEVEL_4, 0, "poweroff_charging4.png", TRUE, {0}},
#ifdef QRZL_UE
	{E_BATTERY_LEVEL_5, 0, "poweroff_charging5.png", TRUE, {0}},
#endif
	{E_BATTERY_LEVEL_CHARGER, 0, NULL, TRUE, {0}}
};

/*�����������ñ�*/
typedef struct {
	E_zMmi_Net_Mode netMode;
	SINT32 sigLevel;
	BOOL isRoam;
	CHAR *path;
	BOOL  isNeedReload;
	BITMAP bmp;
} T_NetSignalConfigInfo;

static T_NetSignalConfigInfo g_lcdNetSignalConfigInfoTab[] = {
	{NET_MODE_4G, 5, FALSE, "4Gsignal5.png", FALSE, {0}},
	{NET_MODE_4G, 4, FALSE, "4Gsignal4.png", FALSE, {0}},
	{NET_MODE_4G, 3, FALSE, "4Gsignal3.png", FALSE, {0}},
	{NET_MODE_4G, 2, FALSE, "4Gsignal2.png", FALSE, {0}},
	{NET_MODE_4G, 1, FALSE, "4Gsignal1.png", FALSE, {0}},
	{NET_MODE_4G, 0, FALSE, "4Gsignal0.png", FALSE, {0}},
	{NET_MODE_4G, 5, TRUE, "4G_R_5.png", FALSE, {0}},
	{NET_MODE_4G, 4, TRUE, "4G_R_4.png", FALSE, {0}},
	{NET_MODE_4G, 3, TRUE, "4G_R_3.png", FALSE, {0}},
	{NET_MODE_4G, 2, TRUE, "4G_R_2.png", FALSE, {0}},
	{NET_MODE_4G, 1, TRUE, "4G_R_1.png", FALSE, {0}},
	{NET_MODE_4G, 0, TRUE, "4G_R_0.png", FALSE, {0}},
	{NET_MODE_3G, 5, FALSE, "3Gsignal5.png", FALSE, {0}},
	{NET_MODE_3G, 4, FALSE, "3Gsignal4.png", FALSE, {0}},
	{NET_MODE_3G, 3, FALSE, "3Gsignal3.png", FALSE, {0}},
	{NET_MODE_3G, 2, FALSE, "3Gsignal2.png", FALSE, {0}},
	{NET_MODE_3G, 1, FALSE, "3Gsignal1.png", FALSE, {0}},
	{NET_MODE_3G, 0, FALSE, "3Gsignal0.png", FALSE, {0}},
	{NET_MODE_3G, 5, TRUE, "3G_R_5.png", FALSE, {0}},
	{NET_MODE_3G, 4, TRUE, "3G_R_4.png", FALSE, {0}},
	{NET_MODE_3G, 3, TRUE, "3G_R_3.png", FALSE, {0}},
	{NET_MODE_3G, 2, TRUE, "3G_R_2.png", FALSE, {0}},
	{NET_MODE_3G, 1, TRUE, "3G_R_1.png", FALSE, {0}},
	{NET_MODE_3G, 0, TRUE, "3G_R_0.png", FALSE, {0}},
#ifndef QRZL_UE
	{NET_MODE_2G, 5, FALSE, "2Gsignal5.png", FALSE, {0}},
	{NET_MODE_2G, 4, FALSE, "2Gsignal4.png", FALSE, {0}},
	{NET_MODE_2G, 3, FALSE, "2Gsignal3.png", FALSE, {0}},
	{NET_MODE_2G, 2, FALSE, "2Gsignal2.png", FALSE, {0}},
	{NET_MODE_2G, 1, FALSE, "2Gsignal1.png", FALSE, {0}},
	{NET_MODE_2G, 0, FALSE, "2Gsignal0.png", FALSE, {0}},
	{NET_MODE_2G, 5, TRUE, "2G_R_5.png", FALSE, {0}},
	{NET_MODE_2G, 4, TRUE, "2G_R_4.png", FALSE, {0}},
	{NET_MODE_2G, 3, TRUE, "2G_R_3.png", FALSE, {0}},
	{NET_MODE_2G, 2, TRUE, "2G_R_2.png", FALSE, {0}},
	{NET_MODE_2G, 1, TRUE, "2G_R_1.png", FALSE, {0}},
	{NET_MODE_2G, 0, TRUE, "2G_R_0.png", FALSE, {0}},
#endif
	{NET_MODE_NOSERVICE, 0, TRUE, "signalx.png", FALSE, {0}},
	{NET_MODE_LIMITSERVICE, 0, TRUE, "signalx.png", FALSE, {0}},
	{NET_MODE_DEFAULT, 0, TRUE, "signalx.png", FALSE, {0}},
};

static CHAR* g_lcdNetConnectPicPath = "download.png";
static BITMAP g_lcdNetConnectBmp = {0};

#ifdef QRZL_UE
static CHAR* g_lcdLteHotSpotPicPath = "wxh.png";
static BITMAP g_lcdLteHotSpotBmp = {0};

#ifdef QRZL_CUSTOMER_XIANJI
static CHAR* g_lcdNetProviderLogoPicPath = "net_provider_logo.png";
static BITMAP g_lcdNetProviderLogoBmp = {0};
#endif
#endif

/*Wifi�������ñ�*/
typedef struct {
	E_zMmi_Wifi_State wifiState;
	SINT32 connected_userNum;
	CHAR *path;
	BOOL  isNeedReload;
	BITMAP bmp;
} T_WifiConfigInfo;

static T_WifiConfigInfo g_lcdWifiConfigInfoTab[] = {
	{WIFI_OFF, 0, "wifi_x.png", FALSE, {0}},
	{WIFI_ON, 0, "wifi_0.png", FALSE, {0}},
	{WIFI_ON, 1, "wifi_1.png", FALSE, {0}},
	{WIFI_ON, 2, "wifi_2.png", FALSE, {0}},
	{WIFI_ON, 3, "wifi_3.png", FALSE, {0}},
	{WIFI_ON, 4, "wifi_4.png", FALSE, {0}},
	{WIFI_ON, 5, "wifi_5.png", FALSE, {0}},
	{WIFI_ON, 6, "wifi_6.png", FALSE, {0}},
	{WIFI_ON, 7, "wifi_7.png", FALSE, {0}},
	{WIFI_ON, 8, "wifi_8.png", FALSE, {0}},
	{WIFI_ON, 9, "wifi_9.png", FALSE, {0}},
	{WIFI_ON, 10, "wifi_10.png", FALSE, {0}},
	{WIFI_ON, 11, "wifi_11.png", FALSE, {0}},
	{WIFI_ON, 12, "wifi_12.png", FALSE, {0}},
	{WIFI_ON, 13, "wifi_13.png", FALSE, {0}},
	{WIFI_ON, 14, "wifi_14.png", FALSE, {0}},
	{WIFI_ON, 15, "wifi_15.png", FALSE, {0}},
	{WIFI_ON, 16, "wifi_16.png", FALSE, {0}},
	{WIFI_ON, 17, "wifi_17.png", FALSE, {0}},
	{WIFI_ON, 18, "wifi_18.png", FALSE, {0}},
	{WIFI_ON, 19, "wifi_19.png", FALSE, {0}},
	{WIFI_ON, 20, "wifi_20.png", FALSE, {0}},
	{WIFI_ON, 21, "wifi_21.png", FALSE, {0}},
	{WIFI_ON, 22, "wifi_22.png", FALSE, {0}},
	{WIFI_ON, 23, "wifi_23.png", FALSE, {0}},
	{WIFI_ON, 24, "wifi_24.png", FALSE, {0}},
	{WIFI_ON, 25, "wifi_25.png", FALSE, {0}},
	{WIFI_ON, 26, "wifi_26.png", FALSE, {0}},
	{WIFI_ON, 27, "wifi_27.png", FALSE, {0}},
	{WIFI_ON, 28, "wifi_28.png", FALSE, {0}},
	{WIFI_ON, 29, "wifi_29.png", FALSE, {0}},
	{WIFI_ON, 30, "wifi_30.png", FALSE, {0}},
	{WIFI_ON, 31, "wifi_31.png", FALSE, {0}},
	{WIFI_ON, 32, "wifi_32.png", FALSE, {0}}
};

typedef struct {
	E_zMmi_WifiStation_State wifistationState;
	SINT32 sigLevel;
	CHAR *path;
	BOOL  isNeedReload;
	BITMAP bmp;

} T_WifiStationConfigInfo;

static T_WifiStationConfigInfo g_lcdWifiStationConfigInfoTab[] = {
	{WIFISTATION_ON, 0, "/etc_ro/mmi/wifi_sig_0.png", FALSE, {0}},
	{WIFISTATION_ON, 1, "/etc_ro/mmi/wifi_sig_1.png", FALSE, {0}},
	{WIFISTATION_ON, 2, "/etc_ro/mmi/wifi_sig_2.png", FALSE, {0}},
	{WIFISTATION_ON, 3, "/etc_ro/mmi/wifi_sig_3.png", FALSE, {0}},
	{WIFISTATION_ON, 4, "/etc_ro/mmi/wifi_sig_4.png", FALSE, {0}}
};

typedef struct {
	E_zMmi_WifiCode_State wificodeState;
	CHAR *path;
	BOOL  isNeedReload;
	BITMAP bmp;
} T_WifiCodeConfigInfo;

static T_WifiCodeConfigInfo g_lcdWifiCodeConfigInfoTab[] = {
#if defined(QRZL_UE) && defined(QRZL_CUSTOMER_XIANJI)
	{WIFICODE_MAIN, "/etc_ro/mmi/user_guide.png", TRUE, {0}},
	{WIFICODE_GUST1, "/etc_ro/mmi/user_guide.png", TRUE, {0}}
#else
	{WIFICODE_MAIN, WIFICODE_MAIN_PATH, TRUE, {0}},
	{WIFICODE_GUST1, WIFICODE_GUST1_PATH, TRUE, {0}}
#endif
};


/*****************************��ʾ��Ϣ��ض���******************************************/
CHAR* g_mmiNewVersionBmpPath =  "fota_make_ready_0.png";
BITMAP g_mmiNewVersionBmp = {0};

typedef struct {
	E_zMmi_Sim_Tip sim_state;
	CHAR* tipString;
} T_SimTipStringItem;

T_SimTipStringItem g_simStaTipStringTab[] = {
	{INSERT_SIM, "Insert SIM"},
	{PIN_LOCK, "PIN Lock"},
	{PUK_LOCK, "PUK Lock"},
	{SIM_BUSY, "SIM Busy"},
	{INVALID_SIM, "Invalid SIM"},
	{SIM_LOCK, "SIM Lock"}
};

typedef struct {
	E_zMmi_WpsAct_Tip wps_state;
	CHAR* tipString;
} T_WpsActTipStringItem;

T_WpsActTipStringItem g_wpsActTipStringTab[] = {
	{WPS_ACTIVING, "WPS Active Waiting"},
	{WPS_DEACTIVING, "WPS Deactive Waiting"},
	{WPS_ACTIVED, "WPS Active"},
	{WPS_DEACTIVED, "WPS Deactive"},
	{WPS_FAIL, "WPS Start Fail"},

	{WPS_KEY_BAND, "Band Switching"},
};

typedef struct {
	E_zMmi_NetCon_Tip net_con;
	CHAR* tipString;
} T_NetConTipStringItem;

T_NetConTipStringItem g_netContTipStringTab[] = {
	{NET_CONNECTING, "Connecting..."},
	{NET_DISCONNECTING, "Disconnecting..."},
	{NET_NOSERVICE, "No Service"},
	{NET_LIMITSERVICE, "Limited Service"},
	{NET_SEARCHING, "SIM Busy"},
};


typedef struct {
	E_zMmi_Poc_State state;
	CHAR* tips;
} T_powerOffChargerTipsItem;

static T_powerOffChargerTipsItem g_powerOffChargerLowBatteryTab[] = {
	{POC_STATE_LOWBATTERY, "Low Battery"},
	{POC_STATE_NOBATTERY, "No Battery"}
};

typedef struct {
	E_zMMI_Fota_Tip fota_tip;
	CHAR* tipString;
} T_FotaTipStringItem;

T_FotaTipStringItem g_FotaTipStringTab[] = {
	{FOTA_DOWNLOADING, "Downloading"},
	{FOTA_DOWNLOAD_FAILED, "Download failed"},
	{FOTA_DOWNLOAD_OK, "Prepare to restart"},
	{FOTA_DOWNLOAD_LOWBATTERY, "Low battery, please charge before update"},
	{FOTA_UPDATE_SUCCESS, "Update success"},
	{FOTA_UPDATE_FAILED, "Update failed"}
};


#define STRING_SSID 			"SSID:"
#define STRING_SSID1 			"SSID1:"
#define STRING_SSID2 			"SSID2:"

#define STRING_SSID_5G 			"SSID_5G:"
#define STRING_SSID1_5G 		"SSID1_5G:"
#define STRING_SSID2_5G			"SSID2_5G:"

#define STRING_WIFIKEY		 	"KEY:"

//extern PLOGFONT mmi_middle_fourteen_font;
extern PLOGFONT mmi_smallest_font;
extern PLOGFONT mmi_small_font;
extern PLOGFONT mmi_middle_twelve_font;
extern PLOGFONT mmi_middle_sixteen_font;

/*****************************************************************************
 ���ñ���
******************************************************************************/
/* ��ʾͼƬ*/
//static char* g_mmi_bitmem = NULL;
static VOID mmi_showLcdPicture(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	BITMAP tempBitmap = {0};
	VOID * temp = malloc((UINT32)(showInfo.bitmap->bmHeight * showInfo.bitmap->bmWidth * showInfo.bitmap->bmBytesPerPixel));
	if(temp == NULL)
	{
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_showLcdPicture no mem!!!\n");
		return;
	}
	memcpy((VOID *)temp, (const VOID *)(showInfo.bitmap->bmBits), (UINT32)(showInfo.bitmap->bmHeight * showInfo.bitmap->bmWidth * showInfo.bitmap->bmBytesPerPixel));
	memcpy((VOID *)(&tempBitmap), (const VOID *)showInfo.bitmap, sizeof(BITMAP));
	tempBitmap.bmBits = temp;
	FillBoxWithBitmap(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, \
	                  g_LcdConfigInfoTab[showInfo.item].rect.top, RECTW(g_LcdConfigInfoTab[showInfo.item].rect), \
	                  RECTH(g_LcdConfigInfoTab[showInfo.item].rect), &tempBitmap);
	free(temp);
}

/* ��ʾ����*/
static BOOL g_ssid_need_scroll = FALSE;
static BOOL g_wifikey_need_scroll = FALSE;
static BOOL g_ssid2_need_scroll = FALSE;
static BOOL g_wifikey2_need_scroll = FALSE;
//static BOOL g_ssid1_need_scroll = FALSE;
//static BOOL g_wifikey1_need_scroll = FALSE;
static BOOL g_main_quota_need_scroll = FALSE;
//static BOOL g_bonus_quota_need_scroll = FALSE;
//static BOOL g_isdn_need_scroll = FALSE;
static BOOL g_wifista_ssid_need_scroll = FALSE;

static SINT32 iPos_ssid = 0;
static SINT32 iPos_wifikey = 0;
static SINT32 iPos_ssid2 = 0;
static SINT32 iPos_wifikey2 = 0;
//static SINT32 iPos_ssid1 = 0;
//static SINT32 iPos_wifikey1 = 0;
static SINT32 iPos_main_quota = 0;
//static SINT32 iPos_bonus_quota = 0;
//static SINT32 iPos_isdn = 0;
static SINT32 iPos_wifista_ssid = 0;

SINT32 mmi_scroll_text_timer_fb()
{
	if (g_ssid_need_scroll || g_wifikey_need_scroll || g_ssid2_need_scroll || g_wifikey2_need_scroll/* ||g_ssid1_need_scroll || g_wifikey1_need_scroll*/) {
		mmi_set_update_flag(MMI_TASK_SSID);
	} else if (g_main_quota_need_scroll) {
		mmi_set_update_flag(MMI_TASK_TRAFFIC);
	} else if (g_wifista_ssid_need_scroll) {
		mmi_set_update_flag(MMI_TASK_TIP_WIFISTATION);
	}
	return 0;
}

/*�Ƿ���Ҫ������ʾ*/
BOOL isNeedScroll(T_LcdShowInfoItem showInfo)
{
	if (showInfo.item == LCD_SHOW_WIFI_SSID || showInfo.item == LCD_SHOW_WIFI_PASSWORD ||
	    showInfo.item == LCD_SHOW_WIFI_SSID2 || showInfo.item == LCD_SHOW_WIFI_PASSWORD2/*||
        showInfo.item == LCD_SHOW_WIFI_PASSWORD1 || showInfo.item == LCD_SHOW_WIFI_SSID1 */) {
		if (showInfo.textLen > MMI_LCD_CHAR8_MAX_LEN) {
			return TRUE;
		}
	} else {
		if (showInfo.textLen > MMI_LCD_CHAR9_MAX_LEN) {
			return TRUE;
		}
	}
	return FALSE;
}

static SINT32 mmi_showWifiInfoText(T_LcdShowInfoItem showInfo, HDC hdc)
{
	if (isNeedScroll(showInfo)) {
		if (showInfo.item == LCD_SHOW_WIFI_SSID) {
			g_ssid_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER, &iPos_ssid);
		} else if (showInfo.item == LCD_SHOW_WIFI_PASSWORD) {
			g_wifikey_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER, &iPos_wifikey);
		} else if (showInfo.item == LCD_SHOW_WIFI_SSID2) {
			g_ssid2_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER, &iPos_ssid2);
		} else if (showInfo.item == LCD_SHOW_WIFI_PASSWORD2) {
			g_wifikey2_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER, &iPos_wifikey2);
		}

		/*else if(showInfo.item == LCD_SHOW_WIFI_SSID1)
		{
			g_ssid1_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT|DT_VCENTER, &iPos_ssid1);
		}
		else if(showInfo.item == LCD_SHOW_WIFI_PASSWORD1)
		{
			g_wifikey1_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT|DT_VCENTER, &iPos_wifikey1);
		}*/
		//#if MMI_SHOW_PAGE_THIRD_ON
		else if (g_show_pagefirst && showInfo.item == LCD_SHOW_TRAFFIC) {
			g_main_quota_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER, &iPos_main_quota);
		}
		//#endif
		else if (showInfo.item == LCD_SHOW_TIP_WIFISTA_SSID) {
			g_wifista_ssid_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT | DT_VCENTER | DT_SINGLELINE, &iPos_wifista_ssid);
		}
		/*else if(showInfo.item == LCD_SHOW_TRAFFIC1)
		{
			g_bonus_quota_need_scroll = TRUE;
			DrawScrollText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_LEFT|DT_VCENTER, &iPos_bonus_quota);
		}*/
		mmi_startLcdShowScrollSSIDTimer(mmi_scroll_text_timer_fb);
	} else {
		if (showInfo.item == LCD_SHOW_WIFI_SSID) {
			iPos_ssid = 0;
			g_ssid_need_scroll = FALSE;
		} else if (showInfo.item == LCD_SHOW_WIFI_PASSWORD) {
			iPos_wifikey = 0;
			g_wifikey_need_scroll = FALSE;
		} else if (showInfo.item == LCD_SHOW_WIFI_SSID2) {
			iPos_ssid2 = 0;
			g_ssid2_need_scroll = FALSE;
		} else if (showInfo.item == LCD_SHOW_WIFI_PASSWORD2) {
			iPos_wifikey2 = 0;
			g_wifikey2_need_scroll = FALSE;
		}
		/*else if(showInfo.item == LCD_SHOW_WIFI_SSID1)
		{
			iPos_ssid1 = 0;
			g_ssid1_need_scroll = FALSE;
		}
		else if(showInfo.item == LCD_SHOW_WIFI_PASSWORD1)
		{
			iPos_wifikey1= 0;
			g_wifikey1_need_scroll = FALSE;
		}*/
		//#if MMI_SHOW_PAGE_THIRD_ON
		else if (g_show_pagefirst && showInfo.item == LCD_SHOW_TRAFFIC) { //corem
			iPos_main_quota = 0;
			g_main_quota_need_scroll = FALSE;
		}
		//#endif
		/*else if(showInfo.item == LCD_SHOW_TRAFFIC1)
		{
			iPos_bonus_quota= 0;
			g_bonus_quota_need_scroll= FALSE;
		}*/
		else if (showInfo.item == LCD_SHOW_TIP_WIFISTA_SSID) {
			iPos_wifista_ssid = 0;
			g_wifista_ssid_need_scroll = FALSE;
		}
		if (!g_ssid_need_scroll && !g_wifikey_need_scroll && !g_ssid2_need_scroll
		    && !g_wifikey2_need_scroll/*&& !g_ssid1_need_scroll && !g_wifikey1_need_scroll */
		    && !g_main_quota_need_scroll && ! g_wifista_ssid_need_scroll/*&& !g_isdn_need_scroll&& !g_bonus_quota_need_scroll*/) {
			mmi_stopLcdShowScrollSSIDTimer();
		}
		DrawText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), showInfo.nFormat);
	}
	return 0;
}
static SINT32 mmi_showLcdText(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	SetBkMode(hdc, BM_TRANSPARENT);
	SelectFont(hdc, showInfo.font);
	if (showInfo.color != 0) {
		SetTextColor(hdc, showInfo.color);
	} else {
		SetTextColor(hdc, PIXEL_lightwhite);
	}

	if (showInfo.item == LCD_SHOW_WIFI_SSID || showInfo.item == LCD_SHOW_WIFI_PASSWORD ||
	    showInfo.item == LCD_SHOW_WIFI_SSID2 || showInfo.item == LCD_SHOW_WIFI_PASSWORD2/* || */

	    //#if MMI_SHOW_PAGE_THIRD_ON
	    /* showInfo.item == LCD_SHOW_WIFI_SSID1|| showInfo.item == LCD_SHOW_WIFI_PASSWORD1*/ ||
	    (g_show_pagefirst && showInfo.item == LCD_SHOW_TRAFFIC)/*|| showInfo.item == LCD_SHOW_TRAFFIC1 ||*/
	    // #endif
	    || showInfo.item == LCD_SHOW_TIP_WIFISTA_SSID) {
		mmi_showWifiInfoText(showInfo, hdc);
	} else if (showInfo.item == LCD_SHOW_TIP_WPS_ACTIVE) { /* ����Ҫ��waiting����һ�о�����ʾ*/
		CHAR * iPos = NULL;
		CHAR * sPstr = "Waiting";

		if (!showInfo.text) {
			return 0;
		}
		if ((iPos = strstr(showInfo.text, sPstr)) != NULL) { /* �����waiting,��������ʾ*/
			SINT32 iLen = iPos - showInfo.text ;
			RECT nRect;

			//zOss_Printf(SUBMDL_FS, PRINT_LEVEL_ABNORMAL, "ZTE_MMI mmi_showLcdText showInfo.textLen is %d,  iLen is %d\n", showInfo.textLen, iLen);
			DrawText(hdc, showInfo.text, iLen - 1, &(g_LcdConfigInfoTab[showInfo.item].rect), DT_CENTER);

			nRect.top = g_LcdConfigInfoTab[showInfo.item].rect.bottom - 17;
			nRect.left = g_LcdConfigInfoTab[showInfo.item].rect.left;
			nRect.right = g_LcdConfigInfoTab[showInfo.item].rect.right;
			nRect.bottom = g_LcdConfigInfoTab[showInfo.item].rect.bottom + 17;
			DrawText(hdc, sPstr,  strlen(sPstr), &nRect, DT_CENTER);
		} else {
			DrawText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), showInfo.nFormat);
		}
	} else {
		DrawText(hdc, showInfo.text, showInfo.textLen, &(g_LcdConfigInfoTab[showInfo.item].rect), showInfo.nFormat);
	}
	return 0;
}
/* ��䷽��*/
static SINT32 mmi_showLcdRect(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	SetBrushColor(hdc, showInfo.color);
	FillBox(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, g_LcdConfigInfoTab[showInfo.item].rect.top, RECTW(g_LcdConfigInfoTab[showInfo.item].rect), RECTH(g_LcdConfigInfoTab[showInfo.item].rect));
	return 0;
}

/* ���Ʒ�������������*/
static SINT32 mmi_showLcdRectHLines(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	SetPenColor(hdc, showInfo.color);
	//Draw_LineToEx(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, g_LcdConfigInfoTab[showInfo.item].rect.top, g_LcdConfigInfoTab[showInfo.item].rect.right, g_LcdConfigInfoTab[showInfo.item].rect.top);
	//Draw_LineToEx(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, g_LcdConfigInfoTab[showInfo.item].rect.bottom - 1, g_LcdConfigInfoTab[showInfo.item].rect.right, g_LcdConfigInfoTab[showInfo.item].rect.bottom - 1);
	Draw_Rectangle(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, g_LcdConfigInfoTab[showInfo.item].rect.top, g_LcdConfigInfoTab[showInfo.item].rect.right - 1, g_LcdConfigInfoTab[showInfo.item].rect.bottom - 1);
	return 0;
}

/* ����ˮƽ������*/
static SINT32 mmi_showLcdHLines(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	SetPenColor(hdc, showInfo.color);
	Draw_MoveTo(hdc, g_LcdConfigInfoTab[showInfo.item].rect.left, g_LcdConfigInfoTab[showInfo.item].rect.top);
	Draw_LineTo(hdc, g_LcdConfigInfoTab[showInfo.item].rect.right, g_LcdConfigInfoTab[showInfo.item].rect.bottom);
	return 0;
}

/* ��䱳��ͼƬ*/
extern BITMAP g_mmiMainBg;
static SINT32 mmi_showLcdBackgound(HWND hwnd, T_LcdShowInfoItem showInfo, HDC hdc)
{
	FillBoxWithBitmap(hdc, 0, 0, 128, 128, &g_mmiMainBg);
	return 0;
}

/* ˢ����Ļ */
SINT32 mmi_invalidateLcd(VOID* taskinfo)
{
	if (taskinfo == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_showLcd lcdInfo is null!!!\n");
		return MMI_ERROR;
	}
	if (mmi_Ispoweron_state()) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_invalidateLcd poweron or poweroff now!!!\n");
		return MMI_ERROR;
	}
#if 0
	if (showInfo->type == LCD_SHOW_INVLAID) {
		zOss_Printf(SUBMDL_FS, PRINT_LEVEL_ABNORMAL, "ZTE_MMI showInfo type is LCD_SHOW_INVLAID!!!\n");
		return MMI_ERROR;
	}
#endif
#if 0
	if (showInfo->type == LCD_SHOW_PICTURE || showInfo->type == LCD_SHOW_TEXT || showInfo->type == LCD_SHOW_RECT) {
		wndInvalidateRect(mmi_getMainWnd(), &(showInfo->rect), FALSE);
	} else {
		InvalidateRect(mmi_getMainWnd(), &(showInfo->rect), FALSE);
	}
#endif
	InvalidateRect(mmi_getMainWnd(), NULL, FALSE);
	return MMI_SUCCESS;
}

/*��ʾ��Ļ��Ϣ*/
T_LcdShowInfoItem show_item_now = {0};

SINT32 mmi_showLcdItem(T_LcdShowInfoItem showInfo, HDC hdc)
{

	show_item_now = showInfo;
	switch (showInfo.type) {
	case LCD_SHOW_PICTURE:
		mmi_showLcdPicture(mmi_getMainWnd(), showInfo, hdc);
		break;
	case LCD_SHOW_TEXT:
		mmi_showLcdText(mmi_getMainWnd(), showInfo, hdc);
		break;
	case LCD_SHOW_RECT:
		mmi_showLcdRect(mmi_getMainWnd(), showInfo, hdc);
		break;
	case LCD_SHOW_BOX:
		mmi_showLcdRectHLines(mmi_getMainWnd(), showInfo, hdc);
		break;
	case LCD_SHOW_BACKGROUD:
		//mmi_showLcdBackgound(mmi_getMainWnd(), showInfo, hdc);
		break;
	case LCD_SHOW_HLINE:
		mmi_showLcdHLines(mmi_getMainWnd(), showInfo, hdc);
		break;
	default:
		break;
	}
	return MMI_SUCCESS;
}

BOOL mmi_iswificodeItem(E_LCD_SHOW_CONTENT_ITEM item)
{
	if (g_show_pagethird)
		return (item == LCD_SHOW_WIFI_CODE);

	else
		return FALSE;
}
BOOL mmi_isItemNeedShow(E_LCD_SHOW_CONTENT_ITEM item)
{
	if (mmi_get_lcd_page_index() == MMI_SHOW_PAGE_THIRD) { //&& mmi_get_wps_switch_flag() == TRUE)
		return mmi_iswificodeItem(item);
	} else {
		return !mmi_iswificodeItem(item);
	}
}


void mmi_lcd_backlight_start(void)
{
	if (g_mmi_power_mode) {//not need in the charging state
		send_soc_msg(NEAR_PS, MODULE_ID_AT_CTL, MSG_CMD_AT_FILTER_REQ, 0, NULL);
	}
	slog(MMI_PRINT, SLOG_NORMAL, "[TEST]mmi_lcd_backlight_start\n");

	tp_man_lcd_backlight_start(255);
}


void mmi_lcd_backlight_end(void)
{
	char indNeedAt[AT_CMD_MAX] = "+ZMMI+ZURDY+ZUSLOT+ZICCID^MODE+ZPBIC+ZMSRI+CREG+CEREG+CGREG+CGEV+ZGIPDNS^DSCI+ZCPI+CMT+CMTI+CDS+CDSI";

	slog(MMI_PRINT, SLOG_NORMAL, "[TEST]mmi_lcd_backlight_end\n");
	if (g_mmi_power_mode) {
		send_soc_msg(NEAR_PS, MODULE_ID_AT_CTL, MSG_CMD_AT_FILTER_REQ, strlen(indNeedAt), indNeedAt);
	}

	tp_man_lcd_backlight_end();
}

/*��ʾ��Ļ��Ϣ*/
SINT32 mmi_showLcd(HDC hdc)
{
	UINT32  i = 0;
	mmi_getMutex(&g_mmi_refresh_lcd_mutex);
	if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
		mmi_showLcdItem(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER], hdc);
	} else {
		for (i = 0; i < sizeof(g_LcdShowInfoTab) / sizeof(T_LcdShowInfoItem); ++ i) {
			if (!mmi_isItemNeedShow(g_LcdShowInfoTab[i].item)) {
				continue;
			}
			if (g_LcdShowInfoTab[i].needShowFL && g_LcdShowInfoTab[i].needShow && g_LcdShowInfoTab[i].type != LCD_SHOW_INVLAID) {
				mmi_showLcdItem(g_LcdShowInfoTab[i], hdc);
			}
		}
	}
	zCore_Set_SkipUpdateflag(FALSE);
	mmi_putMutex(&g_mmi_refresh_lcd_mutex);
	return 0;
}

VOID mmi_set_poweroff_charge_show(BOOL showflag)
{
	slog(MMI_PRINT, SLOG_DEBUG, "mmi_set_poweroff_charge_show\n");
	g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].needShow = showflag;
}
static VOID mmi_setPowerOffAniInfo(E_zMmi_Work_Mode mode)
{
	tp_man_Lcd_Sleep_Exit();
	mmi_lcd_backlight_start();
	switch (mode) {
	case MMI_POWEROFF_MODE:
		mmi_startPowerOffFrame();
		break;
	case MMI_RESET_MODE:
		mmi_startPowerResetFrame();
		break;
	case MMI_RESTART_MODE:
		mmi_startPowerRestartFrame();
		break;
	default:
		break;
	}
}
SINT32 mmi_getLcdCtrlInfo(UINT32 taskInfo)
{
	E_zMmi_Work_Mode workMode = *(E_zMmi_Work_Mode*)taskInfo;
	switch (workMode) {
	case MMI_POWERON_MODE:
		mmi_startPowerOnFrame();
		break;
	case MMI_FAST_POWERON_MODE:
		tp_man_Lcd_Sleep_Exit();
		mmi_setMainWindToBg();
		mmi_startFastPowerOnFrame();
		mmi_set_lcd_page_index(MMI_SHOW_PAGE_SECOND);
		mmi_lcd_backlight_start();
		break;
	/*case MMI_POWEROFF_MODE:
		//tp_man_Lcd_Sleep_Exit();
		//tp_man_lcd_backlight_start(255);
		mmi_startPowerOffFrame();
		break;
	case MMI_RESET_MODE:
		mmi_startPowerResetFrame();
		break;
	case MMI_RESTART_MODE:
		mmi_startPowerRestartFrame();
		break;*/
	case MMI_BACKLIGHT_HALFBRIGHT_MODE:
		tp_man_lcd_set_brightness(32);
		break;
	case MMI_BACKLIGHT_OFF_MODE:
	case MMI_FAKE_POWEROFF_MODE:
		mmi_setMainWindToBg();//�ر���ǰ����Ļˢ��
		mmi_lcd_backlight_end();
		tp_man_Lcd_Sleep_Enter();
		set_wake_unlock(MMI_MAIN_LOCK_ID);
		break;
	case MMI_ACTIVE_MODE:
	case MMI_FAKE_POWEROFF_CHARGE_MODE:
		tp_man_Lcd_Sleep_Exit();
		mmi_lcd_backlight_start();
		break;
	default:
		break;
	}
	if (workMode == MMI_POWEROFF_MODE || workMode == MMI_RESET_MODE || workMode == MMI_RESTART_MODE) {
		mmi_setPowerOffAniInfo(workMode);
	}
	return MMI_SUCCESS;
}

SINT32 mmi_getBitmapFromFile(PBITMAP pdesBitmap, CHAR* path, PBITMAP psrcBitmap, BOOL isNeedReload)
{
	CHAR * sPstr[2] = {"/", "\\"};
	CHAR sPath[64] = {0};
	CHAR *iPos = NULL;
	int i = 0;
	SINT32 ret = -1;
	BITMAP tempBitmap = {0};
	BITMAP tempBitmapFree = {0};

	for (i = 0; i < 2; i++) {
		if ((iPos = strstr(path, sPstr[i])) != NULL)
			break;
	}
	if (iPos == NULL) {
		//strcpy(sPath, MMI_RESOURCE_PATH);
		//strcat(sPath, path);
		snprintf(sPath, sizeof(sPath), MMI_RESOURCE_PATH"%s", path);
	} else {
		strncpy(sPath, path, sizeof(sPath)-1);
	}

	if (psrcBitmap == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI mmi_getBitmapFromFile psrcBitmap=NULL!!\n");
		return -1;
	}

	if (!isNeedReload && psrcBitmap->bmBits != NULL) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getBitmapFromFile memcpy ,path:%s!!!\n", path);
		memcpy((VOID *)pdesBitmap, (const VOID *)psrcBitmap, sizeof(BITMAP));
		return 0;
	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getBitmapFromFile reload ,path:%s!!!\n", path);
		if (!isNeedReload) {
			ret = LoadBitmapFromFile(HDC_SCREEN, psrcBitmap, sPath);
			if (ret != 0) {
				slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI load bitmap failed path:%s!!!\n", path);
			}
			memcpy((VOID *)pdesBitmap, (const VOID *)psrcBitmap, sizeof(BITMAP));
		} else {
			memcpy((VOID *)(&tempBitmapFree), (const VOID *)pdesBitmap, sizeof(BITMAP));
			ret = LoadBitmapFromFile(HDC_SCREEN, &tempBitmap, sPath);
			if (ret != 0) {
				slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI load bitmap failed path:%s!!!\n", path);
			}
			memcpy((VOID *)pdesBitmap, (const VOID *)(&tempBitmap), sizeof(BITMAP));
			UnloadBitmap(&tempBitmapFree);
		}
		return ret;
	}
}

#if 1
/**********************************************************************************
*��������:��ȡ����lcd��ʾ��Ϣ
***********************************************************************************/
static BOOL g_LastNewSmsHasShow = FALSE;
static BOOL g_LastFullSmsHasShow = FALSE;

VOID mmi_getLcdSmsNewInfo(SINT32 smsUnreadCount)
{
	if (smsUnreadCount > 0) {
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_PICTURE;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_SMS].bitmap, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NEW].path,
		                          &g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NEW].bmp, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NEW].isNeedReload) != 0)

		{
			g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
		}

		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].type = LCD_SHOW_TEXT;
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].font = mmi_smallest_font;
		memset(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text, 0, MMI_LCD_SHOW_STRING_LEN);
		//sprintf(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text, "58");
		sprintf(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text, "%d", smsUnreadCount);
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text);
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].color = PIXEL_red;
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].needShow = TRUE;
	} else {
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].type = LCD_SHOW_INVLAID;
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
	}
}

VOID mmi_getLcdSmsUnreadInfo(SINT32 smsUnreadCount)
{
	if (smsUnreadCount > 0) {
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_PICTURE;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_SMS].bitmap, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_UNREAD].path,
		                          &g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_UNREAD].bmp, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_UNREAD].isNeedReload) != 0) {
			g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
		}

		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].type = LCD_SHOW_TEXT;
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].font = mmi_smallest_font;
		memset(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text, 0, MMI_LCD_SHOW_STRING_LEN);
		sprintf(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text, "%d", smsUnreadCount);
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].text);
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].color = PIXEL_red;
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].needShow = TRUE;
	} else {
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].type = LCD_SHOW_INVLAID;
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
	}
}

VOID mmi_getLcdSmsFullInfo()
{
	g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_PICTURE;
	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_SMS].bitmap, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_FULL].path,
	                          &g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_FULL].bmp, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_FULL].isNeedReload) != 0) {
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdSmsFullInfo LastFullSmsHasShow==false!!!\n");
}

SINT32 mmi_getLcdSmsInfo(UINT32 taskInfo)
{
	T_zMmi_Sms_Info *pSmsInfo = (T_zMmi_Sms_Info*)taskInfo;
	SINT32 smsUnreadNum = pSmsInfo->mSmsNum;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdSmsInfo status = %d!!!", pSmsInfo->recvBox_sta);
	switch (pSmsInfo->recvBox_sta) {
#ifdef QRZL_UE
	case SMS_RECVBOX_STATUS_UNREAD:
	case SMS_RECVBOX_STATUS_NEW:
	case SMS_RECVBOX_STATUS_FULL:
	default:
		//短信图标只显示一个
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].needShow = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_PICTURE;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_SMS].bitmap, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NOR].path,
								&g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NOR].bmp, g_SmsConfigInfoTab[SMS_RECVBOX_STATUS_NOR].isNeedReload) != 0) {
			g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
		}
		break;
#else
	case SMS_RECVBOX_STATUS_UNREAD:
		mmi_getLcdSmsUnreadInfo(smsUnreadNum);
		break;
	case SMS_RECVBOX_STATUS_NEW:
		mmi_getLcdSmsNewInfo(smsUnreadNum);
		break;
	case SMS_RECVBOX_STATUS_FULL:
		mmi_getLcdSmsFullInfo();
		break;
	default:
		g_LcdShowInfoTab[LCD_SHOW_SMS].type = LCD_SHOW_INVLAID;
		g_LcdShowInfoTab[LCD_SHOW_SMS_NUM].type = LCD_SHOW_INVLAID;
		break;
#endif
	}
	return MMI_SUCCESS;
}
#endif
/**********************************************************************************
*��������:��ȡ���lcd��ʾ��Ϣ
***********************************************************************************/
static BOOL g_LastNullBatteryHasShow = FALSE;
static VOID mmi_getLcdBatteryLowInfo(T_zMMIBatteryInfo *pBatteryInfo)
{
	if (pBatteryInfo->bat_grid == 0) { //0-5%,����Ҫ��С����5����ʾ���0��
		mmi_startLcdBatteryTimer((SINT32)MMI_TASK_BATTERY);
		if (!g_LastNullBatteryHasShow) {
			g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = g_LcdConfigInfoTab[LCD_SHOW_BATTERY].type;
			if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_BATTERY].bitmap, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_NULL].path,
			                          &g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_NULL].bmp, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_NULL].isNeedReload) != 0) {
				g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = LCD_SHOW_INVLAID;
			}
			g_LastNullBatteryHasShow = TRUE;
		} else {
			g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = LCD_SHOW_BACKGROUD;
			g_LastNullBatteryHasShow = FALSE;
		}
	} else if (pBatteryInfo->bat_grid == 1) { //5%-25%����Ҫ��5-25����ʾ��ɫ1��
		g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = g_LcdConfigInfoTab[LCD_SHOW_BATTERY].type;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_BATTERY].bitmap, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_LOWER].path,
		                          &g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_LOWER].bmp, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_LOWER].isNeedReload) != 0) {
			g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = LCD_SHOW_INVLAID;
		}

	}
}

static VOID mmi_innerGetLcdBatteryCharinginfo(SINT32 task, T_LcdShowInfoItem* item)
{
	item->type = LCD_SHOW_PICTURE;

#ifndef QRZL_UE
	if (item->last < E_BATTERY_LEVEL_0 || item->last >= E_BATTERY_LEVEL_4) {
		item->last = E_BATTERY_LEVEL_0;
	} else {
		(item->last)++;
	}
#endif


	if (task == MMI_TASK_POWEROFF_CHARGER || mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
#ifdef QRZL_UE
		if (item->last < E_BATTERY_LEVEL_0 || item->last >= E_BATTERY_LEVEL_4) {
			item->last = E_BATTERY_LEVEL_0;
		} else {
			(item->last)++;
		}
#endif
		if (mmi_getBitmapFromFile(item->bitmap, g_lcdPowerOffBatteryConfigInfo[item->last].path,
		                          &g_lcdPowerOffBatteryConfigInfo[item->last].bmp, g_lcdPowerOffBatteryConfigInfo[item->last].isNeedReload) != 0) {
			item->type = LCD_SHOW_INVLAID;
		}
	} else {

#ifdef QRZL_UE
		if (item->last < E_BATTERY_LEVEL_1 || item->last >= E_BATTERY_LEVEL_5) {
			item->last = E_BATTERY_LEVEL_1;
		} else {
			(item->last)++;
		}
#endif
		if (mmi_getBitmapFromFile(item->bitmap, g_lcdBatteryConfigInfo[item->last].path,
		                          &g_lcdBatteryConfigInfo[item->last].bmp, g_lcdBatteryConfigInfo[item->last].isNeedReload) != 0) {
			item->type = LCD_SHOW_INVLAID;
		}
	}
	mmi_startLcdBatteryTimer(task);
}
static VOID mmi_getLcdBatteryCharinginfo()
{
	g_LcdShowInfoTab[LCD_SHOW_POWER].needShow = FALSE;
	if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
		mmi_innerGetLcdBatteryCharinginfo(MMI_TASK_BATTERY, &(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER]));
	} else {
		mmi_innerGetLcdBatteryCharinginfo(MMI_TASK_BATTERY, &(g_LcdShowInfoTab[LCD_SHOW_BATTERY]));
	}

}

static VOID mmi_innerGetLcdBatteryFullinfo(T_LcdShowInfoItem* item)
{
	item->type = LCD_SHOW_PICTURE;
	if (item->item == LCD_SHOW_POWER_OFF_CHARGER) {
		if (mmi_getBitmapFromFile(item->bitmap, g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].path,
		                          &g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].bmp, g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].isNeedReload) != 0) {
			item->type = LCD_SHOW_INVLAID;
		}
	} else {
#ifdef QRZL_UE
		if (mmi_getBitmapFromFile(item->bitmap, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_5].path,
		                          &g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_5].bmp, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_5].isNeedReload) != 0)
		{
			item->type = LCD_SHOW_INVLAID;
		}
#else
		if (mmi_getBitmapFromFile(item->bitmap, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_4].path,
		                          &g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_4].bmp, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_4].isNeedReload) != 0)
		{
			item->type = LCD_SHOW_INVLAID;
		}
#endif

	}
}

static VOID mmi_innerGetLcdOverVoltageinfo(T_LcdShowInfoItem* item)
{
	item->type = LCD_SHOW_PICTURE;
	if (mmi_getBitmapFromFile(item->bitmap, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_OVERVOL].path,
	                          &g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_OVERVOL].bmp, g_lcdBatteryConfigInfo[E_BATTERY_LEVEL_OVERVOL].isNeedReload) != 0) {
		item->type = LCD_SHOW_INVLAID;
	}
}
static VOID mmi_getLcdBatteryFullinfo(T_zMMIBatteryInfo *pBatteryInfo)
{
	if (mmi_get_lcd_mode() == MMI_FAKE_POWEROFF_CHARGE_MODE) {
		mmi_innerGetLcdBatteryFullinfo(&(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER]));
	} else {
		mmi_innerGetLcdBatteryFullinfo(&(g_LcdShowInfoTab[LCD_SHOW_BATTERY]));
	}
}

static VOID mmi_getLcdBatteryNormalinfo(T_zMMIBatteryInfo *pBatteryInfo)
{
	g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = g_LcdConfigInfoTab[LCD_SHOW_BATTERY].type;

	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_BATTERY].bitmap, g_lcdBatteryConfigInfo[pBatteryInfo->bat_grid + E_BATTERY_LEVEL_0].path,
	                          &g_lcdBatteryConfigInfo[pBatteryInfo->bat_grid + E_BATTERY_LEVEL_0].bmp, g_lcdBatteryConfigInfo[pBatteryInfo->bat_grid + E_BATTERY_LEVEL_0].isNeedReload) != 0) {
		g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = LCD_SHOW_INVLAID;
	}
}

SINT32 mmi_getLcdBatteryInfo(UINT32 taskInfo)
{
	T_zMMIBatteryInfo *pBatteryInfo = (T_zMMIBatteryInfo *)taskInfo;
	g_LcdShowInfoTab[LCD_SHOW_BATTERY].type = LCD_SHOW_INVLAID;
	mmi_stopLcdBatteryTimer();
	switch (pBatteryInfo->chg_state) {
	case STATE_CHARGING://charging
		mmi_getLcdBatteryCharinginfo();
		break;
	case STATE_FULL://full
		mmi_getLcdBatteryFullinfo(pBatteryInfo);
		break;
	case STATE_DISCHARGE: { //discharge
		if (pBatteryInfo->bat_level == VOLT_5PERCENTLEVEL || pBatteryInfo->bat_level == VOLT_10PERCENTLEVEL
		    || pBatteryInfo->bat_level == VOLT_20PERCENTLEVEL || pBatteryInfo->bat_level == VOLT_25PERCENTLEVEL) {
			mmi_getLcdBatteryLowInfo(pBatteryInfo);
		} else {
			mmi_getLcdBatteryNormalinfo(pBatteryInfo);
		}
		break;
	}
	case STATE_CHARGERROR:
		break;
	default:
		break;
	}
	return MMI_SUCCESS;
}
SINT32 mmi_getLcdPowerInfo(UINT32 taskInfo)
{
	T_zMMIBatteryInfo *pBatteryInfo = (T_zMMIBatteryInfo *)taskInfo;
	g_LcdShowInfoTab[LCD_SHOW_POWER].type = LCD_SHOW_TEXT;
	if (!mmi_getFotaNewVersionInfo()) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].needShow = FALSE;
		if (pBatteryInfo->chg_state != STATE_CHARGING) {
			g_LcdShowInfoTab[LCD_SHOW_POWER].needShow = TRUE;
		}
		g_LcdShowInfoTab[LCD_SHOW_POWER].font = mmi_small_font;
		sprintf(g_LcdShowInfoTab[LCD_SHOW_POWER].text, "%d\%%", pBatteryInfo->bat_pers);
		g_LcdShowInfoTab[LCD_SHOW_POWER].textLen = 0;
		if (g_LcdShowInfoTab[LCD_SHOW_POWER].text != NULL) {
			g_LcdShowInfoTab[LCD_SHOW_POWER].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_POWER].text);
		}
	}
	return MMI_SUCCESS;
}

/**********************************************************************************
*��������:��ȡwifiStation����ʱlcd��ʾͼƬ��·����Ϣ  (СͼƬ�����޸�ʱ ��Ϊ��������)
***********************************************************************************/

UINT32 mmi_getLcdWifiStationPicturePathFromWifiStationInfo(T_zMMIWifiStationInfo * wifistationInfo)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_lcdWifiStationConfigInfoTab) / sizeof(T_WifiStationConfigInfo); ++ i) {
		if (wifistationInfo->signal_num == g_lcdWifiStationConfigInfoTab[i].sigLevel) {
			break;
		}
	}
	return i;
}

/**********************************************************************************
*��������:��ȡ����lcd��ʾ��Ϣ
***********************************************************************************/

UINT32 mmi_getLcdNetPicturePathFromNetInfo(T_zMMINetInfo * netInfo)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_lcdNetSignalConfigInfoTab) / sizeof(T_NetSignalConfigInfo); ++ i) {
		if (netInfo->net_mode == NET_MODE_NOSERVICE || netInfo->net_mode == NET_MODE_LIMITSERVICE || netInfo->net_mode == NET_MODE_DEFAULT) {
			if (netInfo->net_mode == g_lcdNetSignalConfigInfoTab[i].netMode) {
				break;
			}
		} else {
			if (netInfo->net_mode == g_lcdNetSignalConfigInfoTab[i].netMode && netInfo->signal_num == g_lcdNetSignalConfigInfoTab[i].sigLevel &&
			    netInfo->roam_mode == g_lcdNetSignalConfigInfoTab[i].isRoam) {
				break;
			}
		}
	}
	return i;
}
SINT32 mmi_getLcdNetInfo(UINT32 taskInfo)
{
	T_zMMINetInfo * pNetInfo = (T_zMMINetInfo *)taskInfo;
	UINT32 nettab_index = 0;
#ifdef QRZL_UE
	char nv_net_pro [64] = {0};
	UINT32 net_provider_change_flag = 0;
#endif

	g_LcdShowInfoTab[LCD_SHOW_NET_SIGNAL].type = LCD_SHOW_INVLAID;
	g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].type = LCD_SHOW_INVLAID;
	g_LcdShowInfoTab[LCD_SHOW_NET_SIGNAL].needShow = TRUE;

	nettab_index = mmi_getLcdNetPicturePathFromNetInfo(pNetInfo);
	if (nettab_index >= sizeof(g_lcdNetSignalConfigInfoTab) / sizeof(T_NetSignalConfigInfo))
		return MMI_ERROR;

	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_NET_SIGNAL].bitmap, g_lcdNetSignalConfigInfoTab[nettab_index].path,
	                          &g_lcdNetSignalConfigInfoTab[nettab_index].bmp, g_lcdNetSignalConfigInfoTab[nettab_index].isNeedReload) == 0) {
		g_LcdShowInfoTab[LCD_SHOW_NET_SIGNAL].type = LCD_SHOW_PICTURE;
	}
	if (pNetInfo->connect_status == 1 && pNetInfo->net_mode != NET_MODE_NOSERVICE && pNetInfo->net_mode != NET_MODE_LIMITSERVICE) { //e58
		g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShow = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].needShow = TRUE;

		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].bitmap, g_lcdNetConnectPicPath, &g_lcdNetConnectBmp, FALSE) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].type = LCD_SHOW_PICTURE;
		}

#ifdef QRZL_UE
#ifdef QRZL_CUSTOMER_XIANJI
		if (strcmp(g_lcdLteHotSpotPicPath, "xianji.png") != 0 ) {
			g_lcdLteHotSpotPicPath = "xianji.png";
			net_provider_change_flag = 1;
		} else {
			net_provider_change_flag = 0;
		}
#else
		cfg_get_item(NV_NETWORK_PROVIDER, nv_net_pro, 64);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdNetInfo salvikie nv_net_pro=[%s] g_lcdLteHotSpotPicPath=[%s]!!\n", nv_net_pro, g_lcdLteHotSpotPicPath);
		if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CMCC) && strcmp(g_lcdLteHotSpotPicPath, "zgyd.png") != 0) {
			g_lcdLteHotSpotPicPath = "zgyd.png";
			net_provider_change_flag = 1;
		} else if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CUCC) && strcmp(g_lcdLteHotSpotPicPath, "zglt.png") != 0) {
			g_lcdLteHotSpotPicPath = "zglt.png";
			net_provider_change_flag = 1;
		} else if (!strcasecmp(nv_net_pro, NV_NET_PROVIDER_CTCC) && strcmp(g_lcdLteHotSpotPicPath, "zgdx.png") != 0 ) {
			g_lcdLteHotSpotPicPath = "zgdx.png";
			net_provider_change_flag = 1;
		}
#endif
		g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].needShow = TRUE;
		if(net_provider_change_flag) {
			memset(&g_lcdLteHotSpotBmp, 0, sizeof(g_lcdLteHotSpotBmp));
		}
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].bitmap, g_lcdLteHotSpotPicPath, &g_lcdLteHotSpotBmp, FALSE) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].type = LCD_SHOW_PICTURE;
		}

		/*
		if(net_provider_change_flag ) {
			if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].bitmap, g_lcdLteHotSpotPicPath, &g_lcdLteHotSpotBmp, TRUE) == 0) {
				g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].type = LCD_SHOW_PICTURE;
			}
		} else {
			if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].bitmap, g_lcdLteHotSpotPicPath, &g_lcdLteHotSpotBmp, FALSE) == 0) {
				g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].type = LCD_SHOW_PICTURE;
			}
		}
		*/
#endif
	} else {
		g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].needShow = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].type = LCD_SHOW_INVLAID;
		if (mmi_get_wifiStationConnect_state()) {
			g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShow = TRUE;
		}

#ifdef QRZL_UE
#ifdef QRZL_CUSTOMER_XIANJI
		if (strcmp(g_lcdLteHotSpotPicPath, "xianji.png") != 0 ) {
			g_lcdLteHotSpotPicPath = "xianji.png";
			net_provider_change_flag = 1;
		} else {
			net_provider_change_flag = 0;
		}
#else
		 if (strcmp(g_lcdLteHotSpotPicPath, "wxh.png") != 0 ) {
			g_lcdLteHotSpotPicPath = "wxh.png";
			net_provider_change_flag = 1;
		} else {
			net_provider_change_flag = 0;
		}
#endif

		g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].needShow = TRUE;
		if(net_provider_change_flag) {
			memset(&g_lcdLteHotSpotBmp, 0, sizeof(g_lcdLteHotSpotBmp));
		}
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].bitmap, g_lcdLteHotSpotPicPath, &g_lcdLteHotSpotBmp, FALSE) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_LTE_HOTSPOT].type = LCD_SHOW_PICTURE;
		}
#endif
	}

#if defined(QRZL_UE) && defined(QRZL_CUSTOMER_XIANJI)
	g_LcdShowInfoTab[LCD_SHOW_NET_PROVIDER_LOGO].needShow = TRUE;
	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_NET_PROVIDER_LOGO].bitmap, g_lcdNetProviderLogoPicPath, &g_lcdNetProviderLogoBmp, FALSE) == 0) {
		g_LcdShowInfoTab[LCD_SHOW_NET_PROVIDER_LOGO].type = LCD_SHOW_PICTURE;
	}
#endif

	return MMI_SUCCESS;
}

/**********************************************************************************
*��������:����LCD�в���ʾ��
***********************************************************************************/
E_LCD_SHOW_CONTENT_ITEM g_mmi_cur_show_item = LCD_SHOW_MAX;

static BOOL mmi_IsShowSimState(VOID)
{
	E_zMmi_Sim_Tip sim_sta = mmi_getSIMStateInfo();
	return (sim_sta == INSERT_SIM || sim_sta == SIM_BUSY || sim_sta == PIN_LOCK || sim_sta == PUK_LOCK || sim_sta == SIM_LOCK || sim_sta == INVALID_SIM) ? TRUE : FALSE;
}

static BOOL mmi_IsShowNetPro(VOID)
{
	E_zMmi_NetCon_Tip net_sta = mmi_getNetConInfo();
	return (net_sta != NET_MAX && net_sta != NET_SEARCHING && net_sta != NET_NOSERVICE
	        && net_sta != NET_LIMITSERVICE && net_sta != NET_DISCONNECTING && net_sta != NET_CONNECTING) ? TRUE : FALSE;
}

static VOID mmi_set_middle_current_show(E_LCD_SHOW_CONTENT_ITEM item)
{
	g_LcdShowInfoTab[item].needShow = TRUE;
	g_mmi_cur_show_item = item;

}
static VOID mmi_set_middle_needshow()
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].needShow = FALSE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].needShow = FALSE;
	E_zMmi_WpsAct_Tip wps_sta = mmi_get_wps_state();
	E_zMMI_Fota_Tip  fota_sta = (E_zMMI_Fota_Tip)mmi_getFotaUpdateStateInfo();

	if (wps_sta == WPS_ACTIVE_MAX && (fota_sta == FOTA_MAX || fota_sta == FOTA_SHOW_FINISH)) {

		if (mmi_get_wifiStationConnect_state()) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show wifiSta!!\n");
			mmi_set_middle_current_show(LCD_SHOW_TIP_WIFISTA_SSID);
		} else if (mmi_IsShowSimState()) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show SIM!!\n");
			mmi_set_middle_current_show(LCD_SHOW_TIP_SIM_STATE);
		} else if (mmi_IsShowNetPro()) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show netpro!!\n");
			mmi_set_middle_current_show(LCD_SHOW_TIP_NET_PROVIDER);
		} else {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show netcon!!\n");
			mmi_set_middle_current_show(LCD_SHOW_TIP_NET_CONNECTING);
		}
	} else {
		if (wps_sta != WPS_ACTIVE_MAX) {
			if (fota_sta != FOTA_MAX && fota_sta != FOTA_SHOW_FINISH && g_mmi_cur_show_item == LCD_SHOW_TIP_WPS_ACTIVE) {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow WPS show FOTA!!\n");
				mmi_set_middle_current_show(LCD_SHOW_TIP_UPDATE_INFO);
			} else {
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show WPS!!\n");
				mmi_set_middle_current_show(LCD_SHOW_TIP_WPS_ACTIVE);
			}
		} else {
			//kw 3
			//if (wps_sta != WPS_ACTIVE_MAX && g_mmi_cur_show_item == LCD_SHOW_TIP_UPDATE_INFO) {
			//	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow FOTA show WPS!!\n");
			//	mmi_set_middle_current_show(LCD_SHOW_TIP_WPS_ACTIVE);
			//} else 
			{
				slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_set_middle_needshow show FOTA!!\n");
				mmi_set_middle_current_show(LCD_SHOW_TIP_UPDATE_INFO);
			}
		}
	}
}

/**********************************************************************************
*��������:��ȡWIFI��ά����ʾ��Ϣ
***********************************************************************************/
SINT32 mmi_getLcdWifiCodeInfo(UINT32 taskInfo)
{
	T_zMMIWifiCodeInfo *pWifiCodeInfo = (T_zMMIWifiCodeInfo *)taskInfo;

	if (g_show_pagethird) {
		g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE].type = LCD_SHOW_INVLAID;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE].bitmap, g_lcdWifiCodeConfigInfoTab[0].path,
		                          &g_lcdWifiCodeConfigInfoTab[0].bmp, g_lcdWifiCodeConfigInfoTab[0].isNeedReload) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE].needShow = TRUE;
			g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE].type = LCD_SHOW_PICTURE;
		}
	}

	if (pWifiCodeInfo->multi_ssid_switch) {
		g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE2].type = LCD_SHOW_INVLAID;
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE2].bitmap, g_lcdWifiCodeConfigInfoTab[1].path,
		                          &g_lcdWifiCodeConfigInfoTab[1].bmp, g_lcdWifiCodeConfigInfoTab[1].isNeedReload) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE2].needShow = TRUE;
			g_LcdShowInfoTab[LCD_SHOW_WIFI_CODE2].type = LCD_SHOW_PICTURE;
		}
	}
	return MMI_SUCCESS;
}

/**********************************************************************************
*��������:��ȡWIFI STATION��ʾ��Ϣ
***********************************************************************************/
static VOID mmi_setLcdWifiStationSSID(CHAR *sta_ssid)
{
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI mmi_getLcdWifiStationSSID sta_ssid=%s!!\n", sta_ssid);
	g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].type = LCD_SHOW_TEXT;
	memset(g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].text, 0, MMI_LCD_SHOW_STRING_LEN);
	strncpy(g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].text, sta_ssid, MMI_LCD_SHOW_STRING_LEN);
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_WIFISTA_SSID].font = mmi_middle_sixteen_font;

	mmi_set_middle_needshow();
}

SINT32 mmi_getLcdWifiStationInfo(UINT32 taskInfo)//wifistation
{
	T_zMMIWifiStationInfo *pWifiStationInfo = (T_zMMIWifiStationInfo *)taskInfo;
	UINT32 wifistatab_index = 0;
	BOOL modemConnectState = mmi_net_connected();
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdWifiStationInfo pWifiStationInfo->wifistation_connect_state= %d ssid=%s!!\n", pWifiStationInfo->wifistation_connect_state, pWifiStationInfo->wifista_ssid);

	wifistatab_index = mmi_getLcdWifiStationPicturePathFromWifiStationInfo(pWifiStationInfo);
	if (wifistatab_index >= sizeof(g_lcdWifiStationConfigInfoTab) / sizeof(T_WifiStationConfigInfo))
		return MMI_ERROR;

	if (modemConnectState) {
		g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].needShow = TRUE;
		g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShow = FALSE;
	} else {
		g_LcdShowInfoTab[LCD_SHOW_NET_CONNECT].needShow = FALSE;
		if (pWifiStationInfo->wifistation_connect_state) {
			g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShow = TRUE;
		} else {
			g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].needShow = FALSE;
		}
	}
	if (pWifiStationInfo->wifistation_connect_state) {
		if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].bitmap, g_lcdWifiStationConfigInfoTab[wifistatab_index].path,
		                          &g_lcdWifiStationConfigInfoTab[wifistatab_index].bmp, g_lcdWifiStationConfigInfoTab[wifistatab_index].isNeedReload) == 0) {
			g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].type = LCD_SHOW_PICTURE;
		} else {
			g_LcdShowInfoTab[LCD_SHOW_WIFISTATION_CONNECT].type = LCD_SHOW_INVLAID;
		}
	}

	mmi_setLcdWifiStationSSID(pWifiStationInfo->wifista_ssid);
	return MMI_SUCCESS;
}
/**********************************************************************************
*��������:��ȡWIFIlcd��ʾ��Ϣ
***********************************************************************************/
UINT32 mmi_getLcdWifiPicturePathFromWifiInfo(T_zMMIWifiInfo * wifiInfo)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_lcdWifiConfigInfoTab) / sizeof(T_WifiConfigInfo); ++ i) {
		if (g_lcdWifiConfigInfoTab[i].wifiState == wifiInfo->wifi_state &&
		    g_lcdWifiConfigInfoTab[i].connected_userNum == wifiInfo->connected_userNum) {
			return i;
		}
	}
	return 0;
}
SINT32 mmi_getLcdWifiInfo(UINT32 taskInfo)
{
	T_zMMIWifiInfo * pWifiInfo = (T_zMMIWifiInfo *)taskInfo;
	UINT32 wifitab_index = 0;

	wifitab_index = mmi_getLcdWifiPicturePathFromWifiInfo(pWifiInfo);
	g_LcdShowInfoTab[LCD_SHOW_WIFI].type = LCD_SHOW_INVLAID;

	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_WIFI].bitmap, g_lcdWifiConfigInfoTab[wifitab_index].path,
	                          &g_lcdWifiConfigInfoTab[wifitab_index].bmp, g_lcdWifiConfigInfoTab[wifitab_index].isNeedReload) == 0) {
		g_LcdShowInfoTab[LCD_SHOW_WIFI].type = LCD_SHOW_PICTURE;
	}

	return MMI_SUCCESS;
}

#define MMI_IS_VALID_TRAFFIC_NUM(num) (num >= 0 && num <= 0xFFFFFFFF)

/**********************************************************************************
*��������:��ȡ����lcd��ʾ��Ϣ
***********************************************************************************/
SINT32 mmi_getLcdTrafficInfo(UINT32 taskInfo)
{
	T_zMMITrafficInfo *pTrafficInfo = (T_zMMITrafficInfo *)taskInfo;

	if (!g_show_pagefirst) {
		return MMI_SUCCESS;
	}

	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].type = LCD_SHOW_TEXT;
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].type = LCD_SHOW_INVLAID;
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].type = LCD_SHOW_INVLAID;
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_WARING].type = LCD_SHOW_INVLAID;
#ifdef QRZL_UE
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShow = FALSE;
#else
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].needShow = TRUE;
#endif
	
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].needShow = TRUE;

	//sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%s\MB", pTrafficInfo->quota);
	//g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text);
	//g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].font = mmi_middle_fourteen_font;
	g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].font = mmi_middle_twelve_font;


	if (MMI_IS_VALID_TRAFFIC_NUM(pTrafficInfo->uesd_traffic) && MMI_IS_VALID_TRAFFIC_NUM(pTrafficInfo->total_traffic)
	    /*&&pTrafficInfo->uesd_traffic <=  pTrafficInfo->total_traffic*/) {
		memset(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, 0, MMI_LCD_SHOW_STRING_LEN);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdTrafficInfo unit = %d!\n", pTrafficInfo->traffic_unit);
		if (pTrafficInfo->traffic_unit == TRAFFIC_UNIT_DATA) {
			if (pTrafficInfo->traffic_switch == TRAFFIC_LIMIT_SWITCH_OFF) {
				if (pTrafficInfo->useddata_unit == TRAFFIC_DATA_UNIT_MB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\M/--", pTrafficInfo->uesd_traffic);
				} else if (pTrafficInfo->useddata_unit == TRAFFIC_DATA_UNIT_GB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\G/--", pTrafficInfo->uesd_traffic);
				} else if (pTrafficInfo->useddata_unit == TRAFFIC_DATA_UNIT_TB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\T/--", pTrafficInfo->uesd_traffic);
				}
			} else {
				if (pTrafficInfo->data_unit == TRAFFIC_DATA_UNIT_MB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\M/%.2f\M", pTrafficInfo->uesd_traffic, pTrafficInfo->total_traffic);
				} else if (pTrafficInfo->data_unit == TRAFFIC_DATA_UNIT_GB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\G/%.2f\G", pTrafficInfo->uesd_traffic, pTrafficInfo->total_traffic);
				} else if (pTrafficInfo->data_unit == TRAFFIC_DATA_UNIT_TB) {
					sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\T/%.2f\T", pTrafficInfo->uesd_traffic, pTrafficInfo->total_traffic);
				}
			}
		} else if (pTrafficInfo->traffic_unit == TRAFFIC_UNIT_TIME) {
			sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%.2f\hr/%.2f\hr", pTrafficInfo->uesd_traffic, pTrafficInfo->total_traffic);
		}

		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text);
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].nFormat = DT_RIGHT;

		if (pTrafficInfo->traffic_switch == TRAFFIC_LIMIT_SWITCH_ON) {
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_WARING].needShow = TRUE;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_WARING].type = LCD_SHOW_RECT;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_WARING].color = PIXEL_red;

			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShow = TRUE;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].type = LCD_SHOW_RECT;

			if (pTrafficInfo->total_traffic > 0) { //kw 3
				if (pTrafficInfo->uesd_traffic > pTrafficInfo->total_traffic) {
					g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].color = PIXEL_red;
					g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_SLIDER].rect.right = g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_SLIDER].rect.left + \
					        RECTW(g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_BAR].rect);
				} else {
					g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].color = RGBA2Pixel(HDC_SCREEN, 0, 0xff, 0, 0xff);//PIXEL_green;
					g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_SLIDER].rect.right = g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_SLIDER].rect.left + \
					        RECTW(g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_BAR].rect) * pTrafficInfo->uesd_traffic / pTrafficInfo->total_traffic;
				}
			}
			g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_WARING].rect.left = g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_BAR].rect.left + \
			        pTrafficInfo->warning_tip_level * RECTW(g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_BAR].rect) / 100;
			g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_WARING].rect.right = g_LcdConfigInfoTab[LCD_SHOW_TRAFFIC_WARING].rect.left + 1;

		} else {
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_WARING].needShow = FALSE;
			g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_SLIDER].needShow = FALSE;
		}
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].type = LCD_SHOW_BOX;
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC_BAR].color = PIXEL_lightwhite;

	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI  used trffic = %f, total traffic =%f!!!", pTrafficInfo->uesd_traffic, pTrafficInfo->total_traffic);
		memset(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, 0, MMI_LCD_SHOW_STRING_LEN);
		sprintf(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text, "%s", "get traffic failed");
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].text);
		g_LcdShowInfoTab[LCD_SHOW_TRAFFIC].nFormat = DT_CENTER;
	}
	return MMI_SUCCESS;
}

/**********************************************************************************
*��������:��ȡ�ػ����lcd��ʾ��Ϣ
***********************************************************************************/
static VOID mmi_getLcdPowerOffChargerBatteryFullinfo()
{
	g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].type = LCD_SHOW_PICTURE;
	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].bitmap, g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].path,
	                          &g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].bmp, g_lcdPowerOffBatteryConfigInfo[E_BATTERY_LEVEL_4].isNeedReload) != 0) {
		g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].type = LCD_SHOW_INVLAID;
	}
}
static VOID mmi_getLcdPowerOffChargerOverVoltageinfo()
{
	mmi_innerGetLcdOverVoltageinfo(&(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER]));
}
static VOID mmi_getLcdPowerOffChargerLowBatteryinfo(E_zMmi_Poc_State state)
{
	if (state == POC_STATE_LOWBATTERY || state == POC_STATE_NOBATTERY) {//klocwork
		g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].type = LCD_SHOW_TEXT;
		g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].font = mmi_middle_sixteen_font;//mmi_middle_fourteen_font;
		g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].text = tp_i18n_get_text(g_powerOffChargerLowBatteryTab[state - POC_STATE_LOWBATTERY].tips);
		if (g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].text != NULL)
			g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER].text);
	}
}
SINT32 mmi_getLcdPowerOffChagerInfo(UINT32 taskInfo)
{
	T_zMmi_Poc_Info *pPocInfo = (T_zMmi_Poc_Info *)taskInfo;
	if (pPocInfo->backlight_sta == 0) {
		mmi_lcd_backlight_end();
		tp_man_Lcd_Sleep_Enter();
		if(FALSE == mmi_is_offchg_poweroff)	{
			set_wake_unlock(MMI_POWEROFF_LOCK_LCD_ID);
		}		
	} else {
		mmi_getMutex(&g_mmi_poweron_mutex);
		if (!g_mmi_poweroff_turnon_flag) {
			tp_man_Lcd_Sleep_Exit();
			mmi_lcd_backlight_start();
		}
		mmi_putMutex(&g_mmi_poweron_mutex);
	}
	if (pPocInfo->overvoltage_mode == TRUE) {
		mmi_getLcdPowerOffChargerOverVoltageinfo();
	} else {
		if (pPocInfo->poc_sta == POC_STATE_FULL) {
			mmi_getLcdPowerOffChargerBatteryFullinfo();
		} else if (pPocInfo->poc_sta == POC_STATE_LOWBATTERY || pPocInfo->poc_sta == POC_STATE_NOBATTERY) {
			mmi_getLcdPowerOffChargerLowBatteryinfo(pPocInfo->poc_sta);
		} else {
			mmi_innerGetLcdBatteryCharinginfo(MMI_TASK_POWEROFF_CHARGER, &(g_LcdShowInfoTab[LCD_SHOW_POWER_OFF_CHARGER]));
		}
	}
	return MMI_SUCCESS;

}
/**********************************************************************************
*��������:��ȡ��ʾlcd ��ʾ��Ϣ
***********************************************************************************/
static CHAR* mmi_findStringFromSimState(E_zMmi_Sim_Tip sim_sta)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_simStaTipStringTab) / sizeof(T_SimTipStringItem); ++ i) {
		if (g_simStaTipStringTab[i].sim_state == sim_sta) {
			//return g_simStaTipStringTab[i].tipString;
			return (CHAR*)tp_i18n_get_text(g_simStaTipStringTab[i].tipString);
		}
	}
	return NULL;
}
static CHAR* mmi_findStringFromWpsAct(E_zMmi_WpsAct_Tip wps_act)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_wpsActTipStringTab) / sizeof(T_WpsActTipStringItem); ++ i) {
		if (g_wpsActTipStringTab[i].wps_state == wps_act) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_findStringFromWpsAct %s success!!\n", g_wpsActTipStringTab[i].tipString);
			//return g_wpsActTipStringTab[i].tipString;
			return (CHAR*)tp_i18n_get_text(g_wpsActTipStringTab[i].tipString);
		}
	}

	return NULL;
}

static CHAR* mmi_findStringFromNetCon(E_zMmi_NetCon_Tip net_con)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_netContTipStringTab) / sizeof(T_NetConTipStringItem); ++ i) {
		if (g_netContTipStringTab[i].net_con == net_con) {
			//return g_netContTipStringTab[i].tipString;
			return (CHAR*)tp_i18n_get_text(g_netContTipStringTab[i].tipString);
		}
	}
	return NULL;
}

static CHAR* mmi_findStringFromFota(E_zMMI_Fota_Tip fota_tip)
{
	UINT32 i = 0;
	for (i = 0; i < sizeof(g_FotaTipStringTab) / sizeof(T_FotaTipStringItem); ++ i) {
		if (g_FotaTipStringTab[i].fota_tip == fota_tip) {
			//return g_FotaTipStringTab[i].tipString;
			return (CHAR*)tp_i18n_get_text(g_FotaTipStringTab[i].tipString);
		}
	}
	return NULL;
}

static VOID mmi_setSIMStateTipInfo(E_zMmi_Sim_Tip sim_sta)
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].type = LCD_SHOW_TEXT;
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].text = mmi_findStringFromSimState(sim_sta);
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_SIM_STATE].font = mmi_middle_sixteen_font;
}

static VOID mmi_setWpsActTipInfo(E_zMmi_WpsAct_Tip wps_act)
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].type = LCD_SHOW_TEXT;
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].text = mmi_findStringFromWpsAct(wps_act);
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_WPS_ACTIVE].font = mmi_middle_sixteen_font;
}

static VOID mmi_setNeConTipInfo(E_zMmi_NetCon_Tip net_con)
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].type = LCD_SHOW_TEXT;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].text = mmi_findStringFromNetCon(net_con);
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_CONNECTING].font = mmi_middle_sixteen_font;
}
static VOID mmi_setNetProTipInfo(CHAR* net_pro)
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].type = LCD_SHOW_TEXT;
	//g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].text = net_pro;
	memset(g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].text, 0, MMI_LCD_SHOW_STRING_LEN);
	strncpy(g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].text, tp_i18n_get_text(net_pro), MMI_LCD_SHOW_STRING_LEN);
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_NET_PROVIDER].font = mmi_middle_sixteen_font;
}

static VOID mmi_setNewVersionTipInfo()
{
	g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].type = LCD_SHOW_PICTURE;
	g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].needShow = TRUE;
	if (mmi_getBitmapFromFile(g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].bitmap, g_mmiNewVersionBmpPath, &g_mmiNewVersionBmp, FALSE) != 0) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].type = LCD_SHOW_INVLAID;
	}
}

static VOID mmi_setFotaTipInfo(E_zMMI_Fota_Tip fota_tip)
{
	CHAR *str_Fota = mmi_findStringFromFota(fota_tip);
	g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].type = LCD_SHOW_TEXT;
	memset(g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].text, 0, MMI_LCD_SHOW_STRING_LEN);
	if (str_Fota != NULL) {
		sprintf(g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].text, "%s", str_Fota);
	}
	if (g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_TIP_UPDATE_INFO].font = mmi_small_font;

}

static VOID mmi_strcatStringWithPre(CHAR* output, CHAR* key, CHAR* value, SINT32 totallen)
{
	memset(output, 0, totallen);
	memcpy((VOID *)output, (const VOID *)key, (UINT32)strlen(key));
	if (value != NULL && strlen(value) > 0) {
		memcpy((VOID *)(output + strlen(key)), (const VOID *)value, (UINT32)(totallen - strlen(key) - 1));
	} else {
		memcpy((VOID *)(output + strlen(key)), (const VOID *)INVALID_STR, (UINT32)(strlen(INVALID_STR)));
	}
}
/* Started by AICoder, pid:69e0fj3ec3xbaff1404c0a1fe064b4205cc8dff0 */
static char *get_ssid_name(void)
{
    char wifi_band[8] = {0};
    cfg_get_item("wifi_band", wifi_band, sizeof(wifi_band));
    
    if (strcmp(wifi_band, "a") == 0) {
        return STRING_SSID_5G;
    } else {
        return STRING_SSID;
    }
}

static char *get_ssid1_name(void)
{
    char wifi_band[8] = {0};
    cfg_get_item("wifi_band", wifi_band, sizeof(wifi_band));
    
    if (strcmp(wifi_band, "a") == 0) {
        return STRING_SSID1_5G;
    } else {
        return STRING_SSID1;
    }
}

static char *get_ssid2_name(void)
{
    char wifi_band[8] = {0};
    cfg_get_item("wifi_band", wifi_band, sizeof(wifi_band));
    
    if (strcmp(wifi_band, "a") == 0) {
        return STRING_SSID2_5G;
    } else {
        return STRING_SSID2;
    }
}
/* Ended by AICoder, pid:69e0fj3ec3xbaff1404c0a1fe064b4205cc8dff0 */

static VOID mmi_setSSIDKeyTipInfo(T_LcdShowInfoItem *ssidItem, T_LcdShowInfoItem *wifikeyItem, CHAR* ssid, CHAR *wifikey)
{
	char *ssid_str = NULL;
	ssidItem->type = LCD_SHOW_TEXT;
	ssidItem->needShow = TRUE;
	if (mmi_get_multi_ssid_switch_flag()) {
		ssid_str = get_ssid1_name();
		mmi_strcatStringWithPre(ssidItem->text, (CHAR*)tp_i18n_get_text(ssid_str), ssid, MMI_LCD_SHOW_STRING_LEN);
	} else {
		ssid_str = get_ssid_name();
		mmi_strcatStringWithPre(ssidItem->text, (CHAR*)tp_i18n_get_text(ssid_str), ssid, MMI_LCD_SHOW_STRING_LEN);
	}
	if (ssidItem->text != NULL) {
		ssidItem->textLen = strlen(ssidItem->text);
	}
	ssidItem->font = mmi_middle_twelve_font;

	wifikeyItem->type = LCD_SHOW_TEXT;
	wifikeyItem->needShow = TRUE;
	if (wifikeyItem->text != NULL) {
		mmi_strcatStringWithPre(wifikeyItem->text, (CHAR*)tp_i18n_get_text(STRING_WIFIKEY), wifikey, MMI_LCD_SHOW_STRING_LEN);
		wifikeyItem->textLen = strlen(wifikeyItem->text);
	}
	wifikeyItem->font = mmi_middle_twelve_font;
}


static VOID mmi_setSSIDKeyTip2Info(T_LcdShowInfoItem *ssidItem, T_LcdShowInfoItem *wifikeyItem, CHAR* ssid, CHAR *wifikey)
{
	char *ssid_str = NULL;
	
	ssidItem->type = LCD_SHOW_TEXT;
	ssidItem->needShow = TRUE;
	if (ssidItem->text != NULL) {
		ssid_str = get_ssid2_name();
		mmi_strcatStringWithPre(ssidItem->text, (CHAR*)tp_i18n_get_text(ssid_str), ssid, MMI_LCD_SHOW_STRING_LEN);
		ssidItem->textLen = strlen(ssidItem->text);
	}
	ssidItem->font = mmi_middle_twelve_font;

	wifikeyItem->type = LCD_SHOW_TEXT;
	wifikeyItem->needShow = TRUE;
	if (wifikeyItem->text != NULL) {
		mmi_strcatStringWithPre(wifikeyItem->text, (CHAR*)tp_i18n_get_text(STRING_WIFIKEY), wifikey, MMI_LCD_SHOW_STRING_LEN);
		wifikeyItem->textLen = strlen(wifikeyItem->text);
	}
	wifikeyItem->font = mmi_middle_twelve_font;
}

SINT32 mmi_getLcdTipInfo(UINT32 taskInfo)//sim state sim cmcc
{
	T_zMMITipInfo *pTipInfo = (T_zMMITipInfo *)taskInfo;

	if (pTipInfo->sim_tip != SIM_MAX) {
		mmi_setSIMStateTipInfo(pTipInfo->sim_tip);
		slog(MMI_PRINT, SLOG_DEBUG, "zte_mmi mmi_getLcdTipInfo ready for show SIM\n");
	} else if (mmi_IsShowNetPro()) {
		mmi_setNetProTipInfo(pTipInfo->net_pro);
		slog(MMI_PRINT, SLOG_DEBUG, "zte_mmi mmi_getLcdTipInfo ready for show NET CONNECT\n");
	}

	mmi_set_middle_needshow();
	return MMI_SUCCESS;
}

SINT32 mmi_getLcdTipNetConnInfo(UINT32 taskInfo)//net connect
{
	T_zMMITipInfo *pTipInfo = (T_zMMITipInfo *)taskInfo;
	mmi_setNeConTipInfo(pTipInfo->net_tip);
	mmi_set_middle_needshow();
	return MMI_SUCCESS;
}
SINT32 mmi_getLcdTipWpsInfo(UINT32 taskInfo)//wps
{
	T_zMMIWifiInfo *pWpsInfo = (T_zMMIWifiInfo *)taskInfo;
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI mmi_getLcdTipWpsInfo pWpsInfo->wps_state= %d!!\n", pWpsInfo->wps_state);
	mmi_setWpsActTipInfo(pWpsInfo->wps_state);
	mmi_set_middle_needshow();
	return MMI_SUCCESS;
}
SINT32 mmi_getLcdTipFotaInfo(UINT32 taskInfo)//FOTA
{
	T_zMMIFotaInfo *pFotaInfo = (T_zMMIFotaInfo *)taskInfo;
	if (pFotaInfo->fota_update != 0) {
		g_LcdShowInfoTab[LCD_SHOW_POWER].needShow = FALSE;
		mmi_setNewVersionTipInfo();
	} else {
		g_LcdShowInfoTab[LCD_SHOW_TIP_NEW_VERSION].needShow = FALSE;
		g_LcdShowInfoTab[LCD_SHOW_POWER].needShow = TRUE;
	}
	mmi_setFotaTipInfo(pFotaInfo->fota_tip);
	mmi_set_middle_needshow();

	return MMI_SUCCESS;
}

#if 0
/**********************************************************************************
*��������:��ȡʱ����ʾ��Ϣ
***********************************************************************************/
SINT32 mmi_getLcdTimeInfo(UINT32 taskInfo)
{
	T_zMMITimeInfo *pTimeInfo = (T_zMMITimeInfo *)taskInfo;
	g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].type = LCD_SHOW_TEXT;
	sprintf(g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].text, "%s:%s", pTimeInfo->hour, pTimeInfo->minute);
	if (g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].text != NULL) {
		g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].textLen = strlen(g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].text);
	}
	g_LcdShowInfoTab[LCD_SHOW_CURRENT_TIME].font = mmi_middle_twelve_font;
	return MMI_SUCCESS;
}
#endif
/**********************************************************************************
*��������:��ȡSSID/WIFIKEY��ʾ��Ϣ
***********************************************************************************/
SINT32 mmi_getLcdSSIDKeyInfo(UINT32 taskInfo)
{
	T_zMMISSIDInfo *pssidInfo = (T_zMMISSIDInfo *)taskInfo;
	mmi_setSSIDKeyTipInfo(&(g_LcdShowInfoTab[LCD_SHOW_WIFI_SSID]), &(g_LcdShowInfoTab[LCD_SHOW_WIFI_PASSWORD]), pssidInfo->ssid, pssidInfo->wifi_key);
	if (mmi_get_multi_ssid_switch_flag()) {
		mmi_setSSIDKeyTip2Info(&(g_LcdShowInfoTab[LCD_SHOW_WIFI_SSID2]), &(g_LcdShowInfoTab[LCD_SHOW_WIFI_PASSWORD2]), pssidInfo->ssid2, pssidInfo->wifi_key2);
	}
	return MMI_SUCCESS;
}

VOID mmi_initLcdShowInfoTab()
{
	UINT32 i = 0;
	//int length = 0;
	//length = tp_man_get_lcd_byte_length();
	for (i = 0; i < sizeof(g_LcdShowInfoTab) / sizeof(T_LcdShowInfoItem); ++ i) {
		if (g_LcdConfigInfoTab[g_LcdShowInfoTab[i].item].type == LCD_SHOW_PICTURE) {
			g_LcdShowInfoTab[i].bitmap = (BITMAP*)malloc(sizeof(BITMAP));
			memset((VOID *)g_LcdShowInfoTab[i].bitmap, 0, sizeof(BITMAP));
		}
		if (g_LcdConfigInfoTab[g_LcdShowInfoTab[i].item].type == LCD_SHOW_TEXT) {
			g_LcdShowInfoTab[i].text = (CHAR*)malloc(MMI_LCD_SHOW_STRING_LEN);
		}
	}
	/*g_mmi_bitmem = (CHAR*)malloc(length);
	if (g_mmi_bitmem == NULL) {
		assert(0);
	}*/
}
#endif
