/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_lcd.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-6-20
*  ����˵��  ��
*
*******************************************************************************/
#ifndef DISABLE_LCD
#include "mmi_common.h"
#include "mmiAppMainwin.h"
#include "app_infos.h"
#include "app_global_defines.h"

/*****************************��ʾ��Ϣ��ض���******************************************/

/*��ʾ��������*/
typedef enum {
	LCD_SHOW_PICTURE = 1,
	LCD_SHOW_TEXT,
	LCD_SHOW_RECT,
	LCD_SHOW_BACKGROUD,
	LCD_SHOW_ANIMATION,
	LCD_SHOW_BOX,//���Ʒ��������������
	LCD_SHOW_HLINE,
	LCD_SHOW_INVLAID,
} E_LCD_SHOW_CONTENT_TYPE;

/*��ʾ��*/
typedef enum {
	//===========top bar===========
	LCD_SHOW_NET_SIGNAL,
	LCD_SHOW_NET_CONNECT,
#ifdef QRZL_UE
	LCD_SHOW_LTE_HOTSPOT,
#endif
	LCD_SHOW_WIFISTATION_CONNECT,
	LCD_SHOW_SMS,
	LCD_SHOW_SMS_NUM,
	LCD_SHOW_WIFI,
	LCD_SHOW_TIP_NEW_VERSION,
	LCD_SHOW_POWER,//����
	LCD_SHOW_BATTERY,//���

	//============PAGE1============
	//============middle==========
	LCD_SHOW_CMCC,
	LCD_SHOW_TIP_WIFISTA_SSID,
	LCD_SHOW_TIP_SIM_STATE,
	LCD_SHOW_TIP_WPS_ACTIVE,
	LCD_SHOW_TIP_NET_CONNECTING,
	LCD_SHOW_TIP_NET_PROVIDER,
	LCD_SHOW_TIP_UPDATE_INFO,//zk add for fotaupdate result
#if defined(QRZL_UE) && defined(QRZL_CUSTOMER_XIANJI)
	LCD_SHOW_NET_PROVIDER_LOGO,
#endif
	//============temp=============
	LCD_SHOW_WIFI_SSID,
	LCD_SHOW_WIFI_PASSWORD,
	//==========PAGE SSID2 WIFI KEY=============
	LCD_SHOW_WIFI_SSID2,
	LCD_SHOW_WIFI_PASSWORD2,
	LCD_SHOW_WIFI_CODE2,


	//============PAGE1=============
	LCD_SHOW_TRAFFIC,
	LCD_SHOW_TRAFFIC_BAR,
	LCD_SHOW_TRAFFIC_SLIDER,
	LCD_SHOW_TRAFFIC_WARING,

	//==========PAGE3=============
	LCD_SHOW_WIFI_CODE,
	//LCD_SHOW_ACESS_DURATION_PRE,
	//LCD_SHOW_ACESS_DURATION,//add
	//LCD_SHOW_CURRENT_USAGE_PRE,
	//LCD_SHOW_CURRENT_USAGE,//add
	LCD_SHOW_POWER_OFF_CHARGER	,


	LCD_SHOW_MAX,

} E_LCD_SHOW_CONTENT_ITEM;

/*��ʾ����λ�á����ݶ�Ӧ��ϵ*/
typedef struct {
	E_LCD_SHOW_CONTENT_ITEM item;
	RECT rect;
	E_LCD_SHOW_CONTENT_TYPE type;
} T_LcdConfigInfo;


/*��ʾ��Ϣ*/
typedef struct {
	E_LCD_SHOW_CONTENT_ITEM item;
	BITMAP* bitmap;
	CHAR* text;
	PLOGFONT font;
	ZDWORD nFormat;
	SINT32 textLen;
	BOOL needShowFL;//�����ʾ���Ƴ�ͻ���⣬�ñ������ȼ�����needShow
	BOOL needShow;
	E_LCD_SHOW_CONTENT_TYPE type;
	UINT32 last;
	UINT32 color;
} T_LcdShowInfoItem;

extern T_zMMITaskInfoItem g_zMMITaskInfoTab[MMI_TASK_MAX];

typedef VOID (*SCROLLED_TIMER_CALLBACK)(VOID);

/*��ʾ��Ļ��Ϣ*/
//SINT32 mmi_showLcd(UINT32 lcdInfo);
VOID mmi_initLcdShowInfoTab();
HWND mmi_getMainWnd();

VOID mmi_startPowerOnFrame();
VOID mmi_startPowerOffFrame();
VOID mmi_startPowerResetFrame();
VOID mmi_startFastPowerOnFrame();
#endif

