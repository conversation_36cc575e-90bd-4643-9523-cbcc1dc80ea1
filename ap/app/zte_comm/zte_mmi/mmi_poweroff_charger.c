/*****************************************************************************
*  �汾���� (C)����ͨѶ�ɷ����޹�˾
*  ģ����    ��MMI
*  �ļ���    ��mmi_poweroff_charger.c
*  �ļ���ʶ  ��
*  ����ļ�  ��
*  ʵ�ֹ���  ���ػ���紦��
*  ����      ��
*  �汾      ��V1.0
*  �������  ��2014-8-6
*  ����˵��  ��
*
*******************************************************************************/
#include <linux/input.h>
#include <linux/netlink.h>
#include <sys/socket.h>
#include "mmi_common.h"
#include "mmi_lcd.h"
#include "hotplug.h"


extern UINT32 g_temp_protect;
extern UINT32 g_discharge_protect;
/*
extern SINT32 g_mmi_hightempvol;
extern SINT32 g_mmi_superhightempvol;
extern SINT32 g_mmi_lowtempvol;
extern SINT32 g_mmi_superlowtempvol;
*/
extern UINT32 g_mmi_poweroff_turnon_flag;
extern pthread_mutex_t g_mmi_poweron_mutex;
extern E_zMmiShowMode g_showMode;


static int g_poc_kpd_handle = -1;//�����豸�����ֻ�ڹػ���������õ�


static E_zMmi_Poc_State s_mmi_poc_state = POC_STATE_MAX;//�ػ����ʱ���״̬
static E_zMmi_Poc_Type s_mmi_poc_charging_type = POC_CHARGING_TYPE_MAX;//�ػ����ʱ�������
static SINT32 s_offchg_backlight_sta = 1;//�ػ����ʱ��������

static SINT32 s_offchg_backlight_timer_status = 0; //20s���������Ķ�ʱ��״̬
static BOOL s_mmi_poc_overvoltage_mode = FALSE;//��Ҫ���жϹػ����ʱ�Ƿ���ֵ��δ�������
static long s_offchg_keypress_begin = 0;//�ػ����ʱ���������¿�ʼ��ʱ
BOOL mmi_is_offchg_poweroff = FALSE; //�Ƿ�Ϊ�ػ����״̬�¹ػ�
static MMI_TEMP_DETECT offchg_last_temp = MMI_TEMP_DETECT_MAX;//�ػ����ʱ�������¶ȵ���һ��״̬
static SINT32 s_offchg_temp_count = 0;//�¶��쳣ʱ��ʼ����������3�κ������ز���
static BOOL g_poc_chg_switch_off = FALSE;//�ػ����ʱ���رտ��أ�Ĭ���������
static BOOL g_poc_dischg_low_current_switch = FALSE;//�ػ����ʱ��ǵ�ǰ�ķŵ������С

static SINT32 s_offchg_dischargeoff_voltage_num_low = 0;//�ŵ�������ó�0.5Aʱ���жϼ�����
static SINT32 s_offchg_dischargeoff_voltage_num_high = 0;//�ŵ�������ó�1.5Aʱ���жϼ�����
#define SET_OFFCHG_BACKLIGHT_OFF_TIME		20000//��Ļ�������Զ�������ʱ��

#ifndef DISABLE_LCD
extern OS_SEMA_ID g_mmi_gui_init_sem_id;
#endif

SINT32 offchg_voltage_state_read(VOID);


SINT32 offchg_get_pocinfo(UINT32 pocinfo)
{
	if (pocinfo != 0) {
		T_zMmi_Poc_Info * pPocInfo = (T_zMmi_Poc_Info *)pocinfo;
		pPocInfo->backlight_sta = s_offchg_backlight_sta;
		pPocInfo->poc_sta = s_mmi_poc_state;
		pPocInfo->overvoltage_mode = s_mmi_poc_overvoltage_mode;
	}
	return MMI_SUCCESS;
}

SINT32 offchg_RegisterPocTaskInfoItem()
{
	T_zMMITaskInfoItem poctaskInfoItem = {0};
	poctaskInfoItem.task = MMI_TASK_POWEROFF_CHARGER;
	poctaskInfoItem.taskinfo = (VOID*)malloc(sizeof(T_zMmi_Poc_Info));
	poctaskInfoItem.get_taskinfo_fun = offchg_get_pocinfo;
#ifndef DISABLE_LCD
	poctaskInfoItem.get_lcdinfo_fun = mmi_getLcdPowerOffChagerInfo;
#endif
	poctaskInfoItem.get_ledinfo_fun = mmi_getLedPowerOffChagerInfo;
	poctaskInfoItem.ledinfo = (VOID*)malloc(sizeof(T_zMmi_Led_Info));
	mmi_register_taskinfo_item(&poctaskInfoItem);
	return MMI_SUCCESS;
}

/**********************************************************************************
��������:�ػ����ر��ⶨʱ���ص�����
***********************************************************************************/
static VOID * offchg_set_backlight_off(VOID *arg)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_set_backlight_off !\n");
	s_offchg_backlight_sta = 0;
	mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
	s_offchg_backlight_timer_status = 0;
	return NULL;
}

/**********************************************************************************
��������:�ػ����ʱ�ް����ر��ⶨʱ����ʱ��20�룬һ���Զ�ʱ��
***********************************************************************************/
static VOID offchg_backlightoff_timer_create(VOID)
{
	int ret = -1;
	ret = CreateSoftTimer(SET_MMI_IDLE_TIMER, TIMER_FLAG_ONCE, SET_MMI_IDLE_TIME, &offchg_set_backlight_off, NULL);
	if (ret != 0) {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_backlightoff_timer_create FAILED\n");
	}
}

static VOID offchg_backlightoff_timer_stop()
{
	DeleteSoftTimer(SET_MMI_IDLE_TIMER);
}

/**********************************************************************************
*����˵������ȡ�ŵ���Ƿ�帺��״̬
 ***********************************************************************************/
static BOOL offchg_get_load_state(VOID)
{
	char load_state_buf[8] = {0};
	int len = 0;
	FILE* fd_boost_state = NULL;

	fd_boost_state = fopen(BOOST_LOAD_STATUS_PATH, "r");
	if (fd_boost_state == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI offchg_get_load_state open boost file fail!\n");
		//MMI_ASSERT(0);
		return FALSE;
	}
	len = fread(load_state_buf, 1, 2, fd_boost_state);
	if (len > 0) { //kw 3
		fclose(fd_boost_state);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_get_load_state load_state_buf=%s!\n", load_state_buf);
		if (strncmp(load_state_buf, "1", strlen("1")) == 0) {
			return TRUE;
		} else if (strncmp(load_state_buf, "0", strlen("0")) == 0) {
			return FALSE;
		} else {
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI offchg_get_load_state read load_state error!\n");
			return FALSE;
		}
	} else {
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_get_load_state read boost file fail len = %d!\n", len);
		fclose(fd_boost_state);
		return FALSE;
	}
}

static int offchg_check_power_low(VOID)
{
	SINT32 bat_volt = 0;
	bat_volt = offchg_voltage_state_read();
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI offchg_check_power_low bat_volt =%d\n\n", bat_volt);

	if (bat_volt != -1 &&  bat_volt < POWERONLEVEL) {
		return 1;
	}
	return 0;
}
/**********************************************************************************
*����˵������ȡ���״̬
 ***********************************************************************************/
static VOID ofchg_get_charge_status(VOID)
{
	char chg_state_buf[CHARGE_STATUS_LENGTH] = {0};
	int len = 0;
	FILE *fd_charger = NULL;
	fd_charger = fopen(CHARGE_STATUS_PATH, "r");
	if (fd_charger == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI ofchg_get_charge_status open charging file fail!\n");
		//MMI_ASSERT(0);
		return;
	}
	len = fread(chg_state_buf, 1, CHARGE_STATUS_LENGTH, fd_charger);
	if (len > 0) { //kw 3
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI ofchg_get_charge_status chg_state_buf=%s !\n", chg_state_buf);
		if (strncmp(chg_state_buf, CHARGE_STATUS_CHARGING, CHARGE_STATUS_CHARGING_LENGTH) == 0) {
			g_poc_chg_switch_off = FALSE;
			if (1 == offchg_check_power_low())
				s_mmi_poc_state = POC_STATE_LOWBATTERY;
			else
				s_mmi_poc_state = POC_STATE_CHARGING;
			mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
		} else if (strncmp(chg_state_buf, CHARGE_STATUS_FULL, CHARGE_STATUS_FULL_LENGTH) == 0) {
			g_poc_chg_switch_off = FALSE;
			s_mmi_poc_state = POC_STATE_FULL;
			mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
		} else if (strncmp(chg_state_buf, CHARGE_STATUS_NOTCHARGING, CHARGE_STATUS_NOTCHARGING_LENGTH) == 0) {
			s_mmi_poc_state = POC_STATE_TEMP_ERROR;
			if (!g_poc_chg_switch_off) {				
				system(MMI_TURN_OFF_CHG);
				g_poc_chg_switch_off = TRUE;
				s_offchg_backlight_sta = 0;
				mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
			}
		} else if (strncmp(chg_state_buf, CHARGE_STATUS_DISCHARGING, CHARGE_STATUS_DISCHARGING_LENGTH) == 0) {
			//��RTC����Ϣ������Ҫ�յ��ظ�
			set_wake_lock(MMI_POWEROFF_LOCK_LCD_ID);
			if (rtc_timer_del_all(MODULE_ID_MMI, MSG_CMD_POWEROFF_RSP) == 0) {
				slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI poweroff_msg_process: send msg to rtc ok!!!\n");
			}
			mmi_is_offchg_poweroff = TRUE;
			s_mmi_poc_overvoltage_mode = TRUE;
			mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
		}
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI ofchg_get_charge_status read voltage file fail len = %d !\n", len);
	}
	fclose(fd_charger);
}

/**********************************************************************************
*����˵���� ��ȡ��ѹֵ
***********************************************************************************/
SINT32 offchg_voltage_state_read(VOID)
{
	char buf_volt[CHARGE_VOLTAGE_LENGTH] = {0};
	FILE* fd_voltage = NULL;
	int len = 0;
	int voltagepower = 0;

	fd_voltage = fopen(CHARGE_VOLTAGE_PATH, "r");
	if (fd_voltage == NULL) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI open voltage file fail!\n");
		//MMI_ASSERT(0);
		return -1;
	}
	len = fread(buf_volt, 1, CHARGE_VOLTAGE_LENGTH, fd_voltage);
	if (len > 0) { //kw 3
		voltagepower = atoi(buf_volt);
		fclose(fd_voltage);
		return voltagepower;
	} else {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI read voltage file fail len = %d !\n", len);
		fclose(fd_voltage);
		return -1;
	}
}
/**********************************************************************************
*��������:�ػ����ʱ���е͵�ŵ籣��
***********************************************************************************/
VOID offchg_lowbattery_discharge_protect(SINT32 voltagepower)
{
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_lowbattery_discharge_protect voltagepower = %d !!!\n", voltagepower);
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_lowbattery_discharge_protect s_offchg_dischargeoff_voltage_num_low = %d, s_offchg_dischargeoff_voltage_num_high = %d !!\n", s_offchg_dischargeoff_voltage_num_low, s_offchg_dischargeoff_voltage_num_high);
	BOOL isLoadFlag = FALSE;
	isLoadFlag = offchg_get_load_state();
	if (isLoadFlag) {
		if (voltagepower < DISCHARGELEVEL) {
			s_offchg_dischargeoff_voltage_num_high = 0;
			s_offchg_dischargeoff_voltage_num_low ++ ;
			if (s_offchg_dischargeoff_voltage_num_low == 3) {
				s_offchg_dischargeoff_voltage_num_low = 0;
				if (!g_poc_dischg_low_current_switch) {
					system(MMI_DISCHG_LOW_CURRENT);
					slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI offchg_lowbattery_discharge_protect turn to LOW current!!!\n");
					g_poc_dischg_low_current_switch = TRUE;
					mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
				}
			}
		} else if (voltagepower > CHARGINGLEVEL) {
			s_offchg_dischargeoff_voltage_num_low = 0;
			s_offchg_dischargeoff_voltage_num_high ++ ;
			if (s_offchg_dischargeoff_voltage_num_high == 3) {
				s_offchg_dischargeoff_voltage_num_high = 0;
				if (g_poc_dischg_low_current_switch) {
					system(MMI_DISCHG_HIGH_CURRENT);
					slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI offchg_lowbattery_discharge_protect turn to HIGH current!!!\n");
					g_poc_dischg_low_current_switch = FALSE;
					mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
				}
			}
		} else {
			s_offchg_dischargeoff_voltage_num_low = 0;
			s_offchg_dischargeoff_voltage_num_high = 0;
		}
	} else {
		mmi_set_discharge_switch(FALSE);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_lowbattery_discharge_protect NO_Load MMI_TURN_OFF_DISCHG!!!\n");
	}
}

/**********************************************************************************
*����˵�� :�ػ����ʱ�͵����߳�
 ***********************************************************************************/
static VOID offchg_create_get_voltage_thread(VOID)
{
	system(MMI_TURN_OFF_DISCHG);
}

/**********************************************************************************
*����˵������������
************************************************************************************/
static VOID offchg_handle_power_on(VOID)
{
	SINT32 bat_volt = 0;
	bat_volt = offchg_voltage_state_read();
	slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI s_mmi_poc_overvoltage_mode=%d, bat_volt =%d\n\n", s_mmi_poc_overvoltage_mode, bat_volt);

	if (bat_volt != -1 &&  bat_volt < POWERONLEVEL) {
		if(s_mmi_poc_overvoltage_mode)
			s_mmi_poc_state = POC_STATE_LOWBATTERY;
		mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
	} else {
		set_wake_lock(MMI_POWEROFF_LOCK_ID);
		slog(MMI_PRINT, SLOG_NORMAL, "ZTE_MMI handleKey ######KEY_POWER LONGPRESS###### POWER ON!!!!!!\n\n");
#ifndef DISABLE_LCD
		if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {
			mmi_getMutex(&g_mmi_poweron_mutex);
			g_mmi_poweroff_turnon_flag = TRUE;
			mmi_putMutex(&g_mmi_poweron_mutex);
			s_offchg_backlight_sta = 0;
			mmi_stopLcdBatteryTimer();
			mmi_setMainWindToBg();
			mmi_lcd_backlight_end();
			tp_man_Lcd_Sleep_Enter();
		}
#endif
		system("reboot");//��ʱ���ز�����������MMI�Լ���		
	}
}
/**********************************************************************************
��������:��ȡ����ʱ��
***********************************************************************************/
long offchg_get_keypress_time()
{
	struct timeval tv;
	long second;
	gettimeofday(&tv, NULL);
	second = tv.tv_sec;
	return second;
}

/************************************************************************************
 ��������:��ȡ������Ϣ
***********************************************************************************/
static VOID offchg_get_keystrokes_data(MMI_KP_INFO *kp_info)
{
	int retR = -1;
	struct input_event kp_event = {0};

	memset((VOID *)kp_info, 0, sizeof(MMI_KP_INFO));
	kp_info->status = KEY_STATUS_MAX;


	retR = read(g_poc_kpd_handle, (CHAR *)&kp_event, sizeof(kp_event));
	if (retR < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI get_keystrokes_data read falied c-err:%d!",errno);
		return ;
	}
	if (kp_event.type == 1) {
		if (kp_event.value == 1) {
			set_wake_lock(MMI_POWEROFF_LOCK_LCD_ID);
			kp_info->status = KEY_STATUS_DOWN;
			s_offchg_keypress_begin = offchg_get_keypress_time();
		} else if (kp_event.value == 0) {
			kp_info->status = KEY_STATUS_UP;
			s_offchg_keypress_begin = 0;
		} else if (kp_event.value == 2) {
			long sec_end = offchg_get_keypress_time();
			int sec_lingpress = (int)(sec_end - s_offchg_keypress_begin);
			if (sec_lingpress >= 3) {
				kp_info->status = KEY_STATUS_LONGPRESS;
			}
		}
		if (kp_event.code == KEY_POWER_CODE) {
			kp_info->type = KEY_TYPE_POWER;
		} else if (kp_event.code == KEY_RESET_CODE) {
			kp_info->type = KEY_TYPE_RESET;
		} else if (kp_event.code == KEY_WPS_CODE) {
			kp_info->type = KEY_TYPE_WPS;
		}
	} else {
		kp_info->status = KEY_STATUS_MAX;
		return;
	}

}

/**********************************************************************************
*����˵������������
************************************************************************************/
static VOID offchg_handle_key_thread(VOID)
{
	MMI_KP_INFO keyData = {0};
	BOOL longPressFlag = FALSE;
	int ret = 0;
	
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_handle_key_thread  begin !!\n ");
	
	prctl(PR_SET_NAME, "offchghandlekey", 0, 0, 0);

#ifndef DISABLE_LCD
	if (g_showMode == MMI_MODE_LCD || g_showMode == MMI_MODE_ALL) {	
		while ((ret = mmi_GetSemaphore(&g_mmi_gui_init_sem_id, MMI_WAIT_FOREVER)) != MMI_SUCCESS) {
			slog(MMI_PRINT, SLOG_DEBUG, "zte_mmi offchg_handle_key_thread get gui_init_sem_id failed ret = %d, errno=%d\n", ret, errno);
			continue;
		}
		mmi_DeleteSemaphore(&g_mmi_gui_init_sem_id);
	}
#endif

	while (1) {
		offchg_get_keystrokes_data(&keyData);
		slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_handle_key_thread Key's type:%d, status:%d\n", keyData.type, keyData.status);
		
		switch (keyData.status) {
		case KEY_STATUS_DOWN: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYDOWN !!! s_offchg_backlight_sta=%d,g_poc_chg_switch_off=%d\n", s_offchg_backlight_sta, g_poc_chg_switch_off);
	#ifndef JCV_HW_MZ801_V1_2
			offchg_backlightoff_timer_stop();
			offchg_backlightoff_timer_create();
	#endif
			if (s_offchg_backlight_sta == 0 && !g_poc_chg_switch_off) {
				s_offchg_backlight_sta = 1;
				mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
			}
			break;
		}
		case KEY_STATUS_UP: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYUP KEYUP !!! longPressFlag=%d", longPressFlag);

			if (TRUE == longPressFlag) {
				longPressFlag = FALSE;		//��ǳ�������
			}
			if (s_mmi_poc_state == POC_STATE_LOWBATTERY || s_mmi_poc_state == POC_STATE_NOBATTERY) {
				if (1 == offchg_check_power_low()) {
					set_wake_unlock(MMI_POWEROFF_LOCK_LCD_ID);
				}
				else {
					s_mmi_poc_state = POC_STATE_CHARGING;
					mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
				}
			}

			break;
		}
		case KEY_STATUS_LONGPRESS: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYLONGPRESS  longPressFlag =%d\n", longPressFlag);
			if (FALSE == longPressFlag) {
				longPressFlag = TRUE;
				if (keyData.type == KEY_TYPE_POWER) {
					offchg_handle_power_on();
				}
			}
			break;
		}
		default: {
			break;
		}
		}
	}

}

#if 0
/***********************************************************************************
   ��������:�Զ�������ģ�ⰴ�����ػ����
***********************************************************************************/
SINT32 zMMI_Handle_Msg_Atest_Chg(VOID *data)
{
	static BOOL longPressFlag = FALSE;
	//value:�����¼� code:������ֵ
	autotest_key_rspmsg *kp_event = (autotest_key_rspmsg *)data;
	MMI_KP_INFO keyData = {0};

	if (kp_event->value == 1) {
		set_wake_lock(MMI_POWEROFF_LOCK_LCD_ID);
		keyData.status = KEY_STATUS_DOWN;
		s_offchg_keypress_begin = offchg_get_keypress_time();
	} else if (kp_event->value == 0) {
		keyData.status = KEY_STATUS_UP;
		s_offchg_keypress_begin = 0;
	} else if (kp_event->value == 2) {
		long sec_end = offchg_get_keypress_time();
		int sec_lingpress = (int)(sec_end - s_offchg_keypress_begin);
		if (sec_lingpress >= 3) {
			keyData.status = KEY_STATUS_LONGPRESS;
		}
	}
		
	if (kp_event->code == KEY_POWER_CODE) {
		keyData.type = KEY_TYPE_POWER;
	} else if (kp_event->code == KEY_RESET_CODE) {
		keyData.type = KEY_TYPE_RESET;
	} else if (kp_event->code == KEY_WPS_CODE) {
		keyData.type = KEY_TYPE_WPS;
	}
	
	switch (keyData.status) {
		case KEY_STATUS_DOWN: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYDOWN !!! s_offchg_backlight_sta=%d,g_poc_chg_switch_off=%d\n", s_offchg_backlight_sta, g_poc_chg_switch_off);
			offchg_backlightoff_timer_stop();
			offchg_backlightoff_timer_create();
			if (s_offchg_backlight_sta == 0 && !g_poc_chg_switch_off) {
				s_offchg_backlight_sta = 1;
				mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
			}
			break;
		}
		case KEY_STATUS_UP: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYUP KEYUP !!! longPressFlag=%d", longPressFlag);

			if (TRUE == longPressFlag) {
				longPressFlag = FALSE;		//��ǳ�������
			}
			if (s_mmi_poc_state == POC_STATE_LOWBATTERY || s_mmi_poc_state == POC_STATE_NOBATTERY) {
				s_mmi_poc_state = POC_STATE_CHARGING;
				mmi_set_update_flag(MMI_TASK_POWEROFF_CHARGER);
			}

			break;
		}
		case KEY_STATUS_LONGPRESS: {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg KEYLONGPRESS  longPressFlag =%d\n", longPressFlag);
			if (FALSE == longPressFlag) {
				longPressFlag = TRUE;
				if (keyData.type == KEY_TYPE_POWER) {
					offchg_handle_power_on();
				}
			}
			break;
		}
		default: {
			break;
		}
	}
	return 0;
}
#endif

/**********************************************************************************
*����˵������������������Ϣ�Ľ���
************************************************************************************/
static VOID offchg_init_handle_key_thread(VOID)
{
	g_poc_kpd_handle = open(KPD_PATH, O_RDONLY);
	if (g_poc_kpd_handle < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI offchg_init_handle_key_thread open kp dev FAILED!!\n");
		MMI_ASSERT(0);
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_init_dev_handle success!\n");

	offchg_handle_key_thread();
}

/**********************************************************************************
*����˵�������״̬(�γ�/����/����) �ı�ʱ�Ļص�����
************************************************************************************/
static void offchg_chargingstates_check()
{
	ofchg_get_charge_status();
}

/**********************************************************************************
*����˵����netlink���ݼ�������
 ��             �룺const char *key��ָ��ؼ��ֵ�ָ��
			          const char *buf��ָ���ַ�����ָ��
			          size_t len���ַ�������
��   ��   ֵ�� �����ַ����ж�Ӧ�ؼ��ֵ���ʼ��ַ�������������ؼ��֣�����NULL
***********************************************************************************/
static const char *offchg_search_netlink(const char *key, const char *buf, size_t len)
{
	size_t curlen = 0;
	size_t keylen = strlen((char *)key);
	char *cur = (char *)buf;

	while (cur < buf + len - keylen) {
		curlen = strlen(cur);
		if (curlen == 0)
			break;
		if (!strncmp(key, cur, keylen) && cur[keylen] == '=') {
			return cur + keylen + 1;
		}
		cur += (curlen + 1);
	}
	return NULL;
}

/**********************************************************************************
*����˵������netlink����������Ϣ�ַ������������Ϣ
 ***********************************************************************************/
void offchg_process_netlink_event(int netlink_fd)
{
	char buf[1024] = {0};
	int byte_counts = -1;
	const char *keys = NULL, *subsys = NULL, *action = NULL;
	const char *power_supply_name = NULL;
	byte_counts = recv(netlink_fd, buf, sizeof(buf)-1, 0);
	buf[sizeof(buf)-1] = '\0';//cov

	if (byte_counts <= 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI receive from netlonk error/n");
		return;
	}
	//��ӡ���ϱ���uevent��Ϣ�ϱ����ַ���
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI received data:%s\n",buf);
	keys = (char *)(buf + strlen((char *)buf) + 1);
	byte_counts -= (strlen((char*)buf) + 1);
	//���������ַ���
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI received keys:%s\n",keys);

	//������Ĺؼ��ֽ�������
	subsys = offchg_search_netlink("SUBSYSTEM", keys, byte_counts);
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI subsys:%s\n",subsys);

	action = offchg_search_netlink("ACTION", keys, byte_counts);
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI action:%s\n",action);

	power_supply_name = offchg_search_netlink("POWER_SUPPLY_NAME", keys, byte_counts);
	//�����ֵײ���power_supply��change,��ȥ��ȡ���״̬����״̬������Ӧ�ĵ�Ʋ�����
	if ((subsys != NULL) && (action != NULL)) { // for kernel 3.0
		if ((!strcmp(subsys, "power_supply")) && (!(strcmp(action, "change")))) {
			if ((power_supply_name != NULL) && (!strcmp(power_supply_name, "charger"))) {//klocwork
				//�ж��Ƿ��ǳ��״̬�ļ������ı仯
				offchg_chargingstates_check();
			} else {
				return;
			}
		}
	}
}
int offchg_app_msg_parse(const char *msg, int msglen, struct hotplug_event *event)
{
	int byte_counts = -1;
	const char *keys = NULL, *subsys = NULL, *action = NULL;
	const char *power_supply_name = NULL;
	byte_counts = msglen;

	if (byte_counts <= 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI receive from netlonk error/n");
		return -1;
	}
	//��ӡ���ϱ���uevent��Ϣ�ϱ����ַ���
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI received data:%s\n",buf);
	keys = (char *)(msg + strlen((char *)msg) + 1);
	byte_counts -= (strlen((char*)msg) + 1);
	//���������ַ���
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI received keys:%s\n",keys);

	//������Ĺؼ��ֽ�������
	subsys = offchg_search_netlink("SUBSYSTEM", keys, byte_counts);
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI subsys:%s\n",subsys);

	action = offchg_search_netlink("ACTION", keys, byte_counts);
	//slog(MMI_PRINT,SLOG_DEBUG,"ZTE_MMI action:%s\n",action);

	power_supply_name = offchg_search_netlink("POWER_SUPPLY_NAME", keys, byte_counts);
	//�����ֵײ���power_supply��change,��ȥ��ȡ���״̬����״̬������Ӧ�ĵ�Ʋ�����
	if ((subsys != NULL) && (action != NULL)) { // for kernel 3.0
		if ((!strcmp(subsys, "power_supply")) && (!(strcmp(action, "change")))) {
			if ((power_supply_name != NULL) && (!strcmp(power_supply_name, "charger"))) {
				//�ж��Ƿ��ǳ��״̬�ļ������ı仯
				offchg_chargingstates_check();
			} else {
				return -1;
			}
		}
	}
	return -1;
}

/**********************************************************************************
*����˵������charging_netlink�׽���
  ����ֵ�� �򿪵�netlink�׽������������򿪳ɹ����ط�0ֵ
 ***********************************************************************************/
int offchg_open_charging_netlink()
{
	struct sockaddr_nl addr;
	int s = -1;
	memset((VOID *)(&addr), 0, sizeof(addr));
	addr.nl_family = AF_NETLINK;
	addr.nl_pid = 0;//getpid();
	addr.nl_groups = 1;
	s = socket(PF_NETLINK, SOCK_DGRAM, NETLINK_KOBJECT_UEVENT);
	if (s < 0) {
		return -1;//wk 3
	}
//  setsockopt(s, SOL_SOCKET, SO_RCVBUFFORCE, &sz, sizeof(sz));
	if (bind(s, (struct sockaddr *) &addr, sizeof(addr)) < 0) {
		close(s);
		return -1;//wk 3
	}
	return s;
}

/**********************************************************************************
*����˵���������������߳�
 ***********************************************************************************/
void *offchg_chargingcheck_process_thread(void *arg)
{
	int charging_netlink = -1;
	int fd_counts = -1;
	fd_set readfds;
	prctl(PR_SET_NAME, "offchgchgck", 0, 0, 0);
	if ((charging_netlink = offchg_open_charging_netlink()) < 0) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI charging_netlink wrong \n");
		return NULL;
	}
	prctl(PR_SET_NAME, "offchgcheck", 0, 0, 0);
	while (1) {
		FD_ZERO(&readfds);
		FD_SET(charging_netlink, &readfds);
		fd_counts = select(charging_netlink + 1, &readfds, NULL, NULL, NULL);

		if (fd_counts < 0) {
			slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI select usb_netlink error! \n");
			continue;
		} else if (fd_counts == 0) {
			slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI select usb_netlink timeout\n");

			continue;
		} else {
			if (charging_netlink > 0 && FD_ISSET(charging_netlink, &readfds)) {
				offchg_process_netlink_event(charging_netlink);
			}
		}
	}
}

static VOID offchg_create_chargestate_check_thread(VOID)
{
	pthread_t mmi_chgsta_thread;
	if (pthread_create(&mmi_chgsta_thread, NULL, &offchg_chargingcheck_process_thread, NULL) == -1) {
		slog(MMI_PRINT, SLOG_ERR, "ZTE_MMI offchg_create_chargestate_check_thread error\n");
		return;
	}
	slog(MMI_PRINT, SLOG_DEBUG, "ZTE_MMI offchg_create_chargestate_check_thread success!!!\n");

}

/**********************************************************************************
*����˵�����ػ���紦����ں���
************************************************************************************/
int zMmi_PowerOffChargerEntry()
{

	set_wake_lock(MMI_POWEROFF_LOCK_LCD_ID);
	mmi_poweroffcharger_init();
	ofchg_get_charge_status();
#ifndef JCV_HW_MZ801_V1_2
	offchg_backlightoff_timer_create();
#endif
	//offchg_create_chargestate_check_thread();
	hotplug_parse_register(DEVICE_TYPE_APP_MMI_OFFCHG, offchg_app_msg_parse);

	if (g_discharge_protect) {
		offchg_create_get_voltage_thread();
	}
	
	if (1 == offchg_check_power_low()) {
		set_wake_unlock(MMI_POWEROFF_LOCK_LCD_ID);
		set_wake_lock_timeout(MMI_POWEROFF_LOCK_LCD_ID, 10);
	}

	offchg_init_handle_key_thread();
	return 0;

}


