#*******************************************************************************
# include ZTE application makefile
#*******************************************************************************
include $(COMMON_MK)

##############USER COMIZE BEGIN################
EXEC = sntp
OBJS = sntp.o 

CFLAGS += -I$(zte_app_path)/include
CFLAGS += -I$(zte_lib_path)/libnvram
CFLAGS += -I$(zte_lib_path)/libsqlite
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -g
CFLAGS += -g -Werror=implicit-function-declaration

ifeq ($(CUSTOM_MODEL), MF29S2)
CFLAGS	+= -DCUSTOM_VERSION_MF29S2_ZTE
endif 

LDLIBS = -lpthread -lm
LDLIBS += -lnvram -L$(zte_lib_path)/libnvram
LDLIBS += -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer

##############USER COMIZE END##################

#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) -Wl,--end-group
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)

clean:
	-rm -f $(EXEC) *.elf *.gdb *.o
