# German translations for dnsmasq package.
#
# This revised version is (C) Copyright by
# <PERSON> <<EMAIL>>, 2010 - 2021.
# <PERSON> <<EMAIL>>, 2014 - 2020.
# It is subject to the GNU General Public License v2,
# or at your option, any later version.
#
# An older version of this file was originally put in the public domain by
# <PERSON> <<EMAIL>>, 2005.
msgid ""
msgstr ""
"Project-Id-Version: dnsmasq 2.85rc2\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-03-20 00:00+0000\n"
"PO-Revision-Date: 2021-03-27 15:30+0100\n"
"Last-Translator: <PERSON> <matthi<PERSON>.<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: cache.c:572
msgid "Internal error in cache."
msgstr "Interner Fehler im Cache."

#: cache.c:1094
#, c-format
msgid "failed to load names from %s: %s"
msgstr "Fehler beim Laden der Namen von %s: %s"

#: cache.c:1116 dhcp.c:931
#, c-format
msgid "bad address at %s line %d"
msgstr "Fehlerhafte Adresse in %s Zeile %d"

#: cache.c:1169 dhcp.c:947
#, c-format
msgid "bad name at %s line %d"
msgstr "Fehlerhafter Name in %s Zeile %d"

#: cache.c:1180 dhcp.c:1022
#, c-format
msgid "read %s - %d addresses"
msgstr "%s gelesen - %d Adressen"

#: cache.c:1296
msgid "cleared cache"
msgstr "Cache geleert"

#: cache.c:1358
#, c-format
msgid "No IPv4 address found for %s"
msgstr "Keine IPv4-Adresse für %s gefunden"

#: cache.c:1404
#, c-format
msgid "%s is a CNAME, not giving it to the DHCP lease of %s"
msgstr "%s ist ein CNAME, weise es der DHCP-Lease von %s nicht zu"

#: cache.c:1428
#, c-format
msgid "not giving name %s to the DHCP lease of %s because the name exists in %s with address %s"
msgstr "Name %s wurde dem DHCP-Lease von %s nicht zugewiesen, da der Name in %s bereits mit Adresse %s existiert"

#: cache.c:1674
#, c-format
msgid "time %lu"
msgstr "Zeit %lu"

#: cache.c:1675
#, c-format
msgid "cache size %d, %d/%d cache insertions re-used unexpired cache entries."
msgstr "Cache Größe %d, %d/%d Cache-Einfügungen verwendeten nicht abgelaufene Cache-Einträge wieder."

#: cache.c:1677
#, c-format
msgid "queries forwarded %u, queries answered locally %u"
msgstr "weitergeleitete Anfragen %u, lokal beantwortete Anfragen %u"

#: cache.c:1680
#, c-format
msgid "queries for authoritative zones %u"
msgstr "Anfragen nach autoritativen Zonen %u"

#: cache.c:1702
#, c-format
msgid "server %s#%d: queries sent %u, retried or failed %u"
msgstr "Server %s#%d: Anfragen gesendet %u, erneut versucht oder fehlgeschlagen %u"

#: util.c:51
#, c-format
msgid "failed to seed the random number generator: %s"
msgstr "Konnte den Zufallszahlengenerator nicht initialisieren: %s"

#: util.c:228
msgid "failed to allocate memory"
msgstr "Konnte Speicher nicht belegen"

#: util.c:285 option.c:665
msgid "could not get memory"
msgstr "Speicher nicht verfügbar"

#: util.c:306
#, c-format
msgid "cannot create pipe: %s"
msgstr "Konnte Pipe nicht erzeugen: %s"

#: util.c:314
#, c-format
msgid "failed to allocate %d bytes"
msgstr "Konnte %d Bytes nicht belegen"

# @Simon: not perfect but I cannot get nearer right now.
#: util.c:520
#, c-format
msgid "infinite"
msgstr "unendlich"

#: util.c:808
#, c-format
msgid "failed to find kernel version: %s"
msgstr "konnte Kernelversion nicht finden: %s"

#: option.c:372
msgid "Specify local address(es) to listen on."
msgstr "Lokale abzuhörende Adresse(n) angeben."

#: option.c:373
msgid "Return ipaddr for all hosts in specified domains."
msgstr "IP-Adresse für alle Hosts in angegebenen Domänen festlegen."

#: option.c:374
msgid "Fake reverse lookups for RFC1918 private address ranges."
msgstr "Rückwärtsauflösungen für private Adressbereiche nach RFC1918 falsch erfinden."

#: option.c:375
msgid "Treat ipaddr as NXDOMAIN (defeats Verisign wildcard)."
msgstr "Diese IP-Adresse als NXDOMAIN interpretieren (wehrt \"Suchhilfen\" ab)."

#: option.c:376
#, c-format
msgid "Specify the size of the cache in entries (defaults to %s)."
msgstr "Größe des Caches (Zahl der Einträge) festlegen (Voreinstellung: %s)."

#: option.c:377
#, c-format
msgid "Specify configuration file (defaults to %s)."
msgstr "Konfigurationsdatei festlegen (Voreinstellung: %s)."

#: option.c:378
msgid "Do NOT fork into the background: run in debug mode."
msgstr "NICHT in den Hintergrund gehen: Betrieb im Debug-Modus."

#: option.c:379
msgid "Do NOT forward queries with no domain part."
msgstr "Anfragen ohne Domänen-Teil NICHT weiterschicken."

#: option.c:380
msgid "Return self-pointing MX records for local hosts."
msgstr "Für lokale Einträge MX-Einträge liefern, die auf sich selbst zeigen."

#: option.c:381
msgid "Expand simple names in /etc/hosts with domain-suffix."
msgstr "Einfache Namen in /etc/hosts um Domänen-Endung erweitern."

#: option.c:382
msgid "Don't forward spurious DNS requests from Windows hosts."
msgstr "Unberechtigte DNS-Anfragen von Windows-Rechnern nicht weiterleiten."

#: option.c:383
msgid "Enable DHCP in the range given with lease duration."
msgstr "DHCP für angegebenen Bereich und Lease-Dauer einschalten."

#: option.c:384
#, c-format
msgid "Change to this group after startup (defaults to %s)."
msgstr "Nach dem Start in diese Benutzergruppe wechseln (Voreinstellung %s)."

#: option.c:385
msgid "Set address or hostname for a specified machine."
msgstr "Adresse oder Hostnamen für einen angegebenen Computer setzen."

#: option.c:386
msgid "Read DHCP host specs from file."
msgstr "DHCP-Host-Angaben aus Datei lesen."

#: option.c:387
msgid "Read DHCP option specs from file."
msgstr "DHCP-Optionen aus Datei lesen."

#: option.c:388
msgid "Read DHCP host specs from a directory."
msgstr "DHCP-Host-Angaben aus einem Verzeichnis lesen."

#: option.c:389
msgid "Read DHCP options from a directory."
msgstr "DHCP-Optionen aus einem Verzeichnis lesen."

#: option.c:390
msgid "Evaluate conditional tag expression."
msgstr "Auswertung eines Ausdrucks bedingter Marken."

#: option.c:391
#, c-format
msgid "Do NOT load %s file."
msgstr "%s-Datei NICHT laden."

#: option.c:392
#, c-format
msgid "Specify a hosts file to be read in addition to %s."
msgstr "Hosts-Datei festlegen, die zusätzlich zu %s gelesen wird."

#: option.c:393
msgid "Read hosts files from a directory."
msgstr "DHCP-Host-Dateien aus einem Verzeichnis lesen."

#: option.c:394
msgid "Specify interface(s) to listen on."
msgstr "Schnittstelle(n) zum Empfang festlegen."

#: option.c:395
msgid "Specify interface(s) NOT to listen on."
msgstr "Schnittstelle(n) festlegen, die NICHT empfangen sollen."

#: option.c:396
msgid "Map DHCP user class to tag."
msgstr "DHCP-Benutzerklasse auf Marke abbilden."

#: option.c:397
msgid "Map RFC3046 circuit-id to tag."
msgstr "RFC3046 \"circuit-id\" auf Marke abbilden."

#: option.c:398
msgid "Map RFC3046 remote-id to tag."
msgstr "RFC3046 \"remote-id\" auf Marke abbilden."

#: option.c:399
msgid "Map RFC3993 subscriber-id to tag."
msgstr "RFC3993 \"subscriber-id\" auf Marke abbilden."

#: option.c:400
msgid "Specify vendor class to match for PXE requests."
msgstr "Herstellerklasse für Vergleich von PXE-Anforderungen angeben"

#: option.c:401
msgid "Don't do DHCP for hosts with tag set."
msgstr "Kein DHCP für Hosts mit gesetzter Marke verwenden."

#: option.c:402
msgid "Force broadcast replies for hosts with tag set."
msgstr "Antwort per Broadcast für Hosts mit gesetzter Marke erzwingen."

#: option.c:403
msgid "Do NOT fork into the background, do NOT run in debug mode."
msgstr "NICHT in den Hintergrund wechseln, NICHT im Debug-Modus laufen."

#: option.c:404
msgid "Assume we are the only DHCP server on the local network."
msgstr "Unterstellen, dass wir der einzige DHCP-Server im lokalen Netz sind."

#: option.c:405
#, c-format
msgid "Specify where to store DHCP leases (defaults to %s)."
msgstr "Festlegen, wo DHCP-Leases gespeichert werden (Voreinstellung %s)."

#: option.c:406
msgid "Return MX records for local hosts."
msgstr "MX-Einträge für lokale Hosts liefern."

#: option.c:407
msgid "Specify an MX record."
msgstr "Einen MX-Eintrag festlegen."

#: option.c:408
msgid "Specify BOOTP options to DHCP server."
msgstr "BOOTP-Optionen für DHCP-Server festlegen."

#: option.c:409
#, c-format
msgid "Do NOT poll %s file, reload only on SIGHUP."
msgstr "%s-Datei NICHT abfragen, nur bei SIGHUP neu laden."

#: option.c:410
msgid "Do NOT cache failed search results."
msgstr "Fehlerhafte Suchergebnisse NICHT zwischenspeichern."

#: option.c:411
#, c-format
msgid "Use nameservers strictly in the order given in %s."
msgstr "Namensserver streng in der in %s angegebenen Reihenfolge verwenden."

#: option.c:412
msgid "Specify options to be sent to DHCP clients."
msgstr "Optionen festlegen, die an DHCP-Klienten gesendet werden."

#: option.c:413
msgid "DHCP option sent even if the client does not request it."
msgstr "DHCP-Option, die selbst ohne Klientenanfrage gesendet wird."

#: option.c:414
msgid "Specify port to listen for DNS requests on (defaults to 53)."
msgstr "Port zum Empfangen der DNS-Anfragen festlegen (53 voreingestellt)."

#: option.c:415
#, c-format
msgid "Maximum supported UDP packet size for EDNS.0 (defaults to %s)."
msgstr "Maximale unterstützte UDP-Paketgröße für EDNS.0 (Voreinstellung %s)."

#: option.c:416
msgid "Log DNS queries."
msgstr "DNS-Anfragen protokollieren."

#: option.c:417
msgid "Force the originating port for upstream DNS queries."
msgstr "Ausgehenden Port für DNS-Anfragen an vorgelagerte Server erzwingen."

#: option.c:418
msgid "Do NOT read resolv.conf."
msgstr "Die resolv.conf NICHT lesen."

#: option.c:419
#, c-format
msgid "Specify path to resolv.conf (defaults to %s)."
msgstr "Pfad zu resolv.conf festlegen (%s voreingestellt)."

#: option.c:420
msgid "Specify path to file with server= options"
msgstr "Pfad für Datei mit server=-Optionen angeben"

#: option.c:421
msgid "Specify address(es) of upstream servers with optional domains."
msgstr "Adresse(n) vorgelagerter Server festlegen, optional mit Domänen."

#: option.c:422
msgid "Specify address of upstream servers for reverse address queries"
msgstr "Adresse(n) vorgelagerter Server festlegen, für Rückwärtsauflösung"

#: option.c:423
msgid "Never forward queries to specified domains."
msgstr "Anfragen für angegebene Domänen niemals weiterleiten."

#: option.c:424
msgid "Specify the domain to be assigned in DHCP leases."
msgstr "Domäne festlegen, die für DHCP-Leases zugewiesen wird."

#: option.c:425
msgid "Specify default target in an MX record."
msgstr "Voreingestelltes Ziel für MX-Einträge festlegen."

#: option.c:426
msgid "Specify time-to-live in seconds for replies from /etc/hosts."
msgstr "Gültigkeitsdauer für Antworten aus /etc/hosts festlegen."

#: option.c:427
msgid "Specify time-to-live in seconds for negative caching."
msgstr "Gültigkeitsdauer in Sekunden für Zwischenspeicher negativer Ergebnisse festlegen."

#: option.c:428
msgid "Specify time-to-live in seconds for maximum TTL to send to clients."
msgstr "Gültigkeitsdauer in Sekunden für Caching negativer Ergebnisse festlegen."

#: option.c:429
msgid "Specify time-to-live ceiling for cache."
msgstr "Spezifiziere obere Gültigkeitsdauergrenze für Zwischenspeicher."

#: option.c:430
msgid "Specify time-to-live floor for cache."
msgstr "Spezifiziere untere Gültigkeitsdauergrenze für Zwischenspeicher."

#: option.c:431
#, c-format
msgid "Change to this user after startup. (defaults to %s)."
msgstr "Nach dem Start diese Benutzerrechte annehmen (%s voreingestellt)."

#: option.c:432
msgid "Map DHCP vendor class to tag."
msgstr "DHCP-\"vendor class\" auf Marke abbilden."

#: option.c:433
msgid "Display dnsmasq version and copyright information."
msgstr "DNSMasq-Version und Urheberrecht anzeigen."

#: option.c:434
msgid "Translate IPv4 addresses from upstream servers."
msgstr "IPv4-Adressen von vorgelagerten Servern umsetzen."

#: option.c:435
msgid "Specify a SRV record."
msgstr "SRV-Eintrag festlegen."

#: option.c:436
msgid "Display this message. Use --help dhcp or --help dhcp6 for known DHCP options."
msgstr "Diese Hilfe anzeigen. Benutzen Sie --help dhcp oder --help dhcp6 für bekannte DHCP-Optionen."

#: option.c:437
#, c-format
msgid "Specify path of PID file (defaults to %s)."
msgstr "Pfad für Prozesskennungsdatei (PID) festlegen (Voreinstellung: %s)."

#: option.c:438
#, c-format
msgid "Specify maximum number of DHCP leases (defaults to %s)."
msgstr "Höchstzahl der DHCP-Leases festlegen (%s voreingestellt)."

#: option.c:439
msgid "Answer DNS queries based on the interface a query was sent to."
msgstr "DNS-Anfragen abhängig der Empfangsschnittstelle beantworten."

#: option.c:440
msgid "Specify TXT DNS record."
msgstr "DNS-TXT-Eintrag festlegen."

#: option.c:441
msgid "Specify PTR DNS record."
msgstr "DNS-PTR-Eintrag festlegen."

#: option.c:442
msgid "Give DNS name to IPv4 address of interface."
msgstr "Schnittstellennamen zur IPv4-Adresse der Schnittstelle auflösen."

#: option.c:443
msgid "Bind only to interfaces in use."
msgstr "Nur an verwendete Schnittstellen binden."

#: option.c:444
#, c-format
msgid "Read DHCP static host information from %s."
msgstr "Statische DHCP-Host-Information aus %s lesen."

#: option.c:445
msgid "Enable the DBus interface for setting upstream servers, etc."
msgstr "DBus-Schnittstelle zum Festlegen vorgelagerter Server usw. festlegen."

#: option.c:446
msgid "Enable the UBus interface."
msgstr "UBus-Schnittstelle aktivieren."

#: option.c:447
msgid "Do not provide DHCP on this interface, only provide DNS."
msgstr "Auf dieser Schnittstelle kein DHCP anbieten, sondern nur DNS."

#: option.c:448
msgid "Enable dynamic address allocation for bootp."
msgstr "Dynamische Adressbelegung für bootp einschalten."

#: option.c:449
msgid "Map MAC address (with wildcards) to option set."
msgstr "MAC-Adresse (mit Jokerzeichen) auf Optionenmenge abbilden."

#: option.c:450
msgid "Treat DHCP requests on aliases as arriving from interface."
msgstr "DHCP-Anfragen von Alias-Schnittstellen für die Hauptschnittstelle beantworten."

#: option.c:451
msgid "Specify extra networks sharing a broadcast domain for DHCP"
msgstr "Geben Sie zusätzliche Netzwerke an, die eine Broadcast-Domäne für DHCP gemeinsam nutzen"

#: option.c:452
msgid "Disable ICMP echo address checking in the DHCP server."
msgstr "ICMP-Echo-Adressprüfung im DHCP-Server abschalten."

#: option.c:453
msgid "Shell script to run on DHCP lease creation and destruction."
msgstr "Skript, das bei Erzeugung/Löschung einer DHCP-Lease laufen soll."

#: option.c:454
msgid "Lua script to run on DHCP lease creation and destruction."
msgstr "Lua-Skript, welches bei Erzeugung/Löschung eines DHCP-Leases laufen soll."

#: option.c:455
msgid "Run lease-change scripts as this user."
msgstr "Lease-Änderungs-Skript mit den Rechten dieses Nutzers ausführen."

#: option.c:456
msgid "Call dhcp-script with changes to local ARP table."
msgstr "Rufe dhcp-script mit Änderungen an der lokalen ARP-Tabelle auf."

#: option.c:457
msgid "Read configuration from all the files in this directory."
msgstr "Konfiguration aus allen Dateien in diesem Verzeichnis lesen."

#: option.c:458
msgid "Log to this syslog facility or file. (defaults to DAEMON)"
msgstr "Für diese Syslog-Anlage oder in Datei loggen (Voreinstellung DAEMON)."

#: option.c:459
msgid "Do not use leasefile."
msgstr "Keine Lease-Datei benützen."

#: option.c:460
#, c-format
msgid "Maximum number of concurrent DNS queries. (defaults to %s)"
msgstr "Höchstzahl nebenläufiger DNS-Anfragen (%s voreingestellt)."

#: option.c:461
#, c-format
msgid "Clear DNS cache when reloading %s."
msgstr "DNS-Zwischenspeicher beim Neuladen von %s löschen."

#: option.c:462
msgid "Ignore hostnames provided by DHCP clients."
msgstr "Von DHCP-Klienten gelieferte Hostnamen ignorieren."

#: option.c:463
msgid "Do NOT reuse filename and server fields for extra DHCP options."
msgstr "Dateinamen und Server-Datenfelder NICHT für zusätzliche DHCP-Optionen wiederverwenden."

#: option.c:464
msgid "Enable integrated read-only TFTP server."
msgstr "Eingebauten schreibgeschützten TFTP-Server einschalten."

#: option.c:465
msgid "Export files by TFTP only from the specified subtree."
msgstr "Nur vom festgelegten Unterbaum Dateien per TFTP exportieren."

#: option.c:466
msgid "Add client IP or hardware address to tftp-root."
msgstr "IP-Adresse oder Hardware-Adresse des Klienten an tftp-root anhängen."

#: option.c:467
msgid "Allow access only to files owned by the user running dnsmasq."
msgstr "Zugriff nur auf Dateien gestatten, die dem dnsmasq betreibenden Benutzer gehören."

#: option.c:468
msgid "Do not terminate the service if TFTP directories are inaccessible."
msgstr "Den Dienst nicht beenden, wenn die TFTP-Verzeichnisse unerreichbar sind."

#: option.c:469
#, c-format
msgid "Maximum number of concurrent TFTP transfers (defaults to %s)."
msgstr "Maximale Anzahl gleichzeitiger TFTP-Übertragungen (%s voreingestellt)."

#: option.c:470
msgid "Maximum MTU to use for TFTP transfers."
msgstr "Maximale MTU für TFTP-Übertragungen erreicht."

#: option.c:471
msgid "Disable the TFTP blocksize extension."
msgstr "TFTP-Blockgrößen-Erweiterung abschalten."

#: option.c:472
msgid "Convert TFTP filenames to lowercase"
msgstr "TFTP-Dateinamen in Kleinschreibung umsetzen"

#: option.c:473
msgid "Ephemeral port range for use by TFTP transfers."
msgstr "Bereich für vorübergehende Ports für TFTP-Übertragungen."

#: option.c:474
msgid "Use only one port for TFTP server."
msgstr "Bitte nur einen Port für den TFTP-Server nutzen."

#: option.c:475
msgid "Extra logging for DHCP."
msgstr "Erweiterte DHCP-Protokollierung."

#: option.c:476
msgid "Enable async. logging; optionally set queue length."
msgstr "Asynchrone Protokollierung einschalten, opt. Warteschlangenlänge festlegen."

#: option.c:477
msgid "Stop DNS rebinding. Filter private IP ranges when resolving."
msgstr "DNS-Rebinding unterbinden, private IP-Bereiche bei der Auflösung ausfiltern."

#: option.c:478
msgid "Allow rebinding of *********/8, for RBL servers."
msgstr "Auflösung zu *********/8 erlauben, für RBL-Server."

#: option.c:479
msgid "Inhibit DNS-rebind protection on this domain."
msgstr "DNS-Rebind-Schutz für diese Domäne aufheben."

#: option.c:480
msgid "Always perform DNS queries to all servers."
msgstr "DNS-Anfragen immer an alle Server weiterleiten."

#: option.c:481
msgid "Set tag if client includes matching option in request."
msgstr "Marke setzen, wenn Klient eine entsprechende Option anfragt."

#: option.c:482
msgid "Set tag if client provides given name."
msgstr "Setzt das Tag, wenn der Client diesen Namen anbietet."

#: option.c:483
msgid "Use alternative ports for DHCP."
msgstr "Alternative Ports für DHCP verwenden."

#: option.c:484
msgid "Specify NAPTR DNS record."
msgstr "DNS-NAPTR-Eintrag festlegen."

#: option.c:485
msgid "Specify lowest port available for DNS query transmission."
msgstr "Niedrigsten verfügbaren Port für Übertragung von DNS-Anfragen festlegen."

#: option.c:486
msgid "Specify highest port available for DNS query transmission."
msgstr "Höchsten verfügbaren Port für Übertragung von DNS-Anfragen festlegen."

#: option.c:487
msgid "Use only fully qualified domain names for DHCP clients."
msgstr "Für DHCP-Klienten nur vollständig bestimmte Domänennamen benutzen."

# FIXME: probably typo in original message. -- MA
#: option.c:488
msgid "Generate hostnames based on MAC address for nameless clients."
msgstr "Generiere Hostnamen auf Basis der MAC-Adresse für namenlose Klienten."

#: option.c:489
msgid "Use these DHCP relays as full proxies."
msgstr "Diese DHCP-Relais als vollwertige Proxies verwenden."

#: option.c:490
msgid "Relay DHCP requests to a remote server"
msgstr "Leite DHCP-Anfragen an entfernten Server weiter"

#: option.c:491
msgid "Specify alias name for LOCAL DNS name."
msgstr "Alias für LOKALEN DNS-Namen festlegen."

#: option.c:492
msgid "Prompt to send to PXE clients."
msgstr "Aufforderung, die an PXE-Klienten geschickt wird."

#: option.c:493
msgid "Boot service for PXE menu."
msgstr "Boot-Dienst für PXE-Menü."

#: option.c:494
msgid "Check configuration syntax."
msgstr "Konfigurationssyntax prüfen."

#: option.c:495
msgid "Add requestor's MAC address to forwarded DNS queries."
msgstr "Anfragende MAC-Adresse in die weiterleitende DNS-Anfrage einfügen."

#: option.c:496
msgid "Add specified IP subnet to forwarded DNS queries."
msgstr "Füge spezifiziertes IP-Subnetz an weitergeleiteten DNS-Anfragen hinzu."

#: option.c:497
msgid "Add client identification to forwarded DNS queries."
msgstr "Füge Klient Identifikationan weitergeleiteten DNS-Anfragen hinzu."

# This is a rather liberal translation to convey the purpose.
# something along "authorize upstream nameservers to validate DNSSEC [for us]"
#: option.c:498
msgid "Proxy DNSSEC validation results from upstream nameservers."
msgstr "Vorgelagerte Namensserver für DNSSEC-Validierung ermächtigen."

#: option.c:499
msgid "Attempt to allocate sequential IP addresses to DHCP clients."
msgstr "Versuche, sequenzielle IP-Adressen an DHCP-Klienten zu vergeben."

#: option.c:500
msgid "Ignore client identifier option sent by DHCP clients."
msgstr "Ignorieren Sie die von DHCP-Clients gesendete Client-ID-Option."

#: option.c:501
msgid "Copy connection-track mark from queries to upstream connections."
msgstr "Kopiere \"connection-track mark\" von Anfragen nach Upstream-Verbindungen."

#: option.c:502
msgid "Allow DHCP clients to do their own DDNS updates."
msgstr "Erlaube DHCP-Klienten, ihre eigenen DDNS-Updates durchzuführen."

#: option.c:503
msgid "Send router-advertisements for interfaces doing DHCPv6"
msgstr "Sende \"Router-Advertisments\" für Netzwerkschnittstellen, welche DHCPv6 nutzen"

#: option.c:504
msgid "Specify DUID_EN-type DHCPv6 server DUID"
msgstr "Spezifiziere DUID_EN-type DHCPv6 Server DUID"

#: option.c:505
msgid "Specify host (A/AAAA and PTR) records"
msgstr "Spezifiziere Host (A/AAAA und PTR) Einträge"

#: option.c:506
msgid "Specify host record in interface subnet"
msgstr "Host-Eintrag für das Unternetzwerk der Schnittstelle angeben"

#: option.c:507
msgid "Specify certification authority authorization record"
msgstr "Autorisierungsdatensatz der Zertifizierungsstelle angeben"

#: option.c:508
msgid "Specify arbitrary DNS resource record"
msgstr "Spezifiziere einen beliebiegen DNS Eintrag"

#: option.c:509
msgid "Bind to interfaces in use - check for new interfaces"
msgstr "Bindung an Schnittstellen in Benutzung - prüfe auf neue Schnittstellen"

#: option.c:510
msgid "Export local names to global DNS"
msgstr "Exportiere lokale Namen in das globale DNS"

#: option.c:511
msgid "Domain to export to global DNS"
msgstr "Domain für globales DNS ausgeben"

#: option.c:512
msgid "Set TTL for authoritative replies"
msgstr "Setze Gültigkeitsdauer für autoritative Antworten"

#: option.c:513
msgid "Set authoritative zone information"
msgstr "Setze autoritative Zoneninformationen"

#: option.c:514
msgid "Secondary authoritative nameservers for forward domains"
msgstr "Sekundärer autoritativer Nameserver für weitergeleitete Domains"

#: option.c:515
msgid "Peers which are allowed to do zone transfer"
msgstr "Peers, die einen Zonentransfer durchführen dürfen"

#: option.c:516
msgid "Specify ipsets to which matching domains should be added"
msgstr "Spezifiziere IPSets, zu denen passende Domains hinzugefügt werden sollen"

#: option.c:517
msgid "Enable filtering of DNS queries with connection-track marks."
msgstr ""

#: option.c:518
msgid "Set allowed DNS patterns for a connection-track mark."
msgstr ""

#: option.c:519
msgid "Specify a domain and address range for synthesised names"
msgstr "Spezifiziere eine Domain und Adressbereich für synthetisierte Namen"

#: option.c:520
msgid "Activate DNSSEC validation"
msgstr "Aktiviere DNSSEC-Validierung"

#: option.c:521
msgid "Specify trust anchor key digest."
msgstr "Spezifiziere Vertrauensursprung (Trust Anchor) der Schlüssel-Prüfdaten (Key Digest)."

# while there is no German manual, mark "not for productive use"
#: option.c:522
msgid "Disable upstream checking for DNSSEC debugging."
msgstr "Deaktiviere die vorgelagerte Prüfung für die DNS-Fehlersuche. (Nicht für produktiven Einsatz!)"

#: option.c:523
msgid "Ensure answers without DNSSEC are in unsigned zones."
msgstr "Stellt sicher, dass Antworten ohne DNSSEC sich in einer unsignierten Zone befinden."

#: option.c:524
msgid "Don't check DNSSEC signature timestamps until first cache-reload"
msgstr "DNSSEC Signatur-Zeitstempel nicht prüfen, bis erstmalig der Cache neugeladen wird"

#: option.c:525
msgid "Timestamp file to verify system clock for DNSSEC"
msgstr "Zeitstempel-Datei für die Verifizierung der Systemuhrzeit für DNSSEC"

#: option.c:526
msgid "Set MTU, priority, resend-interval and router-lifetime"
msgstr "Setze MTU, Priorität, Sendewiederholintervall und Router-Lebensdauer"

#: option.c:527
msgid "Do not log routine DHCP."
msgstr "Protokolliere kein Routine-DHCP."

#: option.c:528
msgid "Do not log routine DHCPv6."
msgstr "Protokolliere kein Routine-DHCPv6."

#: option.c:529
msgid "Do not log RA."
msgstr "RA nicht protokollieren."

#: option.c:530
msgid "Log debugging information."
msgstr "Debug-(Fehlersuch-)Information protokollieren."

#: option.c:531
msgid "Accept queries only from directly-connected networks."
msgstr "Akzeptiere nur Anfragen von direkt verbundenen Netzwerken."

#: option.c:532
msgid "Detect and remove DNS forwarding loops."
msgstr "Erkennen und Entfernen von DNS-Weiterleitungsschleifen."

#: option.c:533
msgid "Ignore DNS responses containing ipaddr."
msgstr "Ignoriere DNS-Antworten, die ipaddr enthalten."

#: option.c:534
msgid "Set TTL in DNS responses with DHCP-derived addresses."
msgstr "Setze TTL in DNS-Antworten mit DHCP-abgeleiteten Adressen."

#: option.c:535
msgid "Delay DHCP replies for at least number of seconds."
msgstr "Verzögere DHCP-Antworten mindestens für gegebene Anzahl von Sekunden."

#: option.c:536
msgid "Enables DHCPv4 Rapid Commit option."
msgstr "Aktiviert die DHCPv4-\"Rapid Commit\"-Option."

#: option.c:537
msgid "Path to debug packet dump file"
msgstr "Pfad zur Paketablagedatei zur Fehlersuche"

#: option.c:538
msgid "Mask which packets to dump"
msgstr "Maskiere Pakete, welche abgelegt werden sollen"

#: option.c:539
msgid "Call dhcp-script when lease expiry changes."
msgstr "Rufe dhcp-script auf, wenn der Ablauf des Leases sich ändert."

#: option.c:540
msgid "Send Cisco Umbrella identifiers including remote IP."
msgstr ""

#: option.c:541
#, fuzzy
msgid "Do not log routine TFTP."
msgstr "Protokolliere kein Routine-DHCP."

#: option.c:771
#, c-format
msgid ""
"Usage: dnsmasq [options]\n"
"\n"
msgstr ""
"Verwendung: dnsmasq [Optionen]\n"
"\n"

#: option.c:773
#, c-format
msgid "Use short options only on the command line.\n"
msgstr "Auf der Befehlszeile nur kurze Optionen verwenden!\n"

#: option.c:775
#, c-format
msgid "Valid options are:\n"
msgstr "Gültige Optionen sind:\n"

#: option.c:822 option.c:933
msgid "bad address"
msgstr "Fehlerhafte Adresse"

#: option.c:847 option.c:851
msgid "bad port"
msgstr "Fehlerhafter Port"

#: option.c:864 option.c:893 option.c:927
msgid "interface binding not supported"
msgstr "Schnittstellenbindung nicht unterstützt"

#: option.c:888 option.c:922
msgid "interface can only be specified once"
msgstr "Schnittstelle kann nur einmal angegeben werden"

#: option.c:901 option.c:4362
msgid "bad interface name"
msgstr "Fehlerhafter Schnittestellenname"

#: option.c:1192
msgid "inappropriate vendor:"
msgstr ""

#: option.c:1199
msgid "inappropriate encap:"
msgstr ""

#: option.c:1225
msgid "unsupported encapsulation for IPv6 option"
msgstr "Nicht unterstützte Verkapselung für eine IPv6-Option"

#: option.c:1239
msgid "bad dhcp-option"
msgstr "Fehlerhafte DHCP-Option"

#: option.c:1317
msgid "bad IP address"
msgstr "Fehlerhafte IP-Adresse"

#: option.c:1320 option.c:1459 option.c:3551
msgid "bad IPv6 address"
msgstr "Fehlerhafte IPv6-Adresse"

#: option.c:1413
msgid "bad IPv4 address"
msgstr "Fehlerhafte IPv4-Adresse"

#: option.c:1486 option.c:1581
msgid "bad domain in dhcp-option"
msgstr "Fehlerhafte Domäne in DHCP-Option"

#: option.c:1625
msgid "dhcp-option too long"
msgstr "DHCP-Option zu lang"

#: option.c:1632
msgid "illegal dhcp-match"
msgstr "Unzulässige dhcp-match-Option"

#: option.c:1691
msgid "illegal repeated flag"
msgstr "unzulässig wiederholte Markierung"

#: option.c:1699
msgid "illegal repeated keyword"
msgstr "unzulässig wiederholtes Schlüsselwort"

#: option.c:1770 option.c:5080
#, c-format
msgid "cannot access directory %s: %s"
msgstr "Kann auf Verzeichnis %s nicht zugreifen: %s"

#: option.c:1816 tftp.c:566 dump.c:68
#, c-format
msgid "cannot access %s: %s"
msgstr "Kann auf %s nicht zugreifen: %s"

#: option.c:1931
msgid "setting log facility is not possible under Android"
msgstr "Die Einstellung der \"log facility\" kann unter Android nicht gesetzt werden"

#: option.c:1940
msgid "bad log facility"
msgstr "Falsche \"log facility\""

#: option.c:1993
msgid "bad MX preference"
msgstr "fehlerhafte MX-Präferenz-Angabe"

#: option.c:1998
msgid "bad MX name"
msgstr "fehlerhafter MX-Name"

#: option.c:2012
msgid "bad MX target"
msgstr "fehlerhaftes MX-Ziel"

#: option.c:2032
msgid "recompile with HAVE_SCRIPT defined to enable lease-change scripts"
msgstr "Neuübersetzung mit HAVE_SCRIPT nötig, um Lease-Änderungs-Skripte auszuführen"

#: option.c:2036
msgid "recompile with HAVE_LUASCRIPT defined to enable Lua scripts"
msgstr "Neuübersetzung mit HAVE_LUASCRIPT nötig, um benutzerdefinierte Lua-Skripte auszuführen"

#: option.c:2291 option.c:2327
#, fuzzy
msgid "bad prefix length"
msgstr "fehlerhaftes Präfix"

#: option.c:2303 option.c:2348 option.c:2398
msgid "bad prefix"
msgstr "fehlerhaftes Präfix"

#: option.c:2418
#, fuzzy
msgid "prefix length too small"
msgstr "Die Präfixlänge muss mindestens 64 sein"

#: option.c:2697
#, fuzzy
msgid "Bad address in --address"
msgstr "Adresse in Gebrauch"

#: option.c:2751
#, fuzzy
msgid "bad IPv4 prefix"
msgstr "fehlerhaftes IPv6-Präfix"

#: option.c:2756 option.c:3569
msgid "bad IPv6 prefix"
msgstr "fehlerhaftes IPv6-Präfix"

#: option.c:2777
msgid "recompile with HAVE_IPSET defined to enable ipset directives"
msgstr "Neuübersetzung mit HAVE_IPSET nötig, um IPSet-Direktiven zu aktivieren"

#: option.c:2843 option.c:2861
#, fuzzy
msgid "recompile with HAVE_CONNTRACK defined to enable connmark-allowlist directives"
msgstr "Neuübersetzung mit HAVE_IPSET nötig, um IPSet-Direktiven zu aktivieren"

#: option.c:3119
msgid "bad port range"
msgstr "falscher Portbereich"

#: option.c:3145
msgid "bad bridge-interface"
msgstr "fehlerhafte Brücken-Schnittstelle"

#: option.c:3189
msgid "bad shared-network"
msgstr "fehlerhaftes geteiltes Netz (shared-network)"

#: option.c:3243
msgid "only one tag allowed"
msgstr "nur eine Marke zulässig"

#: option.c:3264 option.c:3280 option.c:3406 option.c:3414 option.c:3457
msgid "bad dhcp-range"
msgstr "fehlerhafter DHCP-Bereich"

#: option.c:3298
msgid "inconsistent DHCP range"
msgstr "inkonsistenter DHCP-Bereich"

#: option.c:3364
msgid "prefix length must be exactly 64 for RA subnets"
msgstr "Die Präfixlänge für RA-Subnetze muss genau 64 sein"

#: option.c:3366
msgid "prefix length must be exactly 64 for subnet constructors"
msgstr "Die Präfixlänge für Subnet-Konstruktor muss genau 64 sein"

#: option.c:3369
msgid "prefix length must be at least 64"
msgstr "Die Präfixlänge muss mindestens 64 sein"

#: option.c:3372
msgid "inconsistent DHCPv6 range"
msgstr "Inkonsistenter DHCPv6-Bereich"

#: option.c:3391
msgid "prefix must be zero with \"constructor:\" argument"
msgstr "Präfix muss in Verbindung mit \"constructor:\" Argument Null sein"

#: option.c:3516 option.c:3594
msgid "bad hex constant"
msgstr "Fehlerhafte Hex-Konstante"

#: option.c:3617
#, c-format
msgid "duplicate dhcp-host IP address %s"
msgstr "doppelte dhcp-host IP-Adresse %s"

#: option.c:3678
msgid "bad DHCP host name"
msgstr "fehlerhafter DHCP-Hostname"

#: option.c:3764
msgid "bad tag-if"
msgstr "fehlerhafte bedingte Marke (tag-if)"

#: option.c:4107 option.c:4623
msgid "invalid port number"
msgstr "unzulässige Portnummer"

#: option.c:4163
msgid "bad dhcp-proxy address"
msgstr "Fehlerhafte DHCP-Proxy-Adresse"

#: option.c:4204
msgid "Bad dhcp-relay"
msgstr "Unzulässiges \"dhcp-relay\""

#: option.c:4248
msgid "bad RA-params"
msgstr "fehlerhafte RA-Parameter"

#: option.c:4258
msgid "bad DUID"
msgstr "fehlerhafte DUID"

#: option.c:4292
msgid "missing address in alias"
msgstr "Adresse fehlt in Alias"

#: option.c:4298
msgid "invalid alias range"
msgstr "unzulässiger Alias-Bereich"

#: option.c:4347
msgid "missing address in dynamic host"
msgstr "Adresse fehlt in dynamischem Host"

#: option.c:4362
msgid "bad dynamic host"
msgstr "fehlerhafter dynamischer Host"

#: option.c:4380 option.c:4396
msgid "bad CNAME"
msgstr "fehlerhafter CNAME"

#: option.c:4404
msgid "duplicate CNAME"
msgstr "doppelter CNAME"

#: option.c:4431
msgid "bad PTR record"
msgstr "fehlerhafter PTR-Eintrag"

#: option.c:4466
msgid "bad NAPTR record"
msgstr "fehlerhafter NAPTR-Eintrag"

#: option.c:4502
msgid "bad RR record"
msgstr "fehlerhafter RR-Eintrag"

#: option.c:4535
msgid "bad CAA record"
msgstr "fehlerhafter CAA-Eintrag"

#: option.c:4564
msgid "bad TXT record"
msgstr "fehlerhafter TXT-Eintrag"

#: option.c:4607
msgid "bad SRV record"
msgstr "fehlerhafter SRV-Eintrag"

#: option.c:4614
msgid "bad SRV target"
msgstr "fehlerhaftes SRV-Ziel"

#: option.c:4633
msgid "invalid priority"
msgstr "unzulässige Priorität"

#: option.c:4638
msgid "invalid weight"
msgstr "unzulässige Wichtung"

#: option.c:4661
msgid "Bad host-record"
msgstr "fehlerhafter \"host-record\""

#: option.c:4700
msgid "Bad name in host-record"
msgstr "Fehlerhafter Name in \"host-record\""

#: option.c:4742
msgid "bad value for dnssec-check-unsigned"
msgstr "Fehlerhafter Wert für \"dnssec-check-unsigned\""

#: option.c:4778
msgid "bad trust anchor"
msgstr "fehlerhafter Vertrauensursprung (Trust Anchor)"

#: option.c:4794
msgid "bad HEX in trust anchor"
msgstr "fehlerhafter Hexwert in Vertrauensursprung (Trust Anchor)"

#: option.c:4805
msgid "unsupported option (check that dnsmasq was compiled with DHCP/TFTP/DNSSEC/DBus support)"
msgstr "Nicht unterstützte Option (prüfen Sie, ob DNSMasq mit DHCP/TFTP/DNSSEC/DBus-Unterstützung übersetzt wurde)"

#: option.c:4865
msgid "missing \""
msgstr "fehlende \\\""

#: option.c:4922
msgid "bad option"
msgstr "fehlerhafter Option"

#: option.c:4924
msgid "extraneous parameter"
msgstr "überschüssiger Parameter"

#: option.c:4926
msgid "missing parameter"
msgstr "fehlender Parameter"

#: option.c:4928
msgid "illegal option"
msgstr "unzulässige Option"

#: option.c:4935
msgid "error"
msgstr "Fehler"

#: option.c:4937
#, c-format
msgid " at line %d of %s"
msgstr " in Zeile %d von %s"

#: option.c:4952 option.c:5229 option.c:5240
#, c-format
msgid "read %s"
msgstr "%s gelesen"

#: option.c:5015 option.c:5162 tftp.c:775
#, c-format
msgid "cannot read %s: %s"
msgstr "kann %s nicht lesen: %s"

#: option.c:5316
msgid "junk found in command line"
msgstr "Müll in der Kommandozeile gefunden"

#: option.c:5356
#, c-format
msgid "Dnsmasq version %s  %s\n"
msgstr "Dnsmasq Version %s  %s\n"

#: option.c:5357
#, c-format
msgid ""
"Compile time options: %s\n"
"\n"
msgstr ""
"Kompilierungs-Optionen %s\n"
"\n"

#: option.c:5358
#, c-format
msgid "This software comes with ABSOLUTELY NO WARRANTY.\n"
msgstr "Für diese Software wird ABSOLUT KEINE GARANTIE gewährt.\n"

# FIXME: this must be one long string! -- MA
#: option.c:5359
#, c-format
msgid "Dnsmasq is free software, and you are welcome to redistribute it\n"
msgstr "Dnsmasq ist freie Software, und darf unter den Bedingungen der\n"

#: option.c:5360
#, c-format
msgid "under the terms of the GNU General Public License, version 2 or 3.\n"
msgstr "GNU General Public Lizenz, Version 2 oder 3, weiterverteilt werden.\n"

#: option.c:5377
msgid "try --help"
msgstr "versuchen Sie --help"

#: option.c:5379
msgid "try -w"
msgstr "versuchen Sie -w"

#: option.c:5381
#, c-format
msgid "bad command line options: %s"
msgstr "fehlerhafte Optionen auf der Befehlszeile: %s"

#: option.c:5450
#, c-format
msgid "CNAME loop involving %s"
msgstr "CNAME-Schleife mit %s"

#: option.c:5491
#, c-format
msgid "cannot get host-name: %s"
msgstr "kann Hostnamen nicht ermitteln: %s"

#: option.c:5519
msgid "only one resolv.conf file allowed in no-poll mode."
msgstr "mit -n/--no-poll ist nur eine resolv.conf-Datei zulässig."

#: option.c:5529
msgid "must have exactly one resolv.conf to read domain from."
msgstr "muss genau eine resolv.conf-Datei haben, um die Domäne zu lesen."

#: option.c:5532 network.c:1670 dhcp.c:880
#, c-format
msgid "failed to read %s: %s"
msgstr "konnte %s nicht lesen: %s"

#: option.c:5549
#, c-format
msgid "no search directive found in %s"
msgstr "keine \"search\"-Anweisung in %s gefunden"

#: option.c:5570
msgid "there must be a default domain when --dhcp-fqdn is set"
msgstr "Es muss eine Standard-Domain gesetzt sein, wenn --dhcp-fqdn gesetzt ist"

#: option.c:5579
msgid "syntax check OK"
msgstr "Syntaxprüfung OK"

#: forward.c:104
#, c-format
msgid "failed to send packet: %s"
msgstr "Paketversand gescheitert: %s"

#: forward.c:601
msgid "discarding DNS reply: subnet option mismatch"
msgstr "Verwerfe DNS Antwort: Subnetoption stimmt nicht überrein"

#: forward.c:666
#, c-format
msgid "nameserver %s refused to do a recursive query"
msgstr "Namensserver %s hat eine rekursive Anfrage verweigert"

#: forward.c:702
#, c-format
msgid "possible DNS-rebind attack detected: %s"
msgstr "möglichen DNS-Rebind-Angriff entdeckt: %s"

#: forward.c:1074
#, c-format
msgid "reducing DNS packet size for nameserver %s to %d"
msgstr "Reduziere die DNS-Paketgröße für Nameserver %s auf %d"

#: forward.c:1381 forward.c:1910
msgid "Ignoring query from non-local network"
msgstr "Ignoriere Anfrage von auswärtigem Netzwerk"

#: forward.c:2198
#, c-format
msgid "failed to bind server socket to %s: %s"
msgstr "konnte nicht an Server-Socket für %s binden: %s"

#: forward.c:2494
#, c-format
msgid "Maximum number of concurrent DNS queries reached (max: %d)"
msgstr "Höchstzahl an nebenläufiger DNS-Anfragen erreicht (max. %d)"

#: forward.c:2496
#, fuzzy, c-format
msgid "Maximum number of concurrent DNS queries to %s reached (max: %d)"
msgstr "Höchstzahl an nebenläufiger DNS-Anfragen erreicht (max. %d)"

#: network.c:670
#, c-format
msgid "stopped listening on %s(#%d): %s port %d"
msgstr "Empfang auf %s(#%d) beendet: %s Port %d"

#: network.c:867
#, c-format
msgid "failed to create listening socket for %s: %s"
msgstr "Konnte Empfangs-Socket für %s nicht erzeugen: %s"

#: network.c:1148
#, c-format
msgid "listening on %s(#%d): %s port %d"
msgstr "Empfang auf %s(#%d): %s Port %d"

#: network.c:1175
#, c-format
msgid "listening on %s port %d"
msgstr "Empfang auf %s Port %d"

#: network.c:1208
#, c-format
msgid "LOUD WARNING: listening on %s may accept requests via interfaces other than %s"
msgstr "LOUD WARNING: Empfang auf %s kann die Anfragen auf anderen Schnittstellen als %s akzeptieren"

#: network.c:1215
msgid "LOUD WARNING: use --bind-dynamic rather than --bind-interfaces to avoid DNS amplification attacks via these interface(s)"
msgstr "LOUD WARNING: Es sollte --bind-dynamic anstatt --bind-interfaces benutzt werden, um DNS-Verstärkungsangriffe auf diesen Schnittstellen zu unterbinden"

#: network.c:1224
#, c-format
msgid "warning: using interface %s instead"
msgstr "Warnung: benutze stattdessen Schnittstelle %s"

#: network.c:1233
#, c-format
msgid "warning: no addresses found for interface %s"
msgstr "Warnung: keine Adressen für die Schnittstelle %s gefunden"

#: network.c:1291
#, c-format
msgid "interface %s failed to join DHCPv6 multicast group: %s"
msgstr "Schnittstelle %s konnte DHCPv6-Multicast-Gruppe nicht beitreten: %s"

#: network.c:1296
msgid "try increasing /proc/sys/net/core/optmem_max"
msgstr "Versuchen Sie, /proc/sys/net/core/optmem_max zu erhöhen"

#: network.c:1492
#, c-format
msgid "failed to bind server socket for %s: %s"
msgstr "konnte nicht an Server-Socket für %s binden: %s"

#: network.c:1569
#, c-format
msgid "ignoring nameserver %s - local interface"
msgstr "ignoriere Namensserver %s - lokale Schnittstelle"

#: network.c:1580
#, c-format
msgid "ignoring nameserver %s - cannot make/bind socket: %s"
msgstr "ignoriere Namensserver %s - kann Socket nicht erzeugen/binden: %s"

#: network.c:1598
msgid "(no DNSSEC)"
msgstr "(kein DNSSEC)"

# FIXME: this isn't translatable - always provide full strings, do not assemble yourself! -- MA
#: network.c:1601
msgid "unqualified"
msgstr "unqualifizierte"

#: network.c:1601
msgid "names"
msgstr "Namen"

#: network.c:1603
msgid "default"
msgstr "Standard"

#: network.c:1605
msgid "domain"
msgstr "Domäne"

#: network.c:1607
#, fuzzy, c-format
msgid "using nameserver %s#%d for %s %s%s %s"
msgstr "Benutze Namensserver %s#%d für %s %s %s"

#: network.c:1611
#, c-format
msgid "NOT using nameserver %s#%d - query loop detected"
msgstr "Benutze Namensserver %s#%d NICHT - Anfragenschleife festgetellt"

#: network.c:1614
#, c-format
msgid "using nameserver %s#%d(via %s)"
msgstr "Benutze Namensserver %s#%d(via %s)"

#: network.c:1616
#, c-format
msgid "using nameserver %s#%d"
msgstr "Benutze Namensserver %s#%d"

#: network.c:1630
#, fuzzy, c-format
msgid "using only locally-known addresses for %s"
msgstr "Benutze nur lokal-bekannte Adressen für %s %s"

#: network.c:1633
#, fuzzy, c-format
msgid "using standard nameservers for %s"
msgstr "Benutze standard Namensserver für %s %s"

#: network.c:1637
#, c-format
msgid "using %d more local addresses"
msgstr "Benutze weitere %d lokale Adressen"

#: network.c:1639
#, c-format
msgid "using %d more nameservers"
msgstr "Benutze weitere %d Namensserver"

#: dnsmasq.c:184
msgid "dhcp-hostsdir, dhcp-optsdir and hostsdir are not supported on this platform"
msgstr "dhcp-hostsdir, dhcp-optsdir und hostsdir werden auf dieser Plattform nicht unterstüzt"

#: dnsmasq.c:199
msgid "no root trust anchor provided for DNSSEC"
msgstr "Keine Root-Vertrauensanker (Root Trust Anchor) für DNSSEC verfügbar"

#: dnsmasq.c:202
msgid "cannot reduce cache size from default when DNSSEC enabled"
msgstr "Kann die Standard-Zwischenspeichergröße nicht verkleinern, wenn DNSSEC aktiviert ist"

#: dnsmasq.c:204
msgid "DNSSEC not available: set HAVE_DNSSEC in src/config.h"
msgstr "DNSSEC nicht verfügbar: setzen Sie HAVE_DNSSEC in src/config.h"

#: dnsmasq.c:210
msgid "TFTP server not available: set HAVE_TFTP in src/config.h"
msgstr "TFTP-Server nicht verfügbar, setzen Sie HAVE_TFTP in src/config.h"

#: dnsmasq.c:217
msgid "cannot use --conntrack AND --query-port"
msgstr "Kann nicht --conntrack UND --query-port einsetzen"

#: dnsmasq.c:223
msgid "conntrack support not available: set HAVE_CONNTRACK in src/config.h"
msgstr "Conntrack-Unterstützung nicht verfügbar: Aktivieren Sie HAVE_CONNTRACK in src/config.h"

#: dnsmasq.c:228
msgid "asynchronous logging is not available under Solaris"
msgstr "asynchrone Protokollierung ist unter Solaris nicht verfügbar"

#: dnsmasq.c:233
msgid "asynchronous logging is not available under Android"
msgstr "Asynchrone Protokollierung ist unter Android nicht verfügbar"

#: dnsmasq.c:238
msgid "authoritative DNS not available: set HAVE_AUTH in src/config.h"
msgstr "Autoritatives DNS nicht verfügbar: Setzen Sie HAVE_AUTH in src/config.h"

#: dnsmasq.c:243
msgid "loop detection not available: set HAVE_LOOP in src/config.h"
msgstr "Loop-Erkennung nicht verfügbar, aktivieren Sie HAVE_LOOP in src/config.h"

#: dnsmasq.c:248
msgid "Ubus not available: set HAVE_UBUS in src/config.h"
msgstr "UBus nicht verfügbar: setzen Sie HAVE_UBUS in src/config.h"

#: dnsmasq.c:259
msgid "max_port cannot be smaller than min_port"
msgstr "max_port darf nicht kleiner als min_port sein"

#: dnsmasq.c:266
msgid "--auth-server required when an auth zone is defined."
msgstr "--auth-server ist notwendig, wenn eine Auth-Zone definiert ist."

#: dnsmasq.c:271
msgid "zone serial must be configured in --auth-soa"
msgstr "Seriennummer der Zone muss mit --auth-soa konfiguriert werden"

#: dnsmasq.c:291
msgid "dhcp-range constructor not available on this platform"
msgstr "dhcp-range-Konstruktor ist auf dieser Plattform nicht verfügbar"

#: dnsmasq.c:355
msgid "cannot set --bind-interfaces and --bind-dynamic"
msgstr "Kann nicht --bind-interfaces und --bind-dynamic setzen"

#: dnsmasq.c:358
#, c-format
msgid "failed to find list of interfaces: %s"
msgstr "konnte Schnitstellenliste nicht auffinden: %s"

#: dnsmasq.c:367
#, c-format
msgid "unknown interface %s"
msgstr "unbekannte Schnittstelle %s"

#: dnsmasq.c:437
msgid "Packet dumps not available: set HAVE_DUMP in src/config.h"
msgstr "Paketmitschnitt nicht verfügbar: setzen Sie HAVE_DUMP in src/config.h"

#: dnsmasq.c:445 dnsmasq.c:1207
#, c-format
msgid "DBus error: %s"
msgstr "DBus-Fehler: %s"

#: dnsmasq.c:448
msgid "DBus not available: set HAVE_DBUS in src/config.h"
msgstr "DBus nicht verfügbar: setzen Sie HAVE_DBUS in src/config.h"

#: dnsmasq.c:456 dnsmasq.c:1228
#, fuzzy, c-format
msgid "UBus error: %s"
msgstr "DBus-Fehler: %s"

#: dnsmasq.c:459
msgid "UBus not available: set HAVE_UBUS in src/config.h"
msgstr "UBus nicht verfügbar: setzen Sie HAVE_UBUS in src/config.h"

#: dnsmasq.c:489
#, c-format
msgid "unknown user or group: %s"
msgstr "Unbekannter Benutzer oder Gruppe: %s"

#: dnsmasq.c:565
#, c-format
msgid "process is missing required capability %s"
msgstr "Prozess benötigt verlangte Fähigkeit %s"

#: dnsmasq.c:597
#, c-format
msgid "cannot chdir to filesystem root: %s"
msgstr "kann nicht ins Wurzelverzeichnis des Dateisystems wechseln: %s"

# FIXME: this and the next would need commas after the version
#: dnsmasq.c:845
#, c-format
msgid "started, version %s DNS disabled"
msgstr "gestartet, Version %s, DNS abgeschaltet"

#: dnsmasq.c:850
#, c-format
msgid "started, version %s cachesize %d"
msgstr "gestartet, Version %s, Zwischenspeichergröße %d"

#: dnsmasq.c:852
msgid "cache size greater than 10000 may cause performance issues, and is unlikely to be useful."
msgstr "Eine Cachegröße größer als 10000 kann Performanceprobleme verursachen und Nutzen ist wenig wahrscheinlich."

#: dnsmasq.c:855
#, c-format
msgid "started, version %s cache disabled"
msgstr "Gestartet, Version %s, Zwischenspeicher deaktiviert"

#: dnsmasq.c:858
msgid "DNS service limited to local subnets"
msgstr "DNS-Dienst auf Unternetze eingeschränkt"

#: dnsmasq.c:861
#, c-format
msgid "compile time options: %s"
msgstr "Optionen bei Übersetzung: %s"

#: dnsmasq.c:870
msgid "DBus support enabled: connected to system bus"
msgstr "DBus-Unterstützung eingeschaltet: mit Systembus verbunden"

#: dnsmasq.c:872
msgid "DBus support enabled: bus connection pending"
msgstr "DBus-Unterstützung eingeschaltet: warte auf Systembus-Verbindung"

#: dnsmasq.c:880
msgid "UBus support enabled: connected to system bus"
msgstr "UBus-Unterstützung aktiviert: mit Systembus verbunden"

#: dnsmasq.c:882
msgid "UBus support enabled: bus connection pending"
msgstr "UBus-Unterstützung aktiviert: Bus-Verbindung wird hergestellt"

#: dnsmasq.c:902
msgid "DNSSEC validation enabled but all unsigned answers are trusted"
msgstr "DNSSEC-Validierung aktiviert, jedoch wird allen unsignierten Antworten vertraut"

#: dnsmasq.c:904
msgid "DNSSEC validation enabled"
msgstr "DNSSEC-Validierung aktiviert"

#: dnsmasq.c:908
msgid "DNSSEC signature timestamps not checked until receipt of SIGINT"
msgstr "DNSSEC-Signatur-Zeitstempel werden erst nach Empfang von SIGINT überprüft"

#: dnsmasq.c:911
msgid "DNSSEC signature timestamps not checked until system time valid"
msgstr "DNSSEC Signatur-Zeitstempel werden erst überprüft, sobald die Systemuhrzeit gültig ist"

#: dnsmasq.c:914
#, c-format
msgid "configured with trust anchor for %s keytag %u"
msgstr "konfiguriert mit Vertrauensanker für %s Schlüsselanhänger %u"

#: dnsmasq.c:920
#, c-format
msgid "warning: failed to change owner of %s: %s"
msgstr "Warnung: konnte den Besitzer von %s nicht ändern: %s"

#: dnsmasq.c:924
msgid "setting --bind-interfaces option because of OS limitations"
msgstr "Aktiviere --bind-interfaces wegen Einschränkungen des Betriebssystems"

#: dnsmasq.c:936
#, c-format
msgid "warning: interface %s does not currently exist"
msgstr "Warnung: Schnittstelle %s existiert derzeit nicht"

#: dnsmasq.c:941
msgid "warning: ignoring resolv-file flag because no-resolv is set"
msgstr "Warnung: Ignoriere \"resolv-file\", weil \"no-resolv\" aktiv ist"

#: dnsmasq.c:944
msgid "warning: no upstream servers configured"
msgstr "Warnung: keine vorgeschalteten Server konfiguriert"

#: dnsmasq.c:948
#, c-format
msgid "asynchronous logging enabled, queue limit is %d messages"
msgstr "asynchrone Protokollierung eingeschaltet, Warteschlange fasst %d Nachrichten"

#: dnsmasq.c:969
msgid "IPv6 router advertisement enabled"
msgstr "IPv6-Router-Advertisement aktiviert"

#: dnsmasq.c:974
#, c-format
msgid "DHCP, sockets bound exclusively to interface %s"
msgstr "DHCP, Sockets exklusiv an die Schnittstelle %s gebunden"

# FIXME: this and the next few must be full strings to be translatable - do not assemble in code"
#: dnsmasq.c:991
msgid "root is "
msgstr "Wurzel ist "

#: dnsmasq.c:991
msgid "enabled"
msgstr "Aktiviert"

#: dnsmasq.c:993
msgid "secure mode"
msgstr "sicherer Modus"

#: dnsmasq.c:994
msgid "single port mode"
msgstr "Einzelport-Modus"

#: dnsmasq.c:997
#, c-format
msgid "warning: %s inaccessible"
msgstr "Warnung: %s unerreichbar"

#: dnsmasq.c:1001
#, c-format
msgid "warning: TFTP directory %s inaccessible"
msgstr "Warnung: Das TFTP-Verzeichnis %s ist unerreichbar"

#: dnsmasq.c:1027
#, c-format
msgid "restricting maximum simultaneous TFTP transfers to %d"
msgstr "Begrenze gleichzeitige TFTP-Übertragungen auf maximal %d"

#: dnsmasq.c:1204
msgid "connected to system DBus"
msgstr "Mit System-DBus verbunden"

#: dnsmasq.c:1225
#, fuzzy
msgid "connected to system UBus"
msgstr "Mit System-UBus verbunden"

#: dnsmasq.c:1391
#, c-format
msgid "cannot fork into background: %s"
msgstr "kann nicht in den Hintergrund abspalten: %s"

#: dnsmasq.c:1395
#, c-format
msgid "failed to create helper: %s"
msgstr "kann Helfer nicht erzeugen: %s"

#: dnsmasq.c:1399
#, c-format
msgid "setting capabilities failed: %s"
msgstr "kann \"capabilities\" nicht setzen: %s"

#: dnsmasq.c:1403
#, c-format
msgid "failed to change user-id to %s: %s"
msgstr "Kann nicht Benutzerrechte %s annehmen: %s"

#: dnsmasq.c:1407
#, c-format
msgid "failed to change group-id to %s: %s"
msgstr "Kann nicht Gruppenrechte %s annehmen: %s"

#: dnsmasq.c:1411
#, c-format
msgid "failed to open pidfile %s: %s"
msgstr "kann die Prozessidentifikations-(PID)-Datei %s nicht öffnen: %s"

#: dnsmasq.c:1415
#, c-format
msgid "cannot open log %s: %s"
msgstr "Kann Logdatei %s nicht öffnen: %s"

#: dnsmasq.c:1419
#, c-format
msgid "failed to load Lua script: %s"
msgstr "Konnte Lua-Script nicht laden: %s"

#: dnsmasq.c:1423
#, c-format
msgid "TFTP directory %s inaccessible: %s"
msgstr "Das TFTP-Verzeichnis %s ist unerreichbar: %s"

#: dnsmasq.c:1427
#, c-format
msgid "cannot create timestamp file %s: %s"
msgstr "Kann keine Zeitstempel-Datei %s erzeugen: %s"

#: dnsmasq.c:1511
#, c-format
msgid "script process killed by signal %d"
msgstr "Skriptprozess durch Signal %d beendet"

#: dnsmasq.c:1515
#, c-format
msgid "script process exited with status %d"
msgstr "Scriptprozess hat sich mit Status %d beendet"

#: dnsmasq.c:1519
#, c-format
msgid "failed to execute %s: %s"
msgstr "konnte %s nicht ausführen: %s"

#: dnsmasq.c:1559
msgid "now checking DNSSEC signature timestamps"
msgstr "Prüfe jetzt Zeitstempel der DNSSEC-Signaturen"

#: dnsmasq.c:1594 dnssec.c:160 dnssec.c:204
#, c-format
msgid "failed to update mtime on %s: %s"
msgstr "kann die mtime nicht auf %s aktualisieren: %s"

#: dnsmasq.c:1606
msgid "exiting on receipt of SIGTERM"
msgstr "beende nach Empfang von SIGTERM"

#: dnsmasq.c:1634
#, c-format
msgid "failed to access %s: %s"
msgstr "konnte auf %s nicht zugreifen: %s"

#: dnsmasq.c:1664
#, c-format
msgid "reading %s"
msgstr "lese %s"

#: dnsmasq.c:1675
#, c-format
msgid "no servers found in %s, will retry"
msgstr "keine Server in %s gefunden, werde nochmal versuchen"

#: dhcp.c:53
#, c-format
msgid "cannot create DHCP socket: %s"
msgstr "kann DHCP-Socket nicht erzeugen: %s"

#: dhcp.c:68
#, c-format
msgid "failed to set options on DHCP socket: %s"
msgstr "kann Optionen für DHCP-Socket nicht setzen: %s"

#: dhcp.c:89
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCP socket: %s"
msgstr "kann SO_REUSE{ADDR|PORT} für DHCP-Socket nicht aktivieren: %s"

#: dhcp.c:101
#, c-format
msgid "failed to bind DHCP server socket: %s"
msgstr "kann nicht an DHCP-Server-Socket binden: %s"

#: dhcp.c:127
#, c-format
msgid "cannot create ICMP raw socket: %s."
msgstr "kann ICMP-Rohdaten-Socket nicht erzeugen: %s."

#: dhcp.c:252 dhcp6.c:180
#, c-format
msgid "unknown interface %s in bridge-interface"
msgstr "unbekannte Schnittstelle %s in bridge-interface"

#: dhcp.c:293
#, c-format
msgid "DHCP packet received on %s which has no address"
msgstr "DHCP-Paket ohne Adresse an Schnittstelle %s empfangen"

#: dhcp.c:428
#, c-format
msgid "ARP-cache injection failed: %s"
msgstr "Einspeisen in ARP-Zwischenspeicher fehlgeschlagen: %s"

#: dhcp.c:473
#, c-format
msgid "Error sending DHCP packet to %s: %s"
msgstr "Fehler beim Senden des DHCP-Pakets an %s: %s"

#: dhcp.c:530
#, c-format
msgid "DHCP range %s -- %s is not consistent with netmask %s"
msgstr "DHCP-Bereich %s - %s passt nicht zur Netzmaske %s"

#: dhcp.c:918
#, c-format
msgid "bad line at %s line %d"
msgstr "ungültige Zeile %2$d in Datei %1$s"

#: dhcp.c:961
#, c-format
msgid "ignoring %s line %d, duplicate name or IP address"
msgstr "ignoriere %s Zeile %d, doppelter Name oder doppelte IP-Adresse"

#: dhcp.c:1105 rfc3315.c:2182
#, c-format
msgid "DHCP relay %s -> %s"
msgstr "DHCP Relais %s -> %s"

#: lease.c:64
#, c-format
msgid "ignoring invalid line in lease database: %s %s %s %s ..."
msgstr "ignoriere ungültige Zeile in Lease-Datenbank: %s %s %s %s ..."

#: lease.c:101
#, c-format
msgid "ignoring invalid line in lease database, bad address: %s"
msgstr "ignoriere ungültige Zeile in Lease-Datenbank, fehlerhafte Adresse: %s"

#: lease.c:108
msgid "too many stored leases"
msgstr "zu viele Leases gespeichert"

#: lease.c:176
#, c-format
msgid "cannot open or create lease file %s: %s"
msgstr "kann Lease-Datei %s nicht öffnen oder anlegen: %s"

#: lease.c:185
msgid "failed to parse lease database cleanly"
msgstr "sauberes Aufgliedern der Lease-Datenbank fehlgeschlagen"

#: lease.c:188
#, c-format
msgid "failed to read lease file %s: %s"
msgstr "konnte Lease-Datei %s nicht lesen: %s"

#: lease.c:204
#, c-format
msgid "cannot run lease-init script %s: %s"
msgstr "kann lease-init-Skript %s nicht ausführen: %s"

#: lease.c:210
#, c-format
msgid "lease-init script returned exit code %s"
msgstr "lease-init-Skript beendete sich mit Code %s"

#: lease.c:381
#, c-format
msgid "failed to write %s: %s (retry in %u s)"
msgstr "Konnte %s nicht schreiben: %s (Neuversuch in %u s)"

#: lease.c:955
#, c-format
msgid "Ignoring domain %s for DHCP host name %s"
msgstr "Ignoriere Domäne %s für DHCP-Hostnamen %s"

#: rfc2131.c:378
msgid "with subnet selector"
msgstr "mit Subnetz-Wähler"

#: rfc2131.c:383
msgid "via"
msgstr "via"

# FIXME: this and the next few are not translatable. Please provide full
# strings, do not programmatically assemble them.
#: rfc2131.c:389
#, c-format
msgid "no address range available for DHCP request %s %s"
msgstr "Kein verfügbarer Adress-Bereich für DHCP-Anfrage %s %s"

#: rfc2131.c:403
#, c-format
msgid "%u available DHCP subnet: %s/%s"
msgstr "%u verfügbares DHCP-Subnetz: %s/%s"

#: rfc2131.c:409 rfc3315.c:319
#, c-format
msgid "%u available DHCP range: %s -- %s"
msgstr "%u verfügbarer DHCP-Bereich: %s - %s"

#: rfc2131.c:521
#, c-format
msgid "%u vendor class: %s"
msgstr "%u \"Vendor class\": %s"

#: rfc2131.c:523
#, c-format
msgid "%u user class: %s"
msgstr "%u Benutzerklasse: %s"

# FIXME: do not programmatically assemble strings - untranslatable
#: rfc2131.c:557
msgid "disabled"
msgstr "deaktiviert"

#: rfc2131.c:598 rfc2131.c:1087 rfc2131.c:1532 rfc3315.c:632 rfc3315.c:815
#: rfc3315.c:1121
msgid "ignored"
msgstr "ignoriert"

#: rfc2131.c:613 rfc2131.c:1333 rfc3315.c:867
msgid "address in use"
msgstr "Adresse in Gebrauch"

#: rfc2131.c:627 rfc2131.c:1141
msgid "no address available"
msgstr "keine Adresse verfügbar"

#: rfc2131.c:634 rfc2131.c:1295
msgid "wrong network"
msgstr "Falsches Netzwerk"

#: rfc2131.c:649
msgid "no address configured"
msgstr "keine Adresse konfiguriert"

#: rfc2131.c:655 rfc2131.c:1346
msgid "no leases left"
msgstr "keine Leases übrig"

#: rfc2131.c:756 rfc3315.c:499
#, c-format
msgid "%u client provides name: %s"
msgstr "%u Klient stellt Name bereit: %s"

#: rfc2131.c:885
msgid "PXE BIS not supported"
msgstr "PXE BIS nicht unterstützt"

#: rfc2131.c:1054 rfc3315.c:1222
#, c-format
msgid "disabling DHCP static address %s for %s"
msgstr "schalte statische DHCP-Adresse %s für %s ab"

# FIXME: do not assemble
#: rfc2131.c:1075
msgid "unknown lease"
msgstr "Unbekannte Lease"

#: rfc2131.c:1110
#, c-format
msgid "not using configured address %s because it is leased to %s"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie an %s verleast ist"

#: rfc2131.c:1120
#, c-format
msgid "not using configured address %s because it is in use by the server or relay"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie von Server/Relais verwendet wird"

#: rfc2131.c:1123
#, c-format
msgid "not using configured address %s because it was previously declined"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie zuvor abgelehnt wurde"

# FIXME: do not assemble
#: rfc2131.c:1139 rfc2131.c:1339
msgid "no unique-id"
msgstr "keine eindeutige ID"

#: rfc2131.c:1231
msgid "wrong server-ID"
msgstr "falsche Server-ID"

#: rfc2131.c:1250
msgid "wrong address"
msgstr "falsche Adresse"

#: rfc2131.c:1268 rfc3315.c:975
msgid "lease not found"
msgstr "Lease nicht gefunden"

#: rfc2131.c:1303
msgid "address not available"
msgstr "Adresse nicht verfügbar"

#: rfc2131.c:1314
msgid "static lease available"
msgstr "Statischer Lease verfügbar"

#: rfc2131.c:1318
msgid "address reserved"
msgstr "Adresse reserviert"

#: rfc2131.c:1327
#, c-format
msgid "abandoning lease to %s of %s"
msgstr "Gebe Lease von %2$s an %1$s auf"

#: rfc2131.c:1866
#, c-format
msgid "%u bootfile name: %s"
msgstr "%u Name der Bootdatei: %s"

#: rfc2131.c:1875
#, c-format
msgid "%u server name: %s"
msgstr "%u Servername: %s"

#: rfc2131.c:1885
#, c-format
msgid "%u next server: %s"
msgstr "%u nächster Server: %s"

#: rfc2131.c:1889
#, c-format
msgid "%u broadcast response"
msgstr "%u Antwort per Broadcast"

#: rfc2131.c:1952
#, c-format
msgid "cannot send DHCP/BOOTP option %d: no space left in packet"
msgstr "kann DHCP/BOOTP-Opition %d nicht setzen: kein Platz mehr im Paket"

#: rfc2131.c:2262
msgid "PXE menu too large"
msgstr "PXE-Menüeintrag zu groß"

#: rfc2131.c:2425 rfc3315.c:1511
#, c-format
msgid "%u requested options: %s"
msgstr "%u angeforderte Optionen: %s"

#: rfc2131.c:2742
#, c-format
msgid "cannot send RFC3925 option: too many options for enterprise number %d"
msgstr "Kann RFC3925-Option nicht senden: zu viele Optionen für Unternehmen Nr. %d"

#: rfc2131.c:2805
#, c-format
msgid "%u reply delay: %d"
msgstr "%u Antwortverzögerung: %d"

#: netlink.c:93
#, c-format
msgid "cannot create netlink socket: %s"
msgstr "kann Netlink-Socket nicht erzeugen: %s"

#: netlink.c:377
#, c-format
msgid "netlink returns error: %s"
msgstr "Netlink liefert Fehler %s"

#: dbus.c:434
#, c-format
msgid "Enabling --%s option from D-Bus"
msgstr "Aktiviere --%s Option von D-Bus"

#: dbus.c:439
#, c-format
msgid "Disabling --%s option from D-Bus"
msgstr "Deaktiviere --%s Option von D-Bus"

#: dbus.c:713
msgid "setting upstream servers from DBus"
msgstr "vorgeschaltete Server von DBus festgelegt"

#: dbus.c:760
msgid "could not register a DBus message handler"
msgstr "konnte Steuerungsprogramm für DBus-Nachrichten nicht anmelden"

#: bpf.c:261
#, c-format
msgid "cannot create DHCP BPF socket: %s"
msgstr "konnte DHCP-BPF-Socket nicht einrichten: %s"

#: bpf.c:289
#, c-format
msgid "DHCP request for unsupported hardware type (%d) received on %s"
msgstr "DHCP-Anfrage für nicht unterstützen Hardwaretyp (%d) auf %s empfangen"

#: bpf.c:374
#, c-format
msgid "cannot create PF_ROUTE socket: %s"
msgstr "Kann PF_ROUTE-Socket nicht erzeugen: %s"

#: bpf.c:395
msgid "Unknown protocol version from route socket"
msgstr "Unbekannte Protokollversion vom Route-Socket"

#: helper.c:150
msgid "lease() function missing in Lua script"
msgstr "lease()-Funktion fehlt im Lua-Skript"

#: tftp.c:349
msgid "unable to get free port for TFTP"
msgstr "konnte keinen freien Port für TFTP bekommen"

#: tftp.c:365
#, c-format
msgid "unsupported request from %s"
msgstr "nicht unterstützte Anfrage von %s"

#: tftp.c:512
#, c-format
msgid "file %s not found"
msgstr "Datei %s nicht gefunden"

#: tftp.c:602
#, c-format
msgid "ignoring packet from %s (TID mismatch)"
msgstr ""

#: tftp.c:646
#, c-format
msgid "failed sending %s to %s"
msgstr "konnte %s nicht an %s senden"

#: tftp.c:646
#, c-format
msgid "sent %s to %s"
msgstr "%s an %s verschickt"

#: tftp.c:696
#, c-format
msgid "error %d %s received from %s"
msgstr "Fehler %d %s von %s empfangen"

#: log.c:190
#, c-format
msgid "overflow: %d log entries lost"
msgstr "Überlauf: %d Protokolleinträge verloren"

#: log.c:268
#, c-format
msgid "log failed: %s"
msgstr "Protokollierung fehlgeschlagen: %s"

#: log.c:477
msgid "FAILED to start up"
msgstr "Start FEHLGESCHLAGEN"

#: conntrack.c:63
#, c-format
msgid "Conntrack connection mark retrieval failed: %s"
msgstr "\"Conntrack connection mark\"-Abruf fehlgeschlagen: %s"

#: dhcp6.c:52
#, c-format
msgid "cannot create DHCPv6 socket: %s"
msgstr "Kann DHCPv6-Socket nicht erzeugen: %s"

#: dhcp6.c:73
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCPv6 socket: %s"
msgstr "kann SO_REUSE{ADDR|PORT} für DHCPv6-Socket nicht aktivieren: %s"

#: dhcp6.c:85
#, c-format
msgid "failed to bind DHCPv6 server socket: %s"
msgstr "Kann nicht an DHCPv6-Server-Socket binden: %s"

#: rfc3315.c:173
#, c-format
msgid "no address range available for DHCPv6 request from relay at %s"
msgstr "Kein Adressbereich verfügbar für die DHCPv6-Anfrage vom Relais bei %s"

#: rfc3315.c:182
#, c-format
msgid "no address range available for DHCPv6 request via %s"
msgstr "Kein Adressbereich verfügbar für die DHCPv6-Anfrage via %s"

#: rfc3315.c:316
#, c-format
msgid "%u available DHCPv6 subnet: %s/%d"
msgstr "%u verfügbares DHCPv6-Subnetz: %s/%d"

#: rfc3315.c:399
#, c-format
msgid "%u vendor class: %u"
msgstr "%u Herstellerklasse: %u"

#: rfc3315.c:447
#, c-format
msgid "%u client MAC address: %s"
msgstr "%u Klient MAC-Adresse: %s"

#: rfc3315.c:762 rfc3315.c:859
msgid "address unavailable"
msgstr "Adresse nicht verfügbar"

#: rfc3315.c:774 rfc3315.c:903 rfc3315.c:1272
msgid "success"
msgstr "Erfolg"

#: rfc3315.c:789 rfc3315.c:798 rfc3315.c:911 rfc3315.c:913 rfc3315.c:1047
msgid "no addresses available"
msgstr "keine Adressen verfügbar"

#: rfc3315.c:890
msgid "not on link"
msgstr "nicht on link"

#: rfc3315.c:979 rfc3315.c:1180 rfc3315.c:1261
msgid "no binding found"
msgstr "Keine Bindung gefunden"

#: rfc3315.c:1016
msgid "deprecated"
msgstr "veraltet"

#: rfc3315.c:1023
msgid "address invalid"
msgstr "Adresse ungültig"

#: rfc3315.c:1081 rfc3315.c:1083
msgid "confirm failed"
msgstr "Bestätigung fehlgeschlagen"

#: rfc3315.c:1098
msgid "all addresses still on link"
msgstr "Alle Adressen immer noch in Verbindung"

#: rfc3315.c:1189
msgid "release received"
msgstr "Freigabe empfangen"

#: rfc3315.c:2173
msgid "Cannot multicast to DHCPv6 server without correct interface"
msgstr "Kann nicht zum DHCPv6 Server multicasten ohne korrekte Schnittstelle"

#: dhcp-common.c:154
#, c-format
msgid "Ignoring duplicate dhcp-option %d"
msgstr "Ignoriere doppelt vorhandene DHCP-Option %d"

#: dhcp-common.c:231
#, c-format
msgid "%u tags: %s"
msgstr "%u Marken: %s"

#: dhcp-common.c:451
#, c-format
msgid "%s has more than one address in hostsfile, using %s for DHCP"
msgstr "%s hat mehr als eine Adresse in hosts-Datei, benutze %s für DHCP"

#: dhcp-common.c:485
#, c-format
msgid "duplicate IP address %s (%s) in dhcp-config directive"
msgstr "doppelte IP-Adresse %s (%s) in \"dhcp-config\"-Anweisung"

#: dhcp-common.c:549
#, c-format
msgid "failed to set SO_BINDTODEVICE on DHCP socket: %s"
msgstr "kann SO_BINDTODEVICE für DHCP-Socket nicht aktivieren: %s"

#: dhcp-common.c:672
#, c-format
msgid "Known DHCP options:\n"
msgstr "Bekannte DHCP-Optionen:\n"

#: dhcp-common.c:683
#, c-format
msgid "Known DHCPv6 options:\n"
msgstr "Bekannte DHCPv6-Optionen:\n"

#: dhcp-common.c:880
msgid ", prefix deprecated"
msgstr ", Präfix veraltet"

#: dhcp-common.c:883
#, c-format
msgid ", lease time "
msgstr ", Leasezeit "

#: dhcp-common.c:925
#, c-format
msgid "%s stateless on %s%.0s%.0s%s"
msgstr "%s zustandslos auf %s%.0s%.0s%s"

#: dhcp-common.c:927
#, c-format
msgid "%s, static leases only on %.0s%s%s%.0s"
msgstr "%s, nur statische Leases auf %.0s%s%s%.0s"

#: dhcp-common.c:929
#, c-format
msgid "%s, proxy on subnet %.0s%s%.0s%.0s"
msgstr "%s, Proxy im Subnetz %.0s%s%.0s%.0s"

#: dhcp-common.c:930
#, c-format
msgid "%s, IP range %s -- %s%s%.0s"
msgstr "%s, IP-Bereich %s -- %s%s%.0s"

#: dhcp-common.c:943
#, c-format
msgid "DHCPv4-derived IPv6 names on %s%s"
msgstr "DHCPv4-abgeleitete IPv6 Namen auf %s%s"

#: dhcp-common.c:946
#, c-format
msgid "router advertisement on %s%s"
msgstr "Router-Advertisment auf %s%s"

#: dhcp-common.c:957
#, c-format
msgid "DHCP relay from %s to %s via %s"
msgstr "DHCP Weiterleitung von %s nach %s über %s"

#: dhcp-common.c:959
#, c-format
msgid "DHCP relay from %s to %s"
msgstr "DHCP Weiterleitung von %s nach %s"

#: radv.c:110
#, c-format
msgid "cannot create ICMPv6 socket: %s"
msgstr "Kann ICMPv6-Socket nicht erzeugen: %s"

#: auth.c:464
#, c-format
msgid "ignoring zone transfer request from %s"
msgstr "ignoriere Zonentransfer-Anfrage von %s"

#: ipset.c:99
#, c-format
msgid "failed to create IPset control socket: %s"
msgstr "konnte IPset-Kontroll-Socket nicht erzeugen: %s"

#: ipset.c:211
#, c-format
msgid "failed to update ipset %s: %s"
msgstr "Aktualisierung von ipset %s fehlgeschlagen: %s"

#: pattern.c:29
#, c-format
msgid "[pattern.c:%d] Assertion failure: %s"
msgstr ""

#: pattern.c:142
#, c-format
msgid "Invalid DNS name: Invalid character %c."
msgstr ""

#: pattern.c:151
msgid "Invalid DNS name: Empty label."
msgstr ""

#: pattern.c:156
msgid "Invalid DNS name: Label starts with hyphen."
msgstr ""

#: pattern.c:170
msgid "Invalid DNS name: Label ends with hyphen."
msgstr ""

#: pattern.c:176
#, c-format
msgid "Invalid DNS name: Label is too long (%zu)."
msgstr ""

#: pattern.c:184
#, c-format
msgid "Invalid DNS name: Not enough labels (%zu)."
msgstr ""

#: pattern.c:189
msgid "Invalid DNS name: Final label is fully numeric."
msgstr ""

#: pattern.c:199
msgid "Invalid DNS name: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:204
#, c-format
msgid "DNS name has invalid length (%zu)."
msgstr ""

#: pattern.c:258
#, c-format
msgid "Invalid DNS name pattern: Invalid character %c."
msgstr ""

#: pattern.c:267
msgid "Invalid DNS name pattern: Empty label."
msgstr ""

#: pattern.c:272
msgid "Invalid DNS name pattern: Label starts with hyphen."
msgstr ""

#: pattern.c:285
msgid "Invalid DNS name pattern: Wildcard character used more than twice per label."
msgstr ""

#: pattern.c:295
msgid "Invalid DNS name pattern: Label ends with hyphen."
msgstr ""

#: pattern.c:301
#, c-format
msgid "Invalid DNS name pattern: Label is too long (%zu)."
msgstr ""

#: pattern.c:309
#, c-format
msgid "Invalid DNS name pattern: Not enough labels (%zu)."
msgstr ""

#: pattern.c:314
msgid "Invalid DNS name pattern: Wildcard within final two labels."
msgstr ""

#: pattern.c:319
msgid "Invalid DNS name pattern: Final label is fully numeric."
msgstr ""

#: pattern.c:329
msgid "Invalid DNS name pattern: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:334
#, c-format
msgid "DNS name pattern has invalid length after removing wildcards (%zu)."
msgstr ""

#: dnssec.c:206
msgid "system time considered valid, now checking DNSSEC signature timestamps."
msgstr "Systemzeit als gültig betrachtet, prüfe jetzt DNSSEC Signatur-Zeitstempel."

#: dnssec.c:1014
#, c-format
msgid "Insecure DS reply received for %s, check domain configuration and upstream DNS server DNSSEC support"
msgstr "Unsichere DS-Antwort für %s, bitte Domainkonfiguration und Upstream DNS-Server für DNSSEC-Unterstützung überprüfen"

#: blockdata.c:55
#, c-format
msgid "pool memory in use %u, max %u, allocated %u"
msgstr "Speicherpool in Benutzung %u, Max %u, zugewiesen %u"

#: tables.c:61
#, c-format
msgid "failed to access pf devices: %s"
msgstr "konnte auf pf-Geräte nicht zugreifen: %s"

#: tables.c:74
#, c-format
msgid "warning: no opened pf devices %s"
msgstr "Warnung: Keine geöffneten pf-Geräte %s"

#: tables.c:82
#, c-format
msgid "error: cannot use table name %s"
msgstr "Fehler: Kann Tabellenname %s nicht benutzen"

#: tables.c:90
#, c-format
msgid "error: cannot strlcpy table name %s"
msgstr "Fehler: Kann den Tabellennamen %s nicht mit strlcpy kopieren"

#: tables.c:101
#, c-format
msgid "IPset: error: %s"
msgstr "IPset: Fehler: %s"

#: tables.c:108
msgid "info: table created"
msgstr "Info: Tabelle erstellt"

#: tables.c:133
#, c-format
msgid "warning: DIOCR%sADDRS: %s"
msgstr "Warnung: DIOCR%sADDRS: %s"

#: tables.c:137
#, c-format
msgid "%d addresses %s"
msgstr "%d Adressen %s"

#: inotify.c:62
#, c-format
msgid "cannot access path %s: %s"
msgstr "Kann auf Pfad %s nicht zugreifen: %s"

#: inotify.c:95
#, c-format
msgid "failed to create inotify: %s"
msgstr "Kann kein \"inotify\" erzeugen: %s"

#: inotify.c:111
#, c-format
msgid "too many symlinks following %s"
msgstr "zu viele Symlinks beim Verfolgen von %s"

#: inotify.c:127
#, c-format
msgid "directory %s for resolv-file is missing, cannot poll"
msgstr "Verzeichnis %s für resolv-file fehlt, kann nicht abfragen"

#: inotify.c:131 inotify.c:168
#, c-format
msgid "failed to create inotify for %s: %s"
msgstr "Konnte \"inotify\" für %s nicht erzeugen: %s"

#: inotify.c:153
#, c-format
msgid "bad dynamic directory %s: %s"
msgstr "fehlerhaftes dynamisches Verzeichnis %s: %s"

#: inotify.c:257
#, c-format
msgid "inotify, new or changed file %s"
msgstr "inotify, neue oder geänderte Datei %s"

#: dump.c:64
#, c-format
msgid "cannot create %s: %s"
msgstr "kann %s nicht erstellen: %s"

#: dump.c:70
#, c-format
msgid "bad header in %s"
msgstr "Fehlerhafter Kopf in %s"

#: dump.c:205
msgid "failed to write packet dump"
msgstr "schreiben des Paketmitschnitts fehlgeschlagen"

#: dump.c:207
#, c-format
msgid "dumping UDP packet %u mask 0x%04x"
msgstr "Lade UDP Paket %u Maske 0x%04x ab"

#: ubus.c:79
#, c-format
msgid "UBus subscription callback: %s subscriber(s)"
msgstr "UBus-Subskription Rückruf: %s Teilnehmer"

#: ubus.c:99
#, c-format
msgid "Cannot reconnect to UBus: %s"
msgstr "Kann mit UBus nicht erneut verbinden: %s"

#: ubus.c:135
msgid "Cannot set UBus listeners: no connection"
msgstr "Kann UBus-Zuhörer nicht setzen: Keine Verbindung"

#: ubus.c:155
msgid "Cannot poll UBus listeners: no connection"
msgstr "Kann UBus-Zuhörer nicht abfragen: Keine Verbindung"

#: ubus.c:168
msgid "Disconnecting from UBus"
msgstr "Von System-UBus trennen"

#: ubus.c:179 ubus.c:326
#, c-format
msgid "UBus command failed: %d (%s)"
msgstr ""

#: hash-questions.c:40
msgid "Failed to create SHA-256 hash object"
msgstr "Kann SHA-256-Hash-Objekt nicht erstellen"

#~ msgid "Cannot initialize UBus: connection failed"
#~ msgstr "Kann UBus nicht initialisieren: Verbindung fehlgeschlagen"

#~ msgid "Cannot add object to UBus: %s"
#~ msgstr "Kann Objekt zu UBus nicht hinzufügen: %s"

#~ msgid "Failed to send UBus event: %s"
#~ msgstr "Fehlschlag beim Sendes des UBus-Ereignisses: %s"
