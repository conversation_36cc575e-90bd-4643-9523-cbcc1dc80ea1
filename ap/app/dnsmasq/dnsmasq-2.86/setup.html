<HTML>
<HEAD>
<TITLE> Configuring Dnsmasq.</TITLE>
</HEAD>
<BODY BGCOLOR="WHITE"> 
<H1 ALIGN=center>Dnsmasq setup</H1> 
<H2>Installation.</H2>
To compile and install dnsmasq, the following command (as root) is enough.

<PRE>
make install
</PRE>

You might want to edit config.h. Dnsmasq has
been run on (at least) Linux, uCLinux, AIX 4.1.5, FreeBSD 4.4 OpenBSD and Tru64 4.x 

Dnsmasq is normally  run on a firewall machine (the machine with the
modem or other connection to your ISP.) but it can run on any machine
with access to the ISPs nameservers.

Put the binary in <TT>/usr/local/sbin/dnsmasq</TT> (running <TT>make install</TT>  will do this) and arrange for it
to be started at boot time.

Note that dnsmasq needs to run as root, since it binds privileged ports. It will drop root privileges after start-up. Dnsmasq
logs problems using the syslog facility as a daemon. It logs debugging
information to local0
<P>
<H2>Configuration.</H2>
Configuration for dnsmasq is pretty simple in almost all cases. The
program has collected a fair few options as it has developed but most of them
are not needed most of the time. A machine which already has a DNS
configuration (ie one or more external nameservers in <TT>/etc/resolv.conf</TT>
and any local hosts in <TT>/etc/hosts</TT>) can be turned into a nameserver
simply by running dnsmasq, with no options or configuration at
all. Set the IP address of the machine running dnsmasq as the DNS
server in all the other machines on your network, and you're done.
<P>
With a few option flags, it is possible to make dnsmasq do more clever
tricks. Options for dnsmasq can be set either on the command line
when starting dnsmasq, or in its configuration file, <TT>/etc/dnsmasq.conf</TT>.

<h2>Making the nameserver machine use dnsmasq.</h2>
In the simple configuration described above, processes local to the
machine will not use dnsmasq, since they get their information about
which nameservers to use from /etc/resolv.conf, which is set to the
upstream nameservers. To fix this, simply replace the nameserver in
<TT>/etc/resolv.conf</TT> with the local address 127.0.0.1 and give the
address(es) of the upstream nameserver(s) to dnsmasq directly. You can
do this using either the <TT>server</TT> option, or by putting them into
another file, and telling  dnsmasq about its location with 
the <TT>resolv-file</TT> option. 

<h2>Automatic nameserver configuration.</h2>
The two protocols most used for automatic IP network configuration
(PPP and DHCP) can determine the IP addresses for nameservers automatically.
The daemons can be made to write out a file in the resolv.conf format with the
nameservers in which is perfect for dnsmasq to use. When the
nameservers change, for instance on dialling into a new ISP using PPP,
dnsmasq will automatically re-read this file and begin using the new
nameserver(s) completely transparently.

<h3>Automatic DNS server configuration with PPP.</h3>
Later versions of pppd have an option "usepeerdns" which instructs it to write a file containing
the address(es) of the DNS severs in <TT>/etc/ppp/resolv.conf</TT>. Configure dnsmasq
as above with "nameserver 127.0.0.1" in <TT>/etc/resolv.conf</TT> and run dnsmasq 
with to option <TT>resolv-file=/etc/ppp/resolv.conf</TT>.
<P>
On Redhat (at least versions 7.1, 7.2 and 7.3) you can set pppd
options by adding "PPPOPTIONS=usepeerdns" to
<TT>/etc/sysconfig/network-scripts/ifcfg-ippp0</TT>.  In the same file, make sure
that "PEERDNS=no" to stop RedHat's network initscripts from copying
<TT>/etc/ppp/resolv.conf</TT> into <TT>/etc/resolv.conf</TT>.<BR>

On SuSE (at least version 8.1, and 8.2) you should use YaST to activate
<TT>[x] Modify DNS when connected</TT> then stop SuSEs network initscripts 
from copying <TT>/etc/ppp/resolv.conf</TT> into <TT>/etc/resolv.conf</TT> 
by modifying MODIFY_RESOLV_CONF_DYNAMICALLY="no" in <TT>/etc/sysconfig/network/config</TT>.
 

<h3>Automatic DNS server configuration with DHCP.</h3>
You need to get your DHCP client to write the address(es) of the DNS
servers to a file other than <TT>/etc/resolv.conf</TT>. For dhcpcd, the
<TT>dhcpcd.exe</TT> script gets run with the addresses of the nameserver(s) in
the shell variable <TT>$DNS</TT>. The following bit of shell script
uses that to write a file suitable for dnsmasq. 
<PRE>

echo -n >|/etc/dhcpc/resolv.conf
dnsservers=${DNS//,/ }
for serv in $dnsservers; do
    echo "nameserver $serv" >>/etc/dhcpc/resolv.conf
done

</PRE>
 
Remember to give dhcpcd the <TT>-R</TT> flag to stop it overwriting 
<TT>/etc/resolv.conf</TT>.

<P>
For other DHCP clients it should be possible to achieve the same effect.

<h3> DHCP and PPP.</h3>
On a laptop which may potentially connect via a modem and PPP or
ethernet and DHCP it is possible to combine both of the above
configurations. Running dnsmasq with the flags
<TT>resolv-file=/etc/ppp/resolv.conf resolv-file=/etc/dhcpc/resolv.conf</TT>  
makes it poll <B>both</B> files and use whichever was updated
last. The result is automatic switching between DNS servers.
</H3>

<H2> Integration with DHCP.</H2>
Dnsmasq reads <TT>/etc/hosts</TT> so that the names of local machines are
available in DNS. This is fine when you give all your local machines
static IP addresses which can go in <TT>/etc/hosts</TT>, but it doesn't work 
when local machines are configured via DHCP, since the IP address
allocated to machine is not fixed. Dnsmasq comes with an integrated
DHCP daemon to solve this problem.
<P>
The dnsmasq DHCP daemon allocates addresses to hosts on the network and tries
to determine their names. If it succeeds it add the name and address
pair to the DNS. There are basically two ways to associate a name with
a DHCP-configured machine; either the machine knows its name which it
gets a DHCP lease, or dnsmasq gives it a name, based on the MAC
address of its ethernet card. For the former to work, a machine needs to know its name when it
requests a DHCP lease. For dhcpcd, the -h option specifies this. The
names may be anything as far as DHCP is concerned, but dnsmasq adds
some limitations. By default the names must no have a domain part, ie
they must just be alphanumeric names, without any dots.  This is a
security feature to stop a machine on your network telling DHCP that
its name is "www.microsoft.com" and thereby grabbing traffic which
shouldn't go to it. A domain part is only allowed by dnsmasq in DHCP machine names
if the <TT>domain-suffix</TT> option is set, the domain part must match the
suffix.
<P>
As an aside, make sure not to tell DHCP to set the hostname when it
obtains a lease (in dhcpcd that's the -H flag.)
This is not reliable since the DHCP server gets the
hostname from DNS which in this case is dnsmasq. There is a race
condition because the host's name in the DNS may change as a
result of it getting a DHCP lease, but this does not propagate before
the name is looked up. The net effect may be that the host believes it
is called something different to its name in the DNS. To be safe, set
the hostname on a machine locally, and pass the same name to DHCP when
requesting a lease.
<P>
<H2>Setting up a mailhub.</H2>
If you generate mail on the machines attached to your private network, you may
 be interested in the MX record feature of dnsmasq. This allows you to have all
 the machines on your network use your firewall or another machine as a "smarthost" and 
deliver mail to it. The details of how to set this up are highly dependent on
your mailer, system and distribution. The only thing that's relevant to dnsmasq is that the mailer 
needs to be able to interrogate the DNS and find an MX record for your mailhub.
<P>
By giving dnsmasq the <TT>mx-host</TT> option
you instruct dnsmasq to serve an MX record for the specified address. 
By default the MX record 
points to the machine on which dnsmasq is running, so mail delivered to that
name will get sent to the mailer on your firewall machine. You can
have the MX record point to another machine by using the <TT>mx-target</TT>
option.
<P>
In some cases it's useful for all local machines to see an MX record
pointing at themselves: this allows mailers which insist on an MX record and
don't fall back to A records to deliver mail within the
machine. These MX records are enabled using the <TT>selfmx</TT> option.

<H2>Using special servers.</H2>
Dnsmasq has the ability to direct DNS queries for certain domains to
specific upstream nameservers. This feature was added for use with
VPNs but it is fully general. The scenario is this: you have a
standard internet connection via an ISP, and dnsmasq is configured to
forward queries to the ISP's nameservers, then you make a VPN
connection into your companies network, giving access to hosts inside
the company firewall. You have access, but since many of the internal hosts
aren't visible on the public internet, your company doesn't publish 
them to the public DNS and you can't get their IP address from the ISP
nameservers. The solution is to use the companies nameserver for
private domains within the company, and dnsmasq allows this. Assuming
that internal company machines are all in the domain internal.myco.com
and the companies nameserver is at ************ then the option
<TT>server=/internal.myco.com/************</TT> will direct all
queries in the internal domain to the correct nameserver. You can
specify more than one domain in each server option. If there is
more than one nameserver just include as many
<TT>server</TT> options as is needed to specify them all.  

<H2>Local domains.</H2>
Sometimes people have local domains which they do not want forwarded
to upstream servers. This is accommodated by using server options
without the server IP address. To make things clearer <TT>local</TT>
is a synonym for <TT>server</TT>. For example the option
<TT>local=/localnet/</TT> ensures that any domain name query which ends in
<TT>.localnet</TT> will be answered if possible from
<TT>/etc/hosts</TT> or DHCP, but never sent to an upstream server.

<H2>Defeating wildcards in top level domains.</H2>
In September 2003 Verisign installed a wildcard record in the .com and
.net top level domains. The effect of this is that queries for
unregistered .com and .net names now return the address of Verisign's
sitefinder service, rather than a "no such domain" response. To
restore the correct behaviour, you can tell dnsmasq the address of the
sitefinder host and have it substitute an NXDOMAIN reply when it sees
that address. The sitefinder address is currently  ************, so
giving the option <TT>bogus-nxdomain=************</TT> will enable
this facility for Verisign. If other TLDs do that same thing you can
add the correct addresses for them too. See the dnsmasq FAQ for more
details on the <TT>bogus-nxdomain</TT> option.
 
<H2>Other configuration details.</H2>
By default dnsmasq offers DNS service on all the configured interfaces
of a host. It's likely that you don't (for instance) want to offer a
DNS service to the world via an interface connected to ADSL or
cable-modem so dnsmasq allows you to specify which interfaces it will
listen on. Use either the <TT>interface</TT> or <TT>address</TT> options to do this.
<P>
The <TT>filterwin2k</TT> option makes dnsmasq ignore certain DNS requests which
are made by Windows boxen every few minutes. The requests generally
don't get sensible answers in the global DNS and cause trouble by
triggering dial-on-demand internet links.
<P>
Sending SIGHUP to the dnsmasq process will cause it to empty its cache and 
then re-load <TT>/etc/hosts</TT> and <TT>/etc/resolv.conf</TT>.
<P> Sending SIGUSR1 (killall -10 dnsmasq) to the dnsmasq process will
cause to write cache usage statisticss to the log, typically
<TT>/var/log/syslog</TT> or <TT>/var/log/messages</TT>.
<P> The <TT>log-queries</TT> option tells dnsmasq to verbosely log the queries
it is handling and causes SIGUSR1 to trigger a complete dump of the
contents of the cache to the syslog.

<P>For a complete listing of options please take a look at the manpage
dnsmasq(8).
