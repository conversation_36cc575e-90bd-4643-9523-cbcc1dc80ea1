#! /bin/sh
#
# init.d/dnsmasq
#
### BEGIN INIT INFO
# Provides:       dnsmasq
# Required-Start: $network $remote_fs $syslog
# Required-Stop:
# Default-Start:  3 5
# Default-Stop:
# Description:    Starts internet name service masq caching server (DNS)
### END INIT INFO

NAMED_BIN=/usr/sbin/dnsmasq
NAMED_PID=/var/run/dnsmasq.pid
NAMED_CONF=/etc/dnsmasq.conf

if [ ! -x $NAMED_BIN ] ; then
	echo -n "dnsmasq not installed ! "
	exit 5
fi

. /etc/rc.status
rc_reset

case "$1" in
    start)
	echo -n "Starting name service masq caching server "
        checkproc -p $NAMED_PID $NAMED_BIN
        if [ $? -eq 0 ] ; then
           echo -n "- Warning: dnsmasq already running ! "
        else
           [ -e $NAMED_PID ] && echo -n "- Warning: $NAMED_PID exists ! "
	fi
	startproc -p $NAMED_PID $NAMED_BIN -u nobody
	rc_status -v
	;;
    stop)
	echo -n "Shutting name service masq caching server "
	checkproc -p $NAMED_PID $NAMED_BIN
	[ $? -ne 0 ] && echo -n "- Warning: dnsmasq not running ! "
	killproc -p $NAMED_PID -TERM $NAMED_BIN
	rc_status -v
	;;
    try-restart)
	$0 stop  &&  $0 start
	rc_status
	;;
    restart)
	$0 stop
	$0 start
	rc_status
	;;
    force-reload)
	$0 reload
	rc_status
	;;
    reload)
	echo -n "Reloading name service masq caching server "
	checkproc -p $NAMED_PID $NAMED_BIN
	[ $? -ne 0 ] && echo -n "- Warning: dnsmasq not running ! "
	killproc -p $NAMED_PID -HUP $NAMED_BIN
	rc_status -v
	;;
    status)
	echo -n "Checking for name service masq caching server "
	checkproc -p $NAMED_PID $NAMED_BIN
	rc_status -v
	;;
    probe)
	test $NAMED_CONF -nt $NAMED_PID && echo reload
	;;
    *)
	echo "Usage: $0 {start|stop|status|try-restart|restart|force-reload|reload|probe}"
	exit 1
	;;
esac
rc_exit

