This is a patch against SuSEfirewall2-3.1-206 (SuSE 9.x and older)
It fixes the dependency from the dns daemon name 'named'
After appending the patch, the SuSEfirewall is again able to autodetect 
the dnsmasq named service.
This is a very old bug in the SuSEfirewall script.
The SuSE people think the name of the dns server will always 'named'


--- /sbin/SuSEfirewall2.orig	2004-01-23 13:30:09.000000000 +0100
+++ /sbin/SuSEfirewall2	2004-01-23 13:31:56.000000000 +0100
@@ -764,7 +764,7 @@
     echo 'FW_ALLOW_INCOMING_HIGHPORTS_UDP should be set to yes, if you are running a DNS server!'
 
 test "$FW_SERVICE_AUTODETECT" = yes -o "$FW_SERVICE_AUTODETECT" = dmz -o "$FW_SERVICE_AUTODETECT" = ext && {
-    test "$FW_SERVICE_DNS" = no -a '!' "$START_NAMED" = no && check_srv named && {
+    test "$FW_SERVICE_DNS" = no -a '!' "$START_NAMED" = no && check_srv dnsmasq && {
 	echo -e 'Warning: detected activated named, enabling FW_SERVICE_DNS!
 You still have to allow tcp/udp port 53 on internal, dmz and/or external.'
 	FW_SERVICE_DNS=$FW_SERVICE_AUTODETECT
@@ -878,7 +878,7 @@
 test -e /etc/resolv.conf || echo "Warning: /etc/resolv.conf not found"
 # Get ports/IP bindings of NAMED/SQUID
 test "$FW_SERVICE_DNS" = yes -o "$FW_SERVICE_DNS" = dmz -o "$FW_SERVICE_DNS" = ext -o "$START_NAMED" = yes && DNS_PORT=`$LSOF -i -n -P | \
-    $AWK -F: '/^named .* UDP / {print $2}'| $GREP -vw 53 | $SORT -un`
+    $AWK -F: '/^dnsmasq .* UDP / {print $2}'| $GREP -vw 53 | $SORT -un`
 test "$FW_SERVICE_SQUID" = yes -o "$FW_SERVICE_SQUID" = dmz -o "$FW_SERVICE_SQUID" = ext -o "$START_SQUID" = yes && SQUID_PORT=`$LSOF -i -n -P | \
     $AWK -F: '/^squid .* UDP/ {print $2}'| $SORT -un`
