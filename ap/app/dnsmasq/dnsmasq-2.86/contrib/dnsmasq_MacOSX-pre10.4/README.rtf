{\rtf1\mac\ansicpg10000\cocoartf824\cocoasubrtf100
{\fonttbl\f0\fswiss\fcharset77 Helvetica;\f1\fnil\fcharset77 Monaco;}
{\colortbl;\red255\green255\blue255;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11120\viewh10100\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\ql\qnatural\pardirnatural

\f0\fs24 \cf0 1. 	If you've used DNSenabler, or if you're using Mac OS X Server, or if you have in any other way activated Mac OS X's built-in DHCP and/or DNS servers, disable them.  This would usually involve checking that they are either set to -NO- or absent altogether in 
\f1 /etc/hostconfig
\f0 .  If you've never done anything to do with DNS or DHCP servers on a client version of MacOS X, you won't need to worry about this; it will already be configured for you.\
\
2.	Add a configuration item to 
\f1 /etc/hostconfig
\f0  as follows:\
\

\f1 DNSMASQ=-YES-
\f0 \
\
3. 	Create a system-wide StartupItems directory for dnsmasq:\
\

\f1 sudo mkdir -p /Library/StartupItems/DNSmasq\

\f0 \
4.	Copy the files 
\f1 DNSmasq
\f0  and 
\f1 StartupParameters.plist
\f0  into this directory, and make sure the former is executable:\
\

\f1 sudo cp DNSmasq StartupParameters.plist /Library/StartupItems/DNSmasq\
sudo chmod 755 /Library/StartupItems/DNSmasq/DNSmasq\

\f0 \
5.	Start the service:\
\

\f1 sudo /Library/StartupItems/DNSmasq/DNSmasq start\

\f0 \cf0 \
That should be all...}