This script can be used to implement persistent leases on openWRT, DD-WRT
etc. Persistent leases are good: if the lease database is lost on a
reboot, then it will eventually be restored as hosts renew their
leases. Until a host renews (which may take hours/days) it will
not exist in the DNS if dnsmasq's DDNS function is in use.

*WRT systems remount all non-volatile filesystems read-only after boot,
so the normal leasefile will not work. They do, however have NV
storage, accessed with the nvram command:

/usr/lib #  nvram
usage: nvram [get name] [set name=value] [unset name] [show]

The principle is that leases are kept in NV variable with data
corresponding to the line in a leasefile:

dnsmasq_lease_************=3600 00:41:4a:05:80:74 ************ * *

By giving dnsmasq the leasefile-ro command, it no longer creates or writes a
leasefile; responsibility for maintaining the lease database transfers
to the lease change script. At startup, in leasefile-ro mode,
dnsmasq will run

"<lease_change_script> init" 

and read whatever that command spits out, expecting it to
be in dnsmasq leasefile format.

So the lease change script, given "init" as argv[1] will 
suck existing leases out of the NVRAM and emit them from
stdout in the correct format.

The second part of the problem is keeping the NVRAM up-to-date: this
is done by the lease-change script which dnsmasq runs when a lease is
updated. When it is called with argv[1] as "old", "add", or "del"
it updates the relevant nvram entry.

So, dnsmasq should be run as :

dnsmasq --leasefile-ro --dhcp-script=/path/to/lease_update.sh

or the same flags added to /etc/dnsmasq.conf



Notes: 

This needs dnsmasq-2.33 or later to work.

This technique will work with, or without, compilation with
HAVE_BROKEN_RTC. Compiling with HAVE_BROKEN_RTC is
_highly_recommended_ for this application since is avoids problems
with the system clock being warped by NTP, and it vastly reduces the
number of writes to the NVRAM. With HAVE_BROKEN_RTC, NVRAM is updated
only when a lease is created or destroyed; without it, a write occurs
every time a lease is renewed.

It probably makes sense to restrict the number of active DHCP leases
to an appropriate number using dhcp-lease-max. On a new DD_WRT system,
there are about 10K bytes free in the NVRAM. Each lease record is
about 100 bytes, so restricting the number of leases to 50 will limit
use to half that. (The default limit in the distributed source is 150)

Any UI script which reads the dnsmasq leasefile will have to be
amended, probably by changing it to read the output of 
`lease_update init` instead.
 

Thanks:

To Steve Horbachuk for checks on the script and debugging beyond the 
call of duty.


Simon Kelley
Fri Jul 28 11:51:13 BST 2006





