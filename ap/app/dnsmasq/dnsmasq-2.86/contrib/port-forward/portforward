# This file is read by /usr/sbin/dnsmasq-portforward and used to set up port 
# forwarding to hostnames. If the dnsmasq-determined hostname matches the
# first column of this file, then a DNAT port-forward will be set up 
# to the address which has just been allocated by DHCP . The second field 
# is port number(s). If there is only one, then the port-forward goes to 
# the same port on the DHCP-client, if there are two separated with a 
# colon, then the second number is the port to which the connection 
# is forwarded on the DHCP-client. By default, forwarding is set up 
# for TCP, but it can done for UDP instead by prefixing the port to "u". 
# To forward both TCP and UDP, two lines are required. 
#
# eg.
# wwwserver 80
# will set up a port forward from port 80 on this host to port 80 
# at the address allocated to wwwserver whenever wwwserver gets a DHCP lease.
#
# wwwserver 8080:80
# will set up a port forward from port 8080 on this host to port 80
# on the DHCP-client.
#
# dnsserver 53
# dnsserver u53
# will port forward port 53 UDP and TCP from this host to port 53 on dnsserver.
#
# Port forwards will recreated when dnsmasq restarts after a reboot, and
# removed when DHCP leases expire. After editing this file, send
# SIGHUP to dnsmasq to install new iptables entries in the kernel.

