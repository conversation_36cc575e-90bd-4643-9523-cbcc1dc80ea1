diff -Nau dnsmasq-2.35/src/dnsmasq.h dnsmasq/src/dnsmasq.h
--- dnsmasq-2.35/src/dnsmasq.h	2006-10-18 16:24:50.000000000 -0400
+++ dnsmasq/src/dnsmasq.h	2006-11-16 22:06:31.000000000 -0500
@@ -112,6 +112,7 @@
 #define OPT_NO_PING        2097152
 #define OPT_LEASE_RO       4194304
 #define OPT_RELOAD         8388608
+#define OPT_TRY_ALL_NS     16777216
 
 struct all_addr {
   union {
diff -Nau dnsmasq-2.35/src/forward.c dnsmasq/src/forward.c
--- dnsmasq-2.35/src/forward.c	2006-10-18 16:24:50.000000000 -0400
+++ dnsmasq/src/forward.c	2006-11-16 22:08:19.000000000 -0500
@@ -445,6 +445,10 @@
     {
        struct server *server = forward->sentto;
        
+       // If strict-order and try-all-ns are set, treat NXDOMAIN as a failed request
+       if( (daemon->options & OPT_ORDER) && (daemon->options && OPT_TRY_ALL_NS)
+           && header->rcode == NXDOMAIN ) header->rcode = SERVFAIL;
+
        if ((header->rcode == SERVFAIL || header->rcode == REFUSED) && forward->forwardall == 0)
 	 /* for broken servers, attempt to send to another one. */
 	 {
diff -Nau dnsmasq-2.35/src/option.c dnsmasq/src/option.c
--- dnsmasq-2.35/src/option.c	2006-10-18 16:24:50.000000000 -0400
+++ dnsmasq/src/option.c	2006-11-16 22:10:36.000000000 -0500
@@ -28,7 +28,7 @@
 
 /* options which don't have a one-char version */
 #define LOPT_RELOAD 256
-
+#define LOPT_TRY_ALL_NS 257
 
 #ifdef HAVE_GETOPT_LONG
 static const struct option opts[] =  
@@ -102,6 +102,7 @@
     {"leasefile-ro", 0, 0, '9'},
     {"dns-forward-max", 1, 0, '0'},
     {"clear-on-reload", 0, 0, LOPT_RELOAD },
+    {"try-all-ns", 0, 0, LOPT_TRY_ALL_NS },
     { NULL, 0, 0, 0 }
   };
 
@@ -134,6 +135,7 @@
   { '5',            OPT_NO_PING },
   { '9',            OPT_LEASE_RO },
   { LOPT_RELOAD,    OPT_RELOAD },
+  { LOPT_TRY_ALL_NS,OPT_TRY_ALL_NS },
   { 'v',            0},
   { 'w',            0},
   { 0, 0 }
@@ -208,6 +210,7 @@
   { "-9, --leasefile-ro", gettext_noop("Read leases at startup, but never write the lease file."), NULL },
   { "-0, --dns-forward-max=<queries>", gettext_noop("Maximum number of concurrent DNS queries. (defaults to %s)"), "!" }, 
   { "    --clear-on-reload", gettext_noop("Clear DNS cache when reloading %s."), RESOLVFILE },
+  { "    --try-all-ns", gettext_noop("Try all name servers in tandem on NXDOMAIN replies (use with strict-order)."), NULL },
   { NULL, NULL, NULL }
 }; 
 
