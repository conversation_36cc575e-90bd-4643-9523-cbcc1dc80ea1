#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>

#include "qrzl_device_control.h"
#include "json.h"
#include "qrzl_utils.h"
#include "curl/curl.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;


/* http请求的路径 */
static char http_request_path[256] = "http://devcontrol.szqrzl.com/api/device_push/status";
// static char http_request_path[256] = "http://121.196.238.15:9098/api/device_push/status";

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;


// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    if (totalSize >= 5120)
    {
        qrzl_err("http返回值大小: %d,大于已定义的最大长度", totalSize);
        return totalSize;
    }
    strncat((char *)userp, (char *)contents, totalSize);
    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 拼接请求URL和参数
static void qrzl_build_request_url(char *url_buffer, size_t buffer_size) {
    snprintf(url_buffer, buffer_size, "%s", http_request_path);
    snprintf(url_buffer, buffer_size, "%s?sn=%s", url_buffer, g_qrzl_device_static_data.sn);
    snprintf(url_buffer, buffer_size, "%s&imei=%s", url_buffer, g_qrzl_device_static_data.imei);
    snprintf(url_buffer, buffer_size, "%s&currentIccid=%s", url_buffer, g_qrzl_device_dynamic_data.iccid);
    snprintf(url_buffer, buffer_size, "%s&mac=%s", url_buffer, g_qrzl_device_static_data.mac);
    snprintf(url_buffer, buffer_size, "%s&softVersion=%s", url_buffer, g_qrzl_device_static_data.soft_version);
    snprintf(url_buffer, buffer_size, "%s&hardwareVersion=%s", url_buffer, g_qrzl_device_static_data.hw_version);
    snprintf(url_buffer, buffer_size, "%s&eSim1Iccid=%s", url_buffer, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(url_buffer, buffer_size, "%s&eSim2Iccid=%s", url_buffer, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(url_buffer, buffer_size, "%s&eSim3Iccid=%s", url_buffer, g_qrzl_device_static_data.nvro_esim3_iccid);
}

static void qrzl_resp_handler(json_value *value)
{
    json_value* j_device_disabled = json_value_get(value, "deviceDisabled", 14); 
    if (j_device_disabled != NULL && j_device_disabled->type == json_boolean && j_device_disabled->u.boolean == 1)
    {
        qrzl_log("qrzl control shutdown_device");
        shutdown_device();
    }
    /**
     * 限速处理
     * 1，首先读取服务端下发的限速值
     * 2，如果限速值大于0，存入qrzl_limit，然后调用公共限速函数
     * 3, 如果限速值等于0，就判断qrzl_limit是否大于0，如果大于0，就存入qrzl_limit并调用公共限速函数，限速值为0
     */
    uint64_t limit_down_speed = 0;
    uint64_t limit_up_speed = 0;
    char qrzl_limit_down_speed[21] = {0};
    char qrzl_limit_up_speed[21] = {0};
    cfg_get_item("qrzl_limit_down_speed", qrzl_limit_down_speed, sizeof(qrzl_limit_down_speed));
    cfg_get_item("qrzl_limit_up_speed", qrzl_limit_up_speed, sizeof(qrzl_limit_up_speed));

    json_value* j_limit_down_speed = json_value_get(value, "limitDownSpeed", 14);
    if (j_limit_down_speed != NULL && j_limit_down_speed->type == json_integer)
    {
        limit_down_speed = j_limit_down_speed->u.integer;
    }
    json_value* j_limit_up_speed = json_value_get(value, "limitUpSpeed", 12);
    if (j_limit_up_speed != NULL && j_limit_up_speed->type == json_integer) {
        limit_up_speed = j_limit_up_speed->u.integer;
    }

    if (limit_down_speed > 0 || limit_up_speed > 0) {
        snprintf(qrzl_limit_down_speed, sizeof(qrzl_limit_down_speed), "%llu", limit_down_speed);
        snprintf(qrzl_limit_up_speed, sizeof(qrzl_limit_up_speed), "%llu", limit_up_speed);
        cfg_set("qrzl_limit_down_speed", qrzl_limit_down_speed);
        cfg_set("qrzl_limit_up_speed", qrzl_limit_up_speed);
        limit_net_speed(limit_up_speed, limit_down_speed);
    } else {
        uint64_t limit_down_speed_num = strtoull(qrzl_limit_down_speed, NULL, 10);
        uint64_t limit_up_speed_num = strtoull(qrzl_limit_up_speed, NULL, 10);
        if (limit_down_speed_num > 0 || limit_up_speed_num > 0) {
            cfg_set("qrzl_limit_down_speed", "0");
            cfg_set("qrzl_limit_up_speed", "0");
            qrzl_log("qrzl control limit_net_speed 0");
            limit_net_speed(0, 0);
        } else {
            qrzl_log("qrzl control limit_net_speed no change");
        }
    }


}

/**
 * 发起一次http请求，并根据返回值进行处理，如果成功返回0，错误返回-1
 */
static int qrzl_start_process()
{
    int ret;
    char http_response[5120] = {0}; // 存储响应体
    char http_url[1024] = {0};

    update_device_dynamic_data();

    // 构建请求URL
    qrzl_build_request_url(http_url, sizeof(http_url));
    ret = http_send_get_request(http_url, http_response);
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return -1;
    }
    json_value *value = json_parse(http_response, strlen(http_response));
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return -1;
    }
    // 确保顶层是一个对象
    if (value->type != json_object)
    {
        qrzl_err("JSON is not an object.\n");
        json_value_free(value);
        return -1;
    }

    qrzl_resp_handler(value);

    // 释放 JSON 解析结果
    json_value_free(value);
    return 0;
}

void* qrzl_device_control_start()
{
    // 先等个90s，以防开机没有注册上网
    qrzl_log("qrzl_device_control_start sleep 90s");
    sleep(90);

    /**
     * 进入循环，开机至少要成功请求一次
     */

    while (qrzl_start_process() != 0)
    {
        sleep(600);
    }
    
    qrzl_log("qrzl_device_control_start end");

    return NULL;
}
