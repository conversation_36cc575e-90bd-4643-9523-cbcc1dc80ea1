#ifndef __QRZL_CMP_AUTH_CONTROL_H_
#define __QRZL_CMP_AUTH_CONTROL_H_

/**
 * 设备上/下线上报
 */
void device_line_type_push(const int push_type, const char *mac, const char *terminalMac);

void* cmp_http_control_start();

/**
 * 判断终端设备是否已经认证
 * @param mac 设备MAC
 * @param terminal_mac 终端设备MAC
 * @param servAddr 回调地址
 * @return 0 未认证； 1 已认证
 */
int cmp_terminal_is_authed(const char* mac, const char* terminal_mac, const char* servAddr);

void init_cmp_authed_info();

#endif