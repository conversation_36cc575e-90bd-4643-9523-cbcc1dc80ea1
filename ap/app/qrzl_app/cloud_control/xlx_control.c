#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>

#include "xlx_control.h"
#include "../qrzl_utils.h"

#define XLX_SERVER_HOSTNAME "gate.xlxpro.com"
#define XLX_SERVER_PORT 3479

#define XLX_MSG_MAX_LENGTH sizeof(uint8_t) + sizeof(uint8_t) + sizeof(uint16_t) + sizeof(uint16_t) + sizeof(uint16_t) + 0xffff

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static pthread_mutex_t send_message_lock;

// 数据包结构体定义
typedef struct {
    uint8_t start_flag;        // 起始位
    uint8_t cmd;               // 命令号
    uint16_t checksum;         // 校验码
    uint16_t serial_number;    // 序列号
    uint16_t body_length;      // 包体长度
    char *body;             // 包体
} xlx_data_packet_t;

// 释放包体内存的函数
static void xlx_free_packet(xlx_data_packet_t *packet) {
    if (packet->body) {
        free(packet->body);
    }
}

static void print_byte_array(const unsigned char *data, size_t len) {
    size_t i;
    for (i = 0; i < len; i++) {
        printf("0x%02X ", data[i]);
    }
    printf("\n");
}

static const uint16_t crctab16[] = {
    0X0000, 0X1189, 0X2312, 0X329B, 0X4624, 0X57AD, 0X6536, 0X74BF,
    0X8C48, 0X9DC1, 0XAF5A, 0XBED3, 0XCA6C, 0XDBE5, 0XE97E, 0XF8F7,
    0X1081, 0X0108, 0X3393, 0X221A, 0X56A5, 0X472C, 0X75B7, 0X643E,
    0X9CC9, 0X8D40, 0XBFDB, 0XAE52, 0XDAED, 0XCB64, 0XF9FF, 0XE876,
    0X2102, 0X308B, 0X0210, 0X1399, 0X6726, 0X76AF, 0X4434, 0X55BD,
    0XAD4A, 0XBCC3, 0X8E58, 0X9FD1, 0XEB6E, 0XFAE7, 0XC87C, 0XD9F5,
    0X3183, 0X200A, 0X1291, 0X0318, 0X77A7, 0X662E, 0X54B5, 0X453C,
    0XBDCB, 0XAC42, 0X9ED9, 0X8F50, 0XFBEF, 0XEA66, 0XD8FD, 0XC974,
    0X4204, 0X538D, 0X6116, 0X709F, 0X0420, 0X15A9, 0X2732, 0X36BB,
    0XCE4C, 0XDFC5, 0XED5E, 0XFCD7, 0X8868, 0X99E1, 0XAB7A, 0XBAF3,
    0X5285, 0X430C, 0X7197, 0X601E, 0X14A1, 0X0528, 0X37B3, 0X263A,
    0XDECD, 0XCF44, 0XFDDF, 0XEC56, 0X98E9, 0X8960, 0XBBFB, 0XAA72,
    0X6306, 0X728F, 0X4014, 0X519D, 0X2522, 0X34AB, 0X0630, 0X17B9,
    0XEF4E, 0XFEC7, 0XCC5C, 0XDDD5, 0XA96A, 0XB8E3, 0X8A78, 0X9BF1,
    0X7387, 0X620E, 0X5095, 0X411C, 0X35A3, 0X242A, 0X16B1, 0X0738,
    0XFFCF, 0XEE46, 0XDCDD, 0XCD54, 0XB9EB, 0XA862, 0X9AF9, 0X8B70,
    0X8408, 0X9581, 0XA71A, 0XB693, 0XC22C, 0XD3A5, 0XE13E, 0XF0B7,
    0X0840, 0X19C9, 0X2B52, 0X3ADB, 0X4E64, 0X5FED, 0X6D76, 0X7CFF,
    0X9489, 0X8500, 0XB79B, 0XA612, 0XD2AD, 0XC324, 0XF1BF, 0XE036,
    0X18C1, 0X0948, 0X3BD3, 0X2A5A, 0X5EE5, 0X4F6C, 0X7DF7, 0X6C7E,
    0XA50A, 0XB483, 0X8618, 0X9791, 0XE32E, 0XF2A7, 0XC03C, 0XD1B5,
    0X2942, 0X38CB, 0X0A50, 0X1BD9, 0X6F66, 0X7EEF, 0X4C74, 0X5DFD,
    0XB58B, 0XA402, 0X9699, 0X8710, 0XF3AF, 0XE226, 0XD0BD, 0XC134,
    0X39C3, 0X284A, 0X1AD1, 0X0B58, 0X7FE7, 0X6E6E, 0X5CF5, 0X4D7C,
    0XC60C, 0XD785, 0XE51E, 0XF497, 0X8028, 0X91A1, 0XA33A, 0XB2B3,
    0X4A44, 0X5BCD, 0X6956, 0X78DF, 0X0C60, 0X1DE9, 0X2F72, 0X3EFB,
    0XD68D, 0XC704, 0XF59F, 0XE416, 0X90A9, 0X8120, 0XB3BB, 0XA232,
    0X5AC5, 0X4B4C, 0X79D7, 0X685E, 0X1CE1, 0X0D68, 0X3FF3, 0X2E7A,
    0XE70E, 0XF687, 0XC41C, 0XD595, 0XA12A, 0XB0A3, 0X8238, 0X93B1,
    0X6B46, 0X7ACF, 0X4854, 0X59DD, 0X2D62, 0X3CEB, 0X0E70, 0X1FF9,
    0XF78F, 0XE606, 0XD49D, 0XC514, 0XB1AB, 0XA022, 0X92B9, 0X8330,
    0X7BC7, 0X6A4E, 0X58D5, 0X495C, 0X3DE3, 0X2C6A, 0X1EF1, 0X0F78,
};

// 计算给定长度数据的 16 位 CRC。
static uint16_t get_crc16(const uint8_t* pData, int nLength)
{
    uint16_t fcs = 0xffff; // 初始化
    while(nLength>0){
        fcs = (fcs >> 8) ^ crctab16[(fcs ^ *pData) & 0xff];
        nLength--;
        pData++;
    }
    return ~fcs; // 取反
}   

// 将字符转换为8421码并存储在unsigned char数组中 以大端序列存储
static void string_to_8421(const char *str, unsigned char *bcd)
{
    int i = 0;  // 字符串的索引
    int j = 0;  // BCD数组的索引
    
    // 遍历字符串中的每个字符
    while (str[i] != '\0') {
        unsigned char digit = 0;

        if (str[i] >= '0' && str[i] <= '9') {
            digit = str[i] - '0';  // 获取当前字符对应的数字
        } // 处理A-F
        else if (str[i] >= 'A' && str[i] <= 'F') {
            digit = str[i] - 'A' + 10;  // 获取A-F字符对应的数字
        }
        // 处理a-f
        else if (str[i] >= 'a' && str[i] <= 'f') {
            digit = str[i] - 'a' + 10;  // 获取a-f字符对应的数字
        }
        else {
            qrzl_log("Invalid character in the string: %c", str[i]);
            return;
        }

        if (i % 2 == 0) {
            // 存储偶数位的数字到高4位
            bcd[j] |= (digit << 4);  // 将4位数字存储到高4位
        } else {
            // 存储奇数位的数字到高4位
            bcd[j] |= digit;  // 将4位数字存储到低4位
            j++;  // 如果是第二个数字，移动到下一个字节
        }

        i++;
    }
}


static void remove_mac(char *mac_list, const char *target_mac) {
    char *macs[10];  // 存储解析后的 MAC 地址
    int count = 0;
    char *token;
    char buffer[180] = {0};  // 存储最终拼接的 MAC 地址

    // 复制 mac_list，避免 strtok 修改原字符串
    char temp_list[180] = {0};
    strncpy(temp_list, mac_list, sizeof(temp_list) - 1);
    temp_list[sizeof(temp_list) - 1] = '\0';

    // 解析 MAC 地址
    token = strtok(temp_list, ";");
    while (token && count < 10) {
        
        if (strcmp(token, target_mac) != 0) {  // 只保留不匹配的 MAC
            macs[count++] = token;
        }
        token = strtok(NULL, ";");
    }

    // 重新拼接 MAC 地址
    int i;
    for (i = 0; i < count; i++) {
        strcat(buffer, macs[i]);
        if (i < count - 1) {
            strcat(buffer, ";");
        }
    }

    // 更新原 MAC 列表
    strncpy(mac_list, buffer, sizeof(buffer));
}



static int xlx_sockfd = -1;
static uint8_t heartbeat_pack_loss_num = 0;
static uint8_t xlx_is_login = 0;
static uint16_t xlx_send_serial_number = 0;
static uint64_t last_send_total_bytes = 0;

static void close_and_reset_xlx_socket()
{
    qrzl_log("close_and_reset_xlx_socket");
    if (xlx_sockfd != -1) {
        close(xlx_sockfd);
        xlx_sockfd = -1;
    }
    heartbeat_pack_loss_num = 0;
    xlx_is_login = 0;
    xlx_send_serial_number = 0;
}
   

static int create_socket(const char *hostname, int port_int)
{
    qrzl_log("create_socket");
    struct addrinfo hints, *res;
    int sockfd;

    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;          // IPv4
    hints.ai_socktype = SOCK_STREAM;    // TCP
    char port[6] = {0};
    snprintf(port, sizeof(port), "%d", port_int);

    if (getaddrinfo(hostname, port, &hints, &res) != 0) {
        return -1;
    }

    sockfd = socket(res->ai_family, res->ai_socktype, res->ai_protocol);
    if (sockfd == -1) {
        perror("socket creation failed");
        freeaddrinfo(res);
        return -1;
    }

    if (connect(sockfd, res->ai_addr, res->ai_addrlen) == -1) {
        perror("socket 连接失败");
        close(sockfd);
        freeaddrinfo(res);
        return -1;
    }

    freeaddrinfo(res);
    return sockfd;
}


/**
 * 
 * 这个方法是要发生一个完整的数据给服务端
 * 
 * @param cmd_id 控制命令ID
 * @param cmd_data 控制命令要发送时要传输的数据
 * @param cmd_data_len 长度
 * @param serial_number 序列号
 * @return -1 错误；0 发送不完整；1正常
 */
static int send_message_core(int sockfd, uint8_t cmd_id, char *cmd_data, uint16_t cmd_data_len, uint16_t serial_number) 
{
    pthread_mutex_lock(&send_message_lock);
    qrzl_log("start send_message");
    size_t send_data_len = sizeof(uint8_t) + sizeof(uint8_t) + sizeof(uint16_t) + sizeof(uint16_t) + sizeof(uint16_t) + cmd_data_len;
    
    // 这里是先定义发生数据最大的长度，最后发送时看实际长度
    char data[XLX_MSG_MAX_LENGTH] = {0};
    size_t offset = 0;

    uint8_t start_flag = 0xF1;
    memcpy(data+offset, &start_flag, sizeof(start_flag)); // 起始位 暂时只支持0xF1 明文包
    offset += sizeof(start_flag);
    uint8_t cid = cmd_id;
    memcpy(data+offset, &cid, sizeof(cid)); // 命令号
    offset += sizeof(cid);
    // 这里也是先定义一个最大的长度
    uint8_t crc16_data[sizeof(uint16_t) + sizeof(uint16_t) + 10240] = {0};
    uint16_t net_serial_number = htons(serial_number);
    memcpy(crc16_data, &net_serial_number, sizeof(net_serial_number));
    uint16_t net_cmd_net = htons(cmd_data_len);
    memcpy(crc16_data+sizeof(net_serial_number), &net_cmd_net, sizeof(net_cmd_net));
    memcpy(crc16_data+sizeof(net_serial_number)+sizeof(cmd_data_len), cmd_data, cmd_data_len);
    uint16_t crc16_code = get_crc16(crc16_data, sizeof(uint16_t) + sizeof(uint16_t) + cmd_data_len);
    qrzl_log("crc16_code = %d\n", crc16_code);

    uint16_t net_crc16_code = htons(crc16_code);
    memcpy(data+offset, &net_crc16_code, sizeof(net_crc16_code)); // 校验码
    offset += sizeof(net_crc16_code);

    memcpy(data+offset, &net_serial_number, sizeof(net_serial_number)); // 序列号
    offset += sizeof(net_serial_number);

    uint16_t net_cmd_data_len = htons(cmd_data_len);
    memcpy(data+offset, &net_cmd_data_len, sizeof(net_cmd_data_len)); // 包体长度
    offset += sizeof(net_cmd_data_len);

    memcpy(data+offset, cmd_data, cmd_data_len); // 包体数据
    offset += cmd_data_len;
    
    ssize_t send_ret = 0;
    qrzl_log("send_data_len = %ld\n", send_data_len);
    qrzl_log("all data: ");
    print_byte_array(data, send_data_len);
    send_ret = send(sockfd, data, send_data_len, 0);
    qrzl_log("send_ret: %ld\n", send_ret);
    pthread_mutex_unlock(&send_message_lock);
    if (send_ret == -1) {
        return send_ret;
    }
    return send_ret == send_data_len;
}

/**
 * 
 * 这个方法是要发生一个完整的数据给服务端
 * 
 * @param cmd_id 控制命令ID
 * @param cmd_data 控制命令要发送时要传输的数据
 * @param cmd_data_len 长度
 * @return -1 错误；0 发送不完整；1正常
 */
static int send_message(int sockfd, uint8_t cmd_id, char *cmd_data, uint16_t cmd_data_len) 
{
    int ret = send_message_core(sockfd, cmd_id, cmd_data, cmd_data_len, xlx_send_serial_number);
    xlx_send_serial_number++;
    return ret;
}

static int send_login_server(int sockfd)
{
    qrzl_log("send_login_server");
    uint8_t bcd_imei[8] = {0};
    uint8_t bcd_iccid[10] = {0};
    char imei[17] = {0};
    if (strlen(g_qrzl_device_static_data.imei) < 16) {
        snprintf(imei, sizeof(imei), "0%s", g_qrzl_device_static_data.imei);
    } else {
        snprintf(imei, sizeof(imei), "%s", g_qrzl_device_static_data.imei);
    }
    qrzl_log("imei = %s", imei);
    string_to_8421(imei, bcd_imei);
    string_to_8421(g_qrzl_device_dynamic_data.iccid, bcd_iccid);
    uint16_t device_flag = 0xffff;
    uint32_t soft_version = 1;
    uint32_t net_soft_version = htonl(soft_version);

    char cmd_data[sizeof(bcd_imei) + sizeof(bcd_iccid) + sizeof(device_flag) + sizeof(net_soft_version)] = {0};

    memcpy(cmd_data, bcd_imei, sizeof(bcd_imei));
    memcpy(cmd_data+sizeof(bcd_imei), bcd_iccid, sizeof(bcd_iccid));
    memcpy(cmd_data+sizeof(bcd_imei)+sizeof(bcd_iccid), &device_flag, sizeof(device_flag));
    memcpy(cmd_data+sizeof(bcd_imei)+sizeof(bcd_iccid)+sizeof(device_flag), &net_soft_version, sizeof(net_soft_version));
    return send_message(sockfd, 0x01, cmd_data, sizeof(cmd_data));
}

static int send_heartbeat(int sockfd)
{
    qrzl_log("send_heartbeat");
    // update_device_dynamic_data();
    uint8_t power = get_remain_power(); // 电量百分比
    uint8_t satellite_num = 0; // 卫星连接数量，这个保持0就好了
    uint8_t sim_singal = get_rsrp_percentage(); // sim卡信号百分比
    uint8_t device_status = 0; // 32 接电 0没有接电
    if (get_device_charge_status() == 1) {
        device_status = 32;
    } else {
        device_status = 0;
    }
    qrzl_log("power: %d, satellite_num: %d, sim_singal: %d, device_status: %d", power, satellite_num, sim_singal, device_status);

    char cmd_data[sizeof(power) + sizeof(satellite_num) + sizeof(sim_singal) + sizeof(device_status)] = {0};
    size_t offset = 0;
    memcpy(cmd_data + offset, &power, sizeof(power));
    offset += sizeof(power);
    memcpy(cmd_data + offset, &satellite_num, sizeof(satellite_num));
    offset += sizeof(satellite_num);
    memcpy(cmd_data + offset, &sim_singal, sizeof(sim_singal));
    offset += sizeof(sim_singal);
    memcpy(cmd_data + offset, &device_status, sizeof(device_status));
    offset += sizeof(device_status);
    return send_message(sockfd, 0x02, cmd_data, sizeof(cmd_data));
}

static int send_wifi_and_cell_info(int sockfd)
{
    qrzl_log("send_wifi_and_cell_info");
    update_device_dynamic_data();
    char buffer[4096] = {0};
    size_t offset = 0;

    // 包体 - 上报时间 (UTC秒时间)
    uint32_t report_time = get_now_utc_sec(); // 示例时间
    uint32_t net_report_time = htonl(report_time);
    memcpy(buffer + offset, &net_report_time, sizeof(net_report_time));
    offset += sizeof(net_report_time);

    // 包体 - 纬度 (转换为10进制除以1000000得出结果)
    uint64_t latitude = 0; // 示例纬度 39.21
    memcpy(buffer + offset, &latitude, sizeof(latitude));
    offset += sizeof(latitude);

    // 包体 - 经度 (转换为10进制除以1000000得出结果)
    uint64_t longitude = 0; // 示例经度 121.3
    memcpy(buffer + offset, &longitude, sizeof(longitude));
    offset += sizeof(longitude);

    // 包体 - 海拔 (海拔高度m)
    uint16_t altitude = 0; // 示例海拔 200m
    memcpy(buffer + offset, &altitude, sizeof(altitude));
    offset += sizeof(altitude);

    // 包体 - 卫星数量
    uint8_t satellite_count = 0;
    memcpy(buffer + offset, &satellite_count, sizeof(satellite_count));
    offset += sizeof(satellite_count);

    // 包体 - 速度
    uint8_t speed = 0; // 示例速度 60 km/h
    memcpy(buffer + offset, &speed, sizeof(speed));
    offset += sizeof(speed);

    // 包体 - 航向与状态 (位字段)
    uint16_t heading_status = 0; // 示例航向与状态字段
    memcpy(buffer + offset, &heading_status, sizeof(heading_status));
    offset += sizeof(heading_status);

    // 包体 - 模式 (0x00 实时, 0x01 补传)
    uint8_t mode = 0; // 实时模式
    memcpy(buffer + offset, &mode, sizeof(mode));
    offset += sizeof(mode);

    // 包体 - 上报类型 (0x00 定时上传)
    uint8_t report_type = 0; // 定时上传
    memcpy(buffer + offset, &report_type, sizeof(report_type));
    offset += sizeof(report_type);

    // 包体 - 基站数量 (没有就传0)
    uint8_t base_station_count = 1;
    memcpy(buffer + offset, &base_station_count, sizeof(base_station_count));
    offset += sizeof(base_station_count);

    // 附近Wifi数据
    uint8_t wifi_count = 0;
    memcpy(buffer + offset, &wifi_count, sizeof(wifi_count));
    offset += sizeof(wifi_count);

    // 蓝牙数据
    uint8_t bluetooth_count = 0; // 示例 蓝牙 数量
    memcpy(buffer + offset, &bluetooth_count, sizeof(bluetooth_count));
    offset += sizeof(bluetooth_count);

    // 基站数据
    int i;
    for (i = 0; i < base_station_count; ++i) {
        uint16_t mcc = atoi(g_qrzl_device_dynamic_data.mcc); // 国家代号
        uint16_t mnc = atoi(g_qrzl_device_dynamic_data.mcc); // 移动网号码
        uint16_t lac = atoi(g_qrzl_device_dynamic_data.tac); // 位置区码
        uint32_t cell_id = atoi(g_qrzl_device_dynamic_data.cid); // 移动基站 ID
        int8_t signal_strength = atoi(g_qrzl_device_dynamic_data.lte_rsrp); // 信号强度

        uint16_t net_mcc = htons(mcc);
        uint16_t net_mnc = htons(mnc);
        uint16_t net_lac = htons(lac);
        uint32_t net_cell_id = htons(cell_id);


        memcpy(buffer + offset, &net_mcc, sizeof(net_mcc));
        offset += sizeof(net_mcc);
        memcpy(buffer + offset, &net_mnc, sizeof(net_mnc));
        offset += sizeof(net_mnc);
        memcpy(buffer + offset, &net_lac, sizeof(net_lac));
        offset += sizeof(net_lac);
        memcpy(buffer + offset, &net_cell_id, sizeof(net_cell_id));
        offset += sizeof(net_cell_id);
        memcpy(buffer + offset, &signal_strength, sizeof(signal_strength));
        offset += sizeof(signal_strength);
    }


    for (i = 0; i < wifi_count; ++i) {
        uint8_t mac[6] = {0}; // Wi-Fi MAC 地址
        // 使用 sscanf 逐个解析MAC地址的每个字节
        int result = sscanf(g_qrzl_device_static_data.mac, "%2hhx:%2hhx:%2hhx:%2hhx:%2hhx:%2hhx",
            &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]);
        uint8_t ssid_length = strlen(g_qrzl_device_dynamic_data.wifi_ssid); // SSID 长度
        int8_t wifi_signal_strength = 0; // Wi-Fi 信号强度

        memcpy(buffer + offset, mac, sizeof(mac));
        offset += sizeof(mac);
        memcpy(buffer + offset, &ssid_length, sizeof(ssid_length));
        offset += sizeof(ssid_length);
        memcpy(buffer + offset, g_qrzl_device_dynamic_data.wifi_ssid, ssid_length);
        offset += ssid_length;
        memcpy(buffer + offset, &wifi_signal_strength, sizeof(wifi_signal_strength));
        offset += sizeof(wifi_signal_strength);
    }

    uint16_t data_len = 0;

    // 包体 - 上报时间 (UTC秒时间)
    data_len += sizeof(uint32_t);  // report_time: uint32_t

    // 包体 - 纬度 (转换为10进制除以1000000得出结果)
    data_len += sizeof(uint64_t);  // latitude: uint64_t

    // 包体 - 经度 (转换为10进制除以1000000得出结果)
    data_len += sizeof(uint64_t);  // longitude: uint64_t

    // 包体 - 海拔 (海拔高度m)
    data_len += sizeof(uint16_t);  // altitude: uint16_t

    // 包体 - 卫星数量
    data_len += sizeof(uint8_t);   // satellite_count: uint8_t

    // 包体 - 速度
    data_len += sizeof(uint8_t);   // speed: uint8_t

    // 包体 - 航向与状态 (位字段)
    data_len += sizeof(uint16_t);  // heading_status: uint16_t

    // 包体 - 模式 (0x00 实时, 0x01 补传)
    data_len += sizeof(uint8_t);   // mode: uint8_t

    // 包体 - 上报类型 (0x00 定时上传)
    data_len += sizeof(uint8_t);   // report_type: uint8_t

    // 包体 - 基站数量
    data_len += sizeof(uint8_t);   // base_station_count: uint8_t
    // 包体 - wifi数量
    data_len += sizeof(uint8_t);   // uint8_t
    // 包体 - 蓝牙数量
    data_len += sizeof(uint8_t);   // uint8_t

    // 基站数据
    for (i = 0; i < base_station_count; ++i) {
        data_len += sizeof(uint16_t);  // MCC
        data_len += sizeof(uint16_t);  // MNC
        data_len += sizeof(uint16_t);  // LAC
        data_len += sizeof(uint32_t);  // Cell ID
        data_len += sizeof(int8_t);    // 信号强度
    }

    // Wi-Fi数据 (假设有1个Wi-Fi数据)
    for (i = 0; i < wifi_count; ++i) {
        data_len += sizeof(uint8_t) * 6;  // MAC地址 (6 bytes)
        data_len += sizeof(uint8_t);      // SSID长度
        data_len += strlen(g_qrzl_device_dynamic_data.wifi_ssid);                    // 假设SSID是"wifi1" (5 bytes)
        data_len += sizeof(int8_t);       // Wi-Fi 信号强度
    }

    return send_message(sockfd, 0x04, buffer, data_len);
}

static int send_alarm_info(int sockfd, char *alarm_msg, uint16_t alarm_msg_len)
{
    qrzl_log("send_alarm_info");
    update_device_dynamic_data();
    char buffer[4096] = {0};
    size_t offset = 0;

    uint8_t remain_power = atoi(g_qrzl_device_dynamic_data.remain_power); // 电量百分比
    memcpy(buffer + offset, &remain_power, sizeof(remain_power));
    offset += sizeof(remain_power);
    // 包体 - 卫星数量
    uint8_t satellite_num = 0; // 卫星连接数量，这个保持0就好了
    memcpy(buffer + offset, &satellite_num, sizeof(satellite_num));
    offset += sizeof(satellite_num);

    uint8_t singal = get_rsrp_percentage(); // sim卡信号百分比
    memcpy(buffer + offset, &singal, sizeof(singal));
    offset += sizeof(singal);

    uint32_t alarm_time = get_now_utc_sec();
    uint32_t net_alarm_time = htonl(alarm_time);
    memcpy(buffer + offset, &net_alarm_time, sizeof(net_alarm_time));
    offset += sizeof(net_alarm_time);

    if (alarm_msg_len > 0 && alarm_msg != NULL) {
        memcpy(buffer + offset, alarm_msg, alarm_msg_len);
        offset += alarm_msg_len;
    }
    uint16_t data_len = sizeof(uint8_t) + sizeof(uint8_t) + sizeof(uint8_t) + sizeof(uint32_t) + alarm_msg_len;

    return send_message(sockfd, 0x08, buffer, data_len);
}

static int send_device_traffic_statistics(int sockfd)
{
    qrzl_log("send_device_traffic_statistics");
    // 会在发心跳包的时候发流量统计数据，所以不需要再更新一次
    // update_device_dynamic_data();
    char buffer[sizeof(int) * 5 + sizeof(char) * 2] = {0};
    size_t offset = 0;

    static int max_speed = 0; // KB/s
    uint64_t realtime_total_bytes = g_qrzl_device_dynamic_data.realtime_total_bytes;
    
    int flow = (int)(realtime_total_bytes - last_send_total_bytes) / 1024; // 流量 单位KB
    int net_flow = htonl(flow);
    memcpy(buffer + offset, &net_flow, sizeof(net_flow));
    offset += sizeof(net_flow);

    uint64_t speed_bps = g_qrzl_device_dynamic_data.up_speed_bps + g_qrzl_device_dynamic_data.down_speed_bps;
    int average_speed = (int)(speed_bps / 8 / 1000); // 平均网速 单位KB/s
    int net_average_speed = htonl(average_speed);
    memcpy(buffer + offset, &net_average_speed, sizeof(net_average_speed));
    offset += sizeof(net_average_speed);

    if (average_speed > max_speed) {
        max_speed = average_speed;
    }

    int max_net_speed = max_speed; // 最大网速 单位KB/s
    int net_max_net_speed = htonl(max_net_speed);
    memcpy(buffer+offset, &net_max_net_speed, sizeof(net_max_net_speed));
    offset += sizeof(net_max_net_speed);

    uint64_t flux_month_total_bytes = g_qrzl_device_dynamic_data.flux_month_total_bytes;
    int month_total = (int) (flux_month_total_bytes / 1024); //当月总流量 单位KB
    int net_month_total = htonl(month_total);
    memcpy(buffer + offset, &net_month_total, sizeof(net_month_total));
    offset += sizeof(net_month_total);
    
    int limit_speed = (int) (get_down_limit_net_speed() / 8); // 限速单位KB
    int net_limit_speed = htonl(limit_speed);
    memcpy(buffer + offset, &net_limit_speed, sizeof(net_limit_speed));
    offset += sizeof(net_limit_speed);

    char is_disable_net = 1; // 1 停机，0 正常
    if (g_qrzl_device_dynamic_data.user_net_disconn == 1) {
        is_disable_net = 1; // 1 停机，0 正常
    } else {
        is_disable_net = 0;
    }

    memcpy(buffer + offset, &is_disable_net, sizeof(is_disable_net));
    offset += sizeof(is_disable_net);

    char current_sim = 0; // 当前sim卡编号
    int current_sim_index = get_device_current_sim_index();
    if (current_sim_index == 1) {
        current_sim = 1;
    } else if (current_sim_index == 2)
    {
        current_sim = 2;
    } else
    {
        current_sim = 3;
    }

    memcpy(buffer + offset, &current_sim, sizeof(current_sim));
    offset += sizeof(current_sim);
    
    return send_message(sockfd, 0x0d, buffer, sizeof(buffer));
}

static int send_conned_device_info(int sockfd)
{
    qrzl_log("send_conned_device_info");
    char buffer[2048] = {0};
    uint16_t offset = 0;
    char station_mac[512] = {0};

    FILE *fp = NULL;/*lint !e63*/
	typedef struct _DHCPOFFERINFO {
		unsigned long expires;
		unsigned long ip;
		unsigned char mac[6];
		unsigned char host_name[20];
		unsigned char pad[2];
	} DHCPOFFERINFO;
    struct in_addr addr;/*lint !e1080 !e565 */
	DHCPOFFERINFO addrlist;
	int64_t written_at;/*lint !e522*/

    cfg_get_item("station_mac", station_mac, sizeof(station_mac));
    if (strlen(station_mac) == 0)
    {   // 如果连接的设备为空，就不需要再去读取arp文件，并且如果station_mac为空,strstr函数不会返回NULL
        qrzl_log("station_mac is empty");
        return send_message(sockfd, 0x0e, buffer, offset);
    }

	int i = 0;
	memset(&addrlist, 0, sizeof(addrlist));
	fp = fopen("/etc_rw/udhcpd.leases", "r"); /*lint !e63*/

	if (NULL != fp) {
		if (fread(&written_at, 1, sizeof(written_at), fp) == sizeof(written_at)) {

            while (fread(&addrlist, 1, sizeof(addrlist), fp) == sizeof(addrlist)) {
                addr.s_addr = addrlist.ip;	/*lint !e115 !e1013 !e63 */
                printf("ip: %s, %02X:%02X:%02X:%02X:%02X:%02X, hostname: %s\n", inet_ntoa(addr),
                     addrlist.mac[0], addrlist.mac[1], addrlist.mac[2], addrlist.mac[3], addrlist.mac[4], addrlist.mac[5],
                     addrlist.host_name);
                char mac_str[18] = {0};
                snprintf(mac_str, sizeof(mac_str), "%02X:%02X:%02X:%02X:%02X:%02X", addrlist.mac[0], addrlist.mac[1], addrlist.mac[2], addrlist.mac[3], addrlist.mac[4], addrlist.mac[5]);
                if (strcasestr(station_mac, mac_str)) {
                    // 这里才说明是wifi连接的设备
                    uint16_t hostname_len = strlen(addrlist.host_name);
                    uint16_t net_hostname_len = htons(hostname_len);
                    memcpy(buffer + offset, &net_hostname_len, sizeof(net_hostname_len));
                    offset += sizeof(net_hostname_len);
                    memcpy(buffer + offset, addrlist.host_name, hostname_len);
                    offset += hostname_len;
                    memcpy(buffer + offset, addrlist.mac, sizeof(addrlist.mac));
                    offset += sizeof(addrlist.mac);
                    memcpy(buffer + offset, &addr.s_addr, sizeof(addr.s_addr));
                    offset += sizeof(addr.s_addr);
                }
            }

        }
        fclose(fp);
	}
	
    return send_message(sockfd, 0x0e, buffer, offset);
}

// 将MAC地址字符串转换为6字节的二进制格式
static void parse_mac_address(const char *mac_str, char *mac_bin) {
    sscanf(mac_str, "%hhx:%hhx:%hhx:%hhx:%hhx:%hhx",
           &mac_bin[0], &mac_bin[1], &mac_bin[2],
           &mac_bin[3], &mac_bin[4], &mac_bin[5]);
}

static int send_black_white_list(int sockfd)
{
    qrzl_log("send_black_white_list");
    char buffer[256] = {0};
    uint16_t offset = 0;
    char mac_tmp[6] = {0};

    char mode = (char) g_qrzl_device_dynamic_data.wifi_filter_type;
    if (mode < 0 || mode > 2) {
        mode = 0;
    }
    memcpy(buffer + offset, &mode, sizeof(mode));
    offset += sizeof(mode);

    char mac_black_list[180] = {0};
    strcpy(mac_black_list, g_qrzl_device_dynamic_data.mac_black_list);
    char black_flag = 1;
    if (strlen(mac_black_list) > 0) {
        char *token = strtok(mac_black_list, ";");
        while (token) {
            memset(mac_tmp, 0, sizeof(mac_tmp));
            parse_mac_address(token, mac_tmp);
            memcpy(buffer + offset, mac_tmp, sizeof(mac_tmp));
            offset += sizeof(mac_tmp);
            memcpy(buffer + offset, &black_flag, sizeof(black_flag));
            offset += sizeof(black_flag);

            token = strtok(NULL, ";");
        }
    }

    char mac_white_list[180] = {0};
    strcpy(mac_white_list, g_qrzl_device_dynamic_data.mac_white_list);
    char white_flag = 1;
    if (strlen(mac_white_list) > 0) {
        char *token = strtok(mac_white_list, ";");
        while (token) {
            memset(mac_tmp, 0, sizeof(mac_tmp));
            parse_mac_address(token, mac_tmp);
            memcpy(buffer + offset, mac_tmp, sizeof(mac_tmp));
            offset += sizeof(mac_tmp);
            memcpy(buffer + offset, &white_flag, sizeof(white_flag));
            offset += sizeof(white_flag);

            token = strtok(NULL, ";");
        }
    }

    return send_message(sockfd, 0x0f, buffer, offset);
}

static void init_xlx_socket()
{
    qrzl_log("init_xlx_socket");
    int socketfd;
    
    while (1)
    {
        while (1)
        {
            socketfd = create_socket(XLX_SERVER_HOSTNAME, XLX_SERVER_PORT);
            qrzl_log("socketfd: %d\n", socketfd);
            if (socketfd < 0) {
                sleep(30);
            } else {
                xlx_sockfd = socketfd;
                break;
            }
        }

        size_t i;
        for (i = 0; i < 2; i++)
        {
            if (send_login_server(socketfd) == 1) {
                sleep(5);
                if (xlx_is_login == 1) {
                    return;
                }
            } else {
                close_and_reset_xlx_socket();
                break;
            }
        }
        close_and_reset_xlx_socket();
    }

}

/**
 * 心跳检测，自动重连
 */
static void* heartbeat_check_auto_reconn_thread()
{
    while (1)
    {
        if (xlx_sockfd == -1) {
            init_xlx_socket();
        } else {
            if (xlx_is_login == 0) {
                sleep(5);
                continue;
            }
            if (send_heartbeat(xlx_sockfd) < 1) {
                close_and_reset_xlx_socket();
                continue;
            } else {
                send_device_traffic_statistics(xlx_sockfd);
                heartbeat_pack_loss_num++;
            }
            if (heartbeat_pack_loss_num >= 2) {
                close_and_reset_xlx_socket();
            }
            sleep(300);
        }
    }
    return NULL;
}

// 接收数据包的函数（改进版）
static int receive_data(int socket, xlx_data_packet_t *packet) {
    uint8_t buffer[XLX_MSG_MAX_LENGTH];
    ssize_t len;
    size_t total_received = 0;
    size_t packet_header_size = sizeof(uint8_t) * 2 + sizeof(uint16_t) * 3;  // 包头大小
    size_t body_size = 0;

    // 接收数据并构建数据包，直到接收到完整的包体
    while (1) {
        // 每次调用recv()接收数据
        if (total_received >= packet_header_size) {
            len = recv(socket, buffer + total_received, (packet->body_length + packet_header_size) - total_received, 0);
            qrzl_log("recv len = %ld\n", len);
            if (len < 0) {
                perror("Receive failed");
                return -2;
            }
        } else {
            len = recv(socket, buffer + total_received, packet_header_size - total_received, 0);
            qrzl_log("recv len = %ld\n", len);
            if (len < 0) {
                perror("Receive failed");
                return -2;
            }
        }        
        
        total_received += len;

        print_byte_array(buffer, total_received);

        // 数据包头部已接收到足够的字节（至少包括起始位、命令号、校验码、序列号和包体长度）
        if (total_received >= packet_header_size) {
            // 解析包头
            packet->start_flag = buffer[0];
            packet->cmd = buffer[1];
            packet->checksum = (uint16_t)(buffer[2] << 8 | buffer[3]);
            packet->serial_number = (uint16_t)(buffer[4] << 8 | buffer[5]);
            packet->body_length = (uint16_t)(buffer[6] << 8 | buffer[7]);

            // 校验包体长度是否合适
            if (packet->body_length + packet_header_size > XLX_MSG_MAX_LENGTH) {
                qrzl_log("Received data exceeds buffer size\n");
                return -1;
            }

            // 如果包体长度已经包含在接收到的数据中，直接解析包体
            if (total_received >= packet_header_size + packet->body_length) {

                packet->body = (char *)malloc(packet->body_length);
                if (!packet->body) {
                    perror("Memory allocation failed");
                    return -1;
                }
                memcpy(packet->body, buffer + packet_header_size, packet->body_length);

                // 校验码验证
                uint16_t calculated_checksum = get_crc16(buffer + 4, total_received - 4);
                if (packet->checksum != calculated_checksum) {
                    qrzl_log("Checksum mismatch!\n");
                    free(packet->body);
                    return -1;
                }
                qrzl_log("reve one packet");
                // 数据包接收完毕
                return 0;
            }
        }

        // 如果数据包头部不完整，则继续接收
        if (total_received < packet_header_size) {
            qrzl_log("total_received < packet_header_size");
            continue;
        }

        // 如果包体还未接收完整，继续接收剩余部分
        if (total_received < packet_header_size + packet->body_length) {
            qrzl_log("total_received < packet_header_size + packet->body_length");
            continue;
        }

    }

    return 0;
}


static int login_resp_handler(xlx_data_packet_t *packet)
{
    // 登录响应结构如下所示
    uint8_t status = 0;
    uint32_t server_time = 0;
    uint32_t server_ip = 0;
    uint16_t server_port = 0;

    char *now_data = packet->body;
    memcpy(&status, now_data, sizeof(status));
    now_data += sizeof(status);
    memcpy(&server_time, now_data, sizeof(server_time));
    now_data += sizeof(server_time);
    memcpy(&server_ip, now_data, sizeof(server_ip));
    now_data += sizeof(server_ip);
    memcpy(&server_port, now_data, sizeof(server_port));

    server_time = ntohl(server_time);
    server_ip = ntohl(server_ip);
    server_port = ntohs(server_port);

    qrzl_log("status: 0x%02x, servertime: %d, serverip: %d, server_port: %d\n", status, server_time, server_ip, server_port);

    if (status == 0) {
        xlx_is_login = 1;
        send_wifi_and_cell_info(xlx_sockfd);
        send_conned_device_info(xlx_sockfd);
        send_black_white_list(xlx_sockfd);
    }

    return 0;
}

static int heartbeat_resp_handler(xlx_data_packet_t *packet)
{
    uint8_t status = 0;
    memcpy(&status, packet->body, sizeof(status));
    qrzl_log("heartbeat status = 0x%02x\n", status);
    if (status == 0) {
        heartbeat_pack_loss_num = 0;
    }
    return 0;
}

static int server_cmd_handler(xlx_data_packet_t *packet)
{
    update_device_dynamic_data();

    uint16_t cmd_id = 0;
    char cmd_content[1024] = {0};
    memcpy(&cmd_id, packet->body, sizeof(cmd_id));
    // size_t len = strcspn(packet->body, "\n");
    memcpy(cmd_content, packet->body + sizeof(cmd_id), packet->body_length - sizeof(cmd_id));
    qrzl_log("cmd_id: %d, cmd_content: %s", cmd_id, cmd_content);

    char resp_content[1024] = {0};
    char resp[1024+sizeof(uint16_t)] = {0};
    size_t offset = 0;
    memcpy(resp, &cmd_id, sizeof(cmd_id));
    offset += sizeof(cmd_id);

    if (strncmp("Version", cmd_content, 7) == 0) {
        snprintf(resp_content, sizeof(resp_content), "Ok,Version %s\n", g_qrzl_device_static_data.soft_version);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("Restart", cmd_content, 7) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,Restart ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        restart_device();

    } else if (strncmp("Reset", cmd_content, 5) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,Reset ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        reset_device();

    } else if (strncmp("SetServer", cmd_content, 9) == 0) // 这个有点麻烦，先放着
    {
        char ip[16];
        snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        // 使用 sscanf 提取 IP
        if (sscanf(cmd_content, "SetServer,%15[^,]", ip) == 1) {
            if (valid_ipv4(ip) == 1) {

            }
        }

    } else if (strncmp("GetServer", cmd_content, 9) == 0)
    {
        char lan_ipaddr[16] = {0};
        cfg_get_item("lan_ipaddr", lan_ipaddr, sizeof(lan_ipaddr));
        snprintf(resp_content, sizeof(resp_content), "Ok,%s 80\n", lan_ipaddr);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("Ping", cmd_content, 4) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("SetWifi", cmd_content, 7) == 0)
    {
        char ssid[64] = {0};
        char wifi_key[64] = {0};
        // 解析 "SetWifi,name,password\n"
        if (sscanf(cmd_content, "SetWifi,%63[^,],%63[^,\n]", ssid, wifi_key) == 2) {
            qrzl_log("ssid: %s, wifi_key: %s\n", ssid, wifi_key);
            struct wifi_config_t wifi_config = {};
            init_wifi_config_value(&wifi_config);
            snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", ssid);
            snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", wifi_key);
            update_wifi_by_config(&wifi_config);
        } else {
            qrzl_err("Failed to parse!\n");
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }
        snprintf(resp_content, sizeof(resp_content), "Ok,SetWifi ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("getWifi", cmd_content, 7) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,%s,%s\n", g_qrzl_device_dynamic_data.wifi_ssid, g_qrzl_device_dynamic_data.wifi_key);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("setNetSpeed2", cmd_content, 12) == 0)
    {
        qrzl_log("setNetSpeed2 handler");
        uint64_t downspeed, upspeed;
        // 使用 %llu 读取 uint64_t 类型的整数
        if (sscanf(cmd_content, "setNetSpeed2,%lu,%lu", &downspeed, &upspeed) == 2) {
            snprintf(resp_content, sizeof(resp_content), "Ok,SetNetSpeed ok\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
            limit_net_speed(upspeed*8, downspeed*8);
        } else {
            qrzl_err("Failed to parse speeds!\n");
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }


    } else if (strncmp("setNetSpeed", cmd_content, 11) == 0)
    {
        qrzl_log("setNetSpeed handler");
        uint64_t speed;
        // 使用 %llu 读取 uint64_t 类型的整数, 客户这里的单位是KB
        if (sscanf(cmd_content, "setNetSpeed,%lu", &speed) == 1) {
            snprintf(resp_content, sizeof(resp_content), "Ok,SetNetSpeed ok\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
            limit_net_speed(speed * 8, speed * 8);
        } else {
            qrzl_err("Failed to parse speed!\n");
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }

    } else if (strncmp("getDeviceList", cmd_content, 13) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,getDeviceList ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        send_conned_device_info(xlx_sockfd);

    } else if (strncmp("setAccessMode", cmd_content, 13) == 0)
    {
        int mode;
        if (sscanf(cmd_content, "setAccessMode,%d", &mode) == 1) {
            struct mac_filter_config_t mac_filter_config = {};
            init_mac_filter_config_value(&mac_filter_config);
            mac_filter_config.wifi_filter_type = mode;
            update_mac_filter_by_config(&mac_filter_config);
            snprintf(resp_content, sizeof(resp_content), "Ok,setAccessMode ok\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        } else {
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }

    } else if (strncmp("setDeviceWhite", cmd_content, 14) == 0)
    {
        struct mac_filter_config_t mac_filter_config = {};
        init_mac_filter_config_value(&mac_filter_config);
        char white_list_tmp[180] = {0};
        memcpy(white_list_tmp, mac_filter_config.mac_white_list, sizeof(white_list_tmp));
        char tmp[1024] = {0};
        int op = -1;
        strlcpy(tmp, cmd_content, sizeof(tmp));
        // 使用 strtok 分割
        char *token = strtok(tmp, ",");  // 获取 "setDeviceWhite"
        token = strtok(NULL, ",");  // 获取 op
        if (token) {
            op = atoi(token);  // 转换为整数
        }
        while (token)
        {
            token = strtok(NULL, ",");  // 获取 macA
            if (token) {
                if (op == 1) {
                    snprintf(white_list_tmp, sizeof(white_list_tmp), "%s;%s", white_list_tmp, token);
                } else if (op == 0)
                {
                    remove_mac(white_list_tmp, token);
                }
            }
        }
        snprintf(mac_filter_config.mac_white_list, sizeof(mac_filter_config.mac_white_list), "%s", white_list_tmp);
        update_mac_filter_by_config(&mac_filter_config);
        snprintf(resp_content, sizeof(resp_content), "Ok,setDeviceWhite ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("setDeviceBlack", cmd_content, 14) == 0)
    {
        struct mac_filter_config_t mac_filter_config = {};
        init_mac_filter_config_value(&mac_filter_config);
        char black_list_tmp[180] = {0};
        memcpy(black_list_tmp, mac_filter_config.mac_black_list, sizeof(black_list_tmp));
        char tmp[1024] = {0};
        int op = -1;
        strlcpy(tmp, cmd_content, sizeof(tmp));
        // 使用 strtok 分割
        char *token = strtok(tmp, ",");  // 获取 "setDeviceBlack"
        token = strtok(NULL, ",");  // 获取 op
        if (token) {
            op = atoi(token);  // 转换为整数
        }
        while (token)
        {
            token = strtok(NULL, ",");  // 获取 macA
            if (token) {
                if (op == 1) {
                    snprintf(black_list_tmp, sizeof(black_list_tmp), "%s;%s", black_list_tmp, token);
                } else if (op == 0)
                {
                    remove_mac(black_list_tmp, token);
                }
            }
        }
        snprintf(mac_filter_config.mac_black_list, sizeof(mac_filter_config.mac_black_list), "%s", black_list_tmp);
        update_mac_filter_by_config(&mac_filter_config);
        snprintf(resp_content, sizeof(resp_content), "Ok,setDeviceBlack ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);

    } else if (strncmp("getDeviceWB", cmd_content, 11) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,getDeviceWB ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        send_black_white_list(xlx_sockfd);

    } else if (strncmp("SetApn", cmd_content, 6) == 0)
    {
        snprintf(resp_content, sizeof(resp_content), "Ok,SetApn ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("DeviceInfo", cmd_content, 10) == 0)
    {
        /**
         * Ok,DeviceInfo 版本信息,
            设备号,
            硬件信息,
            固件版本号,
            热点连接台数,
            设备运行时间,
            WIFI名称,
            联网状态,
            gsm信号量,
            当前启用卡槽,
            当前使用卡iccid,
            网络限速设置,
            当月流量使用\n
         */
        int index = get_device_current_sim_index();
        snprintf(resp_content, sizeof(resp_content), "Ok,DeviceInfo %s,%s,%s,%s,%d,%u,%s,%s,%s,%d,%s,%lu,%lu\n",
            g_qrzl_device_static_data.soft_version, g_qrzl_device_static_data.sn, g_qrzl_device_static_data.hw_version, g_qrzl_device_static_data.soft_version,
            g_qrzl_device_dynamic_data.conn_num, get_now_utc_sec(), g_qrzl_device_dynamic_data.wifi_ssid, g_qrzl_device_dynamic_data.user_net_disconn == 0 ? "ENABLE" : "DISABLE",
            g_qrzl_device_dynamic_data.lte_rsrp, index == 0 ? 3 : index, g_qrzl_device_dynamic_data.iccid, get_down_limit_net_speed(), g_qrzl_device_dynamic_data.flux_month_total_bytes / 1024);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("ConfigGet", cmd_content, 9) == 0)
    {
        /**
         * 返回设备当前配置的所有参数:
            黑白名单，
            网络访问模式，
            网络开关，
            网络名称，
            IMEI
            blackMacs:48:04:2D:27:1B:58,whiteMacs:48:04:2D:27:1B:58,0,OPEN,WIFI-0000,998766555
         */
        snprintf(resp_content, sizeof(resp_content), "blackMacs:%s,whiteMacs:%s,%d,%s,%s,%s\n",
            g_qrzl_device_dynamic_data.mac_black_list, g_qrzl_device_dynamic_data.mac_white_list, g_qrzl_device_dynamic_data.wifi_filter_type,
            g_qrzl_device_dynamic_data.user_net_disconn == 0 ? "OPEN" : "CLOSE", g_qrzl_device_dynamic_data.wifi_ssid, g_qrzl_device_static_data.imei);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("SetSim", cmd_content, 6) == 0)
    {
        int no;
        if (sscanf(cmd_content, "SetSim,%d", &no) == 1) {
            snprintf(resp_content, sizeof(resp_content), "Ok,SetSim ok\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
            if (no == 3) {
                switch_sim_card_not_restart(0);
            } else if (no > 0){
                switch_sim_card_not_restart(no);
            }
            close_and_reset_xlx_socket(); // 切卡会断网，直接手动断开连接
        } else {
            qrzl_err("Failed to parse no!\n");
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }
    } else if (strncmp("GetSim", cmd_content, 6) == 0)
    {
        int index = get_device_current_sim_index();
        snprintf(resp_content, sizeof(resp_content), "Ok,sim,%d\n", index == 0 ? 3 : index);
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("extSim", cmd_content, 6) == 0)
    {
        // 这个是设置是否可以切外卡，现在只做了三卡的，所以不做处理
        snprintf(resp_content, sizeof(resp_content), "Ok,extSim ok\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    } else if (strncmp("ENABLEDATAFD", cmd_content, 6) == 0)
    {
        char status[16] = {0};
        if (sscanf(cmd_content, "ENABLEDATAFD,%15s", status) == 1) {
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
            send_black_white_list(xlx_sockfd);

            if (strncmp("OPEN", status, 4) == 0) {
                set_network_br0_disconnect(0);
                snprintf(resp_content, sizeof(resp_content), "Ok,ENABLEDATAFD OPEN\n");
            } else if (strncmp("CLOSE", status, 5) == 0)
            {
                set_network_br0_disconnect(1);
                snprintf(resp_content, sizeof(resp_content), "Ok,ENABLEDATAFD CLOSE\n");
            }
        } else {
            snprintf(resp_content, sizeof(resp_content), "Fail,error command\n");
            memcpy(resp + offset, resp_content, strlen(resp_content));
            send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
        }

    } else {
        qrzl_log("未知命令，或暂不支持");
        snprintf(resp_content, sizeof(resp_content), "Fail,Unknown command\n");
        memcpy(resp + offset, resp_content, strlen(resp_content));
        send_message_core(xlx_sockfd, 0x09, resp, strlen(resp_content) + sizeof(uint16_t), packet->serial_number);
    }
    
    return 0;
}

static int data_handler(xlx_data_packet_t *packet)
{
    qrzl_log("cmd = %02X\n", packet->cmd);
    if (packet->cmd == 0x01) {
        return login_resp_handler(packet);
    } else if (packet->cmd == 0x02)
    {
        return heartbeat_resp_handler(packet);
    } else if (packet->cmd == 0x04)
    {
        uint8_t status = 0;
        memcpy(&status, packet->body, sizeof(status));
        qrzl_log("wifi cell info resp: 0x%02x", status);
    } else if (packet->cmd == 0x08)
    {
        uint8_t status = 0;
        memcpy(&status, packet->body, sizeof(status));
        qrzl_log("alarm info resp: 0x%02x", status);
    } else if (packet->cmd == 0x09)
    {
        return server_cmd_handler(packet);
    } else if (packet->cmd == 0x0d)
    {
        uint8_t status = 0;
        memcpy(&status, packet->body, sizeof(status));
        qrzl_log("0x0d status: 0x%02x", status);
        if (status == 0) {
            last_send_total_bytes = g_qrzl_device_dynamic_data.realtime_total_bytes;
            qrzl_log("update last_send_total_bytes: %lu", last_send_total_bytes);
        }
    } else if (packet->cmd == 0x0e)
    {
        uint8_t status = 0;
        memcpy(&status, packet->body, sizeof(status));
        qrzl_log("0x0e status: 0x%02x", status);
    } else if (packet->cmd == 0x0f)
    {
        uint8_t status = 0;
        memcpy(&status, packet->body, sizeof(status));
        qrzl_log("0x0f status: 0x%02x", status);
    } else {
        qrzl_log("unknown cmd: 0x%02x", packet->cmd);
    }
    
    return 0;
}


static void* receive_socket_data_handler_thread()
{
    xlx_data_packet_t datapacket = {};
    int ret;
    while (1)
    {
        if (xlx_sockfd != -1) {
            qrzl_log("start receive_data");
            memset(&datapacket, 0, sizeof(datapacket));
            ret = receive_data(xlx_sockfd, &datapacket);
            if (ret == 0) {
                data_handler(&datapacket);
            } else if (ret == -2)
            {
                close_and_reset_xlx_socket();
            }
            qrzl_log("start free_packet");
            xlx_free_packet(&datapacket);
        }
        sleep(1);
    }
    return NULL;
}

/**
 * 设备状态监听线程，如果设备有些状态发生变化了，就上报服务端
 */
static void* device_status_listeners_thread()
{
    // wifi连接
    char old_station_mac[512] = {0};
    cfg_get_item("station_mac", old_station_mac, sizeof(old_station_mac));
    char new_station_mac[512] = {0};

    char old_wifi_filter_type[2] = {0};
    char old_mac_black_list[180] = {0};
    char old_mac_white_list[180] = {0};

    // mac黑白名单
    cfg_get_item("ACL_mode", old_wifi_filter_type, sizeof(old_wifi_filter_type));
    cfg_get_item("wifi_mac_black_list", old_mac_black_list, sizeof(old_mac_black_list));
    cfg_get_item("wifi_mac_white_list", old_mac_white_list, sizeof(old_mac_white_list));


    char new_wifi_filter_type[2] = {0};
    char new_mac_black_list[180] = {0};
    char new_mac_white_list[180] = {0};
    
    // wifi配置以及基站
    char old_cell_id[16] = {0};
    char old_tac[17] = {0};
    
    cfg_get_item("cell_id", old_cell_id, sizeof(old_cell_id));
    cfg_get_item("tac_code", old_tac, sizeof(old_tac));

    char new_cell_id[16] = {0};
    char new_tac[17] = {0};

    while (1)
    {
        cfg_get_item("station_mac", new_station_mac, sizeof(new_station_mac));
        if (strncmp(old_station_mac, new_station_mac, sizeof(old_station_mac)) != 0 && xlx_is_login == 1) {
            qrzl_log("station_mac changed");
            update_device_dynamic_data();
            send_conned_device_info(xlx_sockfd);
            cfg_get_item("station_mac", old_station_mac, sizeof(old_station_mac));
        }

        cfg_get_item("ACL_mode", new_wifi_filter_type, sizeof(new_wifi_filter_type));
        cfg_get_item("wifi_mac_black_list", new_mac_black_list, sizeof(new_mac_black_list));
        cfg_get_item("wifi_mac_white_list", new_mac_white_list, sizeof(new_mac_white_list));

        if (strncmp(old_wifi_filter_type, new_wifi_filter_type, sizeof(old_wifi_filter_type)) != 0 ||
            strncmp(old_mac_black_list, new_mac_black_list, sizeof(old_mac_black_list)) != 0 || 
            strncmp(old_mac_white_list, new_mac_white_list, sizeof(old_mac_white_list)) != 0) {
            if (xlx_is_login == 1) {
                qrzl_log("wifi filter changed");
                update_device_dynamic_data();
                send_black_white_list(xlx_sockfd);
                cfg_get_item("ACL_mode", old_wifi_filter_type, sizeof(old_wifi_filter_type));
                cfg_get_item("wifi_mac_black_list", old_mac_black_list, sizeof(old_mac_black_list));
                cfg_get_item("wifi_mac_white_list", old_mac_white_list, sizeof(old_mac_white_list));
            }
        }

        cfg_get_item("cell_id", new_cell_id, sizeof(new_cell_id));
        cfg_get_item("tac_code", new_tac, sizeof(new_tac));
        if (strncmp(old_cell_id, new_cell_id, sizeof(old_cell_id)) != 0 ||
        strncmp(old_tac, new_tac, sizeof(new_tac)) != 0) {
            if (xlx_is_login == 1) {
                qrzl_log("cell_id or tac_code changed");
                update_device_dynamic_data();
                send_wifi_and_cell_info(xlx_sockfd);
                cfg_get_item("cell_id", old_cell_id, sizeof(old_cell_id));
                cfg_get_item("tac_code", old_tac, sizeof(old_tac));
            }
            
        }

        sleep(10);
    }
    
    return NULL;
}

void* xlx_control_start()
{
    int err;

    if (pthread_mutex_init(&send_message_lock, NULL) != 0) {
        qrzl_log("send_message_lock init failed\n");
    }

    update_device_static_data();
    update_device_dynamic_data();

    pthread_t heartbeat_check_auto_reconn_tid;
    err = pthread_create(&heartbeat_check_auto_reconn_tid, NULL, heartbeat_check_auto_reconn_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 heartbeat_check_auto_reconn 线程失败, error code: %d", err);
    }
    pthread_t receive_socket_data_handler_thread_tid;
    err = pthread_create(&receive_socket_data_handler_thread_tid, NULL, receive_socket_data_handler_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 receive_socket_data_handler_thread 线程失败, error code: %d", err);
    }

    pthread_t device_status_listeners_tid;
    err = pthread_create(&device_status_listeners_tid, NULL, device_status_listeners_thread, NULL);
    if (err != 0) {
        qrzl_log("创建 device_status_listeners_thread 线程失败, error code: %d", err);
    }
    
    pthread_join(heartbeat_check_auto_reconn_tid, NULL);
    pthread_join(receive_socket_data_handler_thread_tid, NULL);
    return NULL;
}

