#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>

#include "cshttp_control.h"
#include "../json.h"
#include "../qrzl_utils.h"
#include "curl/curl.h"
#include "softap_api.h"
#include "nv_api.h"
#include "MQTTClient.h"
//fota头文件
#include "../../fota_upi/inc/upi_cmd.h"
#include "../../include/fota_common.h"
#include "../fota/fota_utils.h"


/* 请求间隔时间 */
static int request_interval_time = 300;
/* http请求的路径 */
static char http_request_path[256] = {0};
/* 创三 切卡结果上报http请求的路径 */
static char http_request_cs_result_path[256] = {0};

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

/*fota下载标志位*/
static int fota_is_downloader=0;

#define QRZL_CS_HTTP_RESPONSE_MAX 5120

static uint64_t cs_next_flow = 0; // MB 到达此流量时，马上上报数据


#define QRZL_CS_MAX_NET_LIMIT 20  // 允许存储的策略数量
#define QRZL_HEX_LEN 16     // uint64_t 占 16 个十六进制字符

static char cs_day_netlimit_arr_str[1024] = {0};
static char cs_month_netlimit_arr_str[1024] = {0};

typedef struct {
    uint64_t limitValue; // 限速单位 Kbps
    uint64_t triggerValue; // 到达多少流量 单位MB
} NetLimit;

// cs切卡线程传参结构体
typedef struct {
    int cmdId;
    int num;
} csSwitchCard;

typedef struct{
    int cmdId;
    char address[1024];
    uint64_t size;
    char version[1024];
    char md5[128];
}csOta;

static NetLimit cs_day_netlimit_arr[QRZL_CS_MAX_NET_LIMIT] = {};
static NetLimit cs_month_netlimit_arr[QRZL_CS_MAX_NET_LIMIT] = {};

static NetLimit cs_day_netlimit_now = {0, 0};
static NetLimit cs_month_netlimit_now = {0, 0};

//声明fota升级线程函数
void *cs_fota_update(void * args);

/**
 * @brief  将结构体数组转换为十六进制字符串
 */
static void nv_netlimit_serialize(NetLimit *limits, int count, char *output) {
    int i;
    for (i = 0; i < count; i++) {
        sprintf(output + i * 32, "%016llx%016llx", 
                (unsigned long long)limits[i].limitValue, 
                (unsigned long long)limits[i].triggerValue);
    }
    output[count * 32] = '\0'; // 结束符
}

/**
 * @brief  将十六进制字符串解析回结构体数组
 */
static int nv_netlimit_deserialize(const char *input, NetLimit *limits, int max_count) {
    int count = strlen(input) / 32;
    if (count > max_count) count = max_count;
    
    int i;
    for (i = 0; i < count; i++) {
        sscanf(input + i * 32, "%016llx%016llx", 
               (unsigned long long *)&limits[i].limitValue, 
               (unsigned long long *)&limits[i].triggerValue);
    }
    return count; // 返回解析出的策略数
}



/**
 * 初始化一些配置信息 
 * */
static int init_config_data() 
{
    int cfg_ret, cfg_ret2;
    cfg_ret = cfg_get_item(NV_QRZL_CLOUD_HTTP_PATH, http_request_path, 256);
    if (cfg_ret != 0 || http_request_path == NULL || strcmp(http_request_path, "") == 0)
    {
        qrzl_log("http_request_path is NULL");
        return -1;
    }
    cfg_ret2 = cfg_get_item("qrzl_cloud_http_cs_result_path", http_request_cs_result_path, 256);
    if (cfg_ret2 != 0 || http_request_cs_result_path == NULL || strcmp(http_request_cs_result_path, "") == 0)
    {
        qrzl_log("http_request_cs_result_path is NULL");
        return -1;
    }
    
    char cloud_request_interval_time[10] = {0};
    cfg_get_item(NV_QRZL_CLOUD_REQUEST_INTERVAL_TIME, cloud_request_interval_time, 10);
    request_interval_time = atoi(cloud_request_interval_time);
    if (request_interval_time == 0) {
        request_interval_time = 300;
    }

    

    return 0;
}

static void init_cs_nv()
{
    int cfg_ret = 0;
    cfg_ret = cfg_get_item("cs_day_netlimit_arr", cs_day_netlimit_arr_str, sizeof(cs_day_netlimit_arr_str));
    if (cfg_ret != 0 || strlen(cs_day_netlimit_arr_str) == 0) {
        nv_netlimit_serialize(cs_day_netlimit_arr, QRZL_CS_MAX_NET_LIMIT, cs_day_netlimit_arr_str);
        cfg_set("cs_day_netlimit_arr", cs_day_netlimit_arr_str);
    } else {
        nv_netlimit_deserialize(cs_day_netlimit_arr_str, cs_day_netlimit_arr, QRZL_CS_MAX_NET_LIMIT);
    }

    cfg_ret = cfg_get_item("cs_month_netlimit_arr", cs_month_netlimit_arr_str, sizeof(cs_month_netlimit_arr_str));
    if (cfg_ret != 0 || strlen(cs_month_netlimit_arr_str) == 0) {
        nv_netlimit_serialize(cs_month_netlimit_arr, QRZL_CS_MAX_NET_LIMIT, cs_month_netlimit_arr_str);
        cfg_set("cs_month_netlimit_arr", cs_month_netlimit_arr_str);
    } else {
        nv_netlimit_deserialize(cs_month_netlimit_arr_str, cs_month_netlimit_arr, QRZL_CS_MAX_NET_LIMIT);
    }

}

static void update_cs_nv()
{
    nv_netlimit_serialize(cs_day_netlimit_arr, QRZL_CS_MAX_NET_LIMIT, cs_day_netlimit_arr_str);
    cfg_set("cs_day_netlimit_arr", cs_day_netlimit_arr_str);
    nv_netlimit_serialize(cs_month_netlimit_arr, QRZL_CS_MAX_NET_LIMIT, cs_month_netlimit_arr_str);
    cfg_set("cs_month_netlimit_arr", cs_month_netlimit_arr_str);
}


// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    if (totalSize >= QRZL_CS_HTTP_RESPONSE_MAX)
    {
        qrzl_err("http返回值大小: %d,大于已定义的最大长度", totalSize);
        return 0;
    }
    strncat((char *)userp, (char *)contents, totalSize);
    return totalSize;
}


static int https_send_post_request(const char *url, const char *body, char *response)
{
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        // 设置POST请求的内容
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

        // 设置HTTP头（告诉服务器这是JSON数据）
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        // 设置 Basic 认证的用户名和密码
        curl_easy_setopt(curl, CURLOPT_USERPWD, "qrui:Qr@2025");

        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 60L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 60L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_err("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        // 清理
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        if (res != CURLE_OK)
        {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 回调函数，用于写入下载的数据到文件
static size_t write_data_to_file(void *ptr, size_t size, size_t nmemb, FILE *stream) {
    size_t written = fwrite(ptr, size, nmemb, stream);
    return written;
}

/**
 * 使用GET方法下载文件
 * @param url 下载链接
 * @param filepath 保存文件路径
 * @return 0表示成功，非0表示失败
 */
static int http_download_file(const char *url, const char *filepath) {
    CURL *curl_handle = NULL;
    CURLcode res;
    FILE *file = NULL;
    int result = 0;
    
    // 初始化curl
    curl_handle = curl_easy_init();
    if (!curl_handle) {
        fprintf(stderr, "Error: curl_easy_init failed\n");
        return -1;
    }
    
    // 打开文件用于写入
    file = fopen(filepath, "wb");
    if (!file) {
        fprintf(stderr, "Error: cannot open file %s for writing\n", filepath);
        curl_easy_cleanup(curl_handle);
        return -1;
    }
    
    // 设置URL
    curl_easy_setopt(curl_handle, CURLOPT_URL, url);
    
    // 设置回调函数和文件指针
    curl_easy_setopt(curl_handle, CURLOPT_WRITEFUNCTION, write_data_to_file);
    curl_easy_setopt(curl_handle, CURLOPT_WRITEDATA, file);
    
    // 跳过SSL验证（仅用于测试环境）
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYHOST, 0L);
    
    // 设置超时时间
    curl_easy_setopt(curl_handle, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl_handle, CURLOPT_CONNECTTIMEOUT, 10L);
    
    // 设置User-Agent
    curl_easy_setopt(curl_handle, CURLOPT_USERAGENT, "Mozilla/5.0");
    
    // 执行请求
    res = curl_easy_perform(curl_handle);
    if (res != CURLE_OK) {
        fprintf(stderr, "Error: curl_easy_perform() failed: %s\n", curl_easy_strerror(res));
        result = -1;
    }
    
    // 获取HTTP响应码
    long response_code;
    curl_easy_getinfo(curl_handle, CURLINFO_RESPONSE_CODE, &response_code);
    if (response_code != 200 && response_code != 206) {
        fprintf(stderr, "Error: HTTP response code: %ld\n", response_code);
        result = -1;
    }
    
    // 清理资源
    fclose(file);
    curl_easy_cleanup(curl_handle);
    
    return result;
}

static void fota_result_report(void){
    char fota_result[512]={0};
    char bool_strting[10]={0};
    char fota_report_flag_char[10]={0};
    char cr_version[512]={0};
	cfg_get_item("cr_version",cr_version,sizeof(cr_version));
    cfg_get_item("fota_report_flag",fota_report_flag_char,sizeof(fota_report_flag_char));
    cfg_get_item("fota_upgrade_result",bool_strting,sizeof(bool_strting));
    if(strlen(bool_strting) == 0){
        return;
    }
    if(strcmp(fota_report_flag_char,"0") == 0){
        if(strcmp("success",bool_strting) == 0){
            snprintf(fota_result,sizeof(fota_result),"fota升级成功,当前版本号：%s",cr_version);
            snprintf(bool_strting,sizeof(bool_strting),"true");
        }
        else if(strcmp("fail",bool_strting) == 0){
            snprintf(fota_result,sizeof(fota_result),"fota升级失败,当前版本号：%s",cr_version);
            snprintf(bool_strting,sizeof(bool_strting),"false");
        }
        else{
            snprintf(fota_result,sizeof(fota_result),"未知状态：%s---------当前版本号：%s",bool_strting,cr_version);
            snprintf(bool_strting,sizeof(bool_strting),"false");
        }
                
        char http_response[QRZL_CS_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
        char http_post_body[4096] = {0};
                
        int i;
        for (i = 0; i < 3; i++) {
            if (check_network() == 0) {
                break;
            }
        }

        snprintf(http_post_body, sizeof(http_post_body), "{\"cmdId\":%d",123456);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"success\":%s", http_post_body,bool_strting);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"message\":\"%s\"}", http_post_body,fota_result);
        qrzl_log("CS -> http_post_body: %s", http_post_body);
        cfg_set("fota_report_flag","1");
        https_send_post_request(http_request_cs_result_path, http_post_body, http_response);
    }
}

// ================================== CS MQTT start=====================================================
static pthread_mutex_t cs_msg_handler_lock;  // mqtt消息处理互斥锁，防止多线程同时操作
static char mqtt_server[128] = {0};
static int mqtt_port = 1883;
static int mqtt_qos = 0;
static char *mqtt_username = "qrui";
static char *mqtt_password = "Qr@2025";
static char mqtt_clientId[128] = {0};
static char mqtt_topic[128] = {0};
static int is_start_mqtt = 0;

void *cs_switch_card(void *args);

void cs_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    qrzl_log("CS -> Message with token %d delivered", dt);
}

int cs_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("CS -> Message arrived on topic: %s", topicName);
    qrzl_log("CS -> Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    json_value *j_value = json_parse((char*)message->payload, message->payloadlen);
    if (j_value != NULL)
    {
        if (j_value->type == json_object)
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            cs_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        json_value_free(j_value);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void cs_order_msg_handler(json_value* value, MQTTClient* client_p)
{
    json_value* j_success = json_value_get(value, "success", 7);
    if (j_success != NULL && j_success->type == json_boolean) {
        if (j_success->u.boolean) {
            set_network_br0_disconnect(0);
        } else {
            set_network_br0_disconnect(1);
        }
    }

    memset(cs_day_netlimit_arr, 0, sizeof(cs_day_netlimit_arr));
    memset(cs_month_netlimit_arr, 0, sizeof(cs_month_netlimit_arr));
    json_value* j_data_limit = json_value_get(value, "dataLimit", 9);
    if (j_data_limit != NULL && j_data_limit->type == json_array) {
        int i = 0;
        
        int day_i = 0;
        int month_i = 0;

        for (i = 0; i < j_data_limit->u.array.length; i++) {
            json_value* j_limit_json = j_data_limit->u.array.values[i];
            if (j_limit_json != NULL && j_limit_json->type == json_object) {
                json_value* j_limit_type = json_value_get(j_limit_json, "limitType", 9);
                json_value* j_limit_value = json_value_get(j_limit_json, "limitValue", 10);
                json_value* j_trigger_value = json_value_get(j_limit_json, "triggerValue", 12);
                if (j_limit_type != NULL && j_limit_type->type == json_integer) {
                    if (j_limit_type->u.integer == 1) {
                        if (j_limit_value != NULL && j_limit_value->type == json_integer && j_trigger_value != NULL && j_trigger_value->type == json_integer) {
                            cs_day_netlimit_arr[day_i].triggerValue = j_trigger_value->u.integer;
                            cs_day_netlimit_arr[day_i].limitValue = j_limit_value->u.integer;
                            day_i++;
                        }
                    } else if (j_limit_type->u.integer == 2) {
                        if (j_limit_value != NULL && j_limit_value->type == json_integer && j_trigger_value != NULL && j_trigger_value->type == json_integer) {
                            cs_month_netlimit_arr[month_i].triggerValue = j_trigger_value->u.integer;
                            cs_month_netlimit_arr[month_i].limitValue = j_limit_value->u.integer;
                            month_i++;
                        }
                    }
                }
            }
        }
    }
    update_cs_nv();

    json_value* j_next_tick = json_value_get(value, "nextTick", 8);
    if (j_next_tick != NULL && j_next_tick->type == json_integer) {
        request_interval_time = j_next_tick->u.integer;
    }

    json_value* j_next_flow = json_value_get(value, "nextFlow", 8);
    if (j_next_flow != NULL && j_next_flow->type == json_integer) {
        cs_next_flow = j_next_flow->u.integer;
    }
    json_value * j_reboot = json_value_get(value, "reboot", 6);
    {
        if(j_reboot != NULL && j_reboot->type == json_boolean) {
            if (j_reboot->u.boolean) {
                qrzl_log("CS -> 需要重启设备");
                restart_device();
            } else {
                qrzl_log("CS -> 不需要重启设备");
            }
        } else {
            qrzl_log("CS -> reboot is NULL or not a boolean");
        }
    }
    json_value* j_slot = json_value_get(value, "slot", 4);
    if(j_slot != NULL && j_slot->type == json_object) {
        json_value* j_cmd_id = json_value_get(j_slot, "cmdId", 5);
        json_value* j_num = json_value_get(j_slot, "num", 3);
        if(j_cmd_id != NULL && j_num != NULL &&
            j_cmd_id->type == json_integer && j_num->type == json_integer) {

            // 创建参数结构体并分配内存（注意：不能是局部变量，否则线程可能访问野指针）
            csSwitchCard* args = malloc(sizeof(csSwitchCard));
            // 结构体赋值
            memset(args, 0, sizeof(csSwitchCard));
            args->cmdId = j_cmd_id->u.integer;
            args->num = j_num->u.integer;
            if (args != NULL) {
                int err;
                pthread_t cs_mqtt_switch_card_tid;
                err = pthread_create(&cs_mqtt_switch_card_tid, NULL, cs_switch_card, (void*)args);
                if (err != 0) { 
                    qrzl_err("创建cs_mqtt_switch_card_tid 线程失败, error code: %d", err);
                    free(args); // 释放内存
                } else {
                    pthread_detach(cs_mqtt_switch_card_tid);  // 让线程自动回收
                }
            }

        }
    }

#ifdef QRZL_CS_FOTA    

    json_value* j_ota = json_value_get(value,"ota",3);
    if(j_ota!=NULL && j_ota->type == json_object){
        qrzl_log("OTA has been received.");
        json_value* j_cmdId = json_value_get(j_ota,"cmdId",5);
        json_value* j_address = json_value_get(j_ota,"address",7);
        json_value* j_size = json_value_get(j_ota,"size",4);
        json_value* j_version = json_value_get(j_ota,"version",7);
        json_value* j_md5 = json_value_get(j_ota,"md5",3);
        if(j_cmdId != NULL && j_address != NULL && j_size != NULL && j_version != NULL 
            && j_md5 != NULL && j_cmdId->type == json_integer && j_address->type == json_string 
            && j_size->type == json_integer && j_version->type == json_string 
            && j_md5->type == json_string){
                csOta* ota= (csOta*)malloc(sizeof(csOta));
                memset(ota,0,sizeof(csOta));
                ota->cmdId=j_cmdId->u.integer;
                snprintf(ota->address,sizeof(ota->address),"%s",j_address->u.string.ptr);
                ota->size=j_size->u.integer;
                snprintf(ota->version,sizeof(ota->version),"%s",j_version->u.string.ptr);
                snprintf(ota->md5,sizeof(ota->md5),"%s",j_md5->u.string.ptr);
                qrzl_log("fota信息：\n下载地址：%s\n差分包大小：%llu\n版本：%s\n文件校验码：%s",ota->address,ota->size,ota->version,ota->md5);
                if(ota != NULL && fota_is_downloader == 0){
                    fota_is_downloader=1;
                    int err;
                    pthread_t cs_fota_update_tid;
                    err=pthread_create(&cs_fota_update_tid,NULL,cs_fota_update,(void*)ota);
                    if(err!=0){
                        qrzl_err("创建cs_fota_update_tid 线程失败, error code: %d", err);
                        free(ota);
                    }
                    else{
                        pthread_detach(cs_fota_update_tid);
                    }
                }
                else{
                    fota_is_downloader=0;
                    free(ota);
                }
        }
    }

#endif

}

void cs_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    cs_mqtt_connect(client_p);
}

int cs_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 15;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, mqtt_topic, mqtt_qos)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("CS -> Subscribed to topic: %s", mqtt_topic);
    is_start_mqtt = 1;
    return rc;
}

static void cs_mqtt_client_start()
{
    update_device_static_data();
    
    if (pthread_mutex_init(&cs_msg_handler_lock, NULL) != 0) {
        qrzl_log("cs_msg_handler_lock init failed\n");
    }

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, mqtt_clientId, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, cs_mqtt_connlost, cs_message_arrived, cs_on_message_delivered);

    cs_mqtt_connect(&client);

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);
    pthread_mutex_destroy(&cs_msg_handler_lock);
    return;
}

static void cs_mqtt_control_process()
{
    
    qrzl_log("is_start_mqtt : %d", is_start_mqtt);
    qrzl_log("mqtt_server : %s", mqtt_server);
    qrzl_log("mqtt_topic : %s", mqtt_topic);
    qrzl_log("start cs_mqtt_client_start...");
    // 到这里说明信息都齐了，可以开启 mqtt 服务线程
    cs_mqtt_client_start();  // 自定义的 mqtt 启动函数
}

// ================================== CS MQTT end=====================================================


int command_to_switch_card_Logic(int cmd_num) {
    int switch_num = cmd_num;

    if (cmd_num == 3) {
        switch_num = 0;
    }

#if defined(JCV_HW_MZ804_V1_4)
    switch (cmd_num)
    {
        case 1:
            switch_num = 0;
            break;
        case 2:
            switch_num = 1;
            break;
#if defined(QRZL_HAVE_3_ESIM_CARD)
        case 3:
            switch_num = 2;
            break;
#endif
        default:
            break;
    }
#endif

    return switch_num;
}

int current_slot_num() {
    int sim_index = get_device_current_sim_index();
    int slot_num = sim_index;

    if (sim_index == 0) {
        slot_num = 3;
    }
#if defined(JCV_HW_MZ804_V1_4)
    switch (sim_index)
    {
        case 0:
            slot_num = 1;
            break;
        case 1:
            slot_num = 2;
            break;
#if defined(QRZL_HAVE_3_ESIM_CARD)
        case 2:
            slot_num = 3;
            break;
#endif
        default:
            break;
    }
#endif

    return slot_num;
}

static void cs_build_post_body(char *body, size_t body_size)
{
    qrzl_log("start cs_build_post_body");
    snprintf(body, body_size, "{");
    snprintf(body, body_size, "%s\"hVersion\":\"%s\"", body, g_qrzl_device_static_data.hw_version);
    snprintf(body, body_size, "%s,\"sVersion\":\"%s\"", body, g_qrzl_device_static_data.soft_version);
    snprintf(body, body_size, "%s,\"imei\":\"%s\"", body, g_qrzl_device_static_data.imei);
    snprintf(body, body_size, "%s,\"mac\":\"%s\"", body, g_qrzl_device_static_data.mac);
    snprintf(body, body_size, "%s,\"sn\":\"%s\"", body, g_qrzl_device_static_data.sn);
    snprintf(body, body_size, "%s,\"battery\":%s", body, g_qrzl_device_dynamic_data.remain_power);
    snprintf(body, body_size, "%s,\"network\":\"%s\"", body, "LTE");
    snprintf(body, body_size, "%s,\"simImsi\":\"%s\"", body, g_qrzl_device_dynamic_data.imsi);
    snprintf(body, body_size, "%s,\"simIccid\":\"%s\"", body, g_qrzl_device_dynamic_data.iccid);
    snprintf(body, body_size, "%s,\"simMcc\":\"%s\"", body, g_qrzl_device_dynamic_data.mcc);
    snprintf(body, body_size, "%s,\"simMnc\":\"%s\"", body, g_qrzl_device_dynamic_data.mnc);
    snprintf(body, body_size, "%s,\"simLac\":\"%s\"", body, g_qrzl_device_dynamic_data.tac);
    snprintf(body, body_size, "%s,\"simCellId\":\"%s\"", body, g_qrzl_device_dynamic_data.cid);
    snprintf(body, body_size, "%s,\"simRssi\":\"%s\"", body, g_qrzl_device_dynamic_data.rssi);
    
    snprintf(body, body_size, "%s,\"hotspotConnectedNum\":%d", body, g_qrzl_device_dynamic_data.conn_num);
    snprintf(body, body_size, "%s,\"hotspotMode\":\"%s%s\"", body, g_qrzl_device_dynamic_data.wifi_auth_mode, g_qrzl_device_dynamic_data.wifi_encryp_type);
    snprintf(body, body_size, "%s,\"hotspotSsid\":\"%s\"", body, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(body, body_size, "%s,\"hotspotPassword\":\"%s\"", body, g_qrzl_device_dynamic_data.wifi_key);
    char gateway_ip[16] = {0};
    cfg_get_item("lan_ipaddr", gateway_ip, sizeof(gateway_ip));
    snprintf(body, body_size, "%s,\"hotspotIp\":\"%s\"", body, gateway_ip);
    snprintf(body, body_size, "%s,\"hotspotRssi\":%d", body, -30);
    snprintf(body, body_size, "%s,\"networkIp\":\"%s\"", body, g_qrzl_device_dynamic_data.current_wan_ip);

    EsimFluxStat esim_fluxstat = get_esim_fluxstat();
    uint64_t total_flux = esim_fluxstat.rsim_flux_total + esim_fluxstat.esim1_flux_total + esim_fluxstat.esim2_flux_total;
    snprintf(body, body_size, "%s,\"networkTotalData\":\"%lldKB\"", body, total_flux / 1000); // 单位KB
    snprintf(body, body_size, "%s,\"networkDailyTotalData\":\"%lldKB\"", body, g_qrzl_device_dynamic_data.flux_day_total_bytes / 1000);
    snprintf(body, body_size, "%s,\"networkMonthlyTotalData\":\"%lldKB\"", body, g_qrzl_device_dynamic_data.flux_month_total_bytes / 1000);
    snprintf(body, body_size, "%s,\"networkUpData\":\"%lldKbps\"", body, get_up_limit_net_speed());
    snprintf(body, body_size, "%s,\"networkDownData\":\"%lldKbps\"", body, get_down_limit_net_speed());

    snprintf(body, body_size, "%s,\"networkDailyLimit\":\"%lld\"", body, cs_day_netlimit_now.triggerValue);
    snprintf(body, body_size, "%s,\"networkMonthlyLimit\":\"%lld\"", body, cs_month_netlimit_now.triggerValue);

    // networkLimits array start
    snprintf(body, body_size, "%s,\"networkLimits\":[", body);
    int day_i = 0;
    while (cs_day_netlimit_arr[day_i].triggerValue != 0)
    {
        if (day_i > 0) {
            snprintf(body, body_size, "%s,", body);
        }
        snprintf(body, body_size, "%s\"D:%lldMB/%lldKbps\"", body, cs_day_netlimit_arr[day_i].triggerValue, cs_day_netlimit_arr[day_i].limitValue);
        day_i++;
    }

    int month_i = 0;
    while (cs_month_netlimit_arr[month_i].triggerValue != 0)
    {
        if (day_i > 0 || month_i > 0) {
            snprintf(body, body_size, "%s,", body);
        }
        snprintf(body, body_size, "%s\"M:%lldMB/%lldKbps\"", body, cs_month_netlimit_arr[month_i].triggerValue, cs_month_netlimit_arr[month_i].limitValue);
        month_i++;
    }
    
    
    snprintf(body, body_size, "%s]", body);
    // networkLimits array end

    snprintf(body, body_size, "%s,\"chargeTimes\":0", body);
    snprintf(body, body_size, "%s,\"chargeDuration\":0", body);

    // 获取当前上网的卡槽
    int cs_card_isp = current_slot_num();

    snprintf(body, body_size, "%s,\"useSlot\":%d", body, cs_card_isp);

    // slots array start
    snprintf(body, body_size, "%s,\"slots\":[", body);

    // esim1
    snprintf(body, body_size, "%s{\"num\":%d", body, 1);
    snprintf(body, body_size, "%s,\"imsi\":\"%s\"", body, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(body, body_size, "%s,\"iccid\":\"%s\"}", body, g_qrzl_device_static_data.nvro_esim1_iccid);

    // esim2
    if (strlen(g_qrzl_device_static_data.nvro_esim2_iccid) > 0) {
        snprintf(body, body_size, "%s,{\"num\":%d", body, 2);
        snprintf(body, body_size, "%s,\"imsi\":\"%s\"", body, g_qrzl_device_static_data.nvro_esim2_imsi);
        snprintf(body, body_size, "%s,\"iccid\":\"%s\"}", body, g_qrzl_device_static_data.nvro_esim2_iccid);
    }

    // esim3
    if (strlen(g_qrzl_device_static_data.nvro_esim3_iccid) > 0) {
        snprintf(body, body_size, "%s,{\"num\":%d", body, 3);
        snprintf(body, body_size, "%s,\"imsi\":\"%s\"", body, g_qrzl_device_static_data.nvro_esim3_imsi);
        snprintf(body, body_size, "%s,\"iccid\":\"%s\"}", body, g_qrzl_device_static_data.nvro_esim3_iccid);
    }

    snprintf(body, body_size, "%s]", body);
    // slots array start end

    snprintf(body, body_size, "%s}", body);

    qrzl_log("http body: %s\n", body);
    printf("\n");
    printf("http body: %s\n", body);
    return;
}

/**
 * chuangsan 切卡线程函数
 */
void *cs_switch_card(void *args) {
    csSwitchCard* switch_card = (csSwitchCard*)args;
    qrzl_log("into cs_switch_card th");

    // 根据卡数量判断自动切卡的值
    char sim_list[40] = {0};
    char auto_switch_type[2] = {0};
    cfg_get_item("sim_select_num_type", sim_list, sizeof(sim_list));
    if (strcmp(sim_list, "ESIM1_only,ESIM2_only,RSIM_only") == 0) {
        snprintf(auto_switch_type, sizeof(auto_switch_type), "%s", "2");
    } else if (strcmp(sim_list, "ESIM1_only,ESIM2_only") == 0) {
        snprintf(auto_switch_type, sizeof(auto_switch_type), "%s", "1");
    }

    // 关闭自动切卡
    cfg_set("auto_switch_esim_type", "0");
    qrzl_log("CS -> OFF auto_switch_esim ... ");

    // 拿到切卡的参数
    int current_sim = get_device_current_sim_index();
    qrzl_log("CS -> Swtich card num: %d", switch_card->num);
    int cts = command_to_switch_card_Logic(switch_card->num);
    if( cts == -1) {
        qrzl_log("CS -> 该指令没有找到对应的卡，不进行切卡.");
        return NULL;
    }
    switch_sim_card_not_restart(cts);
    qrzl_log("CS -> swtich card end");

    sleep(10);

    // 构建请求URL
    int ret;
    char http_response[QRZL_CS_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char http_post_body[4096] = {0};

    if(check_network() == 0) {
    // 网络正常推送切卡成功 
    // 此处上报（http_request_cs_result_path）使用的与数据上报（http_request_path）使用的是不同的地址。
        // 构造响应体
        snprintf(http_post_body, sizeof(http_post_body), "%s{\"cmdId\":%d", http_post_body, switch_card->num);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"success\":true", http_post_body);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"message\":\"%s\"}", http_post_body, "switch card success");
        qrzl_log("CS -> http_post_body: %s", http_post_body);
        https_send_post_request(http_request_cs_result_path, http_post_body, http_response);
    } else {
        // 切回原来的卡
        switch_sim_card_not_restart(current_sim);

        // 避免下面发送的请求超时，突然设备关机之后没把自动切卡开起来
        cfg_set("auto_switch_esim_type", auto_switch_type);
        qrzl_log("CS -> ON auto_switch_esim ... ");
        
        sleep(10);
        
        if(check_network() != 0) {
            // 原来的卡网络异常
            sleep(30);
        } 
        // 推送切卡失败
        // 构造响应体
        snprintf(http_post_body, sizeof(http_post_body), "%s{\"cmdId\":%d", http_post_body, switch_card->num);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"success\":false", http_post_body);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"message\":\"%s\"}", http_post_body, "switch card fail, not network.");
        qrzl_log("CS -> http_post_body: %s", http_post_body);
        https_send_post_request(http_request_cs_result_path, http_post_body, http_response);
    }
    // 开启自动切卡功能
    cfg_set("auto_switch_esim_type", auto_switch_type);
    qrzl_log("CS -> ON auto_switch_esim ... ");

    free(args); // 释放内存

    return NULL;
}

/*
*chuangsan fota升级线程
*/
void *cs_fota_update(void * args){
    //fota升级结果
    int result=0;
    csOta* ota=(csOta*)args;

    qrzl_log("init cs_fota_update th");
    char err_info[512];
    char cmdId_char[32]={0};
    snprintf(cmdId_char,sizeof(cmdId_char),"%d",ota->cmdId);
    qrzl_log("CS-cmdId:%s",cmdId_char);
    memset(err_info,0,sizeof(err_info));
    //检测fota相关文件和目录
    if(fota_check_and_make_fota_dir == -1){
        snprintf(err_info,sizeof(err_info),"%s","fota相关文件和目录检查失败");
        goto error;
    }
    qrzl_log("fota相关文件和目录检查成功");
    //判断是否已经存在差分包，存在则删除
    if(access(FOTA_PACKAGE_FILE,F_OK)==0){
        qrzl_log("%s文件存在，删除该文件",FOTA_PACKAGE_FILE);
        system("rm /cache/zte_fota/delta.package");
    }

    if(access("/cache/delta.package",F_OK) == 0){
        qrzl_log("%s文件存在，删除该文件","/cache/delta.package");
        system("rm /cache/delta.package");
    }

    if(atoi(g_qrzl_device_dynamic_data.remain_power)<35){
        snprintf(err_info,sizeof(err_info),"当前设备电量低，请稍后升级，当前设备电量为：%s/%",g_qrzl_device_dynamic_data.remain_power);
        goto error;
    }

    //开始下载
    qrzl_log("差分包开始下载");

    if(http_download_file(ota->address,"/cache/delta.package")!=0){
        snprintf(err_info,sizeof(err_info),"差分包下载失败");
        goto error;
    }

    //system("mv /cache/delta.package /cache/zte_fota/delta.package");
    qrzl_log("差分包下载结束");

    //下载结束
    if(access("/cache/delta.package",F_OK)==0){
        qrzl_log("%s文件存在，进入文件md5计算","/cache/delta.package");
    }
    else{
        snprintf(err_info,sizeof(err_info),"%s文件不存在","/cache/delta.package");
        qrzl_log("%s",err_info);
        goto error;
    }
    //存储计算出来的md5值
    char md5Value[128]={0};
    int md5res;
    //开始计算文件md5
    md5res=fota_get_file_hash_from_path("/cache/delta.package",md5Value,128);
    if(!(md5res<0)){
        qrzl_log("文件md5计算成功");
        if(strcmp(ota->md5,md5Value)==0){
            qrzl_log("文件完整性检查通过");
            cfg_set("cs_cmdId",cmdId_char);
            system("mv /cache/delta.package /cache/zte_fota/delta.package");
            result=fota_update();
            if(result==1){
                snprintf(err_info,sizeof(err_info),"%s","fota状态文件打开失败");
            }
            else if(result==2){
                snprintf(err_info,sizeof(err_info),"%s","fota状态文件读取失败");
            }
            else if(result==3){
                snprintf(err_info,sizeof(err_info),"%s","差分包fota_upi校验失败");
            }
            else{
                fota_is_downloader=0;
                free(ota);
                return;
            }
            system("rm /cache/zte_fota/delta.package");
            goto error;
        }
        else{
            snprintf(err_info,sizeof(err_info),"%s","差分包md5校验失败");
            goto error;
        }
    }
    else{
        snprintf(err_info,sizeof(err_info),"%s","差分包文件md5计算失败");
        goto error;
    }
error:{
        char http_response[QRZL_CS_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
        char http_post_body[4096] = {0};
        qrzl_err("%s",err_info);
        fota_is_downloader=0;
        snprintf(http_post_body, sizeof(http_post_body), "%s{\"cmdId\":%d", http_post_body, ota->cmdId);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"success\":false", http_post_body);
        snprintf(http_post_body, sizeof(http_post_body), "%s,\"message\":\"%s\"}", http_post_body, err_info);
        qrzl_log("CS -> http_post_body: %s", http_post_body);
        https_send_post_request(http_request_cs_result_path, http_post_body, http_response);
        free(ota);
    }
}


static void cs_resp_handler(json_value *value)
{
    json_value* j_success = json_value_get(value, "success", 7);
    if (j_success != NULL && j_success->type == json_boolean) {
        if (j_success->u.boolean) {
            set_network_br0_disconnect(0);
        } else {
            set_network_br0_disconnect(1);
        }
    }

    memset(cs_day_netlimit_arr, 0, sizeof(cs_day_netlimit_arr));
    memset(cs_month_netlimit_arr, 0, sizeof(cs_month_netlimit_arr));
    json_value* j_data_limit = json_value_get(value, "dataLimit", 9);
    if (j_data_limit != NULL && j_data_limit->type == json_array) {
        int i = 0;
        
        int day_i = 0;
        int month_i = 0;

        for (i = 0; i < j_data_limit->u.array.length; i++) {
            json_value* j_limit_json = j_data_limit->u.array.values[i];
            if (j_limit_json != NULL && j_limit_json->type == json_object) {
                json_value* j_limit_type = json_value_get(j_limit_json, "limitType", 9);
                json_value* j_limit_value = json_value_get(j_limit_json, "limitValue", 10);
                json_value* j_trigger_value = json_value_get(j_limit_json, "triggerValue", 12);
                if (j_limit_type != NULL && j_limit_type->type == json_integer) {
                    if (j_limit_type->u.integer == 1) {
                        if (j_limit_value != NULL && j_limit_value->type == json_integer && j_trigger_value != NULL && j_trigger_value->type == json_integer) {
                            cs_day_netlimit_arr[day_i].triggerValue = j_trigger_value->u.integer;
                            cs_day_netlimit_arr[day_i].limitValue = j_limit_value->u.integer;
                            day_i++;
                        }
                    } else if (j_limit_type->u.integer == 2) {
                        if (j_limit_value != NULL && j_limit_value->type == json_integer && j_trigger_value != NULL && j_trigger_value->type == json_integer) {
                            cs_month_netlimit_arr[month_i].triggerValue = j_trigger_value->u.integer;
                            cs_month_netlimit_arr[month_i].limitValue = j_limit_value->u.integer;
                            month_i++;
                        }
                    }
                }
            }
        }
    }
    update_cs_nv();

    json_value* j_next_tick = json_value_get(value, "nextTick", 8);
    if (j_next_tick != NULL && j_next_tick->type == json_integer) {
        request_interval_time = j_next_tick->u.integer;
    }
    json_value * j_reboot = json_value_get(value, "reboot", 6);
    {
        if(j_reboot != NULL && j_reboot->type == json_boolean) {
            if (j_reboot->u.boolean) {
                qrzl_log("CS -> 需要重启设备");
                restart_device();
            } else {
                qrzl_log("CS -> 不需要重启设备");
            }
        } else {
            qrzl_log("CS -> reboot is NULL or not a boolean");
        }
    }
    json_value* j_next_flow = json_value_get(value, "nextFlow", 8);
    if (j_next_flow != NULL && j_next_flow->type == json_integer) {
        cs_next_flow = j_next_flow->u.integer;
    }

    json_value* j_slot = json_value_get(value, "slot", 4);
    if(j_slot != NULL && j_slot->type == json_object) {
        json_value* j_cmd_id = json_value_get(j_slot, "cmdId", 5);
        json_value* j_num = json_value_get(j_slot, "num", 3);
        if(j_cmd_id != NULL && j_num != NULL &&
            j_cmd_id->type == json_integer && j_num->type == json_integer) {

            // 创建参数结构体并分配内存（注意：不能是局部变量，否则线程可能访问野指针）
            csSwitchCard* args = malloc(sizeof(csSwitchCard));
            // 结构体赋值
            memset(args, 0, sizeof(csSwitchCard));
            args->cmdId = j_cmd_id->u.integer;
            args->num = j_num->u.integer;
            if (args != NULL) {
                int err;
                pthread_t cs_switch_card_tid;
                err = pthread_create(&cs_switch_card_tid, NULL, cs_switch_card, (void*)args);
                if (err != 0) { 
                    qrzl_err("创建cs_switch_card_tid 线程失败, error code: %d", err);
                    free(args); // 释放内存
                } else {
                    pthread_detach(cs_switch_card_tid);  // 让线程自动回收
                }
            }

        }
    }

#ifdef QRZL_CS_FOTA
    json_value* j_ota = json_value_get(value,"ota",3);
    if(j_ota!=NULL && j_ota->type == json_object){
        qrzl_log("OTA has been received.");
        json_value* j_cmdId = json_value_get(j_ota,"cmdId",5);
        json_value* j_address = json_value_get(j_ota,"address",7);
        json_value* j_size = json_value_get(j_ota,"size",4);
        json_value* j_version = json_value_get(j_ota,"version",7);
        json_value* j_md5 = json_value_get(j_ota,"md5",3);
        if(j_cmdId != NULL && j_address != NULL && j_size != NULL && j_version != NULL 
            && j_md5 != NULL && j_cmdId->type == json_integer && j_address->type == json_string 
            && j_size->type == json_integer && j_version->type == json_string 
            && j_md5->type == json_string){
            csOta* ota= (csOta*)malloc(sizeof(csOta));
            memset(ota,0,sizeof(csOta));
            ota->cmdId=j_cmdId->u.integer;
            snprintf(ota->address,sizeof(ota->address),"%s",j_address->u.string.ptr);
            ota->size=j_size->u.integer;
            snprintf(ota->version,sizeof(ota->version),"%s",j_version->u.string.ptr);
            snprintf(ota->md5,sizeof(ota->md5),"%s",j_md5->u.string.ptr);
            qrzl_log("fota信息：\ncmdId:：%d\n下载地址：%s\n差分包大小：%llu\n版本：%s\n文件校验码：%s",ota->cmdId,ota->address,ota->size,ota->version,ota->md5);
            if(ota != NULL && fota_is_downloader == 0){
                fota_is_downloader=1;
                int err;
                pthread_t cs_fota_update_tid;
                err=pthread_create(&cs_fota_update_tid,NULL,cs_fota_update,(void*)ota);
                if(err!=0){
                    qrzl_err("创建cs_fota_update_tid 线程失败, error code: %d", err);
                    free(ota);
                }
                else{
                    pthread_detach(cs_fota_update_tid);
                }
            }
            else{
                fota_is_downloader=0;
                free(ota);
            }
        }
    }
#endif

    // 获取 MQTT 信息
    json_value* j_mqtt = json_value_get(value, "mqtt", 4);
    if(j_mqtt != NULL && j_mqtt->type == json_object) {
        // 获取mqtt连接信息
        json_value* j_topic = json_value_get(j_mqtt, "topic", 5);
        json_value* j_broker = json_value_get(j_mqtt, "broker", 6);
        json_value* j_clientId = json_value_get(j_mqtt, "clientId", 8);
        json_value* j_qos = json_value_get(j_mqtt, "qos", 3);
        if (j_topic && j_topic->type == json_string)
            qrzl_log("j_topic: %s", j_topic->u.string.ptr);
        else
            qrzl_log("j_topic is NULL or not a string");

        if (j_broker && j_broker->type == json_string)
            qrzl_log("j_broker: %s", j_broker->u.string.ptr);
        else
            qrzl_log("j_broker is NULL or not a string");

        if (j_clientId && j_clientId->type == json_string)
            qrzl_log("j_clientId: %s", j_clientId->u.string.ptr);
        else
            qrzl_log("j_clientId is NULL or not a string");

        if (j_qos && j_qos->type == json_integer)
            qrzl_log("j_qos: %d", j_qos->u.integer);
        else
            qrzl_log("j_qos is NULL or not an integer");

        if((j_topic != NULL && j_topic->type == json_string) && (j_broker != NULL && j_broker->type == json_string) &&
            (j_clientId != NULL && j_clientId->type == json_string) && (j_qos != NULL && j_qos->type == json_integer)) {

                snprintf(mqtt_server, sizeof(mqtt_server), "%s", j_broker->u.string.ptr);
                snprintf(mqtt_topic, sizeof(mqtt_topic), "%s", j_topic->u.string.ptr);
                snprintf(mqtt_clientId, sizeof(mqtt_clientId), "%s", j_clientId->u.string.ptr);
                mqtt_qos = j_qos->u.integer;

                if (is_start_mqtt != 1) {
                    cs_mqtt_control_process();
                } else {
                    qrzl_log("CS MQTT 已启动，无需再启动"); 
                }
        } else {
            qrzl_log("CS MQTT 有信息不完整或类型不正确!!"); 
        } 
    } else {
        qrzl_log("CS MQTT 未传输MQTT信息");
    }

}

static void cs_start_process()
{
    int ret;
    char http_response[QRZL_CS_HTTP_RESPONSE_MAX] = {0}; // 存储响应体
    char http_post_body[4096] = {0};

    update_device_dynamic_data();

    // 构建请求URL
    cs_build_post_body(http_post_body, sizeof(http_post_body));
    ret = https_send_post_request(http_request_path, http_post_body, http_response);
    if (ret != 0 || strcmp(http_response, "") == 0)
    {
        return;
    }
    json_value *value = json_parse(http_response, strlen(http_response));
    if (value == NULL)
    {
        qrzl_err("json value is NULL");
        return;
    }
    // 确保顶层是一个对象
    if (value->type != json_object)
    {
        qrzl_err("JSON is not an object.\n");
        json_value_free(value);
        return;
    }

    cs_resp_handler(value);

    // 释放 JSON 解析结果
    json_value_free(value);
}

/**
 * 自动检测当前使用了多少流量，如果达到限制值，就限速
 */
void* cs_auto_check_flux_limit()
{
    int ret;
    char realtime_total_bytes_str[21] = {0};
    uint64_t realtime_total_bytes = 0;
    char flux_day_total_bytes_str[21] = {0};
    uint64_t flux_day_total_bytes = 0;
    char flux_month_total_bytes_str[21] = {0};
    uint64_t flux_month_total_bytes = 0;
    
    while (1)
    {
        if (cs_next_flow > 0) {
            cfg_get_item("CTotal_vol", realtime_total_bytes_str, 21);
            realtime_total_bytes = atoll(realtime_total_bytes_str);
            if ((realtime_total_bytes / 1024 / 1024) >= cs_next_flow) {
                qrzl_log("realtime_total_bytes >= cs_next_flow cs_start_process");
                cs_start_process();
            }
        }

        cfg_get_item("flux_day_total", flux_day_total_bytes_str, 21);
        flux_day_total_bytes = atoll(flux_day_total_bytes_str);
        cfg_get_item("flux_month_total", flux_month_total_bytes_str, 21);
        flux_month_total_bytes = atoll(flux_month_total_bytes_str);
        cs_day_netlimit_now.limitValue = 0;
        cs_day_netlimit_now.triggerValue = 0;
        cs_month_netlimit_now.limitValue = 0;
        cs_month_netlimit_now.triggerValue = 0;

        int i;
        for (i = 0; i < QRZL_CS_MAX_NET_LIMIT; i++)
        {
            if (cs_day_netlimit_arr[i].triggerValue <= 0) {
                break;
            }

            if ((flux_day_total_bytes / 1024 / 1024) >= cs_day_netlimit_arr[i].triggerValue) {
                cs_day_netlimit_now.triggerValue = cs_day_netlimit_arr[i].triggerValue;
                cs_day_netlimit_now.limitValue = cs_day_netlimit_arr[i].limitValue;
            }
        }

        for (i = 0; i < QRZL_CS_MAX_NET_LIMIT; i++)
        {
            if (cs_month_netlimit_arr[i].triggerValue <= 0) {
                break;
            }

            if ((flux_month_total_bytes / 1024 / 1024) >= cs_month_netlimit_arr[i].triggerValue) {
                cs_month_netlimit_now.triggerValue = cs_month_netlimit_arr[i].triggerValue;
                cs_month_netlimit_now.limitValue = cs_month_netlimit_arr[i].limitValue;
            }
        }

        uint64_t up_limit_net_speed = get_up_limit_net_speed();
        uint64_t down_limit_net_speed = get_down_limit_net_speed();
        if (cs_day_netlimit_now.triggerValue > 0 || cs_month_netlimit_now.triggerValue > 0) {
            uint64_t limit_tmp = 0;
            if (cs_day_netlimit_now.limitValue > 0 && cs_month_netlimit_now.limitValue > 0) {
                if (cs_day_netlimit_now.limitValue < cs_month_netlimit_now.limitValue) {
                    limit_tmp = cs_day_netlimit_now.limitValue;
                } else {
                    limit_tmp = cs_month_netlimit_now.limitValue;
                }
            } else if (cs_day_netlimit_now.limitValue > 0) {
                limit_tmp = cs_day_netlimit_now.limitValue;
            } else if (cs_month_netlimit_now.limitValue > 0) {
                limit_tmp = cs_month_netlimit_now.limitValue;
            }
            
            
            if (limit_tmp != up_limit_net_speed) {
                limit_net_speed(limit_tmp, limit_tmp);
                cs_start_process();
            }
        } else {
            if (up_limit_net_speed > 0 || down_limit_net_speed > 0) {
                limit_net_speed(0, 0);
            }
        }

        sleep(10);
    }
    
    return NULL;
}

/**
 * 还没开始做切卡的函数，因为现在客户只做了单卡，着急出版本。如果做了多卡需要补上
 */

void* cshttp_control_start()
{
    int ret;
    ret = init_config_data();
    if (ret != 0)
    {
        qrzl_err("初始化数据失败，请检查配置是否正确");
        return;
    }

    init_cs_nv();

#if defined(QRZL_CHUANGSAN_AIR_RECORD)
    // 空中写号初始化
    air_write_at_init();
#endif

    int err;
    pthread_t cs_auto_check_flux_limit_tid;
    err = pthread_create(&cs_auto_check_flux_limit_tid, NULL, cs_auto_check_flux_limit, NULL);
    if (err != 0) {
        qrzl_err("创建cs_auto_check_flux_limit 线程失败, error code: %d", err);
    }

    // 防止开机时一开始没网
    int i;
    for (i = 0; i < 3; i++) {
        if (check_network() == 0) {
            break;
        }
    }

#ifdef QRZL_CS_FOTA
    int fota_err;
    pthread_t cs_fota_result_report_tid;
    fota_err = pthread_create(&cs_fota_result_report_tid,NULL,fota_result_report,NULL);
    if(fota_err !=0 ){
        qrzl_err("创建cs_fota_result_report 线程失败, error code: %d", err);
    }
    else{
        pthread_detach(cs_fota_result_report_tid);
    }
#endif

    update_device_static_data();
    while (1) 
    {
        cs_start_process();
        if (request_interval_time <= 0) {
            qrzl_log("request_interval_time is 0, cshttp_control end");
            break;
        }
        qrzl_log("request_interval_time: %d s", request_interval_time);
        sleep(request_interval_time);
    }
    return NULL;
}
