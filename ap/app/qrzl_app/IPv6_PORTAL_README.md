# IPv6 Captive Portal 支持

## 概述

本项目已经添加了IPv6 captive portal支持，通过`CONFIG_IPV6_PORTAL`宏定义来控制IPv6功能的编译和运行。

## 宏定义控制

### CONFIG_IPV6_PORTAL

- **位置**: 在内核配置或Makefile中定义
- **作用**: 控制IPv6 captive portal功能的编译和运行
- **默认**: 未定义（仅支持IPv4）

## 文件修改说明

### 1. 驱动层 (speedlimit.c)

**IPv6功能（受CONFIG_IPV6_PORTAL控制）:**
- IPv6网关地址: `fe80::a82e:a4ff:feed:f062`
- IPv6流量重定向到端口9000
- IPv6响应包处理
- IPv6白名单支持
- IPv6 MAC地址认证

**关键函数:**
- `preRoutingv6()`: IPv6流量重定向
- `postRoutingv6()`: IPv6响应处理
- `calcIpv6TcpChecksum()`: IPv6 TCP校验和计算

### 2. 应用层 (qrzl_captive_portal_server.c)

**IPv6功能（受CONFIG_IPV6_PORTAL控制）:**
- IPv6 socket监听
- IPv6客户端IP获取
- IPv6 MAC地址获取
- IPv6认证流程

**关键函数:**
- `get_client_ip()`: 支持IPv4/IPv6双栈
- `get_mac_from_ipv6()`: IPv6 MAC地址获取
- `is_ipv6_address()`: IPv6地址检测
- `async_http_server_thread_handler()`: 双栈HTTP服务器

## 编译配置

### 启用IPv6支持

1. **内核配置方式:**
   ```bash
   # 在内核配置中添加
   CONFIG_IPV6_PORTAL=y
   ```

2. **Makefile方式:**
   ```makefile
   # 在编译选项中添加
   CFLAGS += -DCONFIG_IPV6_PORTAL
   ```

3. **代码中手动定义:**
   ```c
   // 在qrzl_captive_portal_server.c开头取消注释
   #define CONFIG_IPV6_PORTAL
   ```

### 禁用IPv6支持

不定义`CONFIG_IPV6_PORTAL`宏，系统将只支持IPv4功能。

## 功能特性

### 启用IPv6时 (CONFIG_IPV6_PORTAL=y)

- ✅ IPv4 + IPv6双栈支持
- ✅ IPv6流量重定向
- ✅ IPv6 MAC地址获取
- ✅ IPv6认证流程
- ✅ IPv6白名单
- ✅ IPv6响应处理

### 禁用IPv6时 (未定义CONFIG_IPV6_PORTAL)

- ✅ IPv4单栈支持
- ❌ IPv6功能完全禁用
- ✅ 保持原有IPv4功能不变
- ✅ 减少内存和代码占用

## 网络配置

### IPv6网关地址
```
fe80::a82e:a4ff:feed:f062:9000
```

### 支持的协议和端口
- HTTP (80)
- HTTPS (443)
- 自定义端口 (8080)

## 测试验证

### IPv6功能测试
```bash
# 测试IPv6连接
curl -6 http://[fe80::a82e:a4ff:feed:f062]:9000/

# 查看IPv6邻居表
cat /proc/net/ndisc_cache

# 查看IPv6路由
ip -6 route show
```

### IPv4功能测试
```bash
# 测试IPv4连接
curl -4 http://*************:9000/

# 查看ARP表
cat /proc/net/arp
```

## 注意事项

1. **内核版本**: 适配Linux 3.4.x内核
2. **依赖**: 需要内核IPv6支持
3. **性能**: IPv6功能会增加少量内存和CPU开销
4. **兼容性**: IPv4功能完全向后兼容
5. **配置**: 确保网络接口支持IPv6

## 故障排除

### 常见问题

1. **IPv6连接失败**
   - 检查`CONFIG_IPV6_PORTAL`是否定义
   - 确认内核IPv6支持已启用
   - 验证网络接口IPv6配置

2. **MAC地址获取失败**
   - 检查`/proc/net/ndisc_cache`文件权限
   - 确认IPv6邻居发现正常工作

3. **编译错误**
   - 确认IPv6相关头文件存在
   - 检查宏定义是否正确设置

### 调试信息

启用内核日志查看详细信息:
```bash
dmesg | grep -i ipv6
dmesg | grep -i captive
```
