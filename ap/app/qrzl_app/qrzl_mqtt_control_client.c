#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include <sys/time.h>
#include <unistd.h>
#include <unistd.h>

#include "qrzl_mqtt_control_client.h"
#include "qrzl_utils.h"
#include "MQTTClient.h"
#include "json.h"

#include "cloud_control/xunyou_mqtt_control.h"


extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

static char mqtt_server[128] = {0};
static char mqtt_username[128] = {0};
static char mqtt_password[128] = {0};

/* ========================================= start mqtt type 1类型的处理 ==================================================================== */
static pthread_mutex_t t1_msg_handler_lock;  // 定义T1类型mqtt消息处理互斥锁，防止多线程同时操作

static uint32_t t1_publish_interval = 300;


int t1_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 15;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(receive_topic, sizeof(receive_topic), "iot/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_YT
    snprintf(receive_topic, sizeof(receive_topic), "appprod_signTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_HX
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "lainiiot", "v1", g_qrzl_device_static_data.nvro_esim1_iccid);
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(receive_topic, sizeof(receive_topic), "iotv3/%s/%s/%s", "ZXIC", "803P42U1701", g_qrzl_device_static_data.imei);
#endif
    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}

void t1_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    qrzl_log("Message with token %d delivered", dt);
}

int t1_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
#ifdef QRZL_APP_CUSTOMIZATION_HMM
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");
#elif QRZL_APP_CUSTOMIZATION_YT
    snprintf(topic, sizeof(topic), "appprod_statusTopic/%s", g_qrzl_device_static_data.imei);
#elif QRZL_APP_CUSTOMIZATION_HX
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "lainiiot", "v1");
//沙音用户用的是HMM的通道，不过不可直接用HMM的宏来定义
#elif QRZL_APP_CUSTOMIZATION_SHAYIN
    snprintf(topic, sizeof(topic), "iotv3/%s/%s/push", "ZXIC", "803P42U1701");    
#endif
    char payload[2048] = {0};
    snprintf(payload, sizeof(payload), "{");
    snprintf(payload, sizeof(payload),
            "%s\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
#ifdef QRZL_APP_CUSTOMIZATION_HX
    snprintf(payload,sizeof(payload),"%s,\"cardno\":\"%s\"",payload,g_qrzl_device_static_data.nvro_esim1_iccid);
#endif
    snprintf(payload, sizeof(payload),
            "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);

    snprintf(payload, sizeof(payload),
            "%s,\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    
    snprintf(payload, sizeof(payload),
            "%s,\"simNum\":%d", payload, 2);
    snprintf(payload, sizeof(payload),
            "%s,\"remainPwr\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);
    
    char currentTime[64] = {0};
    get_local_time("%Y-%m-%d %H:%M:%S", currentTime, sizeof(currentTime));
    snprintf(payload, sizeof(payload),
            "%s,\"currentTime\":\"%s\"", payload, currentTime);
    
    snprintf(payload, sizeof(payload),
            "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload),
            "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload),
            "%s,\"hidden\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload),
            "%s,\"connCnt\":%d", payload, g_qrzl_device_dynamic_data.conn_num);
    snprintf(payload, sizeof(payload),
            "%s,\"rssi\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
    snprintf(payload, sizeof(payload),
            "%s,\"dayFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_day_total_bytes/1024); // 这个服务商的流量单位是KB
    snprintf(payload, sizeof(payload),
            "%s,\"monthFlow\":%lld", payload, g_qrzl_device_dynamic_data.flux_month_total_bytes/1024);
    snprintf(payload, sizeof(payload),
            "%s,\"version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimit\":%lld", payload, get_down_limit_net_speed());
    snprintf(payload, sizeof(payload),
            "%s,\"heartbeat\":%d", payload, t1_publish_interval);
    snprintf(payload, sizeof(payload),
            "%s,\"disconn\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn);
    
    // 以下是simList 列表
    snprintf(payload, sizeof(payload), "%s,\"simList\":[", payload);
    snprintf(payload, sizeof(payload), "%s{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    if (strncmp(g_qrzl_device_static_data.nvro_esim1_iccid, g_qrzl_device_dynamic_data.iccid, sizeof(g_qrzl_device_static_data.nvro_esim1_iccid)) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }

    snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    if (strncmp(g_qrzl_device_static_data.nvro_esim2_iccid, g_qrzl_device_dynamic_data.iccid, sizeof(g_qrzl_device_static_data.nvro_esim2_iccid)) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }
    // 如果有外卡就上传外卡
    if (strcmp("RSIM_only", g_qrzl_device_dynamic_data.current_sim) == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":true}", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,{\"simIMEI\":\"%s\"", payload, g_qrzl_device_static_data.imei);
        snprintf(payload, sizeof(payload), "%s,\"iccid\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"isLine\":false}", payload);
    }
    snprintf(payload, sizeof(payload), "%s]", payload);
    // 以上是simList 列表

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    printf("\n printf -> mqtt payload: %s\n", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        qrzl_log("Message published!");
    }
    return 0;
}

int t1_order_msg_handler(json_value* j_value, MQTTClient* client_p)
{
    int ret;

    json_value *j_reset = json_value_get(j_value, "reSet", 5);
    if (j_reset != NULL && j_reset->type == json_integer)
    {
        if (j_reset->u.integer == 1)
        {
            ret = reset_device();
            return ret;
        }
    }

    json_value *j_reboot = json_value_get(j_value, "reboot", 6);
    if (j_reboot != NULL && j_reboot->type == json_integer)
    {
        if (j_reboot->u.integer == 1)
        {
            ret = restart_device();
            return ret;
        }
    }

    json_value *j_ssid = json_value_get(j_value, "ssid", 4);
    json_value *j_password = json_value_get(j_value, "password", 8);
    if (j_ssid != NULL && j_ssid->type == json_string && j_password != NULL && j_password->type == json_string)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->u.string.ptr);
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_password->u.string.ptr);
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    json_value *j_hidden = json_value_get(j_value, "hidden", 6);
    if (j_hidden != NULL && j_hidden->type == json_integer)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if(j_hidden->u.integer==0){
            wifi_config.hide = 1;
        }
        else{
            wifi_config.hide = 0;
        }
        update_wifi_by_config(&wifi_config);
        t1_publish_device_info(client_p);
        return 0;
    }

    json_value* j_get = json_value_get(j_value, "get", 3);
    if (j_get != NULL && j_get->type == json_boolean)
    {
        if (j_get->u.boolean != 0)
        {
            ret = t1_publish_device_info(client_p);
            return ret;
        }
    }

    json_value* j_disconn = json_value_get(j_value, "disconn", 7);
    if (j_disconn != NULL && j_disconn->type == json_integer)
    {
        if (j_disconn->u.integer == 0)
        {
            set_network_br0_disconnect(0);
        }
        else if (j_disconn->u.integer == 1)
        {
            set_network_br0_disconnect(1);
        }
        t1_publish_device_info(client_p);
        return -1;
    }

    json_value* j_speed_limit = json_value_get(j_value, "speedLimit", 10);
    if (j_speed_limit != NULL && j_speed_limit->type == json_integer)
    {
        limit_net_speed(j_speed_limit->u.integer, j_speed_limit->u.integer);
        t1_publish_device_info(client_p);
        return 0;
    }

    json_value* j_month_flow = json_value_get(j_value, "monthFlow", 9);
    if (j_month_flow != NULL && j_month_flow->type == json_integer && j_month_flow->u.integer >= 0)
    {
        char flux_month_total[21] = {0};
        snprintf(flux_month_total, sizeof(flux_month_total), "%lld", j_month_flow->u.integer * 1024);
        cfg_set("flux_month_total", flux_month_total);
        t1_publish_device_info(client_p);
        return 0;
    }

    json_value* j_switch = json_value_get(j_value, "switch", 6);
    if (j_switch != NULL && j_switch->type == json_string)
    {
        int switch_crad_count = 0;
        while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
        {
            qrzl_log("正在测网，不能切卡");
            sleep(5);
            switch_crad_count++;
        }
        
        int ret;
        int tmp = atoi(j_switch->u.string.ptr);
        if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(1);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 3)
        {
            ret = switch_sim_card_not_restart(0);
        }
        
        if (tmp > 0 && tmp < 4 && ret == 0)
        {
            // 暂时不需要做任何操作
        }
        
        t1_publish_device_info(client_p);
        return 0;
    }
    
    json_value* j_heartbeat = json_value_get(j_value, "heartbeat", 9);
    if (j_heartbeat != NULL && j_heartbeat->type == json_string)
    {
        int tmp = atoi(j_heartbeat->u.string.ptr);
        if (tmp < 30)
        {
            qrzl_err("心跳间隔时间不能小于30秒");
        }
        else
        {
            t1_publish_interval = tmp;
        }
        
        t1_publish_device_info(client_p);
        return 0;
    }
    return 0;
}

int t1_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    json_value *j_value = json_parse((char*)message->payload, message->payloadlen);
    if (j_value != NULL)
    {
        if (j_value->type == json_object)
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            t1_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        json_value_free(j_value);
    }
    
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void t1_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    t1_mqtt_connect(client_p);
    t1_publish_device_info(client_p);
}

void* t1_publish_loop(void* client_ptr) {
    qrzl_log("t1_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    uint32_t now_sleep_time_total = 600;
    uint32_t sleep_time = 3;
    int current_conn_num = 10; // 默认给10，这是为了后面判断方便
    char sta_count[3] = {0};
    int ret;

    while (1) {
        now_sleep_time_total += sleep_time;
#ifdef JCV_HW_MZ804_V1_4
    ret = cfg_get_item("sta_count", sta_count, 3);
    if (ret == 0) {
        current_conn_num = atoi(sta_count);
    }
    if (current_conn_num > 0) {
        if (now_sleep_time_total >= t1_publish_interval) {
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    } else {
        if (now_sleep_time_total >= 600) {
            t1_publish_device_info(client_p);
            now_sleep_time_total = 0;
        }
    }
#else
    if (now_sleep_time_total >= t1_publish_interval) {
        t1_publish_device_info(client_p);
        now_sleep_time_total = 0;
    }
#endif

        sleep(sleep_time);
    }
    return NULL;
}

static void t1_cloud_client_start()
{
    update_device_static_data();
    
    if (pthread_mutex_init(&t1_msg_handler_lock, NULL) != 0) {
        qrzl_log("t1_msg_handler_lock init failed\n");
    }

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, t1_mqtt_connlost, t1_message_arrived, t1_on_message_delivered);

    t1_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, t1_publish_loop, &client) != 0) {
        qrzl_err("Failed to create publish thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);
    pthread_mutex_destroy(&t1_msg_handler_lock);
    return;
}

/* ========================================= end mqtt type 1类型的处理 ==================================================================== */

/* ========================================= start mqtt ky 类型的处理 ==================================================================== */

static uint64_t ky_last_publish_rx_byte_flow = 0;
static uint64_t ky_last_publish_tx_byte_flow = 0;
static int64_t ky_publish_interval_ms = 300 * 1000;
static pthread_t ky_shutdown_thread_id = 0;
static int ky_shutdown_time_s = 0; // 关机时间 秒

static void *ky_shutdown_thread(void *arg)
{
    qrzl_log("ky sleep %d s shutdown", ky_shutdown_time_s);
    sleep(ky_shutdown_time_s);
    qrzl_log("ky_shutdown_thread: 开始关机");
    shutdown_device();
    return NULL;
}

int ky_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    snprintf(topic, sizeof(topic), "mifiiot/%s/server", g_qrzl_device_static_data.sn);
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");

    snprintf(payload, sizeof(payload),
            "%s\"api_version\":\"1.0\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"config_version\":\"1.0\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"upload_data_type\":\"normal\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"cur_sim_id\":\"%d\"", payload, now_sim_index);
    // cur_sim_cato 不知道什么意思
    // snprintf(payload, sizeof(payload),
    //         "%s,\"cur_sim_cato\":\"%d\"", payload, -1);

    // sim_info start
    snprintf(payload, sizeof(payload),
            "%s,\"sim_info\":[", payload);

    int lte_rsrp = atoi(g_qrzl_device_dynamic_data.lte_rsrp);
    int sim_signal_level = 0;
    if (lte_rsrp >= -85) {
        sim_signal_level = 5;
    } else if (lte_rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (lte_rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (lte_rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (lte_rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    
    

    // ================== esim1 start ==================================
    snprintf(payload, sizeof(payload), "%s{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"1\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim1_mno);
    if (now_sim_index == 1)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // =================== esim1 end ==================================

    // =================== esim2 start ==================================
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"2\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim2_mno);
    if (now_sim_index == 2)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // ==================== esim2 end ==================================

    // ==================== rsim start ==================================
#ifdef QRZL_HAVE_3_ESIM_CARD
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim3_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim3_mno);
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
 #else   
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{", payload);
        snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_exist\":1", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_network_status\":1", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_romaining\":0", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_dynamic_data.imsi);
        snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
        snprintf(payload, sizeof(payload), "%s}", payload);
    }
    // ==================== rsim end ==================================
#endif
    snprintf(payload, sizeof(payload), "%s]", payload);
    // sim_info end 

    // system_info start
    snprintf(payload, sizeof(payload),
            "%s,\"system_info\":{", payload);

    double uptime_seconds = get_device_uptime();
    int hours = (int)(uptime_seconds / 3600);  // 转换为小时
    int minutes = (int)((uptime_seconds - (hours * 3600)) / 60);  // 剩余的分钟
    int seconds = (int)(uptime_seconds - (hours * 3600) - (minutes * 60));  // 剩余的秒数
    char sys_running_time[64] = {0};

    snprintf(sys_running_time, sizeof(sys_running_time), "%d:%d:%d", hours, minutes, seconds);
    snprintf(payload, sizeof(payload), "%s\"sys_running_time\":\"%s\"", payload, sys_running_time);
    char sys_current_time[64] = {0};

    get_local_time("%Y-%m-%d %H:%M:%S", sys_current_time, sizeof(sys_current_time));

    snprintf(payload, sizeof(payload), "%s,\"sys_current_time\":\"%s\"", payload, sys_current_time);
    snprintf(payload, sizeof(payload), "%s,\"sys_charge_status\":%d", payload, get_device_charge_status());
    snprintf(payload, sizeof(payload), "%s,\"sys_bat_level\":%s", payload, g_qrzl_device_dynamic_data.remain_power);
    snprintf(payload, sizeof(payload), "%s,\"sys_device_status\":%d", payload, g_qrzl_device_dynamic_data.user_net_disconn == 1 ? 0 : 1);
    
    snprintf(payload, sizeof(payload), "%s}", payload);
    // system_info end

    typedef struct {
        char mac[18];
        char ip[16];
    } ArpClient;
    
    ArpClient clients[32];
    int clients_nums = 0;

    FILE *arp_file;
    char arp_file_line[256] = {0};
    arp_file = fopen("/proc/net/arp", "r");
    if (arp_file == NULL) {
        qrzl_err("can't open /proc/net/arp file");
    } else {
        fgets(arp_file_line, sizeof(arp_file_line), arp_file);  // skip header

        while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
            char ip[16], mac[18], device[16], mask[16];
            unsigned int hw_type, flags;

            if (sscanf(arp_file_line, "%15s %x %x %17s %15s %15s", ip, &hw_type, &flags, mac, mask, device) != 6) {
                continue;
            }

            if (strlen(mac) == 17 && flags != 0x0) {
                strncpy(clients[clients_nums].mac, mac, sizeof(clients[clients_nums].mac));
                strncpy(clients[clients_nums].ip, ip, sizeof(clients[clients_nums].ip));
                clients_nums++;
            }
        }

        fclose(arp_file);
    }


    // hotspot_info start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"hotspot_connected_num\":%d", payload, clients_nums);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_wps_status\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_hide\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_on\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_name\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_connected_num_max\":%d", payload, g_qrzl_device_dynamic_data.max_access_num);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_encryption_mode\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_encryp_type);
    char gateway_ip[16] = {0};
    char lan_start_ip[16] = {0};
    char lan_end_ip[16] = {0};
    cfg_get_item("lan_ipaddr", gateway_ip, sizeof(gateway_ip));
    cfg_get_item("dhcpStart", lan_start_ip, sizeof(lan_start_ip));
    cfg_get_item("dhcpEnd", lan_end_ip, sizeof(lan_end_ip));

    snprintf(payload, sizeof(payload), "%s,\"hotspot_gateway_ip\":\"%s\"", payload, gateway_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_start_ip\":\"%s\"", payload, lan_start_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_end_ip\":\"%s\"", payload, lan_end_ip);
    
    // hotspot_connected_clients start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_connected_clients\":[", payload);

    int i;
    for (i = 0; i < clients_nums; i++) {
        if (i != 0) {
            snprintf(payload, sizeof(payload), "%s,", payload);
        }
        snprintf(payload, sizeof(payload), "%s{\"mac\":\"%s\"", payload, clients[i].mac);
        snprintf(payload, sizeof(payload), "%s,\"ip\":\"%s\"", payload, clients[i].ip);
        snprintf(payload, sizeof(payload), "%s,\"name\":\"\"}", payload);
    }

    // FILE *arp_file;
    // char arp_file_line[256] = {0};
    // int clients_nums = 0;
    // char station_mac[512] = {0};
    // cfg_get_item("station_mac", station_mac, sizeof(station_mac));
    // // if (strlen(station_mac) == 0)
    // // {   // 如果连接的设备为空，就不需要再去读取arp文件，并且如果station_mac为空,strstr函数不会返回NULL
    // //     qrzl_log("station_mac is empty");
    // // } else {
    //     // 打开 /proc/net/arp 文件
    //     arp_file = fopen("/proc/net/arp", "r");
    //     if (arp_file == NULL) {
    //         qrzl_err("cann't open /proc/net/arp file");
    //     } else {
    //         // 跳过文件头部
    //         fgets(arp_file_line, sizeof(arp_file_line), arp_file);

    //         // 读取每一行，格式：IP address, HW address, Flags, etc.
    //         while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
    //             char ip[16];
    //             char mac[18];
    //             char device[16];
    //             char mask[16];
    //             unsigned int hw_type, flags;
                

    //             // 解析一行内容
    //             if (sscanf(arp_file_line, "%s %x %x %s %s %s", ip, &hw_type, &flags, mac, &mask, &device) != 6) {
    //                 continue; // 解析失败，跳过
    //             }
    //             // 过滤掉无效的条目（比如不具备有效MAC地址的条目）和 没有真正连接WiFi的人
    //             if (strlen(mac) == 17 && flags != "0x0") {
    //                 if (clients_nums != 0) {
    //                     snprintf(payload, sizeof(payload), "%s,", payload);
    //                 }
    //                 snprintf(payload, sizeof(payload), "%s{\"mac\":\"%s\"", payload, mac);
    //                 snprintf(payload, sizeof(payload), "%s,\"ip\":\"%s\"", payload, ip);
    //                 snprintf(payload, sizeof(payload), "%s,\"name\":\"\"}", payload);
    //                 clients_nums++;
    //             }
    //         }

    //         // 关闭文件
    //         fclose(arp_file);
    //     }
    // }

    snprintf(payload, sizeof(payload), "%s]", payload);
    // hotspot_connected_clients end
    snprintf(payload, sizeof(payload), "%s}", payload);
    // hotspot_info end

    // network_info start
    snprintf(payload, sizeof(payload),
            "%s,\"network_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"network_ip_addr\":\"%s\"", payload, g_qrzl_device_dynamic_data.current_wan_ip);
    snprintf(payload, sizeof(payload), "%s,\"network_strength_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_tx_bytes - ky_last_publish_tx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_download_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_rx_bytes - ky_last_publish_rx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_up_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s,\"network_download_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_down_limit_net_speed() / 8));

    char auto_switch_esim_type[3] = {0};
	cfg_get_item("auto_switch_esim_type", auto_switch_esim_type, sizeof(auto_switch_esim_type));
    if (strcmp(auto_switch_esim_type, "1") == 0)
    {
        snprintf(payload, sizeof(payload), "%s,\"network_auto_slot\":1", payload);
    }
    else
    {
        snprintf(payload, sizeof(payload), "%s,\"network_auto_slot\":0", payload);
    }
    char sim_auth_mode[2] = {0};
    cfg_get_item("sim_auth_mode", sim_auth_mode, sizeof(sim_auth_mode));
    snprintf(payload, sizeof(payload), "%s,\"network_dual_sim\":%d", payload, strcmp("5", sim_auth_mode) == 0 ? 0 : 1);
    snprintf(payload, sizeof(payload), "%s}", payload);
    // network_info end

    // device_info start
    snprintf(payload, sizeof(payload),
            "%s,\"device_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"dev_imei1\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"dev_imei2\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload), "%s,\"dev_sn\":\"%s\"", payload, g_qrzl_device_static_data.sn);
    snprintf(payload, sizeof(payload), "%s,\"dev_mac\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    snprintf(payload, sizeof(payload), "%s,\"dev_sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload), "%s,\"dev_hw_version\":\"%s\"", payload, g_qrzl_device_static_data.hw_version);
    snprintf(payload, sizeof(payload), "%s,\"dev_mocor_sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload), "%s}", payload);
    // device_info end

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        // 这里并不能说明真的被推送了
        qrzl_log("Message published!");
        ky_last_publish_rx_byte_flow = g_qrzl_device_dynamic_data.realtime_rx_bytes;
        ky_last_publish_tx_byte_flow = g_qrzl_device_dynamic_data.realtime_tx_bytes;
    }

    return 0;
}

int ky_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 60;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
    snprintf(receive_topic, sizeof(receive_topic), "mifiiot/%s/client", g_qrzl_device_static_data.sn);

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}

int ky_cmd_handler(json_value* j_cmd, MQTTClient* client_p)
{
    if (j_cmd == NULL || j_cmd->type != json_object)
    {
        return -1;
    }
    json_value *j_cmd_name = json_value_get(j_cmd, "cmd_name", 8);
    if (j_cmd_name == NULL || j_cmd_name->type != json_string)
    {
        return -1;
    }
    qrzl_log("start exec cmd_name: %s", j_cmd_name->u.string.ptr);

    json_value *j_cmd_params = json_value_get(j_cmd, "cmd_params", 10);
    if (strcmp("reboot", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_delay_ms = json_value_get(j_cmd_params, "delay_ms", 8);
            if (j_delay_ms != NULL && j_delay_ms->type == json_integer)
            {
                int delay_ms = j_delay_ms->u.integer;
                if (delay_ms > 0)
                {
                    sleep_ms(delay_ms);
                }
            }
        }
        return restart_device();
    }

    else if (strcmp("power_off", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_delay_ms = json_value_get(j_cmd_params, "delay_ms", 8);
            if (j_delay_ms != NULL && j_delay_ms->type == json_integer)
            {
                int delay_ms = j_delay_ms->u.integer;
                if (delay_ms > 0)
                {
                    sleep_ms(delay_ms);
                }
            }
        }
        return shutdown_device();
    }

    else if (strcmp("factory_reset", j_cmd_name->u.string.ptr) == 0 || strcmp("recovery", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_delay_ms = json_value_get(j_cmd_params, "delay_ms", 8);
            if (j_delay_ms != NULL && j_delay_ms->type == json_integer)
            {
                int delay_ms = j_delay_ms->u.integer;
                if (delay_ms > 0)
                {
                    sleep(delay_ms / 1000);
                }
            }
        }
        return reset_device();
    }

    else if (strcmp("device_enable", j_cmd_name->u.string.ptr) == 0)
    {
        set_network_br0_disconnect(0);
        wifi_switch(1);
        limit_net_speed(0, 0);
        return 0;
    }

    else if (strcmp("device_disable", j_cmd_name->u.string.ptr) == 0)
    {
        set_network_br0_disconnect(1);
        wifi_switch(0);
        limit_net_speed(10, 10);
        return 0;
    }

    else if (strcmp("upload_config_modify", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_dataupload_interval = json_value_get(j_cmd_params, "dataupload_interval", 19);
            if (j_dataupload_interval != NULL && j_dataupload_interval->type == json_integer)
            {
                int64_t dataupload_interval = j_dataupload_interval->u.integer;
                if (dataupload_interval > 0)
                {
                    ky_publish_interval_ms = dataupload_interval;
                }
            }
        }
        return 0;
    }

    else if (strcmp("shutdown_timeout_modify", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            // 这里的单位时是分钟
            json_value* j_time = json_value_get(j_cmd_params, "time", 4);
            if (j_time != NULL && j_time->type == json_integer)
            {
                if (ky_shutdown_thread_id != 0) {
                    qrzl_log("cancel ky shutdown thread: %lu", ky_shutdown_thread_id);
                    pthread_cancel(ky_shutdown_thread_id);
                    ky_shutdown_thread_id = 0;
                }
                if (j_time->u.integer > 0)
                {
                    if (pthread_create(&ky_shutdown_thread_id, NULL, ky_shutdown_thread, NULL) != 0) {
                        qrzl_err("Failed to create shutdown thread");
                    } else {
                        qrzl_log("create ky shutdown thread: %lu", ky_shutdown_thread_id);
                        ky_shutdown_time_s = j_time->u.integer * 60;
                    }
                }
            }
        }
        return 0;
    }

    else if (strcmp("hotspot_on", j_cmd_name->u.string.ptr) == 0
    || strcmp("hotspot_on_24g", j_cmd_name->u.string.ptr) == 0)
    {
        wifi_switch(1);
        return 0;
    }

    else if (strcmp("hotspot_off", j_cmd_name->u.string.ptr) == 0
    || strcmp("hotspot_off_24g", j_cmd_name->u.string.ptr) == 0)
    {
        wifi_switch(0);
        return 0;
    }

    else if (strcmp("hotspot_modify", j_cmd_name->u.string.ptr) == 0)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_hotspot_name = json_value_get(j_cmd_params, "hotspot_name", 12);
            json_value* j_hotspot_password = json_value_get(j_cmd_params, "hotspot_password", 16);
            json_value* j_hotspot_is_hide = json_value_get(j_cmd_params, "hotspot_is_hide", 15);
            json_value* j_hotspot_connected_num_max = json_value_get(j_cmd_params, "hotspot_connected_num_max", 25);

            if (j_hotspot_name != NULL && j_hotspot_name->type == json_string)
            {
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_hotspot_name->u.string.ptr);
            }
            if (j_hotspot_password != NULL && j_hotspot_password->type == json_string)
            {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_hotspot_password->u.string.ptr);
            }
            if (j_hotspot_is_hide != NULL && j_hotspot_is_hide->type == json_integer)
            {
                wifi_config.hide = j_hotspot_is_hide->u.integer;
            }
            if (j_hotspot_connected_num_max != NULL && j_hotspot_connected_num_max->type == json_integer)
            {
                wifi_config.max_access_num = j_hotspot_connected_num_max->u.integer;
            }
            update_wifi_by_config(&wifi_config);
        }
        return 0;
    }

    else if (strcmp("network_speed_limit", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_limit_type = json_value_get(j_cmd_params, "type", 4);
            json_value* j_limit_unit = json_value_get(j_cmd_params, "unit", 4);
            json_value* j_limit_value = json_value_get(j_cmd_params, "value", 5);
            if (j_limit_type != NULL && j_limit_type->type == json_string
                && j_limit_unit != NULL && j_limit_unit->type == json_string
                && j_limit_value != NULL && j_limit_value->type == json_integer)
            {
                
                uint64_t limit_value = (uint64_t) j_limit_value->u.integer;
                if (limit_value < 0)
                {
                    qrzl_log("limit_value is less than 0");
                    return -1;
                }

                if (strcmp("B", j_limit_unit->u.string.ptr) == 0) {
                    limit_value = (limit_value * 8) / 1024;
                }
                else if (strcmp("KB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8);
                }
                else if (strcmp("MB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8 * 1024);
                }
                else if (strcmp("GB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8 * 1024 * 1024);
                }
                else
                {
                    qrzl_log("error limit unit");
                    return -1;
                }
                

                if (strcmp("all", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(limit_value, limit_value);
                }
                else if (strcmp("upload", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(limit_value, 0);
                }
                else if (strcmp("download", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(0, limit_value);
                }
                else
                {
                    qrzl_log("error limit type");
                    return -1;
                }
            }
        }
        return 0;
    }

    else if (strcmp("network_info", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_network_auto_solt = json_value_get(j_cmd_params, "network_auto_solt", 17);
            if (j_network_auto_solt != NULL && j_network_auto_solt->type == json_integer)
            {
                qrzl_log("Kuyu --> 下发智能寻网开关:%d", j_network_auto_solt->u.integer);
                if (j_network_auto_solt->u.integer == 1)
                {
                    cfg_set("auto_switch_esim_type", "1");
                }
                else
                {
                    cfg_set("auto_switch_esim_type", "0");
                }
            }

            json_value* j_network_dual_sim = json_value_get(j_cmd_params, "network_dual_sim", 17);
            if (j_network_dual_sim != NULL && j_network_dual_sim->type == json_integer)
            {
                if (j_network_dual_sim->u.integer == 1)
                {
                    cfg_set("sim_auth_mode", "3");
                }
                else
                {
                    cfg_set("sim_auth_mode", "5");
                }
            }
        }
        return 0;
    }

    else if (strcmp("sim_switch", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_sim_id = json_value_get(j_cmd_params, "sim_id", 6);
            if (j_sim_id != NULL && j_sim_id->type == json_integer)
            {
                int sim_id = j_sim_id->u.integer;
                // 酷鱼：0 外置卡； 1 卡槽2 ； 2 卡槽1
                qrzl_log("Kuyu 下发切卡指令 -->%d",sim_id);
                return switch_sim_card_not_restart(sim_id);
            }
        }
        return -1;
    }

    else if (strcmp("update_dev_info", j_cmd_name->u.string.ptr) == 0)
    {
        return ky_publish_device_info(client_p);
    }

    else if (strcmp("update_dev_info", j_cmd_name->u.string.ptr) == 0)
    {
        return ky_publish_device_info(client_p);
    }
    
    return 1;
}

int ky_order_msg_handler(json_value* j_value, MQTTClient* client_p)
{
    json_value *j_cmds = json_value_get(j_value, "cmds", 4);
    if (j_cmds == NULL || j_cmds->type != json_array)
    {
        return -1;
    }
    int i;

    for (i = 0; i < j_cmds->u.array.length; i++)
    {
        json_value *j_cmd = j_cmds->u.array.values[i];
        ky_cmd_handler(j_cmd, client_p);
    }
    return 0;
}

int ky_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    json_value *j_value = json_parse((char*)message->payload, message->payloadlen);
    if (j_value != NULL)
    {
        if (j_value->type == json_object)
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            ky_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        json_value_free(j_value);
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void ky_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    ky_mqtt_connect(client_p);
    ky_publish_device_info(client_p);
}

void ky_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}

void* ky_publish_loop(void* client_ptr) {
    qrzl_log("ky_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;
    uint32_t now_sleep_ms_total = 600 * 1000;
    uint32_t sleep_time_ms = 3 * 1000;
    int current_conn_num = 10; // 默认给10，这是为了后面判断方便
    char sta_count[3] = {0};
    int ret;

    while (1) {
        now_sleep_ms_total += sleep_time_ms;
#ifdef JCV_HW_MZ804_V1_4
        ret = cfg_get_item("sta_count", sta_count, 3);
        if (ret == 0) {
            current_conn_num = atoi(sta_count);
        }
        if (current_conn_num > 0) {
            if (now_sleep_ms_total >= ky_publish_interval_ms) {
                ky_publish_device_info(client_p);
                now_sleep_ms_total = 0;
            }
        } else {
            if (now_sleep_ms_total >= 600 * 1000) {
                ky_publish_device_info(client_p);
                now_sleep_ms_total = 0;
            }
        }
#else
    if (current_conn_num > 0) {
        if (now_sleep_ms_total >= ky_publish_interval_ms) {
            ky_publish_device_info(client_p);
            now_sleep_ms_total = 0;
        }
    }
#endif

        sleep_ms(sleep_time_ms);
    }
    return NULL;
}

static void ky_cloud_client_start()
{
    update_device_static_data();

    MQTTClient client;

    struct timeval tv;
    gettimeofday(&tv, NULL);
    char rand_str[10] = {0};
    generate_random_string(rand_str, sizeof(rand_str));
    char client_id[24] = {0};
    snprintf(client_id, sizeof(client_id), "%ld%ld%s", tv.tv_sec, tv.tv_usec, rand_str);

    MQTTClient_create(&client, mqtt_server, client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, ky_mqtt_connlost, ky_message_arrived, ky_on_message_delivered);

    ky_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, ky_publish_loop, &client) != 0) {
        qrzl_err("Failed to create ky_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}

/* ========================================= end mqtt ky 类型的处理 ==================================================================== */

/* ========================================= start mqtt yiming 类型的处理 ==================================================================== */

static uint64_t yiming_last_publish_rx_byte_flow = 0;
static uint64_t yiming_last_publish_tx_byte_flow = 0;
static int64_t yiming_publish_interval_ms = 300 * 1000;

// 移除mac地址的":"冒号
void remove_colons(const char *mac_with_colons, char *mac_clean, size_t len) {
    int j = 0, i = 0;
    for ( i ; mac_with_colons[i] != '\0' && j < len - 1; i++) {
        if (mac_with_colons[i] != ':') {
            mac_clean[j++] = mac_with_colons[i];
        }
    }
    mac_clean[j] = '\0';
}

int yiming_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    char hander_mac[32] = {0};
    remove_colons(g_qrzl_device_static_data.mac, hander_mac, sizeof(hander_mac));
    snprintf(topic, sizeof(topic), "public/device/out/%s", hander_mac);
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");

    snprintf(payload, sizeof(payload),
            "%s\"sw_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"sw_build_version\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"hw_version\":\"%s\"", payload, g_qrzl_device_static_data.hw_version);
    snprintf(payload, sizeof(payload),
            "%s,\"cur_sim_id\":\"%d\"", payload, now_sim_index);
    snprintf(payload, sizeof(payload),
            "%s,\"device_imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"device_mac\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    // cur_sim_cato 不知道什么意思
    // snprintf(payload, sizeof(payload),
    //         "%s,\"cur_sim_cato\":\"%d\"", payload, -1);

    // sim_info start
    snprintf(payload, sizeof(payload),
            "%s,\"sim_info\":[", payload);

    int lte_rsrp = atoi(g_qrzl_device_dynamic_data.lte_rsrp);
    int sim_signal_level = 0;
    if (lte_rsrp >= -85) {
        sim_signal_level = 5;
    } else if (lte_rsrp >= -95)
    {
        sim_signal_level = 4;
    } else if (lte_rsrp >= -105)
    {
        sim_signal_level = 3;
    } else if (lte_rsrp >= -115)
    {
        sim_signal_level = 2;
    } else if (lte_rsrp >= -199)
    {
        sim_signal_level = 1;
    }
    
    

    // ================== esim1 start ==================================
    snprintf(payload, sizeof(payload), "%s{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"1\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim1_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim1_mno);
    if (now_sim_index == 1)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // =================== esim1 end ==================================

    // =================== esim2 start ==================================
    snprintf(payload, sizeof(payload), "%s,{", payload);
    snprintf(payload, sizeof(payload), "%s\"sim_id\":\"2\"", payload);
    snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_iccid);
    snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_static_data.nvro_esim2_imsi);
    snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"%s\"", payload, g_qrzl_device_static_data.esim2_mno);
    if (now_sim_index == 2)
    {
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    }
    snprintf(payload, sizeof(payload), "%s}", payload);
    // ==================== esim2 end ==================================

    // ==================== rsim start ==================================
    if (now_sim_index == 0)
    {
        snprintf(payload, sizeof(payload), "%s,{", payload);
        snprintf(payload, sizeof(payload), "%s\"sim_id\":\"0\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
        snprintf(payload, sizeof(payload), "%s,\"sim_imsi\":\"%s\"", payload, g_qrzl_device_dynamic_data.imsi);
        snprintf(payload, sizeof(payload), "%s,\"sim_operator_info\":\"\"", payload);
        snprintf(payload, sizeof(payload), "%s,\"sim_signal_level\":%d", payload, sim_signal_level);
        snprintf(payload, sizeof(payload), "%s,\"sim_dbm\":\"%s\"", payload, g_qrzl_device_dynamic_data.rssi);
        snprintf(payload, sizeof(payload), "%s,\"sim_snr\":\"%s\"", payload, g_qrzl_device_dynamic_data.sinr);
        snprintf(payload, sizeof(payload), "%s,\"sim_tac\":\"%s\"", payload, g_qrzl_device_dynamic_data.tac);
        snprintf(payload, sizeof(payload), "%s,\"sim_lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
        snprintf(payload, sizeof(payload), "%s,\"sim_cell_id\":\"%s\"", payload, g_qrzl_device_dynamic_data.cid);
        snprintf(payload, sizeof(payload), "%s,\"sim_band_info\":\"%s\"", payload, g_qrzl_device_dynamic_data.net_band);
        snprintf(payload, sizeof(payload), "%s,\"sim_mcc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mcc);
        snprintf(payload, sizeof(payload), "%s,\"sim_mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
        snprintf(payload, sizeof(payload), "%s}", payload);
    }
    // ==================== rsim end ==================================

    snprintf(payload, sizeof(payload), "%s]", payload);
    // sim_info end 

    // system_info start
    snprintf(payload, sizeof(payload),
            "%s,\"system_info\":{", payload);

    double uptime_seconds = get_device_uptime();
    int hours = (int)(uptime_seconds / 3600);  // 转换为小时
    int minutes = (int)((uptime_seconds - (hours * 3600)) / 60);  // 剩余的分钟
    int seconds = (int)(uptime_seconds - (hours * 3600) - (minutes * 60));  // 剩余的秒数
    char sys_running_time[64] = {0};

    snprintf(sys_running_time, sizeof(sys_running_time), "%d:%d:%d", hours, minutes, seconds);
    snprintf(payload, sizeof(payload), "%s\"sys_running_time\":\"%s\"", payload, sys_running_time);
    char sys_current_time[64] = {0};

    get_local_time("%Y-%m-%d %H:%M:%S", sys_current_time, sizeof(sys_current_time));

    snprintf(payload, sizeof(payload), "%s,\"sys_current_time\":\"%s\"", payload, sys_current_time);
    snprintf(payload, sizeof(payload), "%s,\"sys_charge_status\":%d", payload, get_device_charge_status());
    snprintf(payload, sizeof(payload), "%s,\"sys_board_temperature\":%s", payload, g_qrzl_device_dynamic_data.board_temperature);
    snprintf(payload, sizeof(payload), "%s,\"sys_bat_level\":%s", payload, g_qrzl_device_dynamic_data.remain_power);

    snprintf(payload, sizeof(payload), "%s}", payload);
    // system_info end

    typedef struct {
        char mac[18];
        char ip[16];
    } ArpClient;
    
    ArpClient clients[32];
    int clients_nums = 0;

    FILE *arp_file;
    char arp_file_line[256] = {0};
    arp_file = fopen("/proc/net/arp", "r");
    if (arp_file == NULL) {
        qrzl_err("can't open /proc/net/arp file");
    } else {
        fgets(arp_file_line, sizeof(arp_file_line), arp_file);  // skip header

        while (fgets(arp_file_line, sizeof(arp_file_line), arp_file) != NULL) {
            char ip[16], mac[18], device[16], mask[16];
            unsigned int hw_type, flags;

            if (sscanf(arp_file_line, "%15s %x %x %17s %15s %15s", ip, &hw_type, &flags, mac, mask, device) != 6) {
                continue;
            }

            if (strlen(mac) == 17 && flags != 0x0) {
                strncpy(clients[clients_nums].mac, mac, sizeof(clients[clients_nums].mac));
                strncpy(clients[clients_nums].ip, ip, sizeof(clients[clients_nums].ip));
                clients_nums++;
            }
        }
        clients_nums -= 1; // 移除本机的计数，看客户需要，这里要去除，因为前端web是不显示本机的
        fclose(arp_file);
    }


    // hotspot_info start
    snprintf(payload, sizeof(payload),
            "%s,\"hotspot_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"hotspot_connected_num\":%d", payload, clients_nums);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_wps_status\":0", payload);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_hide\":%d", payload, g_qrzl_device_dynamic_data.wifi_hide);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_on\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_whether_enable\":\"%d\"", payload, g_qrzl_device_dynamic_data.wifi_enable);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_name\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_connected_num_max\":%d", payload, g_qrzl_device_dynamic_data.max_access_num);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_encryption_mode\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_encryp_type);
    char gateway_ip[16] = {0};
    char lan_start_ip[16] = {0};
    char lan_end_ip[16] = {0};
    cfg_get_item("lan_ipaddr", gateway_ip, sizeof(gateway_ip));
    cfg_get_item("dhcpStart", lan_start_ip, sizeof(lan_start_ip));
    cfg_get_item("dhcpEnd", lan_end_ip, sizeof(lan_end_ip));

    snprintf(payload, sizeof(payload), "%s,\"hotspot_gateway_ip\":\"%s\"", payload, gateway_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_start_ip\":\"%s\"", payload, lan_start_ip);
    snprintf(payload, sizeof(payload), "%s,\"hotspot_end_ip\":\"%s\"", payload, lan_end_ip);
    
    snprintf(payload, sizeof(payload), "%s}", payload);
    // hotspot_info end

    // network_info start
    snprintf(payload, sizeof(payload),
            "%s,\"network_info\":{", payload);
    snprintf(payload, sizeof(payload), "%s\"network_ip_addr\":\"%s\"", payload, g_qrzl_device_dynamic_data.current_wan_ip);
    // 网络连接状态
    snprintf(payload, sizeof(payload), "%s,\"network_status\":\"%s\"", payload, get_current_net_status());
    snprintf(payload, sizeof(payload), "%s,\"network_strength_status\":1", payload);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_tx_bytes - yiming_last_publish_tx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_download_dataflow\":\"%lldKB\"", payload,
        (g_qrzl_device_dynamic_data.realtime_rx_bytes - yiming_last_publish_rx_byte_flow) / 1024);
    snprintf(payload, sizeof(payload), "%s,\"network_upload_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_up_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s,\"network_download_speed_limit\":\"%.3lfKB\"", payload,
        ((double) get_down_limit_net_speed() / 8));
    snprintf(payload, sizeof(payload), "%s}", payload);
    // network_info end

    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        // 这里并不能说明真的被推送了
        qrzl_log("Message published!");
        yiming_last_publish_rx_byte_flow = g_qrzl_device_dynamic_data.realtime_rx_bytes;
        yiming_last_publish_tx_byte_flow = g_qrzl_device_dynamic_data.realtime_tx_bytes;
    }

    return 0;
}

int yiming_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    conn_opts.username = mqtt_username;
    conn_opts.password = mqtt_password;

    conn_opts.keepAliveInterval = 60;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        sleep(10);
    }

    char receive_topic[256] = {0};
    char hander_mac[32] = {0};
    remove_colons(g_qrzl_device_static_data.mac, hander_mac, sizeof(hander_mac));
    snprintf(receive_topic, sizeof(receive_topic), "public/device/in/%s", hander_mac);

    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    printf("Subscribed to topic: %s\n", receive_topic);
    return rc;
}

void* yiming_publish_loop(void* client_ptr) {
    qrzl_log("yiming_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;

    while (1) {
        yiming_publish_device_info(client_p);
        sleep_ms(yiming_publish_interval_ms);
    }
    return NULL;
}


int yiming_cmd_handler(json_value* j_cmd, MQTTClient* client_p)
{
    if (j_cmd == NULL || j_cmd->type != json_object)
    {
        return -1;
    }
    json_value *j_cmd_name = json_value_get(j_cmd, "cmd_name", 8);
    if (j_cmd_name == NULL || j_cmd_name->type != json_string)
    {
        return -1;
    }
    qrzl_log("start exec cmd_name: %s", j_cmd_name->u.string.ptr);

    json_value *j_cmd_params = json_value_get(j_cmd, "cmd_params", 10);
    if (strcmp("reboot", j_cmd_name->u.string.ptr) == 0)
    {
        return restart_device();
    }

    else if (strcmp("power_off", j_cmd_name->u.string.ptr) == 0)
    {
        return shutdown_device();
    }

    else if (strcmp("recovery", j_cmd_name->u.string.ptr) == 0)
    {
        return reset_device();
    }

    else if (strcmp("hotspot_on", j_cmd_name->u.string.ptr) == 0)
    {
        return wifi_switch(1);
    }

    else if (strcmp("hotspot_off", j_cmd_name->u.string.ptr) == 0)
    {
        return wifi_switch(0);
    }

    else if (strcmp("hotspot_modify", j_cmd_name->u.string.ptr) == 0)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_hotspot_name = json_value_get(j_cmd_params, "hotspot_name", 12);
            json_value* j_hotspot_password = json_value_get(j_cmd_params, "hotspot_password", 16);
            json_value* j_hotspot_is_hide = json_value_get(j_cmd_params, "hotspot_is_hide", 15);
            json_value* j_hotspot_connected_num_max = json_value_get(j_cmd_params, "hotspot_connected_num_max", 25);

            if (j_hotspot_name != NULL && j_hotspot_name->type == json_string)
            {
                snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_hotspot_name->u.string.ptr);
            }
            if (j_hotspot_password != NULL && j_hotspot_password->type == json_string)
            {
                snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_hotspot_password->u.string.ptr);
            }
            if (j_hotspot_is_hide != NULL && j_hotspot_is_hide->type == json_integer)
            {
                wifi_config.hide = j_hotspot_is_hide->u.integer;
            }
            if (j_hotspot_connected_num_max != NULL && j_hotspot_connected_num_max->type == json_integer)
            {
                wifi_config.max_access_num = j_hotspot_connected_num_max->u.integer;
            }
            update_wifi_by_config(&wifi_config);
        }
        return 0;
    }

    else if (strcmp("network_speed_limit", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_limit_type = json_value_get(j_cmd_params, "type", 4);
            json_value* j_limit_unit = json_value_get(j_cmd_params, "unit", 4);
            json_value* j_limit_value = json_value_get(j_cmd_params, "value", 5);
            if (j_limit_type != NULL && j_limit_type->type == json_string
                && j_limit_unit != NULL && j_limit_unit->type == json_string
                && j_limit_value != NULL && j_limit_value->type == json_integer)
            {
                
                uint64_t limit_value = (uint64_t) j_limit_value->u.integer;
                if (limit_value < 0)
                {
                    qrzl_log("limit_value is less than 0");
                    return -1;
                }

                if (strcmp("B", j_limit_unit->u.string.ptr) == 0) {
                    limit_value = (limit_value * 8) / 1024;
                }
                else if (strcmp("KB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8);
                }
                else if (strcmp("MB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8 * 1024);
                }
                else if (strcmp("GB", j_limit_unit->u.string.ptr) == 0)
                {
                    limit_value = (limit_value * 8 * 1024 * 1024);
                }
                else
                {
                    qrzl_log("error limit unit");
                    return -1;
                }
                

                if (strcmp("all", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(limit_value, limit_value);
                }
                else if (strcmp("upload", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(limit_value, 0);
                }
                else if (strcmp("download", j_limit_type->u.string.ptr) == 0)
                {
                    limit_net_speed(0, limit_value);
                }
                else
                {
                    qrzl_log("error limit type");
                    return -1;
                }
            }
        }
        return 0;
    }

    else if (strcmp("sim_switch", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_sim_id = json_value_get(j_cmd_params, "sim_id", 6);
            if (j_sim_id != NULL && j_sim_id->type == json_integer)
            {
                int sim_id = j_sim_id->u.integer;
                if (sim_id > 2) {
                    qrzl_log("yiming sim_id --> %d , 不可用。",sim_id);
                    return -1;
                }
                if (sim_id == 1 && g_qrzl_device_dynamic_data.slot_esim1_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , ESIM1 卡槽不可用。",sim_id);
                    return -1;
                } else if (sim_id == 2 && g_qrzl_device_dynamic_data.slot_esim2_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , ESIM2 卡槽不可用。",sim_id);
                    return -1;
                } else if (sim_id == 0 && g_qrzl_device_dynamic_data.slot_esim3_is_enable != 1) {
                    qrzl_log("yiming sim_id --> %d , 外置 卡槽不可用。",sim_id);
                    return -1;
                }
                // 0 外置卡； 1 卡槽1 ； 2 卡槽2
                qrzl_log("yiming 下发切卡指令 -->%d",sim_id);
                return switch_sim_card_not_restart(sim_id);
            }
        }
        return -1;
    }

    else if (strcmp("device_enable", j_cmd_name->u.string.ptr) == 0)
    {
        set_network_br0_disconnect(0);
        limit_net_speed(0, 0);
        return 0;
    }

    else if (strcmp("device_disable", j_cmd_name->u.string.ptr) == 0)
    {
        set_network_br0_disconnect(1);
        limit_net_speed(10, 10);
        return 0;
    }

    else if (strcmp("upload_config_modify", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_dataupload_interval = json_value_get(j_cmd_params, "dataupload_interval", 19);
            if (j_dataupload_interval != NULL && j_dataupload_interval->type == json_integer)
            {
                int64_t dataupload_interval = j_dataupload_interval->u.integer;
                if (dataupload_interval > 0)
                {
                    yiming_publish_interval_ms = dataupload_interval;
                    qrzl_log("yiming_publish_interval_ms now is %d", yiming_publish_interval_ms);
                }
            }
        }
        return 0;
    }

    // 启用或禁用卡槽
    else if (strcmp("enable_disable_slots", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_sim_ids = json_value_get(j_cmd_params, "sim_ids", 7);
            json_value* j_sim_disable = json_value_get(j_cmd_params, "disable", 7);
            if(j_sim_ids != NULL && j_sim_ids->type == json_array && j_sim_disable != NULL && j_sim_disable->type == json_integer) {
                int i;
                for (i = 0 ; i < j_sim_ids->u.array.length; i++)
                {
                    json_value* item = j_sim_ids->u.array.values[i];
                    if (item && item->type == json_string) {
                        qrzl_log("sim_ids-> i = %d, sim_id = %s", i, item->u.string.ptr);
                        set_slot_state(j_sim_disable->u.integer, atoi(item->u.string.ptr));
                    } else {
                        qrzl_log("sim_ids->%d is not a string or NULL", i);
                        return 0;
                    }
                }
            }
        }
        return 0;
    }

    // 隐藏或显示卡槽
    else if (strcmp("enable_hidden_slots", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_sim_id = json_value_get(j_cmd_params, "sim_id", 6);
            json_value* j_sim_hidden = json_value_get(j_cmd_params, "hidden", 6);
            if (j_sim_id != NULL && j_sim_id->type == json_integer && j_sim_hidden != NULL && j_sim_hidden->type == json_integer) {
                char sim_list_str[40] = {0};
                cfg_get_item("sim_select_num_type", sim_list_str, sizeof(sim_list_str));
                char sim_str[15] = {0};
                switch (j_sim_id->u.integer)
                {
                    case 1:
                        snprintf(sim_str, sizeof(sim_str), "ESIM1_only");
                        break;
                    case 2:
                        snprintf(sim_str, sizeof(sim_str), "ESIM2_only");
                        break;
                    case 0:
                        snprintf(sim_str, sizeof(sim_str), "RSIM_only");
                        break;
                    default:
                        break;
                }
                if (j_sim_hidden->u.integer == 0) {
                    // 隐藏卡槽
                    if(contains_type(sim_list_str, sim_str) == 1) {
                        qrzl_log("hidden slot %s ", sim_str);
                        remove_item_from_csv(sim_list_str, sim_str);
                    }
                } else if(j_sim_hidden->u.integer == 1) {
                    // 显示卡槽
                    if(contains_type(sim_list_str, sim_str) != 1) {
                        qrzl_log("show slot %s ", sim_str);
                        add_item_to_csv(sim_list_str, sim_str);
                    }
                }
                // 设置ESIM列表
                cfg_set("sim_select_num_type", sim_list_str);
            }
            return 0;
        }
        return 0;
    }

    else if (strcmp("netmode_switch", j_cmd_name->u.string.ptr) == 0)
    {
        if (j_cmd_params != NULL && j_cmd_params->type == json_object)
        {
            json_value* j_net_mode = json_value_get(j_cmd_params, "netmode", 6);
            if (j_net_mode != NULL && j_net_mode->type == json_integer) {
                if (j_net_mode->u.integer == 4) {
                    qrzl_log("netmode_switch-> 4G");
                    // 目前只有4G
                } else if (j_net_mode->u.integer == 5) {
                    qrzl_log("netmode_switch-> 5G");
                } else if (j_net_mode->u.integer == 6) {
                    qrzl_log("netmode_switch-> 4G/5G");
                }
            }
            return 0;
        }
        return 0;
    }

    else if (strcmp("update_dev_info", j_cmd_name->u.string.ptr) == 0)
    {
        return yiming_publish_device_info(client_p);
    }
    
    return 1;
}


int yiming_order_msg_handler(json_value* j_value, MQTTClient* client_p)
{
    json_value *j_cmds = json_value_get(j_value, "cmds", 4);
    if (j_cmds == NULL || j_cmds->type != json_array)
    {
        return -1;
    }
    int i;

    for (i = 0; i < j_cmds->u.array.length; i++)
    {
        json_value *j_cmd = j_cmds->u.array.values[i];
        yiming_cmd_handler(j_cmd, client_p);
    }
    return 0;
}

int yiming_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    json_value *j_value = json_parse((char*)message->payload, message->payloadlen);
    if (j_value != NULL)
    {
        if (j_value->type == json_object)
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            yiming_order_msg_handler(j_value, client_p);
        }
        // 释放 JSON 解析结果
        json_value_free(j_value);
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}

void yiming_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    yiming_mqtt_connect(client_p);
    yiming_publish_device_info(client_p);
}

void yiming_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}

static void yiming_cloud_client_start()
{
    update_device_static_data();

    MQTTClient client;

    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, yiming_mqtt_connlost, yiming_message_arrived, yiming_on_message_delivered);

    yiming_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, yiming_publish_loop, &client) != 0) {
        qrzl_err("Failed to create yiming_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}

/* ========================================= end mqtt yiming 类型的处理 ==================================================================== */

/* ========================================= start mqtt wuxing 类型的处理 ==================================================================== */
static int64_t wuxing_publish_interval_ms = 300 * 1000;
// 全局 NV 标志，表示当前是否使用新服务器
static int wuxing_new_server_flag = 0;
static int new_server_fail_count = 0;
//下面数组保存的是最近一次指令下发的MQTT更改信息，连接新地址要用
static char wuxing_new_mqtt_server[128] = {0};
static char wuxing_new_mqtt_username[128] = {0};
static char wuxing_new_mqtt_password[128] = {0};
int wuxing_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message);
void wuxing_mqtt_connlost(void *context, char *cause);
void wuxing_on_message_delivered(void* context, MQTTClient_deliveryToken dt);
static void *wuxing_mqtt_update_server(void *arg);
static void wuxing_revert_to_factory(MQTTClient *client_p);
static void timestamp_to_string(time_t timestamp, char *buffer, size_t buffer_size)
{
    // 将时间戳转换为 tm 结构
    struct tm *time_info = localtime(&timestamp);

    // 格式化时间为 yyyymmddhhmmss
    strftime(buffer, buffer_size, "%Y%m%d%H%M%S", time_info);
}
static void set_wuxing_new_server_flag(int flag)
{
    wuxing_new_server_flag = flag ? 1 : 0;      // 内存标志
    cfg_set("wuxing_new_server_flag", flag ? "1" : "0");  // 写 NV
    if (!flag) {
        new_server_fail_count = 0;             // 回退时清零失败计数
    }
}
// 字节转 MB
static inline double bytes_to_mb(uint64_t bytes)
{
    return bytes / (1024.0 * 1024.0);
}
//将出厂设置的MQTT信息赋给运行时的配置
static void get_factory_MQTT_info()
{
    cfg_get_item("qrzl_cloud_mqtt_server", mqtt_server, sizeof(mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_username", mqtt_username, sizeof(mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_password", mqtt_password, sizeof(mqtt_password));
}
int wuxing_publish_device_info(MQTTClient* client_p)
{
    if (!MQTTClient_isConnected(*client_p)) 
    {
        qrzl_log("MQTT not connected, don't publish device info");
        return -1;
    }
    update_device_dynamic_data();
    qrzl_log("开始推送信息至mqtt broker");
    char topic[256] = {0};
    snprintf(topic, sizeof(topic), "wxiot/up/3/%s/recinfo", g_qrzl_device_static_data.imei);
    char payload[2048] = {0};
    int now_sim_index = get_device_current_sim_index_by_data();
    snprintf(payload, sizeof(payload), "{");
    //CID暂时没给出来，随便填一个，dID同理
    snprintf(payload, sizeof(payload),
            "%s\"cId\":\"3\"", payload);
    snprintf(payload, sizeof(payload),
            "%s,\"dId\":\"%s\"", payload,g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"imei\":\"%s\"", payload, g_qrzl_device_static_data.imei);
    snprintf(payload, sizeof(payload),
            "%s,\"iccid\":\"%s\"", payload, g_qrzl_device_dynamic_data.iccid);
    snprintf(payload, sizeof(payload),
            "%s,\"us\":\"%.2f\"", payload,g_qrzl_device_dynamic_data.up_speed_bps / 1000000.0);//Mbps
    snprintf(payload, sizeof(payload),
            "%s,\"ds\":\"%.2f\"", payload,g_qrzl_device_dynamic_data.down_speed_bps / 1000000.0);

    uint64_t speedLimitUp = get_up_limit_net_speed();
    uint64_t speedLimitDown = get_down_limit_net_speed();
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimitUp\":\"%llu\"", payload, speedLimitUp * 1000 / 8);//byte
    snprintf(payload, sizeof(payload),
            "%s,\"speedLimitDown\":\"%llu\"", payload, speedLimitDown * 1000 / 8);//byte
    snprintf(payload, sizeof(payload),
            "%s,\"rsrp\":\"%s\"", payload, g_qrzl_device_dynamic_data.lte_rsrp);
    int main_sim = 0;
#ifdef QRZL_QICHENG_SWITCHCARD_ZERO_IS_OUTCARD
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 1;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 2;
    } else {
        main_sim = 0;
    }
#else
    if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM1_only") == 0) {
        main_sim = 0;
    } else if (strcmp(g_qrzl_device_dynamic_data.current_sim, "ESIM2_only") == 0) {
        main_sim = 1;
    } else {
        main_sim = 2;
    }
#endif
    //这个值不具有实时性
    snprintf(payload, sizeof(payload),
            "%s,\"mainSim\":\"%d\"", payload, main_sim);
    snprintf(payload, sizeof(payload),
            "%s,\"dualSim\":\"1\"", payload);
    int wifistatus = 1;
    if (g_qrzl_device_dynamic_data.wifi_enable == 0) {
        wifistatus = 0;
    } else if (g_qrzl_device_dynamic_data.wifi_enable == 1 && g_qrzl_device_dynamic_data.wifi_hide == 1)
    {
        wifistatus = 2;
    }
    snprintf(payload, sizeof(payload),
            "%s,\"wifiStatus\":\"%d\"", payload, wifistatus);
    char time_string[15] = {0}; // 长度需要能存储 "yyyymmddhhmmss" 和一个 '\0'
    time_t current_time = time(NULL);
    timestamp_to_string(current_time, time_string, sizeof(time_string));
    snprintf(payload, sizeof(payload),
            "%s,\"currentTime\":\"%s\"", payload, time_string);
    snprintf(payload, sizeof(payload),
            "%s,\"tem\":\"%s\"", payload, g_qrzl_device_dynamic_data.board_temperature);
    snprintf(payload, sizeof(payload),
            "%s,\"mnc\":\"%s\"", payload, g_qrzl_device_dynamic_data.mnc);
    snprintf(payload, sizeof(payload),
            "%s,\"lac\":\"%s\"", payload, g_qrzl_device_dynamic_data.lac);
    snprintf(payload, sizeof(payload),
            "%s,\"ssid\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_ssid);
    snprintf(payload, sizeof(payload),
            "%s,\"password\":\"%s\"", payload, g_qrzl_device_dynamic_data.wifi_key_base64);
    snprintf(payload, sizeof(payload),
            "%s,\"connectedNum\":\"%d\"", payload, g_qrzl_device_dynamic_data.conn_num);
    snprintf(payload, sizeof(payload),
            "%s,\"softwareVersion\":\"%s\"", payload, g_qrzl_device_static_data.soft_version);
    snprintf(payload, sizeof(payload),
            "%s,\"deviceModel\":\"%s\"", payload, g_qrzl_device_static_data.device_type);
    snprintf(payload, sizeof(payload),
            "%s,\"macAddress\":\"%s\"", payload, g_qrzl_device_static_data.mac);
    EsimFluxStat esim_fluxstat = get_esim_fluxstat();
    snprintf(payload, sizeof(payload),
            "%s,\"card1DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim1_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card1MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim1_flux_month_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card2DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim2_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card2MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.esim2_flux_month_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card3DailyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.rsim_flux_day_total));
    snprintf(payload, sizeof(payload),
            "%s,\"card3MonthlyFlow\":\"%.2f\"", payload, bytes_to_mb(esim_fluxstat.rsim_flux_month_total));
    char networkStatus[2]={0};
    int ret=cfg_get_item("qrzl_user_net_disconn", networkStatus, sizeof(networkStatus));
    if (ret != 0)  // 没获取到
    {
        snprintf(networkStatus, sizeof(networkStatus), "0");
    }   
    snprintf(payload, sizeof(payload),
            "%s,\"networkStatus\":\"%d\"", payload, !atoi(networkStatus));
    snprintf(payload, sizeof(payload),
            "%s,\"sn\":\"%s\"", payload, g_qrzl_device_static_data.sn);
    snprintf(payload, sizeof(payload),
            "%s,\"battery\":\"%s\"", payload, g_qrzl_device_dynamic_data.remain_power);
    int jugdeTelecom=get_current_isp();
    snprintf(payload, sizeof(payload),
            "%s,\"switchCustomTelecom\":\"%d\"", payload, jugdeTelecom == 3? 0 : 1);
    snprintf(payload, sizeof(payload),"%s,\"o\":\"\"", payload);//预留的
    snprintf(payload, sizeof(payload), "%s}", payload);

    qrzl_log("mqtt payload: %s", payload);

    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    pubmsg.payload = payload;
    pubmsg.payloadlen = (int)strlen(payload);
    pubmsg.qos = 0;
    pubmsg.retained = 0;

    MQTTClient_deliveryToken token;
    int rc = MQTTClient_publishMessage(*client_p, topic, &pubmsg, &token);
    if (rc != MQTTCLIENT_SUCCESS) {
        qrzl_err("Failed to publish message, return code %d", rc);
    } else {
        //这里并不能说明真的推送了
        qrzl_log("Message published!");
    }
    return 0;
}
int wuxing_mqtt_connect(MQTTClient* client_p)
{
    int rc;

    MQTTClient_connectOptions conn_opts = MQTTClient_connectOptions_initializer;
    if (wuxing_new_server_flag) {
        conn_opts.username = wuxing_new_mqtt_username;
        conn_opts.password = wuxing_new_mqtt_password;
    } else {
        conn_opts.username = mqtt_username;
        conn_opts.password = mqtt_password;
    }

    conn_opts.keepAliveInterval = 15;
    conn_opts.cleansession = 1;

    while ((rc = MQTTClient_connect(*client_p, &conn_opts)) != MQTTCLIENT_SUCCESS) {
        qrzl_log("MQTT connect failed with code %d. Retrying in 10 seconds...", rc);
        qrzl_log("MQTT connect username:%s,password:%s failed", conn_opts.username, conn_opts.password);
        sleep(10);
        //如果地址不是最初始的地址,就设置一个次数
        if(wuxing_new_server_flag == 1)
        {
            new_server_fail_count++;
            if(new_server_fail_count >= 3)
            {
                pthread_t thread_time_out;
                pthread_create(&thread_time_out, NULL, wuxing_revert_to_factory, (void *)client_p);
                pthread_detach(thread_time_out);  // 让线程自动回收
                return -1; // 直接退出当前连接流程
            }
        }
    }

    char receive_topic[256] = {0};
    snprintf(receive_topic, sizeof(receive_topic), "wxiot/down/3/%s", g_qrzl_device_static_data.imei);
    qrzl_log("mqtt connected successfully!");
    if ((rc = MQTTClient_subscribe(*client_p, receive_topic, 1)) != MQTTCLIENT_SUCCESS)
    {
        qrzl_log("Failed to subscribe to topic, return code %d", rc);
    }

    qrzl_log("Subscribed to topic: %s", receive_topic);
    return rc;
}
void* wuxing_publish_loop(void* client_ptr) {
    qrzl_log("wuxing_publish_loop start");
    MQTTClient* client_p = (MQTTClient*)client_ptr;

    while (1) {
        wuxing_publish_device_info(client_p);
        sleep_ms(wuxing_publish_interval_ms);
    }
    return NULL;
}
static void wuxing_revert_to_factory(MQTTClient *client_p) {
    sleep(10);
    qrzl_log("回退MQTT到出厂版本...");
    get_factory_MQTT_info();
    //进到回退这里认为在旧地址
    set_wuxing_new_server_flag(0);
    new_server_fail_count = 0;

    // 断开并销毁旧连接
    MQTTClient_disconnect(*client_p, 0);
    MQTTClient_destroy(client_p);
    qrzl_log("reconnect target :%s", mqtt_server);
    // 重建客户端
    MQTTClient_create(client_p, mqtt_server, g_qrzl_device_static_data.imei,
                      MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(*client_p, client_p, 
                            wuxing_mqtt_connlost, 
                            wuxing_message_arrived, 
                            wuxing_on_message_delivered);

    // 重新连接
    wuxing_mqtt_connect(client_p);
    wuxing_publish_device_info(client_p);
}
int wuxing_cmd_handler(json_value* j_value, MQTTClient* client_p)
{
    int ret;

    json_value* j_switch = json_value_get(j_value, "simSwitch", 9);
    if (j_switch != NULL && j_switch->type == json_string && j_switch->u.string.ptr)
    {
        int switch_crad_count = 0;
        while (g_qrzl_device_dynamic_data.is_test_net == 1 && switch_crad_count < 30)
        {
            qrzl_log("正在测网，不能切卡");
            sleep(5);
            switch_crad_count++;
        }
        
        int ret;
        int tmp = atoi(j_switch->u.string.ptr);
        if (tmp == 0)
        {
            ret = switch_sim_card_not_restart(1);
        }
        else if (tmp == 1)
        {
            ret = switch_sim_card_not_restart(2);
        }
        else if (tmp == 2)
        {
            ret = switch_sim_card_not_restart(0);
        }
        
        if (tmp > 0 && tmp < 4 && ret == 0)
        {
            // 暂时不需要做任何操作
        }
    }
    
    json_value *j_ssid = json_value_get(j_value, "ssid", 4);
    if (j_ssid != NULL && j_ssid->type == json_string && j_ssid->u.string.ptr)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.ssid, sizeof(wifi_config.ssid), "%s", j_ssid->u.string.ptr);
        update_wifi_by_config(&wifi_config);
    }
    json_value *j_password = json_value_get(j_value, "password", 8);
    if (j_password != NULL && j_password->type == json_string && j_password->u.string.ptr)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        snprintf(wifi_config.key, sizeof(wifi_config.key), "%s", j_password->u.string.ptr);
        update_wifi_by_config(&wifi_config);
    }
    json_value *j_hidden = json_value_get(j_value, "hidden", 6);
    if (j_hidden != NULL && j_hidden->type == json_string && j_hidden->u.string.ptr)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        if(strcmp(j_hidden->u.string.ptr, "0") == 0){
            wifi_config.enable = 0;
        }else if (strcmp(j_hidden->u.string.ptr, "1") == 0)
        {
            wifi_config.enable = 1;
            wifi_config.hide = 0;
        }else if (strcmp(j_hidden->u.string.ptr, "2") == 0)
        {
            wifi_config.enable = 1;
            wifi_config.hide = 1;
        }
        update_wifi_by_config(&wifi_config);
    }
    json_value *j_reboot = json_value_get(j_value, "reboot", 6);
    if (j_reboot != NULL && j_reboot->type == json_string && j_reboot->u.string.ptr)
    {
        if (strcmp(j_reboot->u.string.ptr, "1") == 0)
        {
            qrzl_log("强制重启设备");
            ret = restart_device();
        }
    }
    json_value *j_speedDownLink=json_value_get(j_value, "speedDownLink", 13);
    if (j_speedDownLink != NULL && j_speedDownLink->type == json_string && j_speedDownLink->u.string.ptr)
    {
        uint64_t limit_speed=atoi(j_speedDownLink->u.string.ptr);
        uint64_t up_limit_speed=get_up_limit_net_speed();
        limit_net_speed(up_limit_speed, limit_speed * 8 /1000);
    }
    json_value *j_speedUpLink=json_value_get(j_value, "speedUpLink", 11);
    if (j_speedUpLink != NULL && j_speedUpLink->type == json_string && j_speedUpLink->u.string.ptr)
    {
        uint64_t limit_speed=atoi(j_speedUpLink->u.string.ptr);
        uint64_t down_limit_speed=get_down_limit_net_speed();
        limit_net_speed(down_limit_speed, 0);
    }
    json_value *j_nextRptTime=json_value_get(j_value, "nextRptTime", 11);
    if (j_nextRptTime != NULL && j_nextRptTime->type == json_string && j_nextRptTime->u.string.ptr)
    {   
        uint64_t interval_time = atoi(j_nextRptTime->u.string.ptr);
        if(0 < interval_time)
        {
            wuxing_publish_interval_ms = interval_time * 1000;
        }
    }
    json_value *j_firmwareUrl=json_value_get(j_value, "firmwareUrl", 11);
    if (j_firmwareUrl != NULL && j_firmwareUrl->type == json_string && j_firmwareUrl->u.string.ptr)
    {   
        //不做处理
        qrzl_log("ota_test");
    }
    json_value* j_maxConnection = json_value_get(j_value, "maxConnection", 13);
    if (j_maxConnection != NULL && j_maxConnection->type == json_string && j_maxConnection->u.string.ptr)
    {
        struct wifi_config_t wifi_config = {};
        init_wifi_config_value(&wifi_config);
        wifi_config.max_access_num = atoi(j_maxConnection->u.string.ptr);
        update_wifi_by_config(&wifi_config);
    }
    json_value* j_resetDevice = json_value_get(j_value, "resetDevice", 11);
    if (j_resetDevice != NULL && j_resetDevice->type == json_string && j_resetDevice->u.string.ptr)
    {
        if(atoi(j_resetDevice->u.string.ptr))
        {
            qrzl_log("强制重置设备");
            reset_device();
        }
    }
    json_value* j_pseudoDisnetwork = json_value_get(j_value, "pseudoDisnetwork", 16);
    if (j_pseudoDisnetwork != NULL && j_pseudoDisnetwork->type == json_string && j_pseudoDisnetwork->u.string.ptr)
    {
        if(atoi(j_pseudoDisnetwork->u.string.ptr) == 1)
        {
            qrzl_log("强制断开网络");
            set_network_br0_disconnect(1);
        }
        else if(atoi(j_pseudoDisnetwork->u.string.ptr) == 0)
        {
            qrzl_log("恢复网络连接");
            set_network_br0_disconnect(0);
        }
        else
        {
            qrzl_log("pseudoDisnetwork值无效");
        }
    }
    json_value* j_host = json_value_get(j_value, "host", 4);
    json_value* j_userName = json_value_get(j_value, "userName", 8);
    json_value* j_mqPwd = json_value_get(j_value, "mqPwd", 6);
    if (j_host != NULL && j_host->type == json_string && j_userName != NULL && j_userName->type == json_string 
        && j_mqPwd != NULL && j_mqPwd->type == json_string && j_host->u.string.ptr && j_userName->u.string.ptr && j_mqPwd->u.string.ptr)
    {
        snprintf(wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server), "%s", j_host->u.string.ptr);
        snprintf(wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username), "%s", j_userName->u.string.ptr);
        snprintf(wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password), "%s", j_mqPwd->u.string.ptr);
        qrzl_log("wuxing mqtt new server: %s, username: %s, password: %s", wuxing_new_mqtt_server, wuxing_new_mqtt_username, wuxing_new_mqtt_password);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_server", wuxing_new_mqtt_server);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_username", wuxing_new_mqtt_username);
        cfg_set("qrzl_cloud_mqtt_wuxing_new_password", wuxing_new_mqtt_password);
        // 创建断开线程
        pthread_t thread_receive;
        pthread_create(&thread_receive, NULL, wuxing_mqtt_update_server, (void *)client_p);
        pthread_detach(thread_receive);  // 让线程自动回收
    }
    return 0;
}
int wuxing_order_msg_handler(json_value* j_value, MQTTClient* client_p)
{
    json_value *j_uuid = json_value_get(j_value, "uuid", 4);
    json_value *j_t=json_value_get(j_value, "t", 1);
    json_value *j_o=json_value_get(j_value, "o", 1);//预留的
    if (j_uuid == NULL || j_uuid->type != json_string || j_t == NULL || j_t->type != json_string)
    {
        qrzl_log("公共参数未正常接收,不做命令处理");
        return -1;
    }
        qrzl_log("总计指令数:%d", j_value->u.object.length);
        wuxing_cmd_handler(j_value, client_p);
    
    return 0;
}
int wuxing_message_arrived(void* context, char* topicName, int topicLen, MQTTClient_message* message)
{
    qrzl_log("Message arrived on topic: %s", topicName);
    qrzl_log("Message: %.*s", message->payloadlen, (char*)message->payload);
    MQTTClient* client_p = (MQTTClient*)context;

    json_value *j_value = json_parse((char*)message->payload, message->payloadlen);
    if (j_value != NULL)
    {
        if (j_value->type == json_object)
        {
            qrzl_err("json object normal");
            update_device_dynamic_data();
            wuxing_order_msg_handler(j_value, client_p);
        }
            json_value_free(j_value);
    }else
    {
        qrzl_log("收到的不是JSON体");
        if(wuxing_new_server_flag == 1)
        {
            if(++new_server_fail_count >= 3)
            {
                pthread_t thread_NO_JSON;
                pthread_create(&thread_NO_JSON, NULL, wuxing_revert_to_factory, (void *)client_p);
                pthread_detach(thread_NO_JSON);  // 让线程自动回收
            }
        }
    }

    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);

    return 1;
}
void wuxing_mqtt_connlost(void *context, char *cause)
{
    qrzl_log("MQTT Connection lost. cause: %s", cause);

    qrzl_log("Attempting to reconnect...");
    MQTTClient* client_p = (MQTTClient*)context;
    wuxing_mqtt_connect(client_p);
    wuxing_publish_device_info(client_p);
}
void wuxing_on_message_delivered(void* context, MQTTClient_deliveryToken dt)
{
    // 这里才算真的被推送成功
    qrzl_log("Message with token %d delivered", dt);
}
static void *wuxing_mqtt_update_server(void *arg)
{
    //进到更改地址线程就认为在新地址了
    set_wuxing_new_server_flag(1);
    sleep(10);
    MQTTClient *client_p = (MQTTClient *)arg;  // 进行类型转换
    qrzl_log("in thread disconn MQTT ...");
    MQTTClient_disconnect(*client_p, 0);
    MQTTClient_destroy(client_p);
    // 重新设置MQTT的服务器信息
    MQTTClient_create(client_p, wuxing_new_mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    MQTTClient_setCallbacks(*client_p, client_p, wuxing_mqtt_connlost, wuxing_message_arrived, wuxing_on_message_delivered);
    // 连接MQTT服务器
    wuxing_mqtt_connect(client_p);
    


    wuxing_publish_device_info(client_p);
    return NULL;
}
static void wuxing_cloud_client_start()
{
    update_device_static_data();


    char flag_buf[2] = {0};
    //先看看有没有修改过的地址历史
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_server", wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_username", wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_wuxing_new_password", wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password));
    cfg_get_item("wuxing_new_server_flag", flag_buf, sizeof(flag_buf));
    wuxing_new_server_flag = atoi(flag_buf);

    if (wuxing_new_server_flag == 1 && wuxing_new_mqtt_server[0] != '\0')
    {

        strncpy(mqtt_server, wuxing_new_mqtt_server, sizeof(wuxing_new_mqtt_server));
        strncpy(mqtt_username, wuxing_new_mqtt_username, sizeof(wuxing_new_mqtt_username));
        strncpy(mqtt_password, wuxing_new_mqtt_password, sizeof(wuxing_new_mqtt_password));
    }
    else
    {
        get_factory_MQTT_info();
        wuxing_new_server_flag = 0;
    }
    MQTTClient client;
    
    MQTTClient_create(&client, mqtt_server, g_qrzl_device_static_data.imei, MQTTCLIENT_PERSISTENCE_NONE, NULL);

    MQTTClient_setCallbacks(client, &client, wuxing_mqtt_connlost, wuxing_message_arrived, wuxing_on_message_delivered);

    wuxing_mqtt_connect(&client);

    pthread_t pub_thread;
    if (pthread_create(&pub_thread, NULL, wuxing_publish_loop, &client) != 0) {
        qrzl_err("Failed to create wuxing_publish_loop thread");
    }

    while (1)
    {
        sleep(1); // 主循环间隔
    }

    MQTTClient_disconnect(client, 10000);
    MQTTClient_destroy(&client);

    return;
}
/* ========================================= end mqtt wuxing 类型的处理 ==================================================================== */
void* start_mqtt_control_client()
{
    qrzl_log("start_mqtt_control_client");
    char mqtt_request_type[20] = {0};
    cfg_get_item("qrzl_cloud_mqtt_type", mqtt_request_type, 20);
    cfg_get_item("qrzl_cloud_mqtt_server", mqtt_server, sizeof(mqtt_server));
    cfg_get_item("qrzl_cloud_mqtt_username", mqtt_username, sizeof(mqtt_username));
    cfg_get_item("qrzl_cloud_mqtt_password", mqtt_password, sizeof(mqtt_password));

    qrzl_log("qrzl_cloud_mqtt_type: %s, server: %s, username: %s, password: %s", mqtt_request_type, mqtt_server, mqtt_username, mqtt_password);

    if (strncmp(mqtt_request_type, "MQTT_TYPE_1", sizeof(mqtt_request_type)) == 0)
    {
        qrzl_log("开始 mqtt mqtt_type_1格式云接口处理");
        t1_cloud_client_start();
    }
    else if (strcmp("KY", mqtt_request_type) == 0)
    {
        qrzl_log("开始 mqtt KY 格式云接口处理");
        ky_cloud_client_start();
    }
    else if (strcmp("MQTT_YIMING", mqtt_request_type) == 0)
    {
        qrzl_log("开始 mqtt MQTT_YIMING 格式云接口处理");
        yiming_cloud_client_start();
    }
     else if (strcmp("MQTT_WUXING", mqtt_request_type) == 0)
    {
        qrzl_log("开始 mqtt MQTT_WUXING 格式云接口处理");
        wuxing_cloud_client_start();
    }
    else if (strcmp("XUNYOU", mqtt_request_type) == 0)
    {
        qrzl_log("开始 mqtt XUNYOU 格式云接口处理");
	    pthread_t xunyou_tid;
        int err = pthread_create(&xunyou_tid, NULL, start_xunyou_mqtt_control_client, NULL);
		if (err != 0) {
			qrzl_err("创建start_xunyou_mqtt_control_client 线程失败, error code: %d", err);
		}
    }
    else
    {
        qrzl_log("qrzl_cloud_mqtt_type: %s, 没有匹配当前支持的类型，start_mqtt_control_client线程退出");
    }
    return NULL;
}