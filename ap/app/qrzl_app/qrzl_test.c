#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <arpa/inet.h>

#define LEASE_FILE "/etc_rw/udhcpd.leases"

struct dhcp_lease {
    uint32_t expires;   // Lease expiration (epoch time)
    uint8_t mac[6];     // MAC address
    uint32_t ip;        // IP address (network byte order)
    char hostname[20];  // Hostname (null-terminated, up to 20 bytes)
    uint8_t reserved[4];// Reserved (usually unused)
};

// Function to convert MAC address to human-readable format
void mac_to_str(uint8_t *mac, char *buf, size_t size) {
    snprintf(buf, size, "%02X:%02X:%02X:%02X:%02X:%02X",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
}

// Function to convert IP address to string
void ip_to_str(uint32_t ip, char *buf, size_t size) {
    struct in_addr addr;
    addr.s_addr = ip;
    inet_ntop(AF_INET, &addr, buf, size);
}

static void print_byte_array(const unsigned char *data, size_t len) {
	size_t i;
    for (i = 0; i < len; i++) {
        printf("0x%02X ", data[i]);
    }
    printf("\n");
}

int main() {
    FILE *fp = NULL;/*lint !e63*/
	typedef struct _DHCPOFFERINFO {
		unsigned long expires;
		unsigned long ip;
		unsigned char mac[6];
		unsigned char host_name[20];
		unsigned char pad[2];
	} DHCPOFFERINFO;
    struct in_addr addr;/*lint !e1080 !e565 */
	DHCPOFFERINFO addrlist;
	int64_t written_at;/*lint !e522*/
	int i = 0;
	memset(&addrlist, 0, sizeof(addrlist));
	fp = fopen(LEASE_FILE, "r"); /*lint !e63*/

	if (NULL == fp) {
		printf("errr\n");
        return -1;
	}
	if (fread(&written_at, 1, sizeof(written_at), fp) != sizeof(written_at)) {
		printf("errr\n");
        return -1;
	}

	while (fread(&addrlist, 1, sizeof(addrlist), fp) == sizeof(addrlist)) {
        addr.s_addr = addrlist.ip;	/*lint !e115 !e1013 !e63 */
		printf("ip: %s, %02x:%02x:%02x:%02x:%02x:%02x, hostname: %s\n", inet_ntoa(addr),
             addrlist.mac[0], addrlist.mac[1], addrlist.mac[2], addrlist.mac[3], addrlist.mac[4], addrlist.mac[5],
             addrlist.host_name);
        print_byte_array(addrlist.host_name, sizeof(addrlist.host_name));
	}

//	if (NULL != fp) {  // kw 3
		fclose(fp);
//	}
    return 0;
}
