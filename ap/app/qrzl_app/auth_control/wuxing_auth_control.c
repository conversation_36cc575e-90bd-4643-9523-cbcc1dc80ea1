/**
 * Yang
 * 
 * WUXING 定制认证方式
 * 
 * 全部对接他们家服务器的接口，无需对接运营商接口
 * 
 * 认证地址由他们返回，回调地址由我们传入
 */

#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <wolfssl/wolfcrypt/sha.h>
#include <ctype.h>
#include <sys/time.h>

#include "../cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"
#include "wuxing_auth_control.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define MIN_INTERVAL_MS 2000  // 最小调用间隔：2秒 ，防抖时间（认证线程会频繁探测网络导致一秒内调用多次请求）

static const char *wuxing_appkey = "mRajDQkyeb55CtVamyBUkfaFfurom85X";
static const char *wuxing_secret = "f17b56fc029d473690ccda5986201a2a";
static char *nonce = "99999";
static char wuxing_timestamp[64] = {0};

static char cmp_customers_get_token_url[256] = {0};

// 上一次的 MAC 列表（分号分隔）
static char last_station_mac[2048] = {0};  

// 记录上次调用的时间
static unsigned long last_request_time_ms = 0;

// 节流控制结构体
typedef struct {
    unsigned long last_call_time_ms;
    unsigned int min_interval_ms;
} ThrottleCtrl;

typedef struct {
    const char *key;
    const char *value;
} kv_pair;


static int cmp_kv_pair(const void *a, const void *b) {
    const kv_pair *pa = (const kv_pair *)a;
    const kv_pair *pb = (const kv_pair *)b;
    return strcmp(pa->key, pb->key);
}

static ThrottleCtrl wuxing_get_url_ctrl = {0, MIN_INTERVAL_MS};
static ThrottleCtrl wuxing_get_authed_ctrl = {0, MIN_INTERVAL_MS};

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response, int type) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        // 设置HTTP头
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
        // 设置公共参数
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (headers) {
            curl_slist_free_all(headers);
        }
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 通用的POST请求函数, type=1表示 将电信签名加入到请求头中
static int https_send_post_request_common(const char *url, const char* body, char *response, int type)
{
    CURL *curl;
    CURLcode res;

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("init CURL failed!!");
        return -1;
    }

    qrzl_log("request url: %s", url);
    qrzl_log("request body: %s", body);

    // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    // 设置SSL
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("curl_easy_perform request failed!");
    }

    if (headers) {
        curl_slist_free_all(headers);
    }
    // 清理内存
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }

    qrzl_log("request response: %s\n", response);

    return 0;
}

// 返回当前时间，单位为毫秒
static unsigned long get_timestamp_ms() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (tv.tv_sec * 1000UL) + (tv.tv_usec / 1000UL);
}

static int can_call(ThrottleCtrl *ctrl) {
    unsigned long now = get_timestamp_ms();
    if (ctrl->last_call_time_ms == 0 || 
        (now - ctrl->last_call_time_ms) >= ctrl->min_interval_ms) {
        ctrl->last_call_time_ms = now;
        return 1;  // 可以调用
    }
    return 0;  // 禁止调用（太频繁）
}

// =================================WUXING 认证信息获取 start======================================================

/**
 * 通过get拼接参数并排序，再进行sha1加密计算 （WUXING客户）
 * @param keys 参与排序的键列表
 * @param values 参与排序的值列表
 * @param secret_str 密钥，拼接在最后
 * @param out_sign sign
 * @param sign_buf_len sign 长度
 */
static int generate_sign(const char **keys, const char **values, int count, const char *secret_str, char *out_sign, int sign_buf_len) {
    if (!keys || !values || !secret_str || !out_sign || count <= 0 || sign_buf_len <= 0) return -1;

    kv_pair *pairs = malloc(sizeof(kv_pair) * count);
    if (!pairs) return -1;

    int actual_count = 0;
    int i;
    for (i = 0; i < count; ++i) {
        if (keys[i] && values[i] && keys[i][0] != '\0' && values[i][0] != '\0') {
            pairs[actual_count].key = keys[i];
            pairs[actual_count].value = values[i];
            actual_count++;
        }
    }

    qsort(pairs, actual_count, sizeof(kv_pair), cmp_kv_pair);

    char *buffer = malloc(2048);
    if (!buffer) {
        free(pairs);
        return -1;
    }
    buffer[0] = '\0';
    int j;
    for (j = 0; j < actual_count; ++j) {
        strncat(buffer, pairs[j].key, 512);
        strncat(buffer, "=", 1);
        strncat(buffer, pairs[j].value, 512);
        if (j < actual_count - 1) {
            strncat(buffer, "&", 1);
        }
    }

    strncat(buffer, "&secret=", 9);
    strncat(buffer, secret_str, 512);

    qrzl_log("请求参数排序后的parmes: %s\n", buffer);

    // 使用 wolfSSL 的 SHA1
    Sha sha;
    unsigned char hash[SHA_DIGEST_SIZE]; // 20字节
    wc_InitSha(&sha);
    wc_ShaUpdate(&sha, (const byte *)buffer, strlen(buffer));
    wc_ShaFinal(&sha, hash);

    int n;
    for (n = 0; n < SHA_DIGEST_SIZE; ++n) {
        snprintf(out_sign + n * 2, sign_buf_len - n * 2, "%02X", hash[n]);
    }

    free(pairs);
    free(buffer);
    return 0;
}

/**
 * 获取自定义认证页面 （WUXING客户）
 * @param mac 设备MAC
 * @param current_iccid 当前ICCID
 * @param terminalMac 终端MAC（手机电脑...）
 * @param nonce 随机五位字符
 * @param timestamp 时间戳
 * @param callback_url 回调地址
 * @param url_sign 通过此请求的参数计算出的sign
 * @return 0 成功，-1 失败
 */
CurlResult wuxing_get_customers_page_url(const char *mac, const char *current_iccid, const char *terminalMac, char *nonce, char *timestamp, char *callback_url, const char *url_sign)
{   
    CurlResult curl_result;
    strcpy(curl_result.code, "-1");
    strcpy(curl_result.msg, "");

    int cfg_ret = cfg_get_item("cmp_customers_get_token_url", cmp_customers_get_token_url, 256);
    if (cfg_ret != 0 || cmp_customers_get_token_url == NULL || strcmp(cmp_customers_get_token_url, "") == 0)
    {
        qrzl_log("cmp_customers_get_token_url is NULL");
        snprintf(curl_result.code, sizeof(curl_result.code), "-1");
        snprintf(curl_result.msg, sizeof(curl_result.msg), "接口配置为空");
        return curl_result;
    }

    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = "/getRealNamePage";

    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", cmp_customers_get_token_url, inteface_str);

    // 构造请求体
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac);
    cJSON_AddStringToObject(root, "cardId", current_iccid);
    cJSON_AddStringToObject(root, "servAddr", callback_url);
    cJSON_AddStringToObject(root, "appkey", wuxing_appkey);
    cJSON_AddStringToObject(root, "nonce", nonce);
    cJSON_AddStringToObject(root, "timestamp", timestamp);
    cJSON_AddStringToObject(root, "sign", url_sign);

    // 转字符串
    char *json_str = cJSON_PrintUnformatted(root);
    int request_result = -1;
    if (json_str) {
        // 发送请求
        request_result = https_send_post_request_common(request_url, json_str, response_body, 0);
        free(json_str); // 手动释放内部申请的内存
    } else {
        cJSON_Delete(root);
        snprintf(curl_result.code, sizeof(curl_result.code), "-1");
        snprintf(curl_result.msg, sizeof(curl_result.msg), "构造请求体失败");
        return curl_result;
    }

    if (request_result == 0) {
        // 转json
        cJSON *result = cJSON_Parse(response_body);
        if (result == NULL || result->type != cJSON_Object) {
            qrzl_err("result is not JSON.");
            if (result) cJSON_Delete(result);
            snprintf(curl_result.code, sizeof(curl_result.code), "-1");
            snprintf(curl_result.msg, sizeof(curl_result.msg), "接口返回格式错误。");
            return curl_result;
        } else {
            cJSON *code_json = cJSON_GetObjectItem(result, "code");
            cJSON *message_json = cJSON_GetObjectItem(result, "message");
            if (code_json == NULL || code_json->type != cJSON_String || message_json == NULL || message_json->type != cJSON_String) {
                snprintf(curl_result.msg, sizeof(curl_result.msg), "接口返回Code Msg为NULL，请稍后再试。");
                snprintf(curl_result.code, sizeof(curl_result.code), "-1");
            } else {
                // 如果有返回code和msg则把值取出来
                strncpy(curl_result.code, code_json->valuestring, sizeof(curl_result.code));
                strncpy(curl_result.msg, message_json->valuestring, sizeof(curl_result.msg));

                if (strcmp(curl_result.code, "200") != 0) {
                    // 不是正确的返回，直接return
                    return curl_result;
                }
            }
        }
        
        cJSON *data = cJSON_GetObjectItem(result, "data");
        if (data == NULL || data->type != cJSON_Object ) {
            qrzl_err("data is not Objcet or is NULL.");
            cJSON_Delete(result);
            snprintf(curl_result.code, sizeof(curl_result.code), "-1");
            snprintf(curl_result.msg, sizeof(curl_result.msg), "接口返回数据为NULL，请稍后再试。");
            return curl_result;
        }

        cJSON *url = cJSON_GetObjectItem(data, "url");
        if (url == NULL || url->type != cJSON_String) {
            qrzl_err("url is not Objcet or is NULL.");
            snprintf(curl_result.code, sizeof(curl_result.code), "-1");
            snprintf(curl_result.msg, sizeof(curl_result.msg), "接口返回回调地址为NULL，请稍后再试。");
        }

        // 设置nv
        cfg_set("cmp_customers_auth_page_url", url->valuestring);
        cfg_set("ONE_LINK_customers_auth_page_url", url->valuestring);

        // 释放资源
        cJSON_Delete(result);
        return curl_result;
    } else {
        snprintf(curl_result.msg, sizeof(curl_result.msg), "接口调用失败，请稍后再试。");
        snprintf(curl_result.code, sizeof(curl_result.code), "-1");
        return curl_result;
    }

}

/**
 * 异步认证拦截时调用实时获取认证页面 （WUXING客户）
 * @param mac 设备MAC
 * @param terminalMac 终端MAC（手机电脑...）
 * @param callback_url 回调地址
 * @return 0 成功，-1 失败
 */
CurlResult wuxing_get_sign_info(const char *mac, const char *terminalMac, char *callback_url)
{   
    time_t now = time(NULL);
    snprintf(wuxing_timestamp, sizeof(wuxing_timestamp), "%ld", now);
    qrzl_log("当前时间戳（秒）: %s", wuxing_timestamp);

    // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    char *current_iccid = g_qrzl_device_dynamic_data.iccid;
    char url_sign[254] = {0};

    // mac 转小写
    char convered_mac[32] = {0};
    convert_mac_format(mac, convered_mac, sizeof(convered_mac), ':');

    const char *keys[] = {"appkey", "timestamp", "nonce", "cardId", "mac", "terminalMac", "servAddr"};
    const char *values[] = {wuxing_appkey, wuxing_timestamp, nonce, current_iccid , convered_mac, terminalMac, callback_url};

    if (generate_sign(keys, values, 7, wuxing_secret, url_sign, sizeof(url_sign)) == 0) {
        qrzl_log("get url Sign: %s\n", url_sign);
        // 获取用户自定义的认证页面
        return wuxing_get_customers_page_url(convered_mac, current_iccid, terminalMac, nonce, wuxing_timestamp, callback_url, url_sign);
    } else {
        qrzl_log("Error generating url_sign\n");
        CurlResult res = {"-1", "Error generating url_sign"};
        return res;
    }
}

// 获取已认证的设备
char *wuxing_check_device_authed()
{
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    const char *inteface_str = "/getTerminalList";

    // 通过nv读取卡，不使用动态数据更新的函数，这个函数会频繁更新不必要的数据
    cfg_get_item("ziccid", g_qrzl_device_dynamic_data.iccid, 21);

    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", cmp_customers_get_token_url, inteface_str);

    // 生成 sign
    const char *current_iccid = g_qrzl_device_dynamic_data.iccid;
    char authed_sign[254] = {0};
    const char *keys[] = {"appkey", "timestamp", "nonce", "cardId", "mac"};
    const char *values[] = {wuxing_appkey, wuxing_timestamp, nonce, current_iccid, g_qrzl_device_static_data.mac};
    if (generate_sign(keys, values, 5, wuxing_secret, authed_sign, sizeof(authed_sign)) != 0) {
        qrzl_log("Error generating authed_sign");
        return NULL;
    }

    // 构造请求体
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", g_qrzl_device_static_data.mac);
    cJSON_AddStringToObject(root, "cardId", current_iccid);
    cJSON_AddStringToObject(root, "appkey", wuxing_appkey);
    cJSON_AddStringToObject(root, "nonce", nonce);
    cJSON_AddStringToObject(root, "timestamp", wuxing_timestamp);
    cJSON_AddStringToObject(root, "sign", authed_sign);

    char *json_str = cJSON_PrintUnformatted(root);
    cJSON_Delete(root);
    if (!json_str) return NULL;

    // 发起请求
    int request_result = https_send_post_request_common(request_url, json_str, response_body, 0);
    free(json_str);
    if (request_result != 0) return NULL;

    // 解析 JSON
    cJSON *result = cJSON_Parse(response_body);
    if (!result || result->type != cJSON_Object) {
        cJSON_Delete(result);
        return NULL;
    }

    cJSON *data = cJSON_GetObjectItem(result, "data");
    cJSON *terminalList = data ? cJSON_GetObjectItem(data, "terminalList") : NULL;
    if (!terminalList || terminalList->type != cJSON_Array) {
        cJSON_Delete(result);
        return NULL;
    }

    // 构造当前时间
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[20];
    strftime(datetime, sizeof(datetime), "%Y-%m-%d %H:%M:%S", tm_info);

    // 动态字符串构造
    size_t buffer_size = 1024;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(result);
        return NULL;
    }
    authed_mac_list[0] = '\0';
    int i;
    for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
        cJSON *item = cJSON_GetArrayItem(terminalList, i);
        if (!item) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
        if (!item_mac || item_mac->type != cJSON_String ||
            !expireTime || expireTime->type != cJSON_String) {
            continue;
        }

        if (strcmp(expireTime->valuestring, datetime) < 0) {
            continue;  // 已过期
        }

        // 转换 MAC 格式
        char formatted_mac[32] = {0};
        convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

        // 拼接
        size_t used = strlen(authed_mac_list);
        size_t needed = strlen(formatted_mac) + 2;
        if (used + needed >= buffer_size) break;  // 超出，简单跳出

        strcat(authed_mac_list, formatted_mac);
        strcat(authed_mac_list, ";");
    }

    cJSON_Delete(result);
    return authed_mac_list;  // 需要调用者 free()
}

/**
 * 判断mac是否已认证
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
int wuxing_is_mac_authed(const char *mac)
{   
    int cfg_ret = cfg_get_item("cmp_customers_get_token_url", cmp_customers_get_token_url, 256);
    if (cfg_ret != 0 || cmp_customers_get_token_url == NULL || strcmp(cmp_customers_get_token_url, "") == 0)
    {
        qrzl_log("cmp_customers_get_token_url is NULL");
    }

    int ret = 0;

    if (!mac || strlen(mac) == 0) {
        return ret;
    }

    char *authed_list = wuxing_check_device_authed();
    if (!authed_list) {
        qrzl_err("无法获取已认证 MAC 列表");
        return ret;
    }

    // 格式化传入的 mac
    char formatted_mac[32] = {0};
    convert_mac_format(mac, formatted_mac, sizeof(formatted_mac), ':');

    // 构造 ";xx:xx:xx:xx:xx:xx;" 格式，避免部分匹配问题
    char search_key[40] = {0};
    snprintf(search_key, sizeof(search_key), ";%s;", formatted_mac);

    // 为了统一处理，首尾补上 ';'
    size_t list_len = strlen(authed_list);
    char *wrapped_list = malloc(list_len + 3);
    if (!wrapped_list) {
        free(authed_list);
        return ret;
    }
    snprintf(wrapped_list, list_len + 3, ";%s", authed_list);

    qrzl_log("search_key: %s", search_key);
    qrzl_log("wrapped_list: %s", wrapped_list);

    if (strstr(wrapped_list, search_key) != NULL) {
        ret = 1;
    }

    free(authed_list);
    free(wrapped_list);
    return ret;
}

/**
 * 设备上下线上报 （WUXING客户）
 * @param type 上报类型 [1上线, 2下线]
 * @param mac 设备MAC
 * @param terminalMac 终端MAC（手机电脑...）
 */
void wuxing_device_line_type_push(const char *type, const char *mac, const char *terminalMac)
{
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = "/terminalDataReported";

    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s%s", cmp_customers_get_token_url, inteface_str);

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[20];  // 需要至少 20 字节
    strftime(datetime, sizeof(datetime), "%Y-%m-%d %H:%M:%S", tm_info);

    char terminalMac_fomated[33] = {0};
    char mac_fomated[33] = {0};
    convert_mac_format(mac, mac_fomated, sizeof(mac_fomated), ':');
    convert_mac_format(terminalMac, terminalMac_fomated, sizeof(terminalMac_fomated), ':');

    // 生成sign
    char *current_iccid = g_qrzl_device_dynamic_data.iccid;
    char report_sign[254] = {0};
    const char *keys[] = {"appkey", "timestamp", "nonce", "mac", "terminalMac", "cardId", "busiTime", "imei", "type"};
    const char *values[] = {wuxing_appkey, wuxing_timestamp, nonce, mac_fomated, terminalMac_fomated, current_iccid, datetime, g_qrzl_device_static_data.imei, type};
    if (generate_sign(keys, values, 9, wuxing_secret, report_sign, sizeof(report_sign)) == 0) {
        qrzl_log("Report Sign: %s\n", report_sign);
    } else {
        qrzl_log("Error generating report_sign\n");
        return;
    }

    // 构造请求体
    cJSON *root = cJSON_CreateObject();
    cJSON_AddStringToObject(root, "mac", mac_fomated);
    cJSON_AddStringToObject(root, "cardId", current_iccid);
    cJSON_AddStringToObject(root, "appkey", wuxing_appkey);
    cJSON_AddStringToObject(root, "nonce", nonce);
    cJSON_AddStringToObject(root, "timestamp", wuxing_timestamp);
    cJSON_AddStringToObject(root, "terminalMac", terminalMac_fomated);
    cJSON_AddStringToObject(root, "busiTime", datetime);
    cJSON_AddStringToObject(root, "imei", g_qrzl_device_static_data.imei);
    cJSON_AddStringToObject(root, "type", type);
    cJSON_AddStringToObject(root, "sign", report_sign);

     // 转字符串
    char *json_str = cJSON_PrintUnformatted(root);
    int request_result = -1;
    if (json_str) {
        // 发送请求
        request_result = https_send_post_request_common(request_url, json_str, response_body, 0);
        free(json_str); // 手动释放内部申请的内存
    }

    if (request_result == 0) {
        // 转json
        cJSON *result = cJSON_Parse(response_body);
        if (result == NULL || result->type != cJSON_Object) {
            qrzl_err("result is not JSON.");
            cJSON_Delete(result);
            return;
        }

        cJSON *code = cJSON_GetObjectItem(result, "code");
         if (code == NULL || code->type != cJSON_String) {
            qrzl_err("code is not string or is NULL.");
            cJSON_Delete(result);
            return;
        }

        if (strcmp("200", code->valuestring) == 0) {
            qrzl_log("WUXING %s 成功!", strcmp("1", type) == 0 ? "上线上报" : "下线上报");
        }

        cJSON_Delete(result);
    }
}


// 异常页面字符串返回
char *wuxing_exception_page(const char *msg, const char *code) {
    // 预估最大页面长度（含样式 + msg + code）
    size_t buf_size = 4096;
    char *page = (char *)malloc(buf_size);
    if (!page) return NULL;

    snprintf(page, buf_size,
        "HTTP/1.1 200 OK\r\n"
        "Content-Type: text/html; charset=UTF-8\r\n"
        "Connection: close\r\n"
        "\r\n"
        "<html lang='zh-CN'>\n"
        "<head>\n"
        "  <meta charset='UTF-8'>\n"
        "  <title>系统异常</title>\n"
        "  <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n"
        "  <style>\n"
        "    body { margin: 0; font-family: 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; background-color: #f8f8f8; display: flex; justify-content: center; align-items: center; height: 100vh; text-align: center; color: #333; }\n"
        "    .container { max-width: 500px; padding: 40px; background-color: #fff; border-radius: 16px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); }\n"
        "    .error-code { font-size: 72px; font-weight: bold; color: #ff4c4c; margin-bottom: 10px; }\n"
        "    .error-message { font-size: 22px; margin-bottom: 20px; }\n"
        "    .hint { font-size: 16px; color: #888; margin-bottom: 30px; }\n"
        "    .btn { padding: 12px 24px; font-size: 16px; background-color: #007bff; color: #fff; border: none; border-radius: 8px; cursor: pointer; text-decoration: none; transition: background-color 0.2s ease; }\n"
        "    .btn:hover { background-color: #0056b3; }\n"
        "    @media (max-width: 600px) { .error-code { font-size: 48px; } .error-message { font-size: 18px; } }\n"
        "  </style>\n"
        "</head>\n"
        "<body>\n"
        "  <div class='container'>\n"
        "    <div class='error-code'>Code: %s</div>\n"
        "    <div class='error-message'>%s</div>\n"
        "    <div class='hint'>请稍后重试，或联系客服</div>\n"
        "    <a href='/' class='btn'>重新尝试</a>\n"
        "  </div>\n"
        "</body>\n"
        "</html>\n",
        code, msg
    );

    return page;
}



// =================================WUXING 认证信息获取 end========================================================
