#ifndef __QRZL_XUNYOU_AUTH_CONTROL_H_
#define __QRZL_XUNYOU_AUTH_CONTROL_H_

/**
 * 初始化 xunyou的配置信息
 */
void init_xunyou_config_data();

/**
 * 获取xunyou 上报 Token 信息
 * @param mac 设备mac
 * @param terminalMac terminalMac: 终端mac(手机/电脑....)
 * @param phoneNum 手机号码
 * @param time_str 时间戳 %Y-%m-%d %H:%M:%S
 */
int get_report_token_info_xunyou(const char *mac, const char *terminalMac, const char *phoneNum, const char *time_str);

/**
 * XUNYOU 设备上/下线上报
 * @param push_type 上报类型
 * @param mac 设备mac
 * @param terminalMac terminalMac: 终端mac(手机/电脑....)
 * @param phoneNum 手机号码
 */
int xunyou_device_line_type_push(const int push_type, const char *mac, const char *terminalMac, const char *phoneNum);


/**
 * XUNYOU 获取已认证设备, 不特殊处理
 */
void xunyou_check_device_authed_origin(char *rev_response_str);

/**
 * XUNYOU 判断mac是否已认证
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
int xunyou_is_mac_authed(const char *mac);


/**
 * xunyou  获取 移动token 信息
 */
int xunyou_get_token_code(char *appid_str, size_t appid_len, char *token_str, size_t token_len);

#endif