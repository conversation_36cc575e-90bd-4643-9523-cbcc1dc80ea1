#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048

static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int http_send_get_request(const char *url, char *response) {
    CURL *curl;
    CURLcode res;
    qrzl_log("ONE LINK -> http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // curl_easy_setopt(curl, CURLOPT_POST, 1L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s", response);
        }
        curl_easy_cleanup(curl);
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// =============================== 移动认证相关接口 =============================================

/**
 * 获取移动token
 * @param appid 
 * @param appid_len appid长度
 * @param token 
 * @param token_len token长度
 * @return -1 获取失败， 0 获取成功
 */
int jnzy_get_token(char *appid_str, size_t appid_len, char *token_str, size_t token_len) {

    // 接收请求后返回的JSON数据
    char http_response_content[QRZL_HTTP_RESPONSE_MAX] = {0};

    char *source = "315";
    char *key = "b7d6afe944ea4dcb828eaa39d77cc231";

    char customer_get_token_url[1024] = {0};
    snprintf(customer_get_token_url, sizeof(customer_get_token_url), "http://pushdata-java.china-m2m.com/apigateway/job/api/getToken?source=%s&key=%s", source, key);

    // 发送Get请求
    int res_spr;
    res_spr = http_send_get_request(customer_get_token_url, http_response_content);
    if(res_spr != 0) {
        qrzl_log("请求异常，请重试!");
        return -1;
    }

    // 通过cJSON解析响应数据
    cJSON *root = cJSON_Parse(http_response_content);
    if (root == NULL || root->type != cJSON_Object){
        qrzl_log("get_token_code 解析失败");
        cJSON_Delete(root);
        return -1;
    }

    cJSON *token_item = cJSON_GetObjectItem(root, "authToken");
    if (token_item != NULL && token_item->type == cJSON_String){
        qrzl_log("get_token_code -> Token: %s", token_item->valuestring);
        snprintf(token_str, token_len, "%s", token_item->valuestring);
    } else {
        qrzl_log("找不到 token 或 token 不是字符串");
        return -1;
    }

    // cJSON *appid_item = cJSON_GetObjectItem(root, "appid");
    // if (appid_item != NULL && appid_item->type == cJSON_String){
    //     qrzl_log("get_token_code -> appid: %s", appid_item->valuestring);
    //     snprintf(appid_str, sizeof(appid_len), "%s", appid_item->valuestring);
    // } else {
    //     qrzl_log("找不到 appid 或 appid 不是字符串");
    //     return -1;
    // }

    // 着急做货，appid暂时写死
    snprintf(appid_str, appid_len, "%s", "C5010200A9992000513423");

    cJSON_Delete(root); // 释放 JSON 对象， 只需要释放最外层，内部会自动释放内层的对象，例如result、token_item
    return 0;
}