#ifndef __QRZL_CMP_ORIGINAL_AUTH_CONTROL_H_
#define __QRZL_CMP_ORIGINAL_AUTH_CONTROL_H_

void init_cmp_config_data();

/**
 * 设备上下线上报
 * @param type 上报类型 [1上线, 0下线]
 * @param mac 设备MAC
 * @param terminalMac 终端MAC（手机电脑...）
 */
void cmp_device_line_type_report(const char *mac, const char *terminalMac, const int type);

/**
 * 判断mac是否已认证
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
int cmp_is_mac_authed(const char *mac);

#endif