#ifndef __QRZL_WUXING_AUTH_CONTROL_H_
#define __QRZL_WUXING_AUTH_CONTROL_H_

typedef struct {
    char code[30];           // 0 表示成功，非 0 表示失败
    char msg[128];      // 错误或成功信息
} CurlResult;

// wuxing 获取sign信息接口
CurlResult wuxing_get_sign_info(const char *mac, const char *terminalMac, char *callback_url);

// 获取已认证的设备
char *wuxing_check_device_authed();

/**
 * 判断mac是否已认证
 * @param mac 终端mac
 */
int wuxing_is_mac_authed(const char *mac);

// WUXING 设备上/下线上报
void wuxing_device_line_type_push(const char *type, const char *mac, const char *terminalMac);

// 异常页面字符串返回
char *wuxing_exception_page(const char *msg, const char *code);

#endif