#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>
#include <curl/curl.h>
#include <ctype.h>
#include <sys/time.h>

#include "../cjson.h"
#include "../qrzl_utils.h"
#include "softap_api.h"
#include "nv_api.h"
#include "../common_utils/md5.h"

extern struct device_static_data_t g_qrzl_device_static_data;
extern struct device_dynamic_data_t g_qrzl_device_dynamic_data;

#define QRZL_HTTP_RESPONSE_MAX 5120
#define QRZL_HTTP_REQUEST_BODY_MAX 2048
#define HASH_SIZE 16
#define MIN_INTERVAL_MS 2000  // 最小调用间隔：2秒 ，防抖时间（认证线程会频繁探测网络导致一秒内调用多次请求）

static char appKey[125] = {0};
static char secretKey[64] = {0};
static char auth_http_url[256] = {0};
static char auth_http_port[10] = {0};

static char sign[65] = {0};
static char timestamp[45] = {0};



// 上一次的 MAC 列表（分号分隔）
static char last_station_mac[2048] = {0};  

// 记录上次调用的时间
static unsigned long last_request_time_ms = 0;

// 节流控制结构体
typedef struct {
    unsigned long last_call_time_ms;
    unsigned int min_interval_ms;
} ThrottleCtrl;

typedef struct {
    const char *key;
    const char *value;
} kv_pair;

static int cmp_kv_pair(const void *a, const void *b) {
    const kv_pair *pa = (const kv_pair *)a;
    const kv_pair *pb = (const kv_pair *)b;
    return strcmp(pa->key, pb->key);
}

static ThrottleCtrl get_authed_ctrl = {0, MIN_INTERVAL_MS};

#define KEY_CMP_APPKEY       "cmp_auth_appkey"
#define KEY_CMP_SECRET       "cmp_auth_secretKey"
#define KEY_CMP_URL          "cmp_auth_http_url"
#define KEY_CMP_PORT         "cmp_auth_http_port"

void init_cmp_config_data()
{
    qrzl_log("Start loading CMP authentication configuration items...");

    cfg_get_item(KEY_CMP_APPKEY, appKey, sizeof(appKey));
    if (strlen(appKey) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_APPKEY);
    }

    cfg_get_item(KEY_CMP_SECRET, secretKey, sizeof(secretKey));
    if (strlen(secretKey) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_SECRET);
    }

    cfg_get_item(KEY_CMP_URL, auth_http_url, sizeof(auth_http_url));
    if (strlen(auth_http_url) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_URL);
    }

    cfg_get_item(KEY_CMP_PORT, auth_http_port, sizeof(auth_http_port));
    if (strlen(auth_http_port) == 0) {
        qrzl_log("Configuration item %s is missing !", KEY_CMP_PORT);
    }
}

// HTTP回调函数，用于处理HTTP响应
static size_t http_write_callback(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t totalSize = size * nmemb;
    char *response = (char *)userp;

    size_t currentLen = strlen(response);
    size_t remaining = QRZL_HTTP_RESPONSE_MAX - currentLen - 1; // 预留 '\0'

    if (remaining <= 0) {
        qrzl_log("http返回值已满，不能再写入");
        return 0;
    }

    size_t copyLen = totalSize < remaining ? totalSize : remaining;
    strncat(response, (char *)contents, copyLen);

    return totalSize;
}

// 发送GET请求
static int https_send_get_request(const char *url, char *response, int type) {
    CURL *curl;
    CURLcode res;
    qrzl_log("http request url: %s", url);

    curl = curl_easy_init();
    if (curl) {
        // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
        curl_easy_setopt(curl, CURLOPT_URL, url);
        // 设置SSL
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
        curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L); 
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
        // 设置HTTP头
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
        if (type == 1)
        {
            char h_sign[254] = {0};
            char h_appKey[125] = {0};
            char h_timestamp[254] = {0};
            snprintf(h_appKey, sizeof(h_appKey), "AppKey: %s", appKey);
            snprintf(h_sign, sizeof(h_sign), "Sign: %s", sign);
            snprintf(h_timestamp, sizeof(h_timestamp), "Timestamp: %s", timestamp);
            headers = curl_slist_append(headers, h_appKey);
            headers = curl_slist_append(headers, h_sign);
            headers = curl_slist_append(headers, h_timestamp);

            qrzl_log("it's request : %s", h_appKey);
            qrzl_log("it's request : %s", h_sign);
            qrzl_log("it's request : %s", h_timestamp);

        }
        // 设置公共参数
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        res = curl_easy_perform(curl);
        if (res != CURLE_OK) {
            qrzl_log("curl_easy_perform() failed: %s", curl_easy_strerror(res));
        } else {
            qrzl_log("http Response: %s\n", response);
        }
        curl_easy_cleanup(curl);
        if (headers) {
            curl_slist_free_all(headers);
        }
        if (res != CURLE_OK) {
            return -1;
        }
        return 0;
    }
    return -1;
}

// 通用的POST请求函数, type=1表示 将电信签名加入到请求头中
static int https_send_post_request_common(const char *url, const char* body, char *response, int type)
{
    CURL *curl;
    CURLcode res;

    // 初始化curl
    curl = curl_easy_init();

    if(curl == NULL) {
        qrzl_log("init CURL failed!!");
        return -1;
    }

    qrzl_log("request url: %s", url);
    qrzl_log("request body: %s", body);

    // curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    // 设置SSL
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);
    // 设置请求地址
    curl_easy_setopt(curl, CURLOPT_URL, url);
    // 使用post方式请求
    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    // 设置响应超时时间
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
    // 设置连接超时时间
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5L);
    // 回调函数
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, http_write_callback);
    // 设置POST请求的内容
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);
    // 设置HTTP头
    struct curl_slist *headers = NULL;
    headers = curl_slist_append(headers, "Content-Type: application/json"); // （告诉服务器这是JSON数据）
    // 设置公共参数
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    // 设置 接收请求响应的内容
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    // 正式发起请求
    res = curl_easy_perform(curl);

    // 处理返回值
    if(res != CURLE_OK) {
        qrzl_log("curl_easy_perform request failed!");
    }

    if (headers) {
        curl_slist_free_all(headers);
    }
    // 清理内存
    curl_easy_cleanup(curl);

    if (res != CURLE_OK)
    {
        return -1;
    }

    qrzl_log("request response: %s\n", response);

    return 0;
}

// 返回当前时间，单位为毫秒
static unsigned long get_timestamp_ms() {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (tv.tv_sec * 1000UL) + (tv.tv_usec / 1000UL);
}

static int can_call(ThrottleCtrl *ctrl) {
    unsigned long now = get_timestamp_ms();
    if (ctrl->last_call_time_ms == 0 || 
        (now - ctrl->last_call_time_ms) >= ctrl->min_interval_ms) {
        ctrl->last_call_time_ms = now;
        return 1;  // 可以调用
    }
    return 0;  // 禁止调用（太频繁）
}

// 获取当前时间戳（格式：YYYYMMDDHHMMSS）
static void get_timestamp() {
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    strftime(timestamp, sizeof(timestamp), "%Y%m%d%H%M%S", tm_info);
}

// MD5 64位
static void md5_handler(const char *src, char *out_md5_str)
{
    lpa_MD5_CTX md5ctx;
    unsigned char md[HASH_SIZE];  // HASH_SIZE = 16（128位）
    char tmp_hex[33];             // 原始32位MD5字符串

    // 1. 计算 MD5 二进制值
    lpa_MD5_Init(&md5ctx);
    lpa_MD5_Update(&md5ctx, src, strlen(src));
    lpa_MD5_Final(md, &md5ctx);

    // 2. 转为 32 字节 HEX 字符串
    int i;
    for (i = 0; i < HASH_SIZE; ++i) {
        sprintf(&tmp_hex[i * 2], "%02x", md[i]);
    }
    tmp_hex[32] = '\0';

    // 3. 再将 32 字节 HEX 字符串，每个字符转为两位十六进制（ASCII -> HEX）
    int j;
    for (j = 0; j < 32; ++j) {
        sprintf(&out_md5_str[j * 2], "%02x", (unsigned char)tmp_hex[j]);
    }
    out_md5_str[64] = '\0';
}


/**
 * 通过get拼接参数并排序，再进行sha1加密计算
 * @param keys 参与排序的键列表
 * @param values 参与排序的值列表
 * @param secret_str 密钥，拼接在最后
 * @param out_sign sign
 * @param sign_buf_len sign 长度
 */
static int generate_sign(const char **keys, const char **values, int count, const char *secret_str, char *out_sign, int sign_buf_len) {
    if (!keys || !values || !secret_str || !out_sign || count <= 0 || sign_buf_len <= 0) return -1;

    kv_pair *pairs = malloc(sizeof(kv_pair) * count);
    if (!pairs) return -1;

    int actual_count = 0;
    int i;
    for (i = 0; i < count; ++i) {
        if (keys[i] && values[i] && keys[i][0] != '\0' && values[i][0] != '\0') {
            pairs[actual_count].key = keys[i];
            pairs[actual_count].value = values[i];
            actual_count++;
        }
    }

    qsort(pairs, actual_count, sizeof(kv_pair), cmp_kv_pair);

    char *buffer = malloc(2048);
    if (!buffer) {
        free(pairs);
        return -1;
    }
    buffer[0] = '\0';
    int j;
    for (j = 0; j < actual_count; ++j) {
        strncat(buffer, pairs[j].key, 512);
        strncat(buffer, "=", 1);
        strncat(buffer, pairs[j].value, 512);
        if (j < actual_count - 1) {
            strncat(buffer, "&", 1);
        }
    }

    strncat(buffer, secret_str, 512); // 拼接密钥
    strncat(buffer, timestamp, 20); // 拼接时间戳

    qrzl_log("order by after parmes: %s\n", buffer);

    // MD5 加密
    md5_handler(buffer, out_sign);

    free(pairs);
    free(buffer);
    return 0;
}


/**
 * 设备上下线上报
 * @param mac 设备MAC
 * @param terminalMac 终端MAC（手机电脑...）
 * @param type 上报类型 [1上线, 0下线]
 */
void cmp_device_line_type_report(const char *mac, const char *terminalMac, const int type)
{
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    char *inteface_str = type == 1 ? "/openapi/v1/device/terminalOnline" : "/openapi/v1/device/terminalOffline";

    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s:%s%s", auth_http_url, auth_http_port, inteface_str);

    get_timestamp();

    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char busiTime[20];  // 需要至少 20 字节
    strftime(busiTime, sizeof(busiTime), "%Y-%m-%d %H:%M:%S", tm_info);

    char mac_format[33] = {0};
    char terminalMac_format[33] = {0};
    convert_mac_format(mac, mac_format, sizeof(mac_format), '\0'); // 格式化
    convert_mac_format(terminalMac, terminalMac_format, sizeof(terminalMac_format), '\0'); // 格式化

    // 生成sign
    char *current_iccid = g_qrzl_device_dynamic_data.iccid;
    char report_sign[254] = {0};
    const char *keys[] = {"mac", "terminalMac", "iccid", "imei", "busiTime"};
    const char *values[] = {mac_format, terminalMac_format, current_iccid, g_qrzl_device_static_data.imei, busiTime};
    if (generate_sign(keys, values, 5, secretKey, report_sign, sizeof(report_sign)) == 0) {
        // 更新全局sign
        qrzl_log("Report Sign: %s\n", report_sign);
        snprintf(sign, sizeof(sign), "%s", report_sign);
    } else {
        qrzl_log("Error generating report_sign");
        return;
    }

    // 请求参数
    char parmes[1024] = {0};
    snprintf(parmes, sizeof(parmes), "mac=%s&terminalMac=%s&iccid=%s&imei=%s&busiTime=%s", mac_format, terminalMac_format, current_iccid, g_qrzl_device_static_data.imei, busiTime);

    // 请求参数 url编码
    char parmes_encoded[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    if (url_encode(parmes, parmes_encoded) != 0) {
        qrzl_err("parmes_encoded failed !!!");
        return;
    }

    // 构造请求路径
    snprintf(request_url, sizeof(request_url), "%s?%s", request_url, parmes_encoded);

    int request_result = -1;
    request_result = https_send_get_request(request_url, response_body, 1);

    if (request_result == 0) {
        // 转json
        cJSON *result = cJSON_Parse(response_body);
        if (result == NULL || result->type != cJSON_Object) {
            qrzl_err("result is not JSON.");
            cJSON_Delete(result);
            return;
        }

        cJSON *code = cJSON_GetObjectItem(result, "code");
         if (code == NULL || code->type != cJSON_String) {
            qrzl_err("code is not string or is NULL.");
            cJSON_Delete(result);
            return;
        }

        if (strcmp("0", code->valuestring) == 0) {
            qrzl_log("设备 %s 成功!", type == 1 ? "上线上报" : "下线上报");
        }

        cJSON_Delete(result);
    }
}


// 获取已认证的设备
char *cmp_check_device_authed()
{
    char request_url[QRZL_HTTP_REQUEST_BODY_MAX] = {0};
    char response_body[QRZL_HTTP_RESPONSE_MAX] = {0};
    const char *inteface_str = "/openapi/v1/device/getTerminalList";

    // 拼接请求地址
    snprintf(request_url, sizeof(request_url), "%s:%s%s", auth_http_url, auth_http_port, inteface_str);

    get_timestamp();

    char mac_format[33] = {0};
    convert_mac_format(g_qrzl_device_static_data.mac, mac_format, sizeof(mac_format), '\0'); // 格式化

    // 生成 sign
    const char *current_iccid = g_qrzl_device_dynamic_data.iccid;
    char authed_sign[254] = {0};
    const char *keys[] = {"mac", "iccid"};
    const char *values[] = {mac_format, current_iccid};
    if (generate_sign(keys, values, 2, secretKey, authed_sign, sizeof(authed_sign)) == 0) {
        // 更新全局sign
        snprintf(sign, sizeof(sign), "%s", authed_sign);
    } else {
        qrzl_log("Error generating authed_sign");
        return NULL;
    }

    // 请求参数
    char parmes[1024] = {0};
    snprintf(parmes, sizeof(parmes), "mac=%s&iccid=%s", mac_format, current_iccid);

    // 构造请求路径
    snprintf(request_url, sizeof(request_url), "%s?%s", request_url, parmes);

    int request_result = -1;
    request_result = https_send_get_request(request_url, response_body, 1);

    if (request_result != 0) return NULL;

    // 解析 JSON
    cJSON *root = cJSON_Parse(response_body);
    if (!root || root->type != cJSON_Object) {
        cJSON_Delete(root);
        return NULL;
    }

    cJSON *result = cJSON_GetObjectItem(root, "result");
    cJSON *terminalList = result ? cJSON_GetObjectItem(result, "terminalList") : NULL;
    if (!terminalList || terminalList->type != cJSON_Array) {
        cJSON_Delete(root);
        return NULL;
    }

    // 构造当前时间
    time_t t = time(NULL);
    struct tm *tm_info = localtime(&t);
    char datetime[20];
    strftime(datetime, sizeof(datetime), "%Y-%m-%d %H:%M:%S", tm_info);

    // 动态字符串构造
    size_t buffer_size = 1024;
    char *authed_mac_list = malloc(buffer_size);
    if (!authed_mac_list) {
        cJSON_Delete(root);
        return NULL;
    }
    authed_mac_list[0] = '\0';
    int i;
    for (i = 0; i < cJSON_GetArraySize(terminalList); ++i) {
        cJSON *item = cJSON_GetArrayItem(terminalList, i);
        if (!item) continue;

        cJSON *item_mac = cJSON_GetObjectItem(item, "terminalMac");
        cJSON *expireTime = cJSON_GetObjectItem(item, "expireTime");
        if (!item_mac || item_mac->type != cJSON_String ||
            !expireTime || expireTime->type != cJSON_String) {
            continue;
        }

        if (strcmp(expireTime->valuestring, datetime) < 0) {
            continue;  // 已过期
        }

        // 转换 MAC 格式
        char formatted_mac[32] = {0};
        convert_mac_format(item_mac->valuestring, formatted_mac, sizeof(formatted_mac), ':');

        // 拼接
        size_t used = strlen(authed_mac_list);
        size_t needed = strlen(formatted_mac) + 2;
        if (used + needed >= buffer_size) break;  // 超出，简单跳出

        strcat(authed_mac_list, formatted_mac);
        strcat(authed_mac_list, ";");
    }

    qrzl_log("authed_mac_list: %s", authed_mac_list);

    cJSON_Delete(result);
    return authed_mac_list;  // 需要调用者 free()
}

/**
 * 判断mac是否已认证 (未启用该方案)
 * @param mac 终端mac
 * @return 0 未认证； 1 已认证
 */
int cmp_is_mac_authed(const char *mac)
{   
    int ret = 0;

    if (!mac || strlen(mac) == 0) {
        return ret;
    }

    char *authed_list = cmp_check_device_authed();
    if (!authed_list) {
        qrzl_err("无法获取已认证 MAC 列表");
        return ret;
    }

    // 格式化传入的 mac
    char formatted_mac[32] = {0};
    convert_mac_format(mac, formatted_mac, sizeof(formatted_mac), ':');

    // 构造 ";xx:xx:xx:xx:xx:xx;" 格式，避免部分匹配问题
    char search_key[40] = {0};
    snprintf(search_key, sizeof(search_key), ";%s;", formatted_mac);

    qrzl_log("search_key: %s", search_key);

    // 为了统一处理，首尾补上 ';'
    size_t list_len = strlen(authed_list);
    char *wrapped_list = malloc(list_len + 3);
    if (!wrapped_list) {
        free(authed_list);
        return ret;
    }
    snprintf(wrapped_list, list_len + 3, ";%s", authed_list);

    qrzl_log("wrapped_list: %s", wrapped_list);

    if (strstr(wrapped_list, search_key) != NULL) {
        ret = 1;
    }

    free(authed_list);
    free(wrapped_list);
    return ret;
}

