#ifndef __QRZL_FOTA__H_
#define __QRZL_FOTA__H_

#define lock_system fota_write_file("\\sys\\power\\wake_lock","cs_fota_update",sizeof("cs_fota_update"))
#define unlock_system fota_write_file("\\sys\\power\\wake_unlock","cs_fota_update",sizeof("cs_fota_update"))

int fota_is_file_exist(const char* path);
int fota_write_file(const char*path, const char*value, int size);
int fota_mkdirs(const char* path, mode_t mode);
int fota_check_and_make_fota_dir(void);
int fota_update(void);
int fota_get_file_hash_from_path(const char *filepath, char *hash_output, size_t output_len);


#endif
