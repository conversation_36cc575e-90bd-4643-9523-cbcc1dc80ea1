#*******************************************************************************
# include build/common makefile
# 以生成两个应用为例，如果只有一个删除EXEC2 OBJS2即可
#*******************************************************************************
include $(COMMON_MK)

EXEC = qrzl_app
OBJS = qrzl_app.o qrzl_http_control_client.o qrzl_mqtt_control_client.o fota/fota_utils.o \
 		qrzl_device_control.o cloud_control/xlx_control.o cloud_control/hmm_control.o \
		cloud_control/qc_http_post_control.o cloud_control/cshttp_control.o \
		qrzl_utils.o json.o qrzl_captive_portal_server.o \
		cloud_control/one_link_http_control.o cjson.o common_utils/md5.o \
		cloud_control/qc_tcp_control.o \
		cloud_control/jijia_http_control.o \
		cloud_control/cmp_auth_control.o \
		cloud_control/xunyou_mqtt_control.o \
		auth_control/cmp_original_auth_control.o auth_control/wuxing_auth_control.o auth_control/xunyou_auth_control.o auth_control/jnzy_auth_control.o \
		
#EXEC 特有的LIB参数
#LDLIBS_demo1 = -lnvram -L$(LIB_DIR)/libnvram
LDLIBS_qrzl_app  = -lsoftap -L$(zte_lib_path)/libsoftap
LDLIBS_qrzl_app  += -lsoft_timer -L$(zte_lib_path)/libsoft_timer
LDLIBS_qrzl_app  += -latutils -L$(zte_lib_path)/libatutils
LDLIBS_qrzl_app  += -lnvram -L$(zte_lib_path)/libnvram
LDLIBS_qrzl_app  += -lcurl -L$(zte_lib_path)/libcurl/install/lib/
LDLIBS_qrzl_app  += -lm
LDLIBS_qrzl_app  += -lpaho-mqtt3c -L$(zte_lib_path)/libpahomqttc/install/lib/
LDLIBS_qrzl_app  += -lwolfssl -L$(zte_lib_path)/libwolfssl/install/lib/
LDLIBS_qrzl_app	 += -lssl -lcrypto -L$(zte_lib_path)/libssl/install/lib/

EXEC2 = qrzl_test 
OBJS2 = qrzl_test.o

#EXEC2 特有的LIB参数
#LDLIBS_qrzl_test = 

#宏和头文件目录在CFLAGS里定义，要用+=,不要用=,否则会覆盖COMMON_MK里的值
CFLAGS += -I$(APP_DIR)/include
CFLAGS += -I$(zte_lib_path)/libsoft_timer
CFLAGS += -I$(zte_lib_path)/libnvram
CFLAGS += -I$(zte_lib_path)/libsoftap
CFLAGS += -I$(zte_lib_path)/libatutils
CFLAGS += -I$(zte_lib_path)/libcurl/install/include
CFLAGS += -I$(zte_lib_path)/libpahomqttc/install/include
CFLAGS += -I$(zte_lib_path)/libwolfssl/install/include
CFLAGS += -I$(zte_lib_path)/libssl/install/include


#EXEC EXEC2 公共LIB参数，第一行定义LDLIBS用=，不要用+=,应用连接的库都在本Makefile定义
LDLIBS = -lpthread


#*******************************************************************************
# targets
#*******************************************************************************
all: $(EXEC) $(EXEC2)

$(EXEC): $(OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) $(LDLIBS_$@) -Wl,--end-group
	@cp $@ $@.elf

$(EXEC2): $(OBJS2)
	$(CC) $(LDFLAGS) -o $@ $^ -Wl,--start-group $(LDLIBS) $(LDLIBS_$@) -Wl,--end-group
	@cp $@ $@.elf

romfs:
	$(ROMFSINST) $(EXEC) /bin/$(EXEC)

clean:
	-@rm -f $(EXEC) $(EXEC2) *.elf *.gdb *.o cloud_control/*.o
