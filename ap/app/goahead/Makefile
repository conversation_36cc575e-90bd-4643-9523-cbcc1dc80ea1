.EXPORT_ALL_VARIABLES:

DIRS = interface5.0 server

ROOT_DIRECTORY = /etc/web
CGIS_DIRECTORY = ./bins/cgis
CGI_SRC_DIRECTORY = ./cgi-src

UPLOAD_CGI = upload.cgi 
UPLOAD_BOOTLOADER_CGI = upload_bootloader.cgi
UPLOAD_SETTINGS = upload_settings.cgi
USB_UPGRADE_CGI = usb_upgrade.cgi
UPLOAD_CA_CLIENT_CERT_CGI = upload_caclcert.cgi
UPLOAD_KEY_CERT_CGI = upload_keycert.cgi
UPLOAD_WAPI_AS_CERT_CGI = upload_wapi_as_cert.cgi
UPLOAD_WAPI_USER_CERT_CGI = upload_wapi_user_cert.cgi
UPLOAD_WRITE = upload.write
UPLOAD_DAFAULT_PARA = upload_dafault_para.cgi
UPLOAD_TR069_CRET = upload_tr069_cert.cgi


all:
	for i in $(DIRS) ; do make -C $$i $@ || exit $?; done

romfs: 
#	$(ROMFSINST) web/web_7510 $(ROOT_DIRECTORY)
#	$(ROMFSINST) $(CUSTOM_DIRECTORY) $(ROOT_DIRECTORY)
#	chmod 755 $(ROOTFS_DIR)/etc/web -R
	for i in $(DIRS) ; do make -C $$i $@ ; done

	mkdir -p ./bins
	cp -vf ./server/goahead ./bins/goahead
#	cp -vf $(CGI_SRC_DIRECTORY)/$(UPLOAD_CGI) $(CGIS_DIRECTORY)/$(UPLOAD_CGI)
#	cp -vf $(CGI_SRC_DIRECTORY)/$(UPLOAD_BOOTLOADER_CGI) $(CGIS_DIRECTORY)/$(UPLOAD_BOOTLOADER_CGI)
#	cp -vf $(CGI_SRC_DIRECTORY)/reboot.sh $(CGIS_DIRECTORY)/reboot.sh
	#chmod +x *.sh
#	cp -vf $(CGI_SRC_DIRECTORY)/ExportSettings.sh $(CGIS_DIRECTORY)/ExportSettings.sh
#	cp -vf $(CGI_SRC_DIRECTORY)/ExportSyslog.sh $(CGIS_DIRECTORY)/ExportSyslog.sh
#	cp -vf $(CGI_SRC_DIRECTORY)/$(UPLOAD_SETTINGS)  $(CGIS_DIRECTORY)/$(UPLOAD_SETTINGS)
#	cp -vf $(CGI_SRC_DIRECTORY)/upload.write  $(CGIS_DIRECTORY)/upload.write
#	cp -vf $(CGI_SRC_DIRECTORY)/upload_dafault_para.cgi  $(CGIS_DIRECTORY)/upload_dafault_para.cgi
#	cp -vf $(CGI_SRC_DIRECTORY)/history.sh  $(CGIS_DIRECTORY)/history.sh	

#	cp -vf $(CGI_SRC_DIRECTORY)/$(UPLOAD_TR069_CRET)  $(CGIS_DIRECTORY)/$(UPLOAD_TR069_CRET)
ifeq ($(CONFIG_USE_WEBUI_SSL),yes)
#	@mkdir -p $(ROOTFS_DIR)/etc_ro/certs
#	@cp -afvp $(APP_DIR)/goahead/certs/*   $(ROOTFS_DIR)/etc_ro/certs
#	@rm -rfv $(ROOTFS_DIR)/etc_ro/certs/*.csr
endif




clean:
	for i in $(DIRS) ; do make -C $$i clean ; done
	rm -rvf ./bins/

	
	
	
