#ifndef _h_UEMF
#define _h_UEMF 1

#ifdef WIN
	#include	<direct.h>
	#include	<io.h>
	#include	<sys/stat.h>
	#include	<limits.h>
	#include	<tchar.h>
	#include	<windows.h>
	#include	<winnls.h>
	#include	<time.h>
	#include	<sys/types.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* WIN */

#ifdef CE
	/*#include	<errno.h>*/
	#include	<limits.h>
	#include	<tchar.h>
	#include	<windows.h>
	#include	<winsock.h>
	#include	<winnls.h>
	#include	"CE/wincompat.h"
	#include	<winsock.h>
#endif /* CE */

#ifdef NW
	#include	<direct.h>
	#include	<io.h>
	#include	<sys/stat.h>
	#include	<time.h>
	#include	<sys/types.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<fcntl.h>
	#include	<errno.h>
	#include	<niterror.h>
	#define		EINTR EINUSE
	#define		 WEBS	1
	#include	<limits.h>
	#include	<netdb.h>
	#include	<process.h>
	#include	<tiuser.h>
	#include	<sys/time.h>
	#include	<arpa/inet.h>
	#include	<sys/types.h>
	#include	<sys/socket.h>
	#include	<sys/filio.h>
	#include	<netinet/in.h>
#endif /* NW */

#ifdef SCOV5 
	#include	<sys/types.h>
	#include	<stdio.h>
	#include	"sys/socket.h"
	#include	"sys/select.h"
	#include	"netinet/in.h"
	#include 	"arpa/inet.h"
	#include 	"netdb.h"
#endif /* SCOV5 */

#ifdef UNIX
	#include	<stdio.h>
#endif /* UNIX */

#ifdef LINUX
	#include	<sys/types.h>
	#include	<sys/stat.h>
	#include	<sys/param.h>
	#include	<limits.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<unistd.h>
	#include	<sys/socket.h>
	#include	<sys/select.h>
	#include	<netinet/in.h>
	#include 	<arpa/inet.h>
	#include 	<netdb.h>
	#include	<time.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* LINUX */

#ifdef LYNX
	#include	<limits.h>
	#include	<stdarg.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<unistd.h>
	#include	<socket.h>
	#include	<netinet/in.h>
	#include 	<arpa/inet.h>
	#include 	<netdb.h>
	#include	<time.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* LYNX */

#ifdef MACOSX
	#include	<sys/stat.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<unistd.h>
	#include	<sys/socket.h>
	#include	<netinet/in.h>
	#include 	<arpa/inet.h>
	#include 	<netdb.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* MACOSX */

#ifdef UW
	#include	<stdio.h>
#endif /* UW */

#ifdef VXWORKS
	#include	<vxWorks.h>
	#include	<sockLib.h>
	#include	<selectLib.h>
	#include	<inetLib.h>
	#include	<ioLib.h>
	#include	<stdio.h>
	#include	<stat.h>
	#include	<time.h>
	#include	<usrLib.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* VXWORKS */

#ifdef sparc
# define __NO_PACK
#endif /* sparc */

#ifdef SOLARIS
	#include	<sys/types.h>
	#include	<limits.h>
	#include	<stdio.h>
	#include	<stdlib.h>
	#include	<unistd.h>
	#include	<socket.h>
	#include	<sys/select.h>
	#include	<netinet/in.h>
	#include 	<arpa/inet.h>
	#include 	<netdb.h>
	#include	<time.h>
	#include	<fcntl.h>
	#include	<errno.h>
#endif /* SOLARIS */

#ifdef QNX4
	#include	<sys/types.h>
	#include	<stdio.h>
	#include	<sys/socket.h>
	#include	<sys/select.h>
	#include	<netinet/in.h>
	#include 	<arpa/inet.h>
	#include 	<netdb.h>
    #include    <stdlib.h>
    #include    <unistd.h>
    #include    <sys/uio.h>
    #include    <sys/wait.h>
#endif /* QNX4 */

#ifdef ECOS
	#include	<limits.h>
	#include	<cyg/infra/cyg_type.h>
	#include	<cyg/kernel/kapi.h>
	#include	<time.h>
	#include	<network.h>
	#include	<errno.h>
#endif /* ECOS */

#include	<ctype.h>
#include	<stdarg.h>
#include	<string.h>

#ifndef WEBS
#include	"messages.h"
#endif /* ! WEBS */

#ifdef UW
	#define		__NO_PACK		1
#endif /* UW */

#if (defined (SCOV5) || defined (VXWORKS) || defined (LINUX) || defined (LYNX) || defined (MACOSX))
#ifndef O_BINARY
#define O_BINARY 		0
#endif /* O_BINARY */
#define	SOCKET_ERROR	-1
#endif /* SCOV5 || VXWORKS || LINUX || LYNX || MACOSX */

#if (defined (WIN) || defined (CE))

#define		__NO_FCNTL		1

#undef R_OK
#define R_OK	4
#undef W_OK
#define W_OK	2
#undef X_OK
#define X_OK	1
#undef F_OK
#define F_OK	0
#endif /* WIN || CE */

#if (defined (LINUX) && !defined (_STRUCT_TIMEVAL))
struct timeval
{
	time_t	tv_sec;		
	time_t	tv_usec;	
};
#define _STRUCT_TIMEVAL 1
#endif /* LINUX && ! _STRUCT_TIMEVAL */

#ifdef ECOS
	#define		O_RDONLY		1
	#define		O_BINARY		2

	#define		__NO_PACK		1
	#define		__NO_EJ_FILE	1
	#define		__NO_CGI_BIN	1
	#define		__NO_FCNTL		1

	#define		LIBKERN_INLINE

#endif /* ECOS */

#ifdef QNX4
    typedef long        fd_mask;
    #define NFDBITS (sizeof (fd_mask) * NBBY)   /* bits per mask */
#endif /* QNX4 */

#ifdef NW
	#define fd_mask			fd_set
	#define INADDR_NONE		-1l
	#define Sleep			delay

	#define __NO_FCNTL		1

	#undef R_OK
	#define R_OK    4
	#undef W_OK
	#define W_OK    2
	#undef X_OK
	#define X_OK    1
	#undef F_OK
	#define F_OK    0
#endif /* NW */

#define TRACE_MAX			(4096 - 48)
#define VALUE_MAX_STRING	(4096 - 48)
#define SYM_MAX				(512)
#define XML_MAX				4096			
#define BUF_MAX				4096			
#define FMT_STATIC_MAX		256				

#if (defined (LITTLEFOOT) || defined (WEBS))
#define LF_BUF_MAX		(510)
#define LF_PATHSIZE		LF_BUF_MAX
#else
#define	LF_BUF_MAX		BUF_MAX
#define LF_PATHSIZE		PATHSIZE
#define UPPATHSIZE		PATHSIZE
#endif /* LITTLEFOOT || WEBS */

#ifndef CHAR_T_DEFINED
#define CHAR_T_DEFINED 1
#ifdef UNICODE

#define	T(x)				__TXT(x)
#define	__TXT(s)			L ## s
typedef unsigned short 		char_t;
typedef unsigned short		uchar_t;

#define	TSZ(x)				(sizeof(x) / sizeof(char_t))

#define	TASTRL(x)			((wcslen(x) + 1) * sizeof(char_t))

#else
#define	T(s) 				s
typedef char				char_t;
#define	TSZ(x)				(sizeof(x))
#define	TASTRL(x)			(strlen(x) + 1)
#ifdef WIN
typedef unsigned char		uchar_t;
#endif /* WIN */

#endif /* UNICODE */

#endif /* ! CHAR_T_DEFINED */

#ifndef TRUE
#define TRUE 1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#define GOAHEAD_COPYRIGHT \
	T("Copyright (c) GoAhead Software Inc., 1995-2000. All Rights Reserved.")

#if (defined (LITTLEFOOT) && defined (INMEM))
	#include	"lf/inmem.h"
#endif /* LITTLEFOOT && INMEM */

#ifdef UNICODE

#define gmain		wmain

#define gasctime	_wasctime
#define gsprintf	swprintf
#define gprintf		wprintf
#define gfprintf	fwprintf
#define gsscanf		swscanf
#define gvsprintf	vswprintf

#define gstrcpy		wcscpy
#define gstrncpy	wcsncpy
#define gstrncat	wcsncat
#define gstrlen		wcslen
#define gstrcat		wcscat
#define gstrcmp		wcscmp
#define gstrncmp	wcsncmp
#define gstricmp	wcsicmp
#define gstrchr		wcschr
#define gstrrchr	wcsrchr
#define gstrtok		wcstok
#define gstrnset	wcsnset
#define gstrrchr	wcsrchr
#define gstrspn	wcsspn
#define gstrcspn	wcscspn
#define gstrstr		wcsstr
#define gstrtol		wcstol

#define gfopen		_wfopen
#define gopen		_wopen
#define gclose		close
#define gcreat		_wcreat
#define gfgets		fgetws
#define gfputs		fputws
#define gfscanf		fwscanf
#define ggets		_getws
#define glseek		lseek
#define gunlink		_wunlink
#define gread		read
#define grename		_wrename
#define gwrite		write
#define gtmpnam		_wtmpnam
#define gtempnam	_wtempnam
#define gfindfirst	_wfindfirst
#define gfinddata_t	_wfinddata_t
#define gfindnext	_wfindnext
#define gfindclose	_findclose
#define gstat		_wstat
#define gaccess		_waccess
#define gchmod		_wchmod

typedef struct _stat gstat_t;

#define gmkdir		_wmkdir
#define gchdir		_wchdir
#define grmdir		_wrmdir
#define ggetcwd		_wgetcwd

#define gtolower	towlower
#define gtoupper	towupper
#ifdef CE
#define gisspace	isspace
#define gisdigit	isdigit
#define gisxdigit	isxdigit
#define gisupper	isupper
#define gislower	islower
#define gisprint	isprint
#else
#define gremove		_wremove
#define gisspace	iswspace
#define gisdigit	iswdigit
#define gisxdigit	iswxdigit
#define gisupper	iswupper
#define gislower	iswlower
#endif	/* if CE */
#define gisalnum	iswalnum
#define gisalpha	iswalpha
#define gatoi(s)	wcstol(s, NULL, 10)

#define gctime		_wctime
#define ggetenv		_wgetenv
#define gexecvp		_wexecvp

#else /* ! UNICODE */

#ifdef VXWORKS
#define gchdir		vxchdir
#define gmkdir		vxmkdir
#define grmdir		vxrmdir
#elif (defined (LYNX) || defined (LINUX) || defined (MACOSX) || defined (SOLARIS))
#define gchdir		chdir
#define gmkdir(s)	mkdir(s,0755)
#define grmdir		rmdir
#else
#define gchdir		chdir
#define gmkdir		mkdir
#define grmdir		rmdir
#endif /* VXWORKS #elif LYNX || LINUX || MACOSX || SOLARIS*/

#define gclose		close
#define gclosedir	closedir
#define gchmod		chmod
#define ggetcwd		getcwd
#define glseek		lseek
#define gloadModule	loadModule
#define gopen		open
#define gopendir	opendir
#define gread		read
#define greaddir	readdir
#define grename		rename
#define gstat		stat
#define gunlink		unlink
#define gwrite		write

#define gasctime	asctime
#define gsprintf	sprintf
#define gprintf		printf
#define gfprintf	fprintf
#define gsscanf		sscanf
#define gvsprintf	vsprintf

#define gstrcpy		strcpy
#define gstrncpy	strncpy
#define gstrncat	strncat
#define gstrlen		strlen
#define gstrcat		strcat
#define gstrcmp		strcmp
#define gstrncmp	strncmp
#define gstricmp	strcmpci
#define gstrchr		strchr
#define gstrrchr	strrchr
#define gstrtok		strtok
#define gstrnset	strnset
#define gstrrchr	strrchr
#define gstrspn	strspn
#define gstrcspn	strcspn
#define gstrstr		strstr
#define gstrtol		strtol

#define gfopen		fopen
#define gcreat		creat
#define gfgets		fgets
#define gfputs		fputs
#define gfscanf		fscanf
#define ggets		gets
#define gtmpnam		tmpnam
#define gtempnam	tempnam
#define gfindfirst	_findfirst
#define gfinddata_t	_finddata_t
#define gfindnext	_findnext
#define gfindclose	_findclose
#define gaccess		access

typedef struct stat gstat_t;

#define gremove		remove

#define gtolower	tolower
#define gtoupper	toupper
#define gisspace	isspace
#define gisdigit	isdigit
#define gisxdigit	isxdigit
#define gisalnum	isalnum
#define gisalpha	isalpha
#define gisupper	isupper
#define gislower	islower
#define gatoi		atoi

#define gctime		ctime
#define ggetenv		getenv
#define gexecvp		execvp
#ifndef VXWORKS
#define gmain		main
#endif /* ! VXWORKS */
#ifdef VXWORKS
#define	fcntl(a, b, c)
#endif /* VXWORKS */
#endif /* ! UNICODE */

#ifdef INMEM
	#include	"lf/inmem.h"
#endif

#ifndef FNAMESIZE
#define FNAMESIZE			254			
#endif /* FNAMESIZE */

#define E_MAX_ERROR			4096
#define URL_MAX				4096

#define	E_ASSERT			0x1			
#define	E_LOG				0x2			
#define	E_USER				0x3			

#define E_L					T(__FILE__), __LINE__
#define E_ARGS_DEC			char_t *file, int line
#define E_ARGS				file, line

#if (defined (ASSERT) || defined (ASSERT_CE))
	#define a_assert(C)		if (C) ; else error(E_L, E_ASSERT, T("%s"), T(#C))
#else
	#define a_assert(C)		if (1) ; else
#endif /* ASSERT || ASSERT_CE */

typedef enum {
	undefined	= 0,
	byteint		= 1,
	shortint	= 2,
	integer		= 3,
	hex			= 4,
	percent 	= 5,
	octal		= 6,
	big			= 7,
	flag		= 8,
	floating	= 9,
	string 		= 10,
	bytes 		= 11,
	symbol 		= 12,
	errmsg 		= 13
} vtype_t;

#ifndef __NO_PACK
#pragma pack(2)
#endif /* _NO_PACK */

typedef struct {

	union {
		char	flag;
		char	byteint;
		short	shortint;
		char	percent;
		long	integer;
		long	hex;
		long	octal;
		long	big[2];
#ifdef FLOATING_POINT_SUPPORT
		double	floating;
#endif /* FLOATING_POINT_SUPPORT */
		char_t	*string;
		char	*bytes;
		char_t	*errmsg;
		void	*symbol;
	} value;

	vtype_t			type;
	unsigned int	valid		: 8;
	unsigned int	allocated	: 8;		
} value_t;

#ifndef __NO_PACK
#pragma pack()
#endif /* __NO_PACK */

#define VALUE_ALLOCATE		0x1

#define value_numeric(t)	(t >= byteint && t <= big)
#define value_str(t) 		(t >= string && t <= bytes)
#define value_ok(t) 		(t > undefined && t <= symbol)

#define VALUE_VALID			{ {0}, integer, 1 }
#define VALUE_INVALID		{ {0}, undefined, 0 }

typedef struct {
	unsigned char	*buf;				
	unsigned char	*servp;				
	unsigned char	*endp;				
	unsigned char	*endbuf;			
	int				buflen;				
	int				maxsize;			
	int				increment;			
} ringq_t;

#ifdef	B_STATS
#ifndef B_L
#define B_L				T(__FILE__), __LINE__
#define B_ARGS_DEC		char_t *file, int line
#define B_ARGS			file, line
#endif /* B_L */
#endif /* B_STATS */

typedef struct {
	union {
		void	*next;							
		int		size;							
	} u;
	int			flags;							
} bType;

#define B_SHIFT			4					
#define B_ROUND			((1 << (B_SHIFT)) - 1)
#define B_MAX_CLASS		13					
#define B_MALLOCED		0x80000000			
#define B_DEFAULT_MEM	(64 * 1024)			
#define B_MAX_FILES		(512)				
#define B_FILL_CHAR		(0x77)				
#define B_FILL_WORD		(0x77777777)		
#define B_MAX_BLOCKS	(64 * 1024)			


#define B_INTEGRITY			0x8124000		
#define B_INTEGRITY_MASK	0xFFFF000		
#define B_USE_MALLOC		0x1				
#define B_USER_BUF			0x2				


typedef struct sym_t {
	struct sym_t	*forw;					
	value_t			name;					
	value_t			content;				
	int				arg;					
} sym_t;

typedef int sym_fd_t;						

#define EMF_SCRIPT_JSCRIPT			0		
#define EMF_SCRIPT_TCL	 			1		
#define EMF_SCRIPT_EJSCRIPT 		2		
#define EMF_SCRIPT_MAX	 			3

#define	MAXINT		INT_MAX
#define BITSPERBYTE 8
#define BITS(type)	(BITSPERBYTE * (int) sizeof(type))
#define	STRSPACE	T("\t \n\r\t")

#ifndef max
#define max(a,b)  (((a) > (b)) ? (a) : (b))
#endif /* max */

#ifndef min
#define min(a,b)  (((a) < (b)) ? (a) : (b))
#endif /* min */

typedef struct {
	char_t	*minute;
	char_t	*hour;
	char_t	*day;
	char_t	*month;
	char_t	*dayofweek;
} cron_t;

extern long		cronUntil(cron_t *cp, int period, time_t testTime);
extern int		cronAlloc(cron_t *cp, char_t *str);
extern int		cronFree(cron_t *cp);


#if ((defined (WIN) || defined (CE)) && defined (WEBS))
#define EWOULDBLOCK             WSAEWOULDBLOCK
#define ENETDOWN                WSAENETDOWN
#define ECONNRESET              WSAECONNRESET
#endif /* (WIN || CE) && WEBS) */

#define SOCKET_EOF				0x1		
#define SOCKET_CONNECTING		0x2		
#define SOCKET_BROADCAST		0x4		
#define SOCKET_PENDING			0x8		
#define SOCKET_FLUSHING			0x10	
#define SOCKET_DATAGRAM			0x20	
#define SOCKET_ASYNC			0x40	
#define SOCKET_BLOCK			0x80	
#define SOCKET_LISTENING		0x100	
#define SOCKET_CLOSING			0x200	
#define SOCKET_CONNRESET		0x400	

#define SOCKET_PORT_MAX			0xffff	


#define SOCKET_WOULDBLOCK		1		
#define SOCKET_RESET			2		
#define SOCKET_NETDOWN			3		
#define SOCKET_AGAIN			4		
#define SOCKET_INTR				5		
#define SOCKET_INVAL			6		

#define SOCKET_READABLE			0x2		
#define SOCKET_WRITABLE			0x4		
#define SOCKET_EXCEPTION		0x8		
#define EMF_SOCKET_MESSAGE		(WM_USER+13)

#ifdef LITTLEFOOT
#define SOCKET_BUFSIZ			510		
#else
#define SOCKET_BUFSIZ			1024	
#endif /* LITTLEFOOT */

typedef void 	(*socketHandler_t)(int sid, int mask, int data);
typedef int		(*socketAccept_t)(int sid, char *ipaddr, int port, 
					int listenSid);
typedef struct {
	char			host[64];				
	ringq_t			inBuf;					
	ringq_t			outBuf;					
	ringq_t			lineBuf;				
	socketAccept_t	accept;					
	socketHandler_t	handler;				
	int				handler_data;			
	int				handlerMask;			
	int				sid;					
	int				port;					
	int				flags;					
	int				sock;					
	int				fileHandle;				
	int				interestEvents;			
	int				currentEvents;			
	int				selectEvents;			
	int				saveMask;				
	int				error;					
} socket_t;


extern void 	 bclose();
extern int 		 bopen(void *buf, int bufsize, int flags);


#ifdef NO_BALLOC
#define balloc(B_ARGS, num) malloc(num)
#define bfree(B_ARGS, p) free(p)
#define bfreeSafe(B_ARGS, p) \
	if (p) { free(p); } else
#define brealloc(B_ARGS, p, num) realloc(p, num)
extern char_t *bstrdupNoBalloc(char_t *s);
extern char *bstrdupANoBalloc(char *s);
#define bstrdup(B_ARGS, s) bstrdupNoBalloc(s)
#define bstrdupA(B_ARGS, s) bstrdupANoBalloc(s)
#define gstrdup(B_ARGS, s) bstrdupNoBalloc(s)

#else /* BALLOC */

#ifndef B_STATS
#define balloc(B_ARGS, num) balloc(num)
#define bfree(B_ARGS, p) bfree(p)
#define bfreeSafe(B_ARGS, p) bfreeSafe(p)
#define brealloc(B_ARGS, p, size) brealloc(p, size)
#define bstrdup(B_ARGS, p) bstrdup(p)

#ifdef UNICODE
#define bstrdupA(B_ARGS, p) bstrdupA(p)
#else /* UNICODE */
#define bstrdupA bstrdup
#endif /* UNICODE */

#endif /* B_STATS */

#define gstrdup	bstrdup
extern void		*balloc(B_ARGS_DEC, int size);
extern void		bfree(B_ARGS_DEC, void *mp);
extern void		bfreeSafe(B_ARGS_DEC, void *mp);
extern void		*brealloc(B_ARGS_DEC, void *buf, int newsize);
extern char_t	*bstrdup(B_ARGS_DEC, char_t *s);

#ifdef UNICODE
extern char *bstrdupA(B_ARGS_DEC, char *s);
#else /* UNICODE */
#define bstrdupA bstrdup
#endif /* UNICODE */
#endif /* BALLOC */

extern void bstats(int handle, void (*writefn)(int handle, char_t *fmt, ...));

#define B_USE_MALLOC		0x1				/* Okay to use malloc if required */
#define B_USER_BUF			0x2				/* User supplied buffer for mem */


#ifndef LINUX
extern char_t	*basename(char_t *name);
#endif /* !LINUX */

#if (defined (UEMF) && defined (WEBS))

#define emfSchedCallback	websSchedCallBack
#define emfUnschedCallback	websUnschedCallBack
#define emfReschedCallback	websReschedCallBack
#endif /* UEMF && WEBS */

typedef void	(emfSchedProc)(void *data, int id);
extern int		emfSchedCallback(int delay, emfSchedProc *proc, void *arg);
extern void 	emfUnschedCallback(int id);
extern void 	emfReschedCallback(int id, int delay);
extern void		emfSchedProcess();
extern int		emfInstGet();
extern void		emfInstSet(int inst);
extern void		error(E_ARGS_DEC, int flags, char_t *fmt, ...);
extern void		(*errorSetHandler(void (*function)(int etype, char_t *msg))) \
					(int etype, char_t *msg);

#ifdef B_STATS
#define 		hAlloc(x) 				HALLOC(B_L, x)
#define			hAllocEntry(x, y, z)	HALLOCENTRY(B_L, x, y, z)
extern int		HALLOC(B_ARGS_DEC, void ***map);
extern int 		HALLOCENTRY(B_ARGS_DEC, void ***list, int *max, int size);
#else
extern int		hAlloc(void ***map);
extern int 		hAllocEntry(void ***list, int *max, int size);
#endif /* B_STATS */

extern int		hFree(void ***map, int handle);

extern int	 	ringqOpen(ringq_t *rq, int increment, int maxsize);
extern void 	ringqClose(ringq_t *rq);
extern int 		ringqLen(ringq_t *rq);

extern int 		ringqPutc(ringq_t *rq, char_t c);
extern int	 	ringqInsertc(ringq_t *rq, char_t c);
extern int	 	ringqPutStr(ringq_t *rq, char_t *str);
extern int 		ringqGetc(ringq_t *rq);

extern int		fmtValloc(char_t **s, int n, char_t *fmt, va_list arg);
extern int		fmtAlloc(char_t **s, int n, char_t *fmt, ...);
extern int		fmtStatic(char_t *s, int n, char_t *fmt, ...);

#ifdef UNICODE
extern int 		ringqPutcA(ringq_t *rq, char c);
extern int	 	ringqInsertcA(ringq_t *rq, char c);
extern int	 	ringqPutStrA(ringq_t *rq, char *str);
extern int 		ringqGetcA(ringq_t *rq);
#else
#define ringqPutcA ringqPutc
#define ringqInsertcA ringqInsertc
#define ringqPutStrA ringqPutStr
#define ringqGetcA ringqGetc
#endif /* UNICODE */

extern int 		ringqPutBlk(ringq_t *rq, unsigned char *buf, int len);
extern int 		ringqPutBlkMax(ringq_t *rq);
extern void 	ringqPutBlkAdj(ringq_t *rq, int size);
extern int 		ringqGetBlk(ringq_t *rq, unsigned char *buf, int len);
extern int 		ringqGetBlkMax(ringq_t *rq);
extern void 	ringqGetBlkAdj(ringq_t *rq, int size);
extern void 	ringqFlush(ringq_t *rq);
extern void 	ringqAddNull(ringq_t *rq);

extern int		scriptSetVar(int engine, char_t *var, char_t *value);
extern int		scriptEval(int engine, char_t *cmd, char_t **rslt, int chan);

extern void		socketClose();
extern void		socketCloseConnection(int sid);
extern void		socketCreateHandler(int sid, int mask, socketHandler_t 
					handler, int arg);
extern void		socketDeleteHandler(int sid);
extern int		socketEof(int sid);
extern int 		socketCanWrite(int sid);
extern void 	socketSetBufferSize(int sid, int in, int line, int out);
extern int		socketFlush(int sid);
extern int		socketGets(int sid, char_t **buf);
extern int		socketGetPort(int sid);
extern int		socketInputBuffered(int sid);
extern int		socketOpen();
extern int 		socketOpenConnection(char *host, int port, 
					socketAccept_t accept, int flags);
extern void 	socketProcess(int hid);
extern int		socketRead(int sid, char *buf, int len);
extern int 		socketReady(int hid);
extern int		socketWrite(int sid, char *buf, int len);
extern int		socketWriteString(int sid, char_t *buf);
extern int 		socketSelect(int hid, int timeout);
extern int 		socketGetHandle(int sid);
extern int 		socketSetBlock(int sid, int flags);
extern int 		socketGetBlock(int sid);
extern int 		socketAlloc(char *host, int port, socketAccept_t accept, 
					int flags);
extern void 	socketFree(int sid);
extern int		socketGetError();
extern socket_t *socketPtr(int sid);
extern int 		socketWaitForEvent(socket_t *sp, int events, int *errCode);
extern void 	socketRegisterInterest(socket_t *sp, int handlerMask);
extern int 		socketGetInput(int sid, char *buf, int toRead, int *errCode);

extern char_t	*strlower(char_t *string);
extern char_t	*strupper(char_t *string);

extern char_t	*stritoa(int n, char_t *string, int width);

extern sym_fd_t	symOpen(int hash_size);
extern void		symClose(sym_fd_t sd);
extern sym_t	*symLookup(sym_fd_t sd, char_t *name);
extern sym_t	*symEnter(sym_fd_t sd, char_t *name, value_t v, int arg);
extern int		symDelete(sym_fd_t sd, char_t *name);
extern void 	symWalk(sym_fd_t sd, void (*fn)(sym_t *symp));
extern sym_t	*symFirst(sym_fd_t sd);
extern sym_t	*symNext(sym_fd_t sd);
extern int		symSubOpen();
extern void 	symSubClose();

extern void		trace(int lev, char_t *fmt, ...);
extern void		traceRaw(char_t *buf);
extern void		(*traceSetHandler(void (*function)(int level, char_t *buf))) 
					(int level, char_t *buf);
 
extern value_t 	valueInteger(long value);
extern value_t	valueString(char_t *value, int flags);
extern value_t	valueErrmsg(char_t *value);
extern void 	valueFree(value_t *v);
extern int		vxchdir(char *dirname);

extern unsigned int hextoi(char_t *hexstring);
extern unsigned int gstrtoi(char_t *s);
extern				time_t	timeMsec();

extern char_t 	*ascToUni(char_t *ubuf, char *str, int nBytes);
extern char 	*uniToAsc(char *buf, char_t *ustr, int nBytes);
extern char_t	*ballocAscToUni(char  *cp, int alen);
extern char		*ballocUniToAsc(char_t *unip, int ulen);

extern char_t	*basicGetHost();
extern char_t	*basicGetAddress();
extern char_t	*basicGetProduct();
extern void		basicSetHost(char_t *host);
extern void		basicSetAddress(char_t *addr);

extern int		harnessOpen(char_t **argv);
extern void		harnessClose(int status);
extern void		harnessTesting(char_t *msg, ...);
extern void		harnessPassed();
extern void		harnessFailed(int line);
extern int		harnessLevel();

#endif /* _h_UEMF */

