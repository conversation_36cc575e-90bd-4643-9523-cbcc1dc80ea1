#ifndef _h_WEBS
#define _h_WEBS 1

#include	"ej.h"
#ifdef WEBS_SSL_SUPPORT
	#include	"websSSL.h"
#else	
static inline int websSSLIsOpen()
{
	return 0;
}
#endif


#ifdef WEBINSPECT_FIX 
#define WEBS_NAME				T("Demo-Webs")
#else
#define WEBS_NAME				T("GoAhead-Webs")
#endif
#define WEBS_VERSION			T("2.1.8")

#define WEBS_HEADER_BUFINC 		512			
#define WEBS_ASP_BUFINC			512			
#define WEBS_MAX_PASS			32			

#define WEBS_BUFSIZE			(8*1024)

#define WEBS_MAX_HEADER			(5 * 1024)	
#define WEBS_MAX_URL			4096		
#ifdef FEATURE_ZTE_WEB_TCARD
//added for http share 20111001 start
#define WEBS_SOCKET_BUFSIZ		4*1024		
#else
#define WEBS_SOCKET_BUFSIZ		4*1024		
//added for http share 20111001 end
#endif
//added for PC Client begin, 20120829
#define HTTP_BUFFER_SIZE    (5*1024)
//added for PC Client end, 20120829
#define WEBS_HTTP_PORT			T("httpPort")
#define CGI_BIN					T("cgi-bin")
#define CGI_BIN_UPLOAD			T("cgi-bin/upload/")
#define CGI_BIN_HTTPSHARE		T("cgi-bin/httpshare/")


#define WEBS_LOCAL_PAGE			0x1			
#define WEBS_KEEP_ALIVE			0x2			
#define WEBS_DONT_USE_CACHE		0x4			
#define WEBS_COOKIE				0x8			
#define WEBS_IF_MODIFIED		0x10		
#define WEBS_POST_REQUEST		0x20		
#define WEBS_LOCAL_REQUEST		0x40		
#define WEBS_HOME_PAGE			0x80		
#define WEBS_ASP				0x100		 
#define WEBS_HEAD_REQUEST		0x200		
#define WEBS_CLEN				0x400		
#define WEBS_FORM				0x800		
#define WEBS_REQUEST_DONE		0x1000		
#define WEBS_POST_DATA			0x2000		
#define WEBS_CGI_REQUEST		0x4000		
#define WEBS_SECURE				0x8000		
#define WEBS_AUTH_BASIC			0x10000		
#define WEBS_AUTH_DIGEST		0x20000		
#define WEBS_HEADER_DONE		0x40000		
#define WEBS_CGI_UPLOAD			0x80000		
#define WEBS_CGI_FIRMWARE_UPLOAD 0x100000	
#define WEBS_CGI_HTTPSHARE_UPLOAD 0x200000	


#define CGI_UPLOAD				T("upload")
#define CGI_FIRMWARE_UPLOAD		T("upload.cgi")
#define CGI_FIRMWARE_WRITE      T("upload.write")
#define CGI_HTTPSHARE_UPLOAD      T("httpshare.up")

//#define FIRMWARE_TMP_FILE                  T("/var/firmware_tmp_file")
#define FIRMWARE_TMP_FILE                  T("/firmware_tmp_file")

//added by liuyingnan for PC Client begin, 20120829
#define WEBS_REST_CLIENT_REQUEST       0x200000
#define WEBS_XML_CLIENT_REQUEST       0x400000
//added by liuyingnan for PC Client end, 20120829


#define WEBS_HANDLER_FIRST	0x1			
#define WEBS_HANDLER_LAST	0x2			

typedef struct websRec {
	ringq_t			header;				
	time_t			since;				
	sym_fd_t		cgiVars;			
	sym_fd_t		cgiQuery;			
	time_t			timestamp;			
	int				timeout;			
	char_t			ipaddr[40];			
	char_t			ifaddr[32];			
	char_t			type[64];			
	char_t			*dir;				
	char_t			*path;				
	char_t			*url;				
	char_t			*host;				
	char_t			*lpath;				
	char_t			*query;				
	char_t			*decodedQuery;		
	char_t			*authType;			
	char_t			*password;			
	char_t			*userName;			
	char_t			*cookie;			
	char_t			*referer;			
	char_t			*userAgent;			
	char_t			*protocol;			
	char_t			*protoVersion;		
	int				sid;				
	int				listenSid;			
	int				port;				
	int				state;				
	int				flags;				
	int				code;				
	int				clen;				
	int				wid;				
	char_t			*cgiStdin;			
	int				docfd;				
	int				numbytes;			
	int				written;			
	int				has_firmware_upload_clean; 
    int				has_firmware_upload_shell; 
	void			(*writeSocket)(struct websRec *wp);
#ifdef DIGEST_ACCESS_SUPPORT
    char_t			*realm;		
    char_t			*nonce;		
    char_t			*digest;	
    char_t			*uri;		
    char_t			*opaque;	
    char_t			*nc;		
    char_t			*cnonce;	
    char_t			*qop;		
#endif
#ifdef WEBS_SSL_SUPPORT
	websSSL_t		*wsp;
#endif
#ifdef ZXIC_ONELINK_TEST
    char_t          macaddr[20];
#endif
} websRec;

typedef websRec	*webs_t;
typedef websRec websType;

extern void 	 websSetDefaultDir(char_t *dir);
extern void 	 websSetDefaultPage(char_t *page);

extern int		 websAccept(int sid, char *ipaddr, int port, int listenSid);
extern int 		 websAspDefine(char_t *name, 
					int (*fn)(int ejid, webs_t wp, int argc, char_t **argv));
extern int 		 websAspRequest(webs_t wp, char_t *lpath);

extern void  	 websFooter(webs_t wp);
extern int 		 websFormDefine(char_t *name, void (*fn)(webs_t wp, 
					char_t *path, char_t *query));

extern int 		 websDecode64(char_t *outbuf, char_t *string, int buflen);
extern void		 websDecodeUrl(char_t *token, char_t *decoded, int len);

extern void 	 websEncode64(char_t *outbuf, char_t *string, int buflen);


extern void		 websCloseListen();
extern void  	 websDone(webs_t wp, int code);

extern void  	 websError(webs_t wp, int code, char_t *msg, ...);
extern char_t 	*websErrorMsg(int code);

extern char_t 	*websGetDefaultDir();
extern char_t 	*websGetDefaultPage();

extern char_t 	*websGetRealm();
extern int 		 websGetRequestBytes(webs_t wp);
extern char_t	*websGetRequestDir(webs_t wp);
extern int		 websGetRequestFlags(webs_t wp);

extern char_t 	*websGetHostUrl();
extern char_t 	*websGetIpaddrUrl();
extern char_t 	*websGetPassword();
extern int		 websGetPort();

extern char_t 	*websGetPublishDir(char_t *path, char_t **urlPrefix);
extern char_t	*websGetRequestType(webs_t wp);
extern int 		 websGetRequestWritten(webs_t wp);

extern char_t	*websGetRequestIpaddr(webs_t wp);
extern int 		websGetSid(webs_t wp);
extern char_t 	*websGetRequestLpath(webs_t wp);
extern char_t	*websGetRequestPath(webs_t wp);
extern char_t	*websGetRequestPassword(webs_t wp);

extern char_t 	*websGetVar(webs_t wp, char_t *var, char_t *def);
extern int 		 websCompareVar(webs_t wp, char_t *var, char_t *value);


extern int 		 websPublish(char_t *urlPrefix, char_t *path);
extern void		 websRedirect(webs_t wp, char_t *url);
extern void 	 websSecurityDelete();
extern int 		 websSecurityHandler(webs_t wp, char_t *urlPrefix, 
					char_t *webDir, int arg, char_t *url, char_t *path, 
					char_t *query);

extern void 	 websHeader(webs_t wp);
extern int		 websOpenListen(int port, int retries);
extern int 		 websPageOpen(webs_t wp, char_t *lpath, char_t *path,
					int mode, int perm);
extern void 	 websPageClose(webs_t wp);

extern void 	 websSetEnv(webs_t wp);
extern void 	 websSetHost(char_t *host);
extern void 	 websSetIpaddr(char_t *ipaddr);
extern void 	 websSetPassword(char_t *password);
extern void 	 websSetRealm(char_t *realmName);

extern void 	 websSetVar(webs_t wp, char_t *var, char_t *value);
extern int 		 websTestVar(webs_t wp, char_t *var);

extern int 		 websUrlHandlerDefine(char_t *urlPrefix, char_t *webDir, 
					int arg, int (*fn)(webs_t wp, char_t *urlPrefix, 
					char_t *webDir, int arg, char_t *url, char_t *path, 
					char_t *query), int flags);
extern int 		 websUrlHandlerDelete(int (*fn)(webs_t wp, char_t *urlPrefix,
					char_t *webDir, int arg, char_t *url, char_t *path, 
					char_t *query));
extern int		 websUrlHandlerRequest(webs_t wp);
extern int 		 websUrlParse(char_t *url, char_t **buf, char_t **host, 
					char_t **path, char_t **port, char_t **query, 
					char_t **proto, char_t **tag, char_t **ext);
extern char_t 	*websUrlType(char_t *webs, char_t *buf, int charCnt);

extern int 		 websValid(webs_t wp);
extern int 		 websValidateUrl(webs_t wp, char_t *path);

extern void 	 websSetRequestBytes(webs_t wp, int bytes);
extern void		 websSetRequestFlags(webs_t wp, int flags);
extern void 	 websSetRequestLpath(webs_t wp, char_t *lpath);
extern void 	 websSetRequestPath(webs_t wp, char_t *dir, char_t *path);
extern char_t	*websGetRequestUserName(webs_t wp);
extern void 	 websSetRequestWritten(webs_t wp, int written);

extern int 		 websWrite(webs_t wp, char_t* fmt, ...);
extern int 		 websWriteBlock(webs_t wp, char_t *buf, int nChars);
extern int 		 websWriteDataNonBlock(webs_t wp, char *buf, int nChars);


extern void 	websTimeout(void *arg, int id);
extern void		websSetTimeMark(webs_t wp);
extern void		 websTimeoutCancel(webs_t wp);


extern int 		websAlloc(int sid);
extern void 	websFree(webs_t wp);

extern void 	websReadEvent(webs_t wp);


extern char websRecvHttpBuffer[HTTP_BUFFER_SIZE];
extern int is_print_str(char *str, int len);

#ifdef EMF
extern void 	 websFormExplain(webs_t wp, char_t *path, char_t *query);
#endif

extern int web_make_salt_base64(char *id, int len);

#endif /* _h_WEBS */

