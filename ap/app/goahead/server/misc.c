#ifdef UEMF
	#include	"uemf.h"
#else
	#include	"basic/basicInternal.h"
#endif

#define kUseMemcopy

#define STR_REALLOC		0x1				
#define STR_INC			64				

typedef struct {
	char_t	*s;							
	int		size;						
	int		max;						
	int		count;						
	int		flags;						
} strbuf_t;

enum flag {
	flag_none = 0,
	flag_minus = 1,
	flag_plus = 2,
	flag_space = 4,
	flag_hash = 8,
	flag_zero = 16,
	flag_short = 32,
	flag_long = 64
};

static int 	dsnprintf(char_t **s, int size, char_t *fmt, va_list arg, int msize);
static int	strnlen(char_t *s, unsigned int n);

static void	put_ulong(strbuf_t *buf, unsigned long int value, int base,
				int upper, char_t *prefix, int width, int prec, enum flag f);

static void	put_char(strbuf_t *buf, char_t c);
static void	put_string(strbuf_t *buf, char_t *s, int len,
				int width, int prec, enum flag f);

static void put_char(strbuf_t *buf, char_t c)
{
	if (buf->count >= (buf->size - 1)) {
		if (! (buf->flags & STR_REALLOC)) {
			return;
		}
		buf->size += STR_INC;
		if (buf->size > buf->max && buf->size > STR_INC) {

			buf->size -= STR_INC;
			return;
		}
		if (buf->s == NULL) {
			buf->s = balloc(B_L, buf->size * sizeof(char_t));
		} else {
			buf->s = brealloc(B_L, buf->s, buf->size * sizeof(char_t));
		}
	}
	if(buf->s){
	buf->s[buf->count] = c;
	if (c != '\0') {
		++buf->count;
	}
	}
}

static void put_string(strbuf_t *buf, char_t *s, int len, int width,
		int prec, enum flag f)
{
	int		index;

	if (len < 0) { 
		len = strnlen(s, prec >= 0 ? prec : ULONG_MAX); 
	} else if (prec >= 0 && prec < len) { 
		len = prec; 
	}
	if (width > len && !(f & flag_minus)) {
		for (index = len; index < width; ++index) { 
			put_char(buf, ' '); 
		}
	}
	for (index = 0; index < len; ++index) { 
		put_char(buf, s[index]); 
	}
	if (width > len && f & flag_minus) {
		for (index = len; index < width; ++index) { 
			put_char(buf, ' '); 
		}
	}
}

int fmtAlloc(char_t **s, int n, char_t *fmt, ...)
{
	va_list	ap = {0};
	int		result;

	a_assert(s);
	a_assert(fmt);

	*s = NULL;
	va_start(ap, fmt);
	result = dsnprintf(s, n, fmt, ap, 0);
	va_end(ap);
	return result;
}

int fmtValloc(char_t **s, int n, char_t *fmt, va_list arg)
{
	a_assert(s);
	a_assert(fmt);

	*s = NULL;
	return dsnprintf(s, n, fmt, arg, 0);
}

int fmtRealloc(char_t **s, int n, int msize, char_t *fmt, ...)
{
	va_list	ap = {0};
	int		result;

	a_assert(s);
	a_assert(fmt);

	if (msize == -1) {
		*s = NULL;
	}
	va_start(ap, fmt);
	result = dsnprintf(s, n, fmt, ap, msize);
	va_end(ap);
	return result;
}

static int dsnprintf(char_t **s, int size, char_t *fmt, va_list arg, int msize)
{
	char_t		tmp_c;
	strbuf_t	tmp_buf;
	
	a_assert(s);
	a_assert(fmt);

	memset(&tmp_buf, 0, sizeof(tmp_buf));
	tmp_buf.s = *s;

	if (*s == NULL || msize != 0) {
		tmp_buf.max = size;
		tmp_buf.flags |= STR_REALLOC;
		if (msize != 0) {
			tmp_buf.size = max(msize, 0);
		}
		if (*s != NULL && msize != 0) {
			tmp_buf.count = gstrlen(*s);
		}
	} else {
		tmp_buf.size = size;
	}

	while ((tmp_c = *fmt++) != '\0') {
		if (tmp_c != '%' || (tmp_c = *fmt++) == '%') {
			put_char(&tmp_buf, tmp_c);
		} else {
			enum flag f = flag_none;
			int width = 0;
			int prec = -1;
			for ( ; tmp_c != '\0'; tmp_c = *fmt++) {
				if (tmp_c == '-') { 
					f |= flag_minus; 
				} else if (tmp_c == '+') { 
					f |= flag_plus; 
				} else if (tmp_c == ' ') { 
					f |= flag_space; 
				} else if (tmp_c == '#') { 
					f |= flag_hash; 
				} else if (tmp_c == '0') { 
					f |= flag_zero; 
				} else {
					break;
				}
			}
			if (tmp_c == '*') {
				width = va_arg(arg, int);
				if (width < 0) {
					f |= flag_minus;
					width = -width;
				}
				tmp_c = *fmt++;
			} else {
				for ( ; gisdigit((int)tmp_c); tmp_c = *fmt++) {
					width = width * 10 + (tmp_c - '0');
				}
			}
			if (tmp_c == '.') {
				f &= ~flag_zero;
				tmp_c = *fmt++;
				if (tmp_c == '*') {
					prec = va_arg(arg, int);
					tmp_c = *fmt++;
				} else {
					for (prec = 0; gisdigit((int)tmp_c); tmp_c = *fmt++) {
						prec = prec * 10 + (tmp_c - '0');
					}
				}
			}
			if (tmp_c == 'h' || tmp_c == 'l') {
				f |= (tmp_c == 'h' ? flag_short : flag_long);
				tmp_c = *fmt++;
			}
			if (tmp_c == 'd' || tmp_c == 'i') {
				long int value;
				if (f & flag_short) {
					value = (short int) va_arg(arg, int);
				} else if (f & flag_long) {
					value = va_arg(arg, long int);
				} else {
					value = va_arg(arg, int);
				}
				if (value >= 0) {
					if (f & flag_plus) {
						put_ulong(&tmp_buf, value, 10, 0, T("+"), width, prec, f);
					} else if (f & flag_space) {
						put_ulong(&tmp_buf, value, 10, 0, T(" "), width, prec, f);
					} else {
						put_ulong(&tmp_buf, value, 10, 0, NULL, width, prec, f);
					}
				} else {
					put_ulong(&tmp_buf, -value, 10, 0, T("-"), width, prec, f);
				}
			} else if (tmp_c == 'o' || tmp_c == 'u' || tmp_c == 'x' || tmp_c == 'X') {
				unsigned long int value;
				if (f & flag_short) {
					value = (unsigned short int) va_arg(arg, unsigned int);
				} else if (f & flag_long) {
					value = va_arg(arg, unsigned long int);
				} else {
					value = va_arg(arg, unsigned int);
				}
				if (tmp_c == 'o') {
					if (f & flag_hash && value != 0) {
						put_ulong(&tmp_buf, value, 8, 0, T("0"), width, prec, f);
					} else {
						put_ulong(&tmp_buf, value, 8, 0, NULL, width, prec, f);
					}
				} else if (tmp_c == 'u') {
					put_ulong(&tmp_buf, value, 10, 0, NULL, width, prec, f);
				} else {
					if (f & flag_hash && value != 0) {
						if (tmp_c == 'x') {
							put_ulong(&tmp_buf, value, 16, 0, T("0x"), width, prec, f);
						} else {
							put_ulong(&tmp_buf, value, 16, 1, T("0X"), width, prec, f);
						}
					} else {
						put_ulong(&tmp_buf, value, 16, ('X' == tmp_c) , NULL, width, prec, f);
					}
				}

			} else if (tmp_c == 'c') {
				char_t value = va_arg(arg, int);
				put_char(&tmp_buf, value);

			} else if (tmp_c == 's' || tmp_c == 'S') {
				char_t *value = va_arg(arg, char_t *);
				if (value == NULL) {
					put_string(&tmp_buf, T("(null)"), -1, width, prec, f);
				} else if (f & flag_hash) {
					put_string(&tmp_buf,
						value + 1, (char_t) *value, width, prec, f);
				} else {
					put_string(&tmp_buf, value, -1, width, prec, f);
				}
			} else if (tmp_c == 'p') {
				void *value = va_arg(arg, void *);
				put_ulong(&tmp_buf,
					(unsigned long int) value, 16, 0, T("0x"), width, prec, f);
			} else if (tmp_c == 'n') {
				if (f & flag_short) {
					short int *value = va_arg(arg, short int *);
					*value = tmp_buf.count;
				} else if (f & flag_long) {
					long int *value = va_arg(arg, long int *);
					*value = tmp_buf.count;
				} else {
					int *value = va_arg(arg, int *);
					*value = tmp_buf.count;
				}
			} else {
				put_char(&tmp_buf, tmp_c);
			}
		}
	}
	if (tmp_buf.s == NULL) {
		put_char(&tmp_buf, '\0');
	}

	if (*s == NULL || msize != 0) {
		*s = tmp_buf.s;
	}

	if (*s != NULL && size > 0) {
		if (tmp_buf.count < size) {
			(*s)[tmp_buf.count] = '\0';
		} else {
			(*s)[tmp_buf.size - 1] = '\0';
		}
	}

	if (msize != 0) {
		return tmp_buf.size;
	}
	return tmp_buf.count;
}

static int strnlen(char_t *s, unsigned int n)
{
	unsigned int 	len;

	len = gstrlen(s);
	return min(len, n);
}


static void put_ulong(strbuf_t *buf, unsigned long int value, int base,
		int upper, char_t *prefix, int width, int prec, enum flag f)
{
	unsigned long	x, x2;
	int				len, zeros, index;

	for (len = 1, x = 1; x < ULONG_MAX / base; ++len, x = x2) {
		x2 = x * base;
		if (x2 > value) { 
			break; 
		}
	}
	zeros = (prec > len) ? prec - len : 0;
	width -= zeros + len;
	if (prefix != NULL) { 
		width -= strnlen(prefix, ULONG_MAX); 
	}
	if (!(f & flag_minus)) {
		if (f & flag_zero) {
			for (index = 0; index < width; ++index) { 
				put_char(buf, '0'); 
			}
		} else {
			for (index = 0; index < width; ++index) { 
				put_char(buf, ' '); 
			}
		}
	}
	if (prefix != NULL) { 
		put_string(buf, prefix, -1, 0, -1, flag_none); 
	}
	for (index = 0; index < zeros; ++index) { 
		put_char(buf, '0'); 
	}
	for ( ; x > 0; x /= base) {
		int digit = (value / x) % base;
		put_char(buf, (char)((digit < 10 ? '0' : (upper ? 'A' : 'a') - 10) + digit));
	}
	if (f & flag_minus) {
		for (index = 0; index < width; ++index) { 
			put_char(buf, ' '); 
		}
	}
}

char_t *ascToUni(char_t *ubuf, char *str, int nBytes)
{
    memcpy(ubuf, str, nBytes);
	return ubuf;
}


char_t *ballocAscToUni(char *cp, int alen)
{
	char_t *unip;
	int ulen;

	ulen = (alen + 1) * sizeof(char_t);
	if ((unip = balloc(B_L, ulen)) == NULL) {
		return NULL;
	}
	ascToUni(unip, cp, ulen);
	unip[alen] = 0;
	return unip;
}

unsigned int hextoi(char_t *hexstring)
{
	register char_t			*h;
	register unsigned int	c, v;

	v = 0;
	h = hexstring;
	if (*h == '0' && (*(h+1) == 'x' || *(h+1) == 'X')) {
		h += 2;
	}
	while ((c = (unsigned int)*h++) != 0) {
		if (c >= '0' && c <= '9') {
			c -= '0';
		} else if (c >= 'a' && c <= 'f') {
			c = (c - 'a') + 10;
		} else if (c >=  'A' && c <= 'F') {
			c = (c - 'A') + 10;
		} else {
			break;
		}
		v = (v * 0x10) + c;
	}
	return v;
}

char *uniToAsc(char *buf, char_t *ustr, int nBytes)
{
#ifdef UNICODE
   if (WideCharToMultiByte(CP_ACP, 0, ustr, nBytes, buf, nBytes, 
    NULL, NULL) < 0) 
   {
      return (char*) ustr;
   }
#else
#ifdef kUseMemcopy
   memcpy(buf, ustr, nBytes);
#else
   strncpy(buf, ustr, nBytes);
#endif /* kUseMemcopy */
#endif
   return (char*) buf;
}

char *ballocUniToAsc(char_t *unip, int ulen)
{
	char * cp;

	if ((cp = balloc(B_L, ulen+1)) == NULL) {
		return NULL;
	}
	uniToAsc(cp, unip, ulen);
	cp[ulen] = '\0';
	return cp;
}

unsigned int gstrtoi(char_t *s)
{
	if (*s == '0' && (*(s+1) == 'x' || *(s+1) == 'X')) {
		s += 2;
		return hextoi(s);
	}
	return gatoi(s);
}

