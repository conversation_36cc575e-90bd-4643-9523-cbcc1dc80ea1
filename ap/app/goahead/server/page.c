#include "wsIntrn.h"
#include <sys/stat.h> 
#include <unistd.h>
#ifdef _USE_WEBUI_ZIP
#include "unzip.h"
#define WEB_ZIP_PATH "etc_ro/web.zip"
#define WEB_ZIP_PATH_HEAD "web/"
#endif

int websPageOpen(webs_t wp, char_t *lpath, char_t *path, int mode, int perm)
{
	a_assert(websValid(wp));

#ifdef WEBS_PAGE_ROM
	return websRomPageOpen(wp, path, mode, perm);
#else
#ifdef _USE_WEBUI_ZIP
	char *filename = strstr(lpath,WEB_ZIP_PATH_HEAD);
	if(filename != NULL)
	{
		unzFile uzFile = unzOpen(WEB_ZIP_PATH);
		if(unzLocateFile(uzFile,filename,0) == UNZ_OK
			&& unzOpenCurrentFile(uzFile) == UNZ_OK)
		{
			wp->docfd = uzFile;
		}
		else
		{
			unzClose(uzFile);
			wp->docfd = -1;
		}
		return wp->docfd;
	}
	else
	{
		printf("[zpr]erropen %s\n", lpath);
	}
#endif
	//printf("[httpshare]lpath->%s\n",lpath);

	if(strstr(lpath,"/mmc2") != NULL)
	{
#if 0  // cov M
		unsigned long filesize = -1; 
#else
        long filesize = -1; 
#endif
		struct stat statbuff;	
		
		if(stat(lpath, &statbuff) < 0)
		{	 
		   return filesize;    
	    }
		else
		{	 
	 	   filesize = statbuff.st_size;    
	    }
		
		printf("[httpshare]websPageOpen:size->%d\n", filesize);

#if 0	 // cov M	
		if(filesize<2*1024*1024*1024)
		{
			return (wp->docfd = gopen(lpath, mode, perm));
		}
		else
			return -1;
#else
		if(filesize > 0 && filesize <= LONG_MAX-1)
		{
			return (wp->docfd = gopen(lpath, mode, perm));
		}
		else
			return -1;

#endif
	}

	return (wp->docfd = gopen(lpath, mode, perm));
#endif /* WEBS_PAGE_ROM */
}

void websPageClose(webs_t wp)
{
	a_assert(websValid(wp));

#ifdef WEBS_PAGE_ROM
	websRomPageClose(wp->docfd);
#else
#ifdef _USE_WEBUI_ZIP
	if(wp->docfd > 0 && wp->lpath != NULL && strstr(wp->lpath,WEB_ZIP_PATH_HEAD) != NULL)
	{
		unzCloseCurrentFile(wp->docfd);
		unzClose(wp->docfd);
		wp->docfd = -1;
		return;
	}
#endif	
	if (wp->docfd >= 0) {
		close(wp->docfd);
		wp->docfd = -1;
		//printf("[httpsahre]lpath->%s,path->%s\n",wp->lpath,wp->path);
		if(strstr(wp->path,"/mmc2") != NULL)
		{
			zte_del_download_file();		
		}
	}
#endif
}

int websPageStat(webs_t wp, char_t *lpath, char_t *path, websStatType* sbuf)
{
	if(lpath == NULL)
		return -1;
#ifdef WEBS_PAGE_ROM
	return websRomPageStat(path, sbuf);
#else
#ifdef _USE_WEBUI_ZIP
	if(strstr(lpath,WEB_ZIP_PATH_HEAD) != NULL)
	{
		unz_file_info file_info = {0};
		if(unzGetCurrentFileInfo(wp->docfd, &file_info, NULL, 0, NULL, 0, NULL, 0)< 0)
		{
			printf("[webzip]errStat %s\n", lpath);
			return -1;
		}
		sbuf->size = file_info.uncompressed_size;
		sbuf->mtime = file_info.dosDate;
		sbuf->isDir = websPageIsDirectory(lpath);
		return 0;
	}
#endif
	gstat_t	s;

	if (gstat(lpath, &s) < 0) {
		return -1;
	}
	sbuf->size = s.st_size;
	sbuf->mtime = s.st_mtime;
	sbuf->isDir = s.st_mode & S_IFDIR;
	return 0;
#endif
}

int websPageIsDirectory(char_t *lpath)
{
	if(lpath == NULL)
		return 0;
#ifdef WEBS_PAGE_ROM
	websStatType	sbuf;

	if (websRomPageStat(lpath, &sbuf) >= 0) {
		return(sbuf.isDir);
	} else {
		return 0;
	}
#else
#ifdef _USE_WEBUI_ZIP
	if(strstr(lpath,WEB_ZIP_PATH_HEAD) != NULL)
	{
		char *p = lpath;
		int is_dir = 0;
		while ((*p) != '\0') {
			p++;
		}
		p--;

		if (((*p) == '/') || ((*p) == '\\')) {
			is_dir = 1;
		}
		return is_dir;
	}
#endif
	gstat_t sbuf;

	if (gstat(lpath, &sbuf) >= 0) {
		return(sbuf.st_mode & S_IFDIR);
	} else {
		return 0;
	}
#endif
}

int websPageReadData(webs_t wp, char *buf, int nBytes)
{
	a_assert(websValid(wp));
#ifdef WEBS_PAGE_ROM
	return websRomPageReadData(wp, buf, nBytes);
#else
#ifdef _USE_WEBUI_ZIP
	if(wp->lpath != NULL && strstr(wp->lpath,WEB_ZIP_PATH_HEAD) != NULL)
	{
		return unzReadCurrentFile(wp->docfd, buf, nBytes);
	}
#endif
	return read(wp->docfd, buf, nBytes);
#endif
}

void websPageSeek(webs_t wp, long offset)
{
	a_assert(websValid(wp));
#ifdef WEBS_PAGE_ROM
	websRomPageSeek(wp, offset, SEEK_CUR);
#else
#ifdef _USE_WEBUI_ZIP
	if(wp->lpath != NULL && strstr(wp->lpath,WEB_ZIP_PATH_HEAD) != NULL)
	{
		printf("[webzip]seek %s\n",wp->lpath);
		return;
	}
#endif
	if(lseek(wp->docfd, offset, SEEK_CUR) < 0)
	{
		return;	    
	}
#endif
}

