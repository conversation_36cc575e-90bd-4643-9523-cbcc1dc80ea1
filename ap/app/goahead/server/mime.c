#include	"wsIntrn.h"


websMimeType websMimeList[] = {
	{ T("text/html"), T(".asp") },

	{ T("text/css"), T(".css") },
	{ T("application/java"), T(".class") },

	{ T("text/html"), T(".htm") },
	{ T("text/html"), T(".html") },

	{ T("image/gif"), T(".gif") },
	
	{ T("application/java"), T(".jar") },
	{ T("application/x-javascript"), T(".js") },
	{ T("image/jpeg"), T(".jpg") },
   
   	{ T("application/x-shockwave-flash"), T(".swf") },

	{ T("text/plain"), T(".txt") },

	{ T("text/xml"), T(".xml") },

#ifdef MORE_MIME_TYPES
	{ T("application/postscript"), T(".ai") },
	{ T("video/x-msvideo"), T(".avi") },
	{ T("audio/basic"), T(".au snd") },
	{ T("audio/x-aiff"), T(".aif") },
	{ T("audio/x-aiff"), T(".aiff") },
	{ T("audio/x-aiff"), T(".aifc") },

	{ T("application/octet-stream"), T(".bin") },
	{ T("application/x-bcpio"), T(".bcpio") },
	
	{ T("application/x-cpio"), T(".cpio") },
	{ T("application/x-csh"), T(".csh") },
	{ T("application/x-netcdf"), T(".cdf") },
	{ T("text/html"), T(".cfm") },

	{ T("application/x-dvi"), T(".dvi") },

	{ T("application/binary"), T(".exe") },
	{ T("application/postscript"), T(".eps") },
	{ T("text/x-setext"), T(".etx") },
	
	{ T("application/x-gtar"), T(".gtar") },
	
	{ T("application/x-hdf"), T(".hdf") },
	
	{ T("image/ief"), T(".ief") },
	
	{ T("image/jpeg"), T(".jpeg") },
	{ T("image/jpeg"), T(".jpe") },
	
	{ T("application/x-latex"), T(".latex") },
	
	{ T("application/x-mif"), T(".mif") },
	{ T("application/x-troff-man"), T(".man") },
	{ T("application/x-troff-me"), T(".me") },
	{ T("application/x-troff-ms"), T(".ms") },
	{ T("video/quicktime"), T(".mov") },
	{ T("video/x-sgi-movie"), T(".movie") },
	{ T("video/mpeg"), T(".mpeg mpg mpe") },
	
	{ T("application/x-netcdf"), T(".nc") },
	
	
	{ T("application/gzip"), T(".gz") },
	
	{ T("application/oda"), T(".oda") },
	
	{ T("application/pdf"), T(".pdf") },
	{ T("application/postscript"), T(".ps") },
	{ T("application/x-ns-proxy-autoconfig"), T(".pac") },
	{ T("application/x-patch"), T(".patch") },
	{ T("image/x-portable-anymap"), T(".pnm") },
	{ T("image/x-portable-bitmap"), T(".pbm") },
	{ T("image/x-portable-graymap"), T(".pgm") },
	{ T("image/x-portable-pixmap"), T(".ppm") },

	{ T("video/quicktime"), T(".qt") },
	
	{ T("application/rtf"), T(".rtf") },
	{ T("image/x-rgb"), T(".rgb") },
	{ T("application/x-troff"), T(".roff") },
	{ T("image/x-cmu-raster"), T(".ras") },
	{ T("audio/x-wav"), T(".ram") },
	
	{ T("application/x-sh"), T(".sh") },
	{ T("text/html"), T(".shtm") },
	{ T("text/html"), T(".shtml") },
	{ T("application/x-shar"), T(".shar") },
	{ T("application/x-wais-source"), T(".src") },
	{ T("application/x-sv4cpio"), T(".sv4cpio") },
	{ T("application/x-sv4crc"), T(".sv4crc") },
	
	{ T("application/x-tar"), T(".tar") },
	{ T("application/x-tcl"), T(".tcl") },
	{ T("application/x-tex"), T(".tex") },
	{ T("text/richtext"), T(".rtx") },
	{ T("application/x-troff"), T(".t") },
	{ T("application/x-troff"), T(".tr") },
	{ T("application/x-texinfo"), T(".texinfo") },
	{ T("application/x-texinfo"), T(".texi") },
	{ T("text/tab-separated-values"), T(".tsv") },
	{ T("image/tiff"), T(".tiff") },
	{ T("image/tiff"), T(".tif") },
	
	{ T("application/x-ustar"), T(".ustar") },
	
	{ T("audio/x-wav"), T(".wav") },
	
	{ T("image/x-xbitmap"), T(".xbm") },
	{ T("image/x-xpixmap"), T(".xpm") },
	{ T("image/x-xwindowdump"), T(".xwd") },
	
	{ T("application/compress"), T(".z") },
	{ T("application/zip"), T(".zip") },
#endif
	{ NULL, NULL},
};

/*****************************************************************************/

