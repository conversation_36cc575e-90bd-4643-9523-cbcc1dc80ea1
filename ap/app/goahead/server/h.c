#ifdef UEMF
	#include	"uemf.h"
#else
	#include	"basic/basicInternal.h"
#endif


#define H_LEN		0		
#define H_USED		1		
#define H_OFFSET	2		
#define H_INCR		16		


int hFree(void ***map, int handle)
{
	int 	len;
	int		*mp;	

	a_assert(map);
	mp = &((*(int**)map)[-H_OFFSET]);
	a_assert(mp[H_LEN] >= H_INCR);

	a_assert(mp[handle + H_OFFSET]);
	a_assert(mp[H_USED]);
	mp[handle + H_OFFSET] = 0;
	if (--(mp[H_USED]) == 0) {
		bfree(B_L, (void*) mp);
		*map = NULL;
	}


	if (*map == NULL) {
		handle = -1;
	} else {
		len = mp[H_LEN];
		if (mp[H_USED] < mp[H_LEN]) {
			for (handle = len - 1; handle >= 0; handle--) {
				if (mp[handle + H_OFFSET])
					break;
			}
		} else {
			handle = len;
		}
	}
	return handle + 1;
}


#ifdef B_STATS
int HALLOC(B_ARGS_DEC, void ***map)
#else
int hAlloc(void ***map)
#endif
{
	int		handle, len, memsize, incr;
	int		*mp;

	a_assert(map);

	if (*map == NULL) {
		incr = H_INCR;
		memsize = (incr + H_OFFSET) * sizeof(void**);
#ifdef B_STATS
		if ((mp = (int*) balloc(B_ARGS, memsize)) == NULL) {
#else
		if ((mp = (int*) balloc(B_L, memsize)) == NULL) {
#endif
			return -1;
		}
		memset(mp, 0, memsize);
		mp[H_LEN] = incr;
		mp[H_USED] = 0;
		*map = (void**) &mp[H_OFFSET];
	} else {
		mp = &((*(int**)map)[-H_OFFSET]);
	}

	len = mp[H_LEN];


	if (mp[H_USED] < mp[H_LEN]) {
		for (handle = 0; handle < len; handle++) {
			if (mp[handle+H_OFFSET] == 0) {
				mp[H_USED]++;
				return handle;
			}
		}
	} else {
		handle = len;
	}

	len += H_INCR;
	memsize = (len + H_OFFSET) * sizeof(void**);
	if ((mp = (int*) brealloc(B_L, (void*) mp, memsize)) == NULL) {
		return -1;
	}
	*map = (void**) &mp[H_OFFSET];
	mp[H_LEN] = len;
    memset(&mp[H_OFFSET + len - H_INCR], 0, sizeof(int) * H_INCR);

	mp[H_USED]++;
	return handle;
}


#ifdef B_STATS
int HALLOCENTRY(B_ARGS_DEC, void ***list, int *max, int size)
#else
int hAllocEntry(void ***list, int *max, int size)
#endif
{
	int		id;
	char_t	*cp;

	a_assert(list);
	a_assert(max);

#ifdef B_STATS
	if ((id = HALLOC(B_ARGS, (void***) list)) < 0) {
#else
	if ((id = hAlloc((void***) list)) < 0) {
#endif
		return -1;
	}

	if (size > 0) {
#ifdef B_STATS
		if ((cp = balloc(B_ARGS, size)) == NULL) {
#else
		if ((cp = balloc(B_L, size)) == NULL) {
#endif
			hFree(list, id);
			return -1;
		}
		a_assert(cp);
		memset(cp, 0, size);

		(*list)[id] = (void*) cp;
	}

	if (id >= *max) {
		*max = id + 1;
	}
	return id;
}

