#ifndef _h_websSSL
#define _h_websSSL 1


#ifdef OPENSSL
#define SSLEAY		
#define USE_SOCKETS 
#include <openssl/ssl.h>
#else
#include <sslc.h>
#endif

#ifndef UEMF
	#include	"basic/basic.h"
	#include	"emf/emf.h"
#else
	#include	"uemf.h"
#endif

typedef struct {
	SSL	*ssl;
	BIO	*bio;
} websSSL_t;


extern int	websSSLIsOpen();

extern int	websSSLGets(websSSL_t *wsp, char_t **buf);
extern int	websSSLRead(websSSL_t *wsp, char_t *buf, int nChars);
extern int	websSSLWrite(websSSL_t *wsp, char_t *buf, int nChars);

extern int	websSSLOpen();
extern void websSSLClose();

extern int	websSSLEof(websSSL_t *wsp);

extern int	websSSLFlush(websSSL_t *wsp);
extern int	websSSLFree(websSSL_t *wsp);

extern int	websSSLSetKeyFile(char_t *keyFile);
extern int	websSSLSetCertFile(char_t *certFile);


#endif /* _h_websSSL */

