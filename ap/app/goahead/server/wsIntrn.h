
#ifndef _h_WEBS_INTERNAL
#define _h_WEBS_INTERNAL 1


#include	<ctype.h>
#include	<stdlib.h>
#include	<string.h>
#include	<stdarg.h>

#ifdef NETWARE
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<signal.h>
	#include	<io.h>
#endif

#ifdef WIN
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<io.h>
#endif

#ifdef CE
#ifndef UEMF
	#include	<io.h>
#endif
#endif

#ifdef NW
	#include	<fcntl.h>
	#include	<sys/stat.h>
#endif

#ifdef SCOV5
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<signal.h>
	#include	<unistd.h>
#endif

#ifdef LYNX
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<signal.h>
	#include	<unistd.h>
#endif

#ifdef UNIX
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<signal.h>
	#include	<unistd.h>
#endif

#ifdef QNX4
	#include	<fcntl.h>
	#include	<sys/stat.h>
	#include	<signal.h>
	#include	<unistd.h>
	#include	<unix.h>
#endif

#ifdef SOLARIS
	#include	<macros.h>
	#include	<fcntl.h>
	#include	<sys/stat.h>
#endif

#ifdef VXWORKS
	#include	<vxWorks.h>
	#include	<fcntl.h>
	#include	<sys/stat.h>
#endif

#ifdef UW
	#include	<fcntl.h>
	#include	<sys/stat.h>
#endif

#ifdef UEMF
	#include	"uemf.h"
	#include	"ejIntrn.h"
#else
	#include	"emf/emfInternal.h"
	#include	"ej/ejIntrn.h"
#endif

#include	"webs.h"


#define WEBS_BEGIN			0x1			
#define WEBS_HEADER			0x2			
#define WEBS_POST			0x4			
#define WEBS_POST_CLEN		0x8			
#define WEBS_PROCESSING		0x10		
#define WEBS_KEEP_TIMEOUT	15000		
#define WEBS_TIMEOUT		60000		

#define PAGE_READ_BUFSIZE	512			
#define MAX_PORT_LEN		10			
#define WEBS_SYM_INIT		64			

 
typedef struct {
	int		(*handler)(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, 
			char_t *url, char_t *path, 
			char_t *query);					
	char_t	*webDir;						
	char_t	*urlPrefix;						
	int		len;							
	int		arg;							
	int		flags;							
} websUrlHandlerType;


typedef struct {
	long			errors;					
	long			redirects;
	long			net_requests;
	long			activeNetRequests;
	long			activeBrowserRequests;
	long 			timeouts;
	long			access;					
	long 			localHits;
	long 			remoteHits;
	long 			formHits;
	long 			cgiHits;
	long 			handlerHits;
} websStatsType;

extern websStatsType websStats;				

typedef struct {
	int		code;							
	char_t	*msg;							
} websErrorType;


typedef struct {
	char_t	*type;							
	char_t	*ext;							
} websMimeType;


typedef struct {
	unsigned long	size;					
	int				isDir;					
	time_t			mtime;					
} websStatType;


typedef struct {
	char_t			*path;					
	unsigned char	*page;					
	int				size;					
	int				pos;					
} websRomPageIndexType;


#ifndef CE
#define	SOCKET_RDONLY	O_RDONLY
#define	SOCKET_BINARY	O_BINARY
#else /* CE */
#define	SOCKET_RDONLY	0x1
#define	SOCKET_BINARY	0x2
#endif /* CE */

//#define DOWNLOAD_INTERVAL  1048576  //4194304
extern int zte_process_cgi_end(webs_t wp);
extern int zte_check_downloading_file();
extern int 		 websCgiDownLoadHandler(webs_t wp, char_t *urlPrefix, 
					char_t *webDir, int arg,	char_t *url, char_t *path, 
					char_t *query);

#define DOWNLOAD_INTERVAL  1048576  //4194304
#define UPLOAD_INTERVAL 65536

#ifdef FEATURE_ZTE_WEB_TCARD
//added by guo shoupeng 10124224 for http share 20111001 start
#define UPLOAD_INTERVAL 65536
#define DOWNLOAD_INTERVAL  1048576  //4194304
#define PATH_SD_CARD "/mmc2"
#define PATH_SD_MNT "/mnt"

extern int sd_card_isExist();
extern int sd_card_isClose (webs_t wp);
extern int zte_process_cgi(webs_t wp);
extern int zte_process_cgi_end(webs_t wp);
extern int		 websGetState(webs_t wp);
extern int		 websGetlen(webs_t wp);
extern char_t * websGetURL(webs_t wp);
extern int zte_efs_write(webs_t wp);

extern int zte_http_share_process_flagfile_exist(const char *i_filename );
extern int 		 websCgiDownLoadHandler(webs_t wp, char_t *urlPrefix, 
					char_t *webDir, int arg,	char_t *url, char_t *path, 
					char_t *query);

//added by guo shoupeng 10124224 for http share 20111001 end
#endif
extern websRomPageIndexType	websRomPageIndex[];
extern websMimeType		websMimeList[];		
extern sym_fd_t			websMime;			
extern webs_t*			webs;				
extern int				websMax;			
extern char_t			websHost[64];		
extern char_t			websIpaddr[64];		
extern char_t			*websHostUrl;		
extern char_t			*websIpaddrUrl;		
extern int				websPort;			

extern char_t*	 websGetDateString(websStatType* sbuf);

extern int		strcmpci(char_t* s1, char_t* s2);

extern int		 websAspOpen();
extern void		 websAspClose();

extern int		 websAspWrite(int ejid, webs_t wp, int argc, char_t **argv);

extern void		 websFormOpen();
extern void		 websFormClose();

extern int 		 websCgiHandler(webs_t wp, char_t *urlPrefix, char_t *webDir,
					int arg, char_t *url, char_t *path, char_t *query);
extern void		 websCgiCleanup();
extern int		 websCheckCgiProc(int handle, int *status);
extern char_t	 *websGetCgiCommName();

extern int		 websLaunchCgiProc(char_t *cgiPath, char_t **argp,
					char_t **envp, char_t *stdIn, char_t *stdOut);

extern void  	 websDefaultClose();

extern void 	 websUrlHandlerClose();
extern int 		 websUrlHandlerOpen();

extern int 		 websDefaultHandler(webs_t wp, char_t *urlPrefix, 
					char_t *webDir, int arg, char_t *url, char_t *path, 
					char_t *query);
extern int 		 websFormHandler(webs_t wp, char_t *urlPrefix, char_t *webDir,
					int arg, char_t *url, char_t *path, char_t *query);

extern int 		 websOpen(int sid);
extern void 	 websResponse(webs_t wp, int code, char_t *msg, 
					char_t *redirect);
extern int 		 websJavaScriptEval(webs_t wp, char_t *script);
extern int 		 websPageReadData(webs_t wp, char *buf, int nBytes);

extern void		 websPageSeek(webs_t wp, long offset);
extern int 	 	 websPageStat(webs_t wp, char_t *lpath, char_t *path,
					websStatType *sbuf);
extern int		 websPageIsDirectory(char_t *lpath);

extern int		 websPageOpen(webs_t wp, char_t *lpath, char_t *path, int mode,
					int perm);
extern void		 websPageClose(webs_t wp);


extern int 		 websRomPageOpen(webs_t wp, char_t *path, int mode, int perm);
extern void 	 websRomPageClose(int fd);
extern int 		 websRomPageReadData(webs_t wp, char *buf, int len);
extern int 	 	 websRomPageStat(char_t *path, websStatType *sbuf);
extern long		 websRomPageSeek(webs_t wp, long offset, int origin);

extern int 		 websRomOpen();
extern void		 websRomClose();

extern void 	 websSetRequestSocketHandler(webs_t wp, int mask, 
					void (*fn)(webs_t wp));
extern int 		 websSolutionHandler(webs_t wp, char_t *urlPrefix,
					char_t *webDir, int arg, char_t *url, char_t *path, 
					char_t *query);

extern int 		 websOpenServer(int port, int retries);
extern void 	 websCloseServer();

#ifdef CE
extern int writeUniToAsc(int fid, void *buf, unsigned int len);
extern int readAscToUni(int fid, void **buf, unsigned int len);
#endif

#ifdef EMF
extern int 		 websEmfOpen();
extern void 	 websSetEmfEnvironment(webs_t wp);
extern void 	 websEmfClose();
#endif


#endif /* _h_WEBS_INTERNAL */

