#include	"uemf.h"


int emfInst;
extern void defaultErrorHandler(int etype, char_t *buf);
extern void	defaultTraceHandler(int level, char_t *buf);

static void (*traceHandler)(int level, char_t *buf) = defaultTraceHandler;
static void (*errorHandler)(int etype, char_t *msg) = defaultErrorHandler;

void error(E_ARGS_DEC, int etype, char_t *fmt, ...)
{
	va_list 	args = {0};
	char_t		*fmtBuf, *buf;

	va_start(args, fmt);
	fmtValloc(&fmtBuf, E_MAX_ERROR, fmt, args);

	if (etype == E_LOG) {
		fmtAlloc(&buf, E_MAX_ERROR, T("%s\n"), fmtBuf);
/*#ifdef DEV*/
	} else if (etype == E_ASSERT) {
		fmtAlloc(&buf, E_MAX_ERROR, 
			T("Assertion %s, failed at %s %d\n"), fmtBuf, E_ARGS); 
/*#endif*/
	} else if (etype == E_USER) {
		fmtAlloc(&buf, E_MAX_ERROR, T("%s\n"), fmtBuf);
	}

   else {
      fmtAlloc(&buf, E_MAX_ERROR, T("Unknown error"));
   }
	va_end(args);

	bfree(B_L, fmtBuf);

	printf("%s",buf);

	bfreeSafe(B_L, buf);
}

void traceRaw(char_t *buf)
{
	if(buf)
	printf("%s",buf);
}

void trace(int level, char_t *fmt, ...)
{
	va_list 	args = {0};
	char_t		*buf;

	va_start(args, fmt);
	fmtValloc(&buf, VALUE_MAX_STRING, fmt, args);
	printf("%s",buf);
	bfreeSafe(B_L, buf);
	va_end(args);
}

char_t *strlower(char_t *string)
{
	char_t	*s;

	a_assert(string);

	if (string == NULL) {
		return NULL;
	}

	s = string;
	while (*s) {
		if (gisupper(*s)) {
			*s = (char_t) gtolower(*s);
		}
		s++;
	}
	*s = '\0';
	return string;
}

char_t *strupper(char_t *string)
{
	char_t	*s;

	a_assert(string);
	if (string == NULL) {
		return NULL;
	}

	s = string;
	while (*s) {
		if (gislower(*s)) {
			*s = (char_t) gtoupper(*s);
		}
		s++;
	}
	*s = '\0';
	return string;
}

char_t *basicGetProduct()
{
	return T("uemf");
}

char_t *basicGetAddress()
{
	return T("localhost");
}

char_t *stritoa(int n, char_t *string, int width)
{
	char_t	*cp, *lim, *s;
	int		next, minus;
	char_t	tmp_buf[16];

	a_assert(string && width > 0);

	if (string == NULL) {
		if (width == 0) {
			width = 10;
		}
		if ((string = balloc(B_L, width + 1)) == NULL) {
			return NULL;
		}
	}
	if (n < 0) {
		minus = 1;
		n = -n;
		width--;
	} else {
		minus = 0;
	}

	cp = tmp_buf;
	lim = &tmp_buf[width - 1];
	while (n > 9 && cp < lim) {
		next = n;
		n /= 10;
		*cp++ = (char_t) (next - n * 10 + '0');
	}
	if (cp < lim) {
		*cp++ = (char_t) (n + '0');
	}

	s = string;
	if (minus) {
		*s++ = '-';
	}

	while (cp > tmp_buf) {
		*s++ = *--cp;
	}

	*s++ = '\0';
	return string;
}


