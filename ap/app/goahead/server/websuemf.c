#include	"ejIntrn.h"
#include	"wsIntrn.h"


typedef struct {
	void	(*routine)(void *arg, int id);
	void	*arg;
	time_t	at;
	int		schedid;
} sched_t;


static sched_t		**sched;
static int			schedMax;


void emfUnschedCallback(int schedid)
{
	sched_t	*s;

	if (sched == NULL || schedid == -1 || schedid >= schedMax || 
		(s = sched[schedid]) == NULL) {
		return;
	}
	bfree(B_L, s);
	schedMax = hFree((void***) &sched, schedid);
}


#if 0
int scriptEval(int engine, char_t *cmd, char_t **result, int chan)
{
	int		ejid;

	if (engine == EMF_SCRIPT_EJSCRIPT) {
		ejid = (int) chan;
      
		if (ejEval(ejid, cmd, result) ) {
			return 0;
		} else {
			return -1;
		}
	}
	return -1;
}
#endif


int emfSchedCallback(int delay, emfSchedProc *proc, void *arg)
{
	sched_t	*s;
	int		schedid;

	if ((schedid = hAllocEntry((void***) &sched, &schedMax,
		sizeof(sched_t))) < 0) {
		return -1;
	}
	s = sched[schedid];
	s->arg = arg;
	s->routine = proc;
	s->at = ((delay + 500) / 1000) + time(0);
	s->schedid = schedid;

	return schedid;
}

int strcmpci(char_t *s1, char_t *s2)
{
	int		rc;

	a_assert(s1 && s2);
	if (s1 == NULL || s2 == NULL) {
		return 0;
	}

	if (s1 == s2) {
		return 0;
	}

	do {
		rc = gtolower(*s1) - gtolower(*s2);
		if (*s1 == '\0') {
			break;
		}
		s1++;
		s2++;
	} while (rc == 0);
	return rc;
}

void emfSchedProcess()
{
	sched_t		*s;
	int			schedid;
	static int	next = 0;	


	if (schedMax <= 0) {
		return;
	}

	if (next >= schedMax) {
		next = 0;
	}

	schedid = next;
	while(1) {
		if ((s = sched[schedid]) != NULL &&	(int)s->at <= (int)time(0)) {
			{
				sched_t *s;
			
				a_assert(0 <= schedid && schedid < schedMax);
				s = sched[schedid];
				a_assert(s);
			
				(s->routine)(s->arg, s->schedid);
			}
			next = schedid + 1;
			return;
		}
		if (++schedid >= schedMax) {
			schedid = 0;
		}
		if (schedid == next) {

			return;
		}
	};
}

void emfReschedCallback(int schedid, int delay)
{
	sched_t	*s;

	if (sched == NULL || schedid == -1 || schedid >= schedMax || 
		(s = sched[schedid]) == NULL) {
		return;
	}
	s->at = ((delay + 500) / 1000) + time(0);
}

