/* vi: set sw=4 ts=4 sts=4: */
/*
 *	wireless.c -- Wireless Settings
 *
 *	Copyright (c) Ralink Technology Corporation All Rights Reserved.
 *
 *	$Id: wireless.c,v 1.99.2.4 2008-10-17 10:49:17 winfred Exp $
 */

#include	<stdlib.h>
#include	<sys/ioctl.h>
#include	<arpa/inet.h>
#include	<net/if.h>
#include	<net/route.h>
//#include	"../../autoconf.h"
#ifdef CONFIG_DEFAULTS_KERNEL_2_6_21
#include	<linux/types.h>
#include	<linux/socket.h>
#endif
//#include	<linux/wireless.h>
#include    <string.h>
#include    <dirent.h>
//#include	"nvram.h"
#include	"webs.h"
#include	"zte_web_lan_utils.h"
//#include	"zte_web_lan_oid.h"
//#include	"linux/autoconf.h"
#include    <sys/time.h>
#include    <signal.h>
#include    "zte_web_interface.h"

/*
 * nanosleep(2) don't depend on signal SIGALRM and could cooperate with
 * other SIGALRM-related functions(ex. setitimer(2))
 */
unsigned int Sleep(unsigned int secs)
{
	int rc = 0;
	struct timespec ts, remain;/*lint !e1080 !e565 */
	ts.tv_sec  = secs;/*lint !e115 !e1013 !e63 */
	ts.tv_nsec = 0;/*lint !e115 !e1013 !e63 */
sleep_again:
	rc = nanosleep(&ts, &remain);
	if (rc == -1 && errno == EINTR) {
		ts.tv_sec = remain.tv_sec;/*lint !e115 !e1013 !e63 */
		ts.tv_nsec = remain.tv_nsec;/*lint !e115 !e1013 !e63 */
		goto sleep_again;
	}
	return 0;
}

int setTimer(int microsec, void ((*sigroutine)(int)))
{
	struct itimerval value, ovalue;
	signal(SIGALRM, sigroutine);
	value.it_value.tv_sec = 0;
	value.it_value.tv_usec = microsec;
	value.it_interval.tv_sec = 0;
	value.it_interval.tv_usec = microsec;
	return setitimer(ITIMER_REAL, &value, &ovalue);
}
void stopTimer(void)
{
	struct itimerval value, ovalue;
	value.it_value.tv_sec = 0;
	value.it_value.tv_usec = 0;
	value.it_interval.tv_sec = 0;
	value.it_interval.tv_usec = 0;
	setitimer(ITIMER_REAL, &value, &ovalue);
}

/*
 *  argument:  [IN]     index -- the index array of deleted items(begin from 0)
 *             [IN]     count -- deleted itmes count.
 *             [IN/OUT] value -- original string/return string
 *             [IN]     delimit -- delimitor
 */
int deleteNthValueMulti(int index[], int count, char *value, char delimit)
{
	char *begin = NULL;
	char *end = NULL;
	int i = 0;
	int j = 0;
	int need_check_flag = 0;
	char *buf = strdup(value);
	if (buf == NULL)
		return -1;
	*(buf+strlen(value)) = 0;
	begin = buf;
	end = strchr(begin, delimit);
	while (end) {
		if (i == index[j]) {
			memset(begin, 0, end - begin);
			if (index[j] == 0) {
				need_check_flag = 1;
			}
			j++;
			if (j >= count) {
				break;
			}
		}
		begin = end;
		end = strchr(begin + 1, delimit);
		i++;
	}
	if (!end && index[j] == i) {
		memset(begin, 0, strlen(begin));
	}
	if (need_check_flag) {
		for (i = 0; i < strlen(value); i++) {
			if (buf[i] == '\0') {
				continue;
			}
			if (buf[i] == ';') {
				buf[i] = '\0';
			}
			break;
		}
	}
	for (i = 0, j = 0; i < strlen(value); i++) {
		if (buf[i] != '\0') {
			value[j++] = buf[i];
		}
	}
	value[j] = '\0';
	free(buf);
	return 0;
}

/*
 * substitution of getNthValue which dosen't destroy the original value
 */
int getNthValueSafe(int index, char *value, char delimit, char *result, int len)
{
	int i = 0;
	int result_len = 0;
	char *begin = NULL;
	char *end = NULL;
	if (!value || !result || !len) {
		return -1;
	}
	begin = value;
	end = strchr(begin, delimit);
	while (i < index && end) {
		begin = end + 1;
		end = strchr(begin, delimit);
		i++;
	}
	if (!end) {
		if (i == index) {
			end = begin + strlen(begin);
			result_len = (len - 1) < (end - begin) ? (len - 1) : (end - begin);
		} else {
			return -1;
		}
	} else {
		result_len = (len - 1) < (end - begin) ? (len - 1) : (end - begin);
	}
	memcpy(result, begin, result_len);
	*(result + result_len) = '\0';
	return 0;
}

/*
 * arguments: ifname  - interface name
 *            if_addr - a 18-byte buffer to store mac address
 * description: fetch mac address according to given interface name
 */
int getIfMac(char *ifname, char *if_hw)
{
	struct ifreq ifr;
	char *ptr = NULL;
	int skfd = 0;
	memset(&ifr, 0, sizeof(struct ifreq));
	if ((skfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
		slog(MISC_PRINT, SLOG_ERR,"getIfMac: open socket error");
		return -1;
	}
	strncpy(ifr.ifr_name, ifname, IF_NAMESIZE-1);
	if (ioctl(skfd, SIOCGIFHWADDR, &ifr) < 0) {
		close(skfd);
		return -1;
	}
	ptr = (char *) & (ifr.ifr_addr.sa_data); /*lint !e545*/
	sprintf(if_hw, "%02X:%02X:%02X:%02X:%02X:%02X", (ptr[0] & 0377), (ptr[1] & 0377), (ptr[2] & 0377),	(ptr[3] & 0377), (ptr[4] & 0377), (ptr[5] & 0377));
	close(skfd);
	return 0;
}

/*
 * arguments: ifname  - interface name
 *            if_addr - a 16-byte buffer to store ip address
 * description: fetch ip address, netmask associated to given interface name
 */
int getIfIp(char *ifname, char *if_addr)
{
	struct ifreq ifr;
	int skfd = 0;
	memset(&ifr, 0, sizeof(struct ifreq));
	if ((skfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
		slog(MISC_PRINT, SLOG_DEBUG,"getIfIp: open socket error");
		return -1;
	}
	strncpy(ifr.ifr_name, ifname, IF_NAMESIZE-1);
	if (ioctl(skfd, SIOCGIFADDR, &ifr) < 0) {
		close(skfd);
		return -1;
	}
	strcpy(if_addr, inet_ntoa(((struct sockaddr_in *)&ifr.ifr_addr)->sin_addr));
	close(skfd);
	return 0;
}

/*
 * arguments: index - index of the Nth value (starts from 0)
 *            old_values - un-parsed values
 *            new_value - new value to be replaced
 * description: parse values delimited by semicolon,
 *              replace the Nth value with new_value,
 *              and return the result
 * WARNING: return the internal static string -> use it carefully
 */
char *setNthValue(int index, char *old_values, char *new_value)
{
	int i = 0;
	char *p = NULL;
	char *q = NULL;
	static char ret[2048] = {0};
	char buf[8][256] = {0};
	memset(ret, 0, 2048);
	for (i = 0; i < 8; i++) {
		memset(buf[i], 0, 256);
	}
	for (i = 0, p = old_values, q = strchr(p, ';');
	     i < 8 && q != NULL;
	     i++, p = q + 1, q = strchr(p, ';')) {
		//strncpy(buf[i], p, q - p);
		if (q - p < sizeof(buf[i]))
			snprintf(buf[i],q-p+1,"%s",p);
		else
			snprintf(buf[i],sizeof(buf[i]),"%s",p);
	}
	if (i < 8) { //kw 3
		//strcpy(buf[i], p); //the last one
		if (strlen(p) < sizeof(buf[i]))
			snprintf(buf[i],strlen(p)+1,"%s",p);
		else
			snprintf(buf[i],sizeof(buf[i]),"%s",p);
	}
	strncpy(buf[index], new_value, 255);//cov
	index = (i > index) ? i : index;
	strncat(ret, buf[0],sizeof(ret)-strlen(ret)-1);
	for (i = 1; i <= index; i++) {
		//strncat(ret, ";", 2);
		//strncat(ret, buf[i], 256);
		snprintf(ret+strlen(ret),sizeof(ret)-strlen(ret),";%s",buf[i]);//cov
	}
	p = ret;
	return p;
}

/*
 * description: return LAN interface name
 */
char* getLanIfName(void)
{
	char mode[128] = {0};
	static char *if_name = "br0";
	char num_s[64] = {0};

	cfg_get_item("OperationMode", &mode, sizeof(mode));

	if (strcmp(mode, "") == 0) {
		return if_name;
	}
	if (!strncmp(mode, "0", 2)) { /*lint !e530*/
		if_name = "br0";
	} else if (!strncmp(mode, "1", 2)) {
#if defined CONFIG_RAETH_ROUTER || defined CONFIG_MAC_TO_MAC_MODE || defined CONFIG_RT_3052_ESW
		if_name = "br0";
#elif defined  CONFIG_ICPLUS_PHY && CONFIG_RT2860V2_AP_MBSS
		cfg_get_item("BssidNum", &num_s, sizeof(num_s));

		if (atoi(num_s) > 1) {	// multiple ssid
			if_name = "br0";
		} else {
			if_name = "ra0";
		}
#else
		if_name = "ra0";
#endif
	} else if (!strncmp(mode, "2", 2)) {
		if_name = "eth2";
	} else if (!strncmp(mode, "3", 2)) {
		if_name = "br0";
	}
	return if_name;
}/*lint !e529*/

/*
 * concatenate a string with an integer
 * ex: racat("SSID", 1) will return "SSID1"
 */
char *racat(char *s, int i)
{
	static char str[32] = {0};
	snprintf(str, 32, "%s%1d", s, i);
	return str;
}

