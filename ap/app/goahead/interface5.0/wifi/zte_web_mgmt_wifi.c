#include "zte_web_mgmt_wifi.h"
#include "zte_web_interface.h"

void deal_quick_setup_wifi_basic_mgmt(webs_t wp)
{
	char *ssid_name = NULL;
	char *ssid_broadcast = NULL;

	ssid_name = websGetVar(wp, "SSID_name", T(""));
	ssid_broadcast = websGetVar(wp, "SSID_Broadcast", T(""));

	cfg_set("SSID1", ssid_name);
	cfg_set("HideSSID", ssid_broadcast);
#if defined(WIFI_UNCOEXIST_5G)
	cfg_set("SSID1_5g", ssid_name);
	cfg_set("HideSSID_5g", ssid_broadcast);
#endif
	wlan_set_change_ssid_key_status();
}

void deal_quick_setup_wifi_basic_mgmt_1(webs_t wp)
{
	char *ssid_name = NULL;
	char *ssid_broadcast = NULL;

	ssid_name = websGetVar(wp, "ssid", T(""));
	ssid_broadcast = websGetVar(wp, "broadcastSsidEnabled", T(""));

	cfg_set("SSID1", ssid_name);

	wlan_set_change_ssid_key_status();

	cfg_set("HideSSID", ssid_broadcast);
}

void deal_quick_setup_wifi_security_mgmt(webs_t wp)
{
	char *security_mode = NULL;
	char *shared_mode = NULL;
	char *pass_phrase_str = NULL;
	char_t *cipher_str = NULL;
	uint8 wpa_encry_type[WF_ENCRY_TYPE_LEN] = {0};
	int pass_phrase_str_decode_length = 0;
	char *pass_phrase_str_decode = NULL;
#ifdef WEBS_SECURITY
	char *pass_phrase_b64_encode = NULL;
#endif
	char zte_pass_phrase_str[WIFI_PSW_DEFAULT_LENGTH] = {0};
	char wifi_cur_state[WIFI_STATUS_LEN] = {0};
	unsigned int wifi_set_flags = 0;
	char wifi_set_flags_str[NV_ITEM_STRING_LEN_20] = {0};

	security_mode = websGetVar(wp, "Encryption_Mode_hid", T(""));  /* OPEN | SHARED | WPSPASK...  */
	if (0 == strcmp("SHARED", security_mode) ||
	    0 == strcmp("WEPAUTO", security_mode)) {
		cfg_set("EncrypType", "WEP");
		//cfg_set("DefaultKeyID", "1");
		//cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
		//cfg_set("Key1Str1", websGetVar(wp, "Network_Key_1", T("")));
		cfg_set("DefaultKeyID", websGetVar(wp, "wep_default_key", T("")));
		cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
		cfg_set("Key1Str1", websGetVar(wp, "wep_key_1", T("")));
		cfg_set("Key2Type", websGetVar(wp, "WEP2Select", T("")));
		cfg_set("Key2Str1", websGetVar(wp, "wep_key_2", T("")));
		cfg_set("Key3Type", websGetVar(wp, "WEP3Select", T("")));
		cfg_set("Key3Str1", websGetVar(wp, "wep_key_3", T("")));
		cfg_set("Key4Type", websGetVar(wp, "WEP4Select", T("")));
		cfg_set("Key4Str1", websGetVar(wp, "wep_key_4", T("")));
		wlan_set_change_ssid_key_status();
	} else if (0 == strcmp(WF_AU_OPEN, security_mode)) {
		shared_mode  = websGetVar(wp, "security_shared_mode", T(""));
		if (0 == strcmp("NONE", shared_mode)) {  /* OPEN-NONE */
			cfg_set("EncrypType", "NONE");
			cfg_set(NV_WIFI_WPA_PASS_ENCODE, "");
			cfg_set(NV_WIFI_WPA_PASS, "");
			cfg_set("Key1Str1", "");
			cfg_set("Key2Str1", "");
			cfg_set("Key3Str1", "");
			cfg_set("Key4Str1", "");
		#if defined(WIFI_UNCOEXIST_5G)
			cfg_set("EncrypType_5g", "NONE");
			cfg_set("WPAPSK1_encode_5g", "");
			cfg_set("WPAPSK1_5g", "");
			cfg_set("Key1Str1", "");
			cfg_set("Key2Str1", "");
			cfg_set("Key3Str1", "");
			cfg_set("Key4Str1", "");
		#endif
			wlan_set_change_ssid_key_status();
		} else {
			cfg_set("EncrypType", "WEP");
			//cfg_set("DefaultKeyID", "1");
			//cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
			//cfg_set("Key1Str1", websGetVar(wp, "Network_Key_1", T("")));
			cfg_set("DefaultKeyID", websGetVar(wp, "wep_default_key", T("")));
			cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
			cfg_set("Key1Str1", websGetVar(wp, "wep_key_1", T("")));
			cfg_set("Key2Type", websGetVar(wp, "WEP2Select", T("")));
			cfg_set("Key2Str1", websGetVar(wp, "wep_key_2", T("")));
			cfg_set("Key3Type", websGetVar(wp, "WEP3Select", T("")));
			cfg_set("Key3Str1", websGetVar(wp, "wep_key_3", T("")));
			cfg_set("Key4Type", websGetVar(wp, "WEP4Select", T("")));
			cfg_set("Key4Str1", websGetVar(wp, "wep_key_4", T("")));
			wlan_set_change_ssid_key_status();
		}
	} else if (0 == strcmp("WPAPSK", security_mode) ||
	           0 == strcmp("WPA2PSK", security_mode) ||
	           0 == strcmp("WPAPSKWPA2PSK", security_mode) ||
	           0 == strcmp("WPA3Personal", security_mode) ||
	           0 == strcmp("WPA2WPA3", security_mode)) {
#ifndef CONFIG_CHINA_UNICOM
		if (STR_EQUAL(security_mode, WF_AU_WPA_WPA2)) {
			cipher_str = websGetVar(wp, T("cipher_str"), T(""));
		} else
#endif
		{
			cipher_str = websGetVar(wp, T("WPA_ENCRYPTION_hid"), T(""));
		}
		switch (cipher_str[0]) {
		case '0':
			strncpy(wpa_encry_type, WF_ENCRY_TKIP, sizeof(wpa_encry_type) - 1);
			break;
		case '1':
			strncpy(wpa_encry_type, WF_ENCRY_AES, sizeof(wpa_encry_type) - 1);
			break;
		case '2':
			strncpy(wpa_encry_type, WF_ENCRY_TKIP_AES, sizeof(wpa_encry_type) - 1);
			break;
		default:
			return;
		}
		cfg_set("EncrypType", wpa_encry_type);
	#if defined(WIFI_UNCOEXIST_5G)
		cfg_set("EncrypType_5g", wpa_encry_type);
	#endif

#ifndef CONFIG_CHINA_UNICOM
		if (0 == strcmp("WPAPSKWPA2PSK", security_mode)) {
#endif
			cfg_set("cipher_str", cipher_str);

#ifndef CONFIG_CHINA_UNICOM
		}
#endif
		cfg_set("RekeyMethod", "TIME");
		cfg_set("RekeyInterval", websGetVar(wp, "Key_Rotation_Interval_hid", T("")));
		// cfg_set("DefaultKeyID", "2");

		pass_phrase_str = websGetVar(wp, T("WPA_PreShared_Key"), T(""));
		slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str:%s.\n", pass_phrase_str); /*lint !e26*/
#ifdef WEBS_SECURITY
		pass_phrase_str_decode = js_aes_decode(pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
#else
		pass_phrase_str_decode = zte_base64_decode((const unsigned char *)pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
#endif
		slog(MISC_PRINT, SLOG_DEBUG, "login2 ->  zte_password:%s.\n", pass_phrase_str_decode); /*lint !e26*/

		if (NULL == pass_phrase_str_decode) {
			slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str_decode: psw is empty.\n"); /*lint !e26*/
			free(pass_phrase_str_decode);
			return;
		}
		//strncpy(zte_pass_phrase_str, pass_phrase_str_decode, pass_phrase_str_decode_length);
		if(pass_phrase_str_decode_length >= sizeof(zte_pass_phrase_str))
			snprintf(zte_pass_phrase_str,sizeof(zte_pass_phrase_str),"%s",pass_phrase_str_decode);
		else
			snprintf(zte_pass_phrase_str,pass_phrase_str_decode_length+1,"%s",pass_phrase_str_decode);
		free(pass_phrase_str_decode);
		slog(MISC_PRINT, SLOG_DEBUG, "login3 -> zte_psw_admin:%s.\n", zte_pass_phrase_str); //cov m

#ifdef WEBS_SECURITY
		pass_phrase_b64_encode = zte_base64_encode(zte_pass_phrase_str, strlen(zte_pass_phrase_str));
		if (NULL == pass_phrase_b64_encode) {
			slog(MISC_PRINT, SLOG_DEBUG,"pass_phrase_b64_encode: psw is NULL.\n");/*lint !e26*/
			return;
		}
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_b64_encode);
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
		cfg_set("WPAPSK1_enaes", pass_phrase_str);
	#if defined(WIFI_UNCOEXIST_5G)
		cfg_set("WPAPSK1_encode_5g", pass_phrase_b64_encode);
		cfg_set("WPAPSK1_5g", zte_pass_phrase_str);
		cfg_set("WPAPSK1_enaes_5g", pass_phrase_str);
	#endif
		free(pass_phrase_b64_encode);
#else
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_str);
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
	#if defined(WIFI_UNCOEXIST_5G)
		cfg_set("WPAPSK1_encode_5g", pass_phrase_str);
		cfg_set("WPAPSK1_5g", zte_pass_phrase_str);
	#endif
#endif
		wlan_set_change_ssid_key_status();
	} else if (0 == strcmp("WAPISK", security_mode)) {
		cfg_set("RekeyMethod", "TIME");
		cfg_set("RekeyInterval", websGetVar(wp, "Key_Rotation_Interval_hid", T("")));
		cfg_set("wapiType", "2");
		pass_phrase_str = websGetVar(wp, T("WPA_PreShared_Key"), T(""));
		slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str:%s.\n", pass_phrase_str); /*lint !e26*/
#ifdef WEBS_SECURITY
		pass_phrase_str_decode = js_aes_decode(pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
#else
		pass_phrase_str_decode = zte_base64_decode((const unsigned char *)pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
#endif
		slog(MISC_PRINT, SLOG_DEBUG, "login2 -> zte_password:%s.\n", pass_phrase_str_decode); /*lint !e26*/

		if (NULL == pass_phrase_str_decode) {
			slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str_decode: psw is empty.\n"); /*lint !e26*/
			free(pass_phrase_str_decode);
			return;
		}
		//strncpy(zte_pass_phrase_str, pass_phrase_str_decode, pass_phrase_str_decode_length);
		if(pass_phrase_str_decode_length >= sizeof(zte_pass_phrase_str))
			snprintf(zte_pass_phrase_str,sizeof(zte_pass_phrase_str),"%s",pass_phrase_str_decode);
		else
			snprintf(zte_pass_phrase_str,pass_phrase_str_decode_length+1,"%s",pass_phrase_str_decode);
		free(pass_phrase_str_decode);
		slog(MISC_PRINT, SLOG_DEBUG, "login3 -> zte_psw_admin:%s.\n", zte_pass_phrase_str); /*lint !e26*/

#ifdef WEBS_SECURITY
		pass_phrase_b64_encode = zte_base64_encode(zte_pass_phrase_str, strlen(zte_pass_phrase_str));
		if (NULL == pass_phrase_b64_encode) {
			slog(MISC_PRINT, SLOG_DEBUG,"pass_phrase_b64_encode: psw is NULL.\n");/*lint !e26*/
			return;
		}
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_b64_encode);
		free(pass_phrase_b64_encode);
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
		cfg_set("WPAPSK1_enaes", pass_phrase_str);
#else
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_str);
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
#endif
		wlan_set_change_ssid_key_status();
	} else {
		return;
	}

	cfg_set("AuthMode", security_mode);
#if defined(WIFI_UNCOEXIST_5G)
	cfg_set("AuthMode_5g", security_mode);
#endif
	//cfg_set("IEEE8021X", "0");

	/*TBD:notify wifi module to make parameters go into effect*/
	//(void)snprintf(wifi_set_flags_str,sizeof(wifi_set_flags_str)-1,"%u",wifi_set_flags);
	//(void)zte_web_write(WIFI_NV_ITEM_WIFI_SET_FLAGS, wifi_set_flags_str);
	//slog(MISC_PRINT,SLOG_DEBUG,"zte_wlan_ssid1_set: wifi_set_flags [%u] to [%s].", wifi_set_flags,wifi_set_flags_str);/*lint !e26*/
	//printf(" deal_quick_setup_wifi_security------------------------- 11111\n");

	cfg_get_item("wifi_cur_state", wifi_cur_state, sizeof(wifi_cur_state));
	printf("[%s]wifi_cur_state is %s\n", __FUNCTION__, wifi_cur_state);
	if (strcmp(wifi_cur_state, WIFI_OPENED) != 0) {
		printf("[%s]wifi_cur_state is 0, wifi is off, don't send msg to wlan-server\n", __FUNCTION__);
		return;
	}
	
	wifi_set_flags = ZTE_WLAN_SSID_SET|ZTE_WLAN_BROADCAST_SET|ZTE_WLAN_BASIC_SECURITY_SET;
	(void)snprintf(wifi_set_flags_str, sizeof(wifi_set_flags_str) - 1, "%u", wifi_set_flags);
	(void)zte_web_write(WIFI_NV_ITEM_WIFI_SET_FLAGS, wifi_set_flags_str);
	
	slog(MISC_PRINT, SLOG_NORMAL, "send wifi para to wlan-server@ssid1"); /*lint !e26*/
	//zte_send_message(MODULE_ID_WIFI,MSG_CMD_WIFI_SSID,NULL,NULL);
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_WIFI, MSG_CMD_WIFI_CFG_AP, 0, NULL, 0);

	slog(MISC_PRINT, SLOG_DEBUG, "wifi set cmd done!");	/*lint !e26*/

	return;
}


void deal_quick_setup_wifi_security_mgmt_1(webs_t wp)
{
	char *security_mode = NULL;
	char *shared_mode = NULL;
	char *pass_phrase_str = NULL;
	char_t *cipher_str = NULL;
	uint8 wpa_encry_type[WF_ENCRY_TYPE_LEN] = {0};
	int pass_phrase_str_decode_length = 0;
	char *pass_phrase_str_decode = NULL;
#ifdef WEBS_SECURITY
	char *pass_phrase_b64_encode = NULL;
#endif
	char zte_pass_phrase_str[WIFI_PSW_DEFAULT_LENGTH] = {0};
	char wifi_cur_state[WIFI_STATUS_LEN] = {0};
	security_mode = websGetVar(wp, "security_mode", T(""));  /* OPEN | SHARED | WPSPASK...  */
	if (0 == strcmp("SHARED", security_mode) ||
	    0 == strcmp("WEPAUTO", security_mode)) {
		cfg_set("EncrypType", "WEP");
		//cfg_set("DefaultKeyID", "1");
		//cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
		//cfg_set("Key1Str1", websGetVar(wp, "Network_Key_1", T("")));
		cfg_set("DefaultKeyID", websGetVar(wp, "wep_default_key", T("")));
		cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
		cfg_set("Key1Str1", websGetVar(wp, "wep_key_1", T("")));
		cfg_set("Key2Type", websGetVar(wp, "WEP2Select", T("")));
		cfg_set("Key2Str1", websGetVar(wp, "wep_key_2", T("")));
		cfg_set("Key3Type", websGetVar(wp, "WEP3Select", T("")));
		cfg_set("Key3Str1", websGetVar(wp, "wep_key_3", T("")));
		cfg_set("Key4Type", websGetVar(wp, "WEP4Select", T("")));
		cfg_set("Key4Str1", websGetVar(wp, "wep_key_4", T("")));
		wlan_set_change_ssid_key_status();
	} else if (0 == strcmp(WF_AU_OPEN, security_mode)) {
		shared_mode  = websGetVar(wp, "security_shared_mode", T(""));
		if (0 == strcmp("NONE", shared_mode)) {  /* OPEN-NONE */
			cfg_set("EncrypType", "NONE");
			cfg_set(NV_WIFI_WPA_PASS_ENCODE, "");
			cfg_set(NV_WIFI_WPA_PASS, "");
			cfg_set("Key1Str1", "");
			cfg_set("Key2Str1", "");
			cfg_set("Key3Str1", "");
			cfg_set("Key4Str1", "");
			wlan_set_change_ssid_key_status();
		} else {
			cfg_set("EncrypType", "WEP");
			//cfg_set("DefaultKeyID", "1");
			//cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
			//cfg_set("Key1Str1", websGetVar(wp, "Network_Key_1", T("")));
			cfg_set("DefaultKeyID", websGetVar(wp, "wep_default_key", T("")));
			cfg_set("Key1Type", websGetVar(wp, "WEP1Select", T("")));
			cfg_set("Key1Str1", websGetVar(wp, "wep_key_1", T("")));
			cfg_set("Key2Type", websGetVar(wp, "WEP2Select", T("")));
			cfg_set("Key2Str1", websGetVar(wp, "wep_key_2", T("")));
			cfg_set("Key3Type", websGetVar(wp, "WEP3Select", T("")));
			cfg_set("Key3Str1", websGetVar(wp, "wep_key_3", T("")));
			cfg_set("Key4Type", websGetVar(wp, "WEP4Select", T("")));
			cfg_set("Key4Str1", websGetVar(wp, "wep_key_4", T("")));
			wlan_set_change_ssid_key_status();
		}
	} else if (0 == strcmp("WPAPSK", security_mode) ||
	           0 == strcmp("WPA2PSK", security_mode) ||
	           0 == strcmp("WPAPSKWPA2PSK", security_mode) ||
	           0 == strcmp("WPA3Personal", security_mode) ||
	           0 == strcmp("WPA2WPA3", security_mode)) {
		cipher_str = websGetVar(wp, T("cipher"), T(""));
		switch (cipher_str[0]) {
		case '0':
			strncpy(wpa_encry_type, WF_ENCRY_TKIP, sizeof(wpa_encry_type) - 1);
			break;
		case '1':
			strncpy(wpa_encry_type, WF_ENCRY_AES, sizeof(wpa_encry_type) - 1);
			break;
		case '2':
			strncpy(wpa_encry_type, WF_ENCRY_TKIP_AES, sizeof(wpa_encry_type) - 1);
			break;
		default:
			return;
		}
		cfg_set("EncrypType", wpa_encry_type);

#ifndef CONFIG_CHINA_UNICOM
		if (0 == strcmp("WPAPSKWPA2PSK", security_mode)) {
#endif
			cfg_set("cipher_str", cipher_str);

#ifndef CONFIG_CHINA_UNICOM
		}
#endif
		cfg_set("RekeyMethod", "TIME");
		cfg_set("RekeyInterval", websGetVar(wp, "Key_Rotation_Interval_hid", T("")));
		// cfg_set("DefaultKeyID", "2");

		pass_phrase_str = websGetVar(wp, T("passphrase"), T(""));
		slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str:%s.\n", pass_phrase_str); /*lint !e26*/
		pass_phrase_str_decode = zte_base64_decode((const unsigned char *)pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
		slog(MISC_PRINT, SLOG_DEBUG, "login2 ->  zte_password:%s.\n", pass_phrase_str_decode); /*lint !e26*/

		if (NULL == pass_phrase_str_decode) {
			slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str_decode: psw is empty.\n"); /*lint !e26*/
			free(pass_phrase_str_decode);
			return;
		}
		//strncpy(zte_pass_phrase_str, pass_phrase_str_decode, pass_phrase_str_decode_length);
		if(pass_phrase_str_decode_length >= sizeof(zte_pass_phrase_str))
			snprintf(zte_pass_phrase_str,sizeof(zte_pass_phrase_str),"%s",pass_phrase_str_decode);
		else
			snprintf(zte_pass_phrase_str,pass_phrase_str_decode_length+1,"%s",pass_phrase_str_decode);
		free(pass_phrase_str_decode);
		slog(MISC_PRINT, SLOG_DEBUG, "login3 -> zte_psw_admin:%s.\n", zte_pass_phrase_str); //cov m

#ifdef WEBS_SECURITY
		pass_phrase_b64_encode = zte_base64_encode(zte_pass_phrase_str, strlen(zte_pass_phrase_str));
		if (NULL == pass_phrase_b64_encode) {
			slog(MISC_PRINT, SLOG_DEBUG,"pass_phrase_b64_encode: psw is NULL.\n");/*lint !e26*/
			return;
		}
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_b64_encode);
		free(pass_phrase_b64_encode);
#else
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_str);
#endif
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
		wlan_set_change_ssid_key_status();
	} else if (0 == strcmp("WAPISK", security_mode)) {
		cfg_set("RekeyMethod", "TIME");
		cfg_set("RekeyInterval", websGetVar(wp, "Key_Rotation_Interval_hid", T("")));
		cfg_set("wapiType", "2");
		pass_phrase_str = websGetVar(wp, T("passphrase"), T(""));
		slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str:%s.\n", pass_phrase_str); /*lint !e26*/
		pass_phrase_str_decode = zte_base64_decode((const unsigned char *)pass_phrase_str, strlen(pass_phrase_str), &pass_phrase_str_decode_length);
		slog(MISC_PRINT, SLOG_DEBUG, "login2 -> zte_password:%s.\n", pass_phrase_str_decode); /*lint !e26*/

		if (NULL == pass_phrase_str_decode) {
			slog(MISC_PRINT, SLOG_DEBUG, "pass_phrase_str_decode: psw is empty.\n"); /*lint !e26*/
			free(pass_phrase_str_decode);
			return;
		}
		//strncpy(zte_pass_phrase_str, pass_phrase_str_decode, pass_phrase_str_decode_length);
		if(pass_phrase_str_decode_length >= sizeof(zte_pass_phrase_str))
			snprintf(zte_pass_phrase_str,sizeof(zte_pass_phrase_str),"%s",pass_phrase_str_decode);
		else
			snprintf(zte_pass_phrase_str,pass_phrase_str_decode_length+1,"%s",pass_phrase_str_decode);
		free(pass_phrase_str_decode);
		slog(MISC_PRINT, SLOG_DEBUG, "login3 -> zte_psw_admin:%s.\n", zte_pass_phrase_str); /*lint !e26*/

#ifdef WEBS_SECURITY
		pass_phrase_b64_encode = zte_base64_encode(zte_pass_phrase_str, strlen(zte_pass_phrase_str));
		if (NULL == pass_phrase_b64_encode) {
			slog(MISC_PRINT, SLOG_DEBUG,"pass_phrase_b64_encode: psw is NULL.\n");/*lint !e26*/
			return;
		}
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_b64_encode);
		free(pass_phrase_b64_encode);
#else
		cfg_set(NV_WIFI_WPA_PASS_ENCODE, pass_phrase_str);
#endif
		cfg_set(NV_WIFI_WPA_PASS, zte_pass_phrase_str);
		wlan_set_change_ssid_key_status();
	} else {
		return;
	}

	cfg_set("AuthMode", security_mode);
	//cfg_set("IEEE8021X", "0");

	/*TBD:notify wifi module to make parameters go into effect*/
	//(void)snprintf(wifi_set_flags_str,sizeof(wifi_set_flags_str)-1,"%u",wifi_set_flags);
	//(void)zte_web_write(WIFI_NV_ITEM_WIFI_SET_FLAGS, wifi_set_flags_str);
	//slog(MISC_PRINT,SLOG_DEBUG,"zte_wlan_ssid1_set: wifi_set_flags [%u] to [%s].", wifi_set_flags,wifi_set_flags_str);/*lint !e26*/
	//printf(" deal_quick_setup_wifi_security------------------------- 11111\n");

	cfg_get_item("wifi_cur_state", wifi_cur_state, sizeof(wifi_cur_state));
	if (strcmp(wifi_cur_state, WIFI_OPENED) != 0) {
		slog(MISC_PRINT, SLOG_NORMAL,"[%s]wifi_cur_state is 0, wifi is off, don't send msg to wlan-server\n", __FUNCTION__);
		return;
	}
	slog(MISC_PRINT, SLOG_NORMAL, "send wifi para to wlan-server@ssid1"); /*lint !e26*/
	//zte_send_message(MODULE_ID_WIFI,MSG_CMD_WIFI_SSID,NULL,NULL);
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_WIFI, MSG_CMD_WIFI_CFG_AP, 0, NULL, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "wifi set cmd done!");	/*lint !e26*/

	return;
}

void deal_quick_setup_wps_mgmt(webs_t wp)
{
	return;
}
