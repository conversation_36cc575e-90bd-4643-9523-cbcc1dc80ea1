/*
 *	zte_web_wireless_utils.h -- Wireless Configuration Header
 *
 *	Copyright (c) ZTE Corporation All Rights Reserved.
 *
 *	$Id: zte_web_wireless_utils.h,v 0.1 2010-12-15 $
 *  Authors :	ZHAOYONG - <EMAIL>>
 */
#ifndef __WIRELESS__H__
#define __WIRELESS__H__
#include <stdlib.h>
//#include <stdint.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
//#include	<net/if.h>
//#include	<net/route.h>

#ifdef CONFIG_DEFAULTS_KERNEL_2_6_21
#include <linux/types.h>
#include <linux/socket.h>
#endif
//#include <linux/wireless.h>
//#include <net/if.h>
#include <string.h>
#include <dirent.h>
//#include "nvram.h"
#include "webs.h"
//#include "zte_web_lan_oid.h"
//#include "linux/autoconf.h"
#include "zte_web_interface.h"
#include <sys/socket.h>
#include <asm/types.h>
#include <sys/time.h>
#include <time.h>
#include <signal.h>
#include <ctype.h>

#define RT2860_NVRAM 0
#define RTINIC_NVRAM 1
#define RT2561_NVRAM 2
#define RTDEV_NVRAM    	1
#define CERT_NVRAM    	2
#define WAPI_NVRAM    	3
#define PACKED  __attribute__ ((packed))
#define USHORT  unsigned short
#define UCHAR   unsigned char
#define WPS_AP_TIMEOUT_SECS				120000				// 120 seconds
#define WPS_AP_CATCH_CONFIGURED_TIMER	100					// 0.1 sec 
#define WIFIPIN 8
#define LFW(x, y)	do{												\
						if(! ( x = websGetVar(wp, T(#y), T(""))))	\
							return;									\
					}while(0)

#define LFWi(x, y)	do{														\
						char_t *x##_str;									\
						if(! ( x##_str = websGetVar(wp, T(#y), T(""))))		\
							return;											\
						x = atoi(x##_str);									\
					}while(0)

#define LFF(result, nvram, x, n)	\
							do{		char tmp[128];										\
									char strx[512];   \
									cfg_get_item(#x,strx,sizeof(strx)); \
									strcpy(strx,x); \
									if(!x)				\
										tmp[0] = '\0';									\
									else{												\
										if( getNthValueSafe(n, x, ';', tmp, 128) != -1){\
											gstrncat(result, tmp, 4096);				\
										}												\
									}gstrncat(result, "\r", 4096);						\
							}while(0)

#define cprintf(fmt, args...) do {  \
    FILE *fp = fopen("/dev/console", "w");  \
    if (fp) {   \
        fprintf(fp, fmt, ## args);  \
        fclose(fp); \
    }   \
} while (0)

#define PBC_WPS_34 34
#define RALINK_GPIO_REG_IRQ		0x0A

#if 0
#ifdef CONFIG_RT2860V2_AP_WAPI
#define MAX_NUMBER_OF_MAC               96
#else
#define MAX_NUMBER_OF_MAC               32 // if MAX_MBSSID_NUM is 8, this value can't be larger than 211
#endif

typedef union _MACHTTRANSMIT_SETTING {
	struct  {
		unsigned short  MCS: 7; // MCS
		unsigned short  BW: 1;  //channel bandwidth 20MHz or 40 MHz
		unsigned short  ShortGI: 1;
		unsigned short  STBC: 2; //SPACE
		unsigned short	eTxBF: 1;
		unsigned short	rsv: 1;
		unsigned short	iTxBF: 1;
		unsigned short  MODE: 2; // Use definition MODE_xxx.
	} field;
	unsigned short      word;
} MACHTTRANSMIT_SETTING;

typedef struct _RT_802_11_MAC_ENTRY {
#ifdef CONFIG_RT2860V2_AP_V24_DATA_STRUCTURE
	unsigned char			ApIdx;
#endif
	unsigned char           Addr[6];
	unsigned char           Aid;
	unsigned char           Psm;     // 0:PWR_ACTIVE, 1:PWR_SAVE
	unsigned char           MimoPs;  // 0:MMPS_STATIC, 1:MMPS_DYNAMIC, 3:MMPS_Enabled
	char                    AvgRssi0;
	char                    AvgRssi1;
	char                    AvgRssi2;
	unsigned int            ConnectedTime;
	MACHTTRANSMIT_SETTING	TxRate;
	unsigned int			LastRxRate;
	int						StreamSnr[3];
	int						SoundingRespSnr[3];
} RT_802_11_MAC_ENTRY;

typedef struct _RT_802_11_MAC_TABLE {
	unsigned long            Num;
	RT_802_11_MAC_ENTRY      Entry[MAX_NUMBER_OF_MAC]; //MAX_LEN_OF_MAC_TABLE = 32
} RT_802_11_MAC_TABLE;

/*add by myc for wifi_client_show 2012-04-19 begin*/

typedef struct _DHCPOFFERADDR {
	unsigned long expires;
	unsigned long ip;
	unsigned char mac[6];
	unsigned char host_name[20];
	unsigned char pad[2];
} DHCPOFFERADDR;

#endif

typedef struct PACKED _WSC_CONFIGURED_VALUE {
	USHORT WscConfigured; // 1 un-configured; 2 configured
	UCHAR   WscSsid[32 + 1];
	USHORT WscAuthMode; // mandatory, 0x01: open, 0x02: wpa-psk, 0x04: shared, 0x08:wpa, 0x10: wpa2, 0x
	USHORT  WscEncrypType;  // 0x01: none, 0x02: wep, 0x04: tkip, 0x08: aes
	UCHAR   DefaultKeyIdx;
	UCHAR   WscWPAKey[64 + 1];
} WSC_CONFIGURED_VALUE;

typedef struct {
	unsigned int irq;		//request irq pin number
	pid_t pid;			//process id to notify
} ralink_gpio_reg_info;

typedef struct PACKED _NDIS80211SSID {
	unsigned int    SsidLength;   // length of SSID field below, in bytes;
	// this can be zero.
	unsigned char   Ssid[32]; // SSID information field
} NDIS80211SSID;

typedef struct  _WSC_CREDENTIAL {
	NDIS80211SSID    SSID;               // mandatory
	USHORT              AuthType;           // mandatory, 1: open, 2: wpa-psk, 4: shared, 8:wpa, 0x10: wpa2, 0x20: wpa-psk2
	USHORT              EncrType;           // mandatory, 1: none, 2: wep, 4: tkip, 8: aes
	UCHAR               Key[64];            // mandatory, Maximum 64 byte
	USHORT              KeyLength;
	UCHAR               MacAddr[6];         // mandatory, AP MAC address
	UCHAR               KeyIndex;           // optional, default is 1
	UCHAR               Rsvd[3];            // Make alignment
}   WSC_CREDENTIAL, *PWSC_CREDENTIAL;

typedef struct  _WSC_PROFILE {
#ifndef UINT
#define UINT	unsigned long
#endif
	UINT           	ProfileCnt;
	UINT		ApplyProfileIdx;  // add by johnli, fix WPS test plan 5.1.1
	WSC_CREDENTIAL  	Profile[8];             // Support up to 8 profiles
}   WSC_PROFILE, *PWSC_PROFILE;

typedef struct _ADVANCED_SETTINGS {
	char_t *bg_protection;
	char_t *beacon;
	char_t *dtim;
	char_t *fragment;
	char_t *rts;
	char_t *tx_power;
	char_t *short_preamble;
	char_t *short_slot;
	char_t *tx_burst;
	char_t *pkt_aggregate;
	char_t *ieee_80211h;
	char_t *wmm_capable;
	char_t *apsd_capable;
	char_t *dls_capable;
	char_t *countrycode;
	char_t *m2u_enable;
} ADVANCED_SETTINGS;



//typedef struct _DHCPOFFERADDR {
//	uint8_t hostname[16];
//	uint8_t chaddr[16];
//	uint32_t yiaddr;	/* network order */
//	uint32_t expires;	/* host order */
//} DHCPOFFERADDR;


/*add by myc for wifi_client_show 2012-04-19 end */

extern void formDefineWireless_Advanced(void);
extern void formDefineWireless_Basic(void);
extern void formDefineWireless_Stainfo(void);
extern void formDefineWireless_Security(void);
extern void formDefineWireless(void);
extern void restart8021XDaemon(int nvram);
extern void updateFlash8021x(int nvram);
extern void Security(int nvram, webs_t wp, char_t *path, char_t *query);
extern void confWPAGeneral(int nvram, webs_t wp, int mbssid);
extern void confWEP(int nvram, webs_t wp, int mbssid);
extern void conf8021x(int nvram, webs_t wp, int mbssid);
extern void getSecurity(int nvram, webs_t wp, char_t *path, char_t *query);
extern void DeleteAccessPolicyList(int nvram, webs_t wp, char_t *path, char_t *query);
extern void restart_wlan(void);
extern char *racat(char *s, int i);
extern char* getLanIfName(void);
extern char *setNthValue(int index, char *old_values, char *new_value);
extern int getIfIp(char *ifname, char *if_addr);
extern int deleteNthValueMulti(int index[], int count, char *value, char delimit);
extern int getNthValueSafe(int index, char *value, char delimit, char *result, int len);
extern int getIfMac(char *ifname, char *if_hw);
extern int getIfIp(char *ifname, char *if_addr);
extern char *setNthValue(int index, char *old_values, char *new_value);
extern char *getLanIfName(void);
extern char *racat(char *s, int i);
extern int setTimer(int microsec, void ((*sigroutine)(int)));
extern void stopTimer(void);
extern int wlan_timeout_deal(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlan11aChannels(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlan11bChannels(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlan11gChannels(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlanChannel(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlanCurrentMac(int eid, webs_t wp, int argc, char_t **argv);
extern int  getWlanWdsEncType(int eid, webs_t wp, int argc, char_t **argv);
extern int deleteNthValueMulti(int index[], int count, char *value, char delimit);		/* for Access Policy list deletion*/
extern void DeleteAccessPolicyList(int nvram, webs_t wp, char_t *path, char_t *query);
extern void revise_mbss_value(int old_num, int new_num);
extern void restart8021XDaemon(int nvram);
extern void restart_wlan(void);
extern void updateFlash8021x(int nvram);
extern void STFs(int nvram, int index, char *flash_key, char *value);
extern int AccessPolicyHandle(webs_t wp, int mbssid);
extern int getDLSBuilt(int eid, webs_t wp, int argc, char_t **argv);
extern int getWlanM2UBuilt(int eid, webs_t wp, int argc, char_t **argv);
extern int getWlanStaInfo(int eid, webs_t wp, int argc, char_t **argv);
extern int getWlanStaInfo_ap2(int eid, webs_t wp, int argc, char_t **argv);
extern void wirelessGetSecurity(webs_t wp, char_t *path, char_t *query);
inline void clearRadiusSetting(int nvram, int mbssid);
extern void APSecurity(webs_t wp, char_t *path, char_t *query);
extern void APDeleteAccessPolicyList(webs_t wp, char_t *path, char_t *query);
extern void WPSRestart(void);
extern void formDefineWPS(void);
extern void wps_register(void);
extern void wirelessadvanced_getwebpara(webs_t wp, ADVANCED_SETTINGS *advanced_setting_inside);
extern void wirelessadvanced_setnv(ADVANCED_SETTINGS *advanced_setting_inside, int ssid_num_inside, int wlan_mode_inside);
extern void WPSSingleTriggerHandler(int signo);
/*extern void LFW(webs_t wp,char_t *x,char *y);
extern void LFF(char_t *result,int nvram,char_t *x,int n);
extern void LFWi(webs_t wp,int *intvalue,char *webname);*/

#endif

