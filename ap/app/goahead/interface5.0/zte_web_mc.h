

#include "zte_web_interface.h"
#include "zte_web_get_fw_para.h"
#include "zte_web_pbm.h"
//#include "SMS_main.h"
#include "zte_web_sms.h"

#ifndef ULONG
#define ULONG  unsigned long
#endif

#ifndef OK
#define OK 0
#endif

//typedef int m_boolean;

#define ZTE_SOCKET_PATH_MAIN_CONTROL 		"zte_mc_path" 		/*MAIN CONTROL SOCKET PATH NAME */

/*command type*/


/*the msg id of relay SMS info */
typedef enum {
	ZTE_MC_SMS_MIN_CMD = 0,

	ZTE_MC_SMS_SET_PARAM_CMD,
	ZTE_MC_SMS_SEND_MSG_CMD,
	ZTE_MC_SMS_WRITE_MSG_CMD,
	ZTE_MC_SMS_DELETE_MSG_CMD,
	ZTE_MC_SMS_MODIFY_TAG_CMD,

	ZTE_MC_SMS_MAX_CMDS

} zte_mc_msg_relay_sms_e_type;

/*the msg id of relay pbm info */
typedef enum {
	ZTE_MC_PBM_MIN_CMD = 0,

	ZTE_MC_PBM_CREATE_MODIFY_RECORD_CMD,
	ZTE_MC_PBM_DEL_MULTI_RECORD_CMD,
	ZTE_MC_PBM_DEL_ONE_RECORD_CMD,
	ZTE_MC_PBM_DEL_ALL_RECORD_CMD,

	ZTE_MC_PBM_MAX_CMDS

} zte_mc_msg_relay_pbm_e_type;









































