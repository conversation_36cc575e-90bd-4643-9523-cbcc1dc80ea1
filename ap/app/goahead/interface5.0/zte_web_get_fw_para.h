


#define cprintf(fmt, args...) do {  \
    FILE *fp = fopen("/dev/console", "w");  \
    if (fp) {   \
        fprintf(fp, fmt, ## args);  \
        fclose(fp); \
    }   \
} while (0)
extern void web_feedback_header(webs_t wp);
extern void zte_write_result_to_web(webs_t wp, char_t *result);
extern void zte_write_result_to_web_qrzl(webs_t wp, char_t *result);
extern void zte_get_connection_mode(webs_t wp);

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
extern void zte_get_sim_select(webs_t wp);
extern void zte_get_iccid_lock_info(webs_t wp);
extern void zte_get_esim_status(webs_t wp);
#endif

extern void zte_get_pbm_data(webs_t wp);
extern void zte_get_pbm_data_total(webs_t wp);
extern void zte_get_pbm_parameter_info(webs_t wp);
extern void zte_get_sms_data(webs_t wp);
extern void zte_get_sms_data_total(webs_t wp);
extern void zte_get_sms_parameter_info(webs_t wp);
extern void zte_get_sms_cmd_status_info(webs_t wp);
extern void zte_get_sms_capacity_info(webs_t wp);
extern int zte_get_sms_remain_capacity();
extern void zte_get_sms_status_rpt_data(webs_t wp);
extern void zte_get_sms_unread_count(webs_t wp);
extern void zte_get_boradcast_data(webs_t wp);
extern void zte_get_device_mode(webs_t wp);
extern void zte_lan_user_mac_get(webs_t wp);

extern void zte_get_ussd_data_info(webs_t wp);

extern void zte_web_get_para_xml(webs_t wp, char_t *nv_name);
extern void zte_web_get_para_nologin(webs_t wp, char_t *nv_name);
extern void zte_get_request_process_xml(webs_t wp, const char_t *cmd);
extern void zte_get_request_process_nologin(webs_t wp, const char_t *cmd);

extern void zte_get_fota_settings(webs_t wp);


