#include "zte_web_interface.h"




data_safe_result_type_t zte_Safe_isMacValid(char *str)
{
	int i, len = strlen(str);
	if (len != 17)
		return 0;

	for (i = 0; i < 5; i++) {
		if ((!isxdigit(str[i * 3])) || (!isxdigit(str[i * 3 + 1])) || (str[i * 3 + 2] != ':'))
			return 0;
	}
	return (isxdigit(str[15]) && isxdigit(str[16])) ? 1 : 0;
}


data_safe_result_type_t zte_Safe_isIpValid(char *str)
{
	struct in_addr addr;    /*lint !e565 !e1080*/
	if ((! strcmp(T("any"), str)) || (! strcmp(T("any/0"), str)))
		return 1;

	if (!(inet_aton(str, &addr))) {
		slog(MISC_PRINT, SLOG_DEBUG, "isIpValid(): %s is not a valid IP address.\n", str); /*lint !e26*/
		return 0;
	}
	return 1;
}

data_safe_result_type_t zte_Safe_isNumOnly(char *str)
{
	int i, len = strlen(str);
	for (i = 0; i < len; i++) {
		if ((str[i] >= '0' && str[i] <= '9'))
			continue;
		return 0;
	}
	return 1;
}


data_safe_result_type_t zte_Safe_noSpecialChar(char *str)
{
	int i = 0;
	int len = 0;
	if (NULL == str) {
		return 0;
	}
	len = strlen(str);
	for (i = 0; i < len; i++) {
		if (zte_Safe_valid_SpecialChar(str[i]) != 0) {
			continue;
		} else {
			return 0;
		}
	}
	return 1;

}

data_safe_result_type_t zte_Safe_noSpecialChar_other(char *str)
{
	int i = 0;
	int len = 0;
	if (NULL == str) {
		return 0;
	}
	len = strlen(str);
	for (i = 0; i < len; i++) {
		if (zte_Safe_valid_SpecialChar_other(str[i]) != 0) {
			continue;
		} else {
			return 0;
		}
	}
	return 1;

}


data_safe_result_type_t zte_Safe_isStringOnly(char *str)
{
	int i = 0;
	int len = 0;
	if (NULL == str) {
		return 0;
	}
	len = strlen(str);
	for (i = 0; i < len; i++) {
		if ((str[i] <= 'Z' && str[i] >= 'A')
		    || (str[i] <= 'z' && str[i] >= 'a')
		   )
			continue;
		else {
			return 0;
		}

	}
	return 1;
}

data_safe_result_type_t zte_Safe_isNumorStringOnly(char *str)
{
	int i = 0;
	int len = 0;
	if (NULL == str) {
		return 0;
	}
	len = strlen(str);
	for (i = 0; i < len; i++) {
		if ((str[i] <= 'Z' && str[i] >= 'A') || (str[i] <= 'z' && str[i] >= 'a') || (str[i] <= '9' && str[i] >= '0'))
			continue;
		else {
			return 0;
		}

	}
	return 1;
}

int zte_Safe_valid_SpecialChar(char single_data)
{
	if (single_data == '"') return 0;
	else if (single_data == '\'') return 0;
	else if (single_data == '<') return 0;
	else if (single_data == '>') return 0;
	else if (single_data == '\\') return 0;
	else return 1;
}

int zte_Safe_valid_SpecialChar_other(char single_data)
{
	if (single_data == '&') return 0;
	else if (single_data == '<') return 0;
	else if (single_data == '>') return 0;
	else if (single_data == '\\') return 0;
	else if (single_data == '\'') return 0;
	else if (single_data == '/') return 0;
	else return 1;
}



int zte_valid_length_str(char *string_s, int min, int max)/*lint !e123*/
{
	int str_len = 0;
	str_len = strlen(string_s);
	if (str_len < min || str_len > max) { /*lint !e123*/
		return -1;
	} else {
		return 1;
	}
}















