/******************************************************************
* Description:  provide functions about the ussd management
* Modify Date      Version     Author         Modification
* 2012/04/06        V1.0      chenyi        create
******************************************************************/

//header file
#include "zte_web_interface.h"
//#include "zte_topsw_ussd.h"
//#include "libzte_ussd.h"
//constant

#define USSD_ACTION "ussd_action"
#define USSD_DCS "ussd_dcs"
#define USSD_DATA "ussd_data"

#define ZTE_WEB_USSD_SEND               "ussd_send"
#define ZTE_WEB_USSD_CANCEL           "ussd_cancel"
#define ZTE_WEB_USSD_REPLY              "ussd_reply"
#define ZTE_WEB_USSD_FIRST              "ussd"
#define ZTE_WEB_USSD_OVER               "ussd_over"

#ifndef ZTE_USSD_NV
#define ZTE_USSD_NV  "ussd_write_flag"
#endif

#ifndef ZTE_MAX_USSD_CHAR
#define ZTE_MAX_USSD_CHAR  160
#endif

#define USSD_OPERATING "15"
#define USSD_FIRST_CANCEL "17"
#define USSD_OVER ""

typedef struct {
	int ussd_action;
	int ussd_dcs;
	unsigned char ussd_data[ZTE_USSD_DATA_TO_WEB_LEN];
} zte_ussd_data_to_web_type;
static void zte_web_ussd_cancel(webs_t wp);
static void zte_web_ussd_send(webs_t wp);
static void zte_web_ussd_reply(webs_t wp);
void zte_web_ussd(webs_t wp);
static void zte_web_ussd_over(webs_t wp);

//functions

/**********************************************************************
* Function:         zte_goform_ussd_process
* Description:
* Input:            web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/
void zte_goform_ussd_process(webs_t wp)
{
	char_t *ussd_operator = NULL;

	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd-> invalid input..\n"); /*lint !e26*/
		return;
	}

	ussd_operator = websGetVar(wp, T("USSD_operator"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "ussd_operator is [%s].\n", ussd_operator); /*lint !e26*/

	if (0 == strcmp(ussd_operator, ZTE_WEB_USSD_SEND)) {
		zte_web_ussd_send(wp);
	} else if (0 == strcmp(ussd_operator, ZTE_WEB_USSD_CANCEL)) {
		zte_web_ussd_cancel(wp);
	} else if (0 == strcmp(ussd_operator, ZTE_WEB_USSD_REPLY)) {
		zte_web_ussd_reply(wp);
	} else if (0 == strcmp(ussd_operator, ZTE_WEB_USSD_FIRST)) {
		zte_web_ussd(wp);
	} else if (0 == strcmp(ussd_operator, ZTE_WEB_USSD_OVER)) {
		zte_web_ussd_over(wp);
	} else {
		slog(MISC_PRINT, SLOG_DEBUG, "invalid ussd_operator[%s].\n", ussd_operator); /*lint !e26*/
		zte_write_result_to_web(wp, FAILURE);
	}
}
/**********************************************************************
* Function:         zte_web_ussd_send(webs_t wp)
* Description:
* Input:            web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/
static void zte_web_ussd_send(webs_t wp)
{
	unsigned char ussd_data[ZTE_MAX_USSD_CHAR] = {0};
	unsigned char *ussd_send_number = NULL;

	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_send: invalid input..\n"); /*lint !e26*/
		return;
	}

	ussd_send_number = websGetVar(wp, T("USSD_send_number"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "ussd_send_number is [%s].\n", ussd_send_number); /*lint !e26*/

	if (0 == strcmp(ussd_send_number, "")) {
		slog(MISC_PRINT, SLOG_ERR, "ussd number is empty.\n"); /*lint !e26*/
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	strncpy(ussd_data, ussd_send_number, sizeof(ussd_data) - 1);
	slog(MISC_PRINT, SLOG_DEBUG, "ussd_data is [%s].\n", ussd_data); /*lint !e26*/

	//update the current state
	(void)zte_web_write(ZTE_USSD_NV, USSD_OPERATING);
	(void)zte_web_write("ussd_operater", "send");
	(void)zte_web_write("ussd_string", ussd_data);

	/*handler to mc*/
	if (0 == ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_AT_CTL, MSG_CMD_USSD_SET_REQ, 0, NULL, 0)) {
		slog(MISC_PRINT, SLOG_NORMAL, "zte_web_ussd_send:  successful.\n"); /*lint !e26*/
	} else {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_send:  failed.\n"); /*lint !e26*/
	}

	zte_write_result_to_web(wp, SUCCESS);
}
/**********************************************************************
* Function:         zte_web_ussd_cancel(webs_t wp)
* Description:     to cancel the ussd operation
* Input:            web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/
static void zte_web_ussd_cancel(webs_t wp)
{
	/*check input*/
	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_cancel invalid input..\n"); /*lint !e26*/
		return;
	}

	(void)zte_web_write(ZTE_USSD_NV, USSD_OPERATING);

	(void)zte_web_write("ussd_operater", "cancel");

	/*handler to mc*/
	if (0 == ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_AT_CTL, MSG_CMD_USSD_CANCEL_REQ, 0, NULL, 0)) {
		slog(MISC_PRINT, SLOG_NORMAL, "zte_web_ussd_cancel: zte_topsw_mc_ussd_send successful.\n"); /*lint !e26*/
	} else {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_cancel: zte_topsw_mc_ussd_send failed.\n"); /*lint !e26*/
	}

	zte_write_result_to_web(wp, SUCCESS);
}
/**********************************************************************
* Function:         zte_web_ussd_over(webs_t wp)
* Description:     to reset the ussd_write_flag
* Input:            web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/
static void zte_web_ussd_over(webs_t wp)
{
	/*check input*/
	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_over invalid input..\n"); /*lint !e26*/
		return;
	}

	(void)zte_web_write(ZTE_USSD_NV, USSD_OVER);
	zte_write_result_to_web(wp, SUCCESS);
}
/**********************************************************************
* Function:         zte_web_ussd_reply(webs_t wp)
* Description:      to reply(send)  the ussd cmd
* Input:
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/
static void zte_web_ussd_reply(webs_t wp)
{
	/*data get from page*/
	unsigned char ussd_data_reply[ZTE_MAX_USSD_CHAR] = {0};

	/*check input*/
	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_reply: invalid input..\n"); /*lint !e26*/
		return;
	}

	/*set data */
	strncpy(ussd_data_reply, websGetVar(wp, T("USSD_reply_number"), T("")), sizeof(ussd_data_reply) - 1);

	/*handler to mc*/
	(void)zte_web_write(ZTE_USSD_NV, USSD_OPERATING);
	(void)zte_web_write("ussd_string", ussd_data_reply);

	(void)zte_web_write("ussd_operater", "send");

	if (0 == ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_AT_CTL, MSG_CMD_USSD_SET_REQ, 0, NULL, 0)) {
		slog(MISC_PRINT, SLOG_NORMAL, "zte_web_ussd_reply: successful.\n"); /*lint !e26*/
	} else {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd_reply: failed.\n"); /*lint !e26*/
	}
	zte_write_result_to_web(wp, SUCCESS);
}
/**********************************************************************
* Function:         zte_web_ussd(webs_t wp)
* Description:      to cancel the ussd cmd
* Input:            web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 20120406    V1.0        chenyi        first version
**********************************************************************/

void zte_web_ussd(webs_t wp)
{
	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_web_ussd: invalid input..\n"); /*lint !e26*/
		return;
	}
	slog(MISC_PRINT, SLOG_DEBUG, "zte_web_ussd: ok\n"); /*lint !e26*/

	(void)zte_web_write(ZTE_USSD_NV, USSD_FIRST_CANCEL);
	zte_write_result_to_web(wp, SUCCESS);
}
int zte_ussd_get_to_web_data(zte_ussd_data_to_web_type *para)
{
	char buf[NV_ITEM_STRING_LEN_20] = {0};

	if (NULL == para) {
		slog(MISC_PRINT, SLOG_DEBUG, "para is null\n"); /*lint !e26*/
		return -1;
	}

	cfg_get_item("ussd_mode", buf, sizeof(buf));
	para->ussd_action = atoi(buf);
	slog(MISC_PRINT, SLOG_DEBUG, "ussd_operater is %d\n", para->ussd_action); /*lint !e26*/

	memset(&buf, 0, sizeof(buf));
	cfg_get_item("ussd_dcs", buf, sizeof(buf));
	para->ussd_dcs = atoi(buf);
	slog(MISC_PRINT, SLOG_DEBUG, "ussd_dcs is %d\n", para->ussd_dcs); /*lint !e26*/

	cfg_get_item("ussd_content", (para->ussd_data), sizeof(para->ussd_data));

	slog(MISC_PRINT, SLOG_DEBUG, "ussd_data is %s\n", para->ussd_data); /*lint !e26*/

	return 0;

}
/**********************************************************************
* Function:         zte_get_ussd_data_info
* Description:      to get the ussd data info
* Input:            the web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 2012/04/16    V1.0        chenyi       first version
**********************************************************************/
void zte_get_ussd_data_info(webs_t wp)
{
	zte_ussd_data_to_web_type ussd_data_info;
	int result = 0;

	memset(&ussd_data_info, 0, sizeof(zte_ussd_data_to_web_type));
	result = zte_ussd_get_to_web_data(&ussd_data_info);
#if 0 // kw 3 return  -1 branch is unreachable 	
	if (-1 == result) {
		slog(MISC_PRINT, SLOG_ERR, "call zte_ussd_get_to_web_data fail.\n"); /*lint !e26*/
		web_feedback_header(wp);
		(void)websWrite(wp, T("[]"));
		return ;
	}
#endif

	web_feedback_header(wp);
	(void)websWrite(wp, T("{\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%s\"}"), USSD_ACTION, ussd_data_info.ussd_action, USSD_DCS, ussd_data_info.ussd_dcs, \
	                USSD_DATA, ussd_data_info.ussd_data);
}
