﻿/**************************************************************************
*
*                  Copyright (c) 2013 ZTE Corporation.
*
***************************************************************************
* 模 块 名 :
* 文 件 名 :
* 相关文件 :
* 实现功能 :
* 作    者 :
* 版    本 :
* 完成日期 :
* 其它说明 :
**************************************************************************/

#include <stdlib.h>
#include "zte_web_interface.h"
#include "zte_web_get_fw_para.h"
#include "./wifi/zte_web_lan_utils.h"
#include <linux/wireless.h>
#include <sys/ioctl.h>
#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
#include "libcpnv.h"
#endif
/*Added for get dhcp lease info, 20121018 begin*/
#define MAX_MAC_ADDR_LEN  20
#define MAX_IP_ADDR_LEN     20
#define MAX_HOSET_NAME_LEN   256
#define MAX_DHCP_CLIENT_ID_LEN 765
/*Added for get dhcp lease info, 20121018 end*/

#ifndef IFNAMSIZ
#define IFNAMSIZ 16
#endif
/***********************************************/
/*Struct Definition*/
/***********************************************/
typedef struct web_cmd_nologin_get_struct {
	char cmd[64];
} web_cmd_nologin_get_s_type;

static const unsigned char base64_table[65] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";


web_cmd_nologin_get_s_type web_cmd_get_nologin_data_new[] = {
	NV_MODEM_MAIN_STATE,
	NV_PUKNUMBER,
	NV_PINNUMBER,
	NV_PIN_STATUS,
	STR_SDCARD_MODE_OPT,
	NV_SD_CARD_STATE,
	NV_HTTPSHARE_STATUS,
	NV_HTTPSHARE_WR_AUTH,
	NV_HTTPSHARE_FILE,
	NV_LANGUAGE,
	NV_OPMS_WAN_MODE,
	NV_LOGINFO,
	"ppp_status",
	"sim_imsi",
	"broadcast_data",
	NV_LOGIN_LOCK_TIME,
	"psw_fail_num_str",
	"imei",
#ifdef WEBS_SECURITY
	"rnum_js",
#endif
	"inner_version",
	NV_CR_VERSION,
	"network_type",
	"signalbar",
	"wifi_cur_state"
	"realtime_tx_thrpt",
	"realtime_rx_thrpt",
	"station_mac",
	"battery_pers",
	"battery_charging",
	"EX_SSID1",
	"sta_ip_status",
	"wifi_profile",
	"wifi_profile1",
	"wifi_profile2",
	"wifi_profile3",
	"wifi_profile4",
	"wifi_profile5",
	"wifi_profile6",
	"wifi_profile7",
	"wifi_profile8",
	"wifi_profile9",
	"wifi_profile_num",
	NV_BLC_WAN_MODE,
	"blc_wan_auto_mode",
	"sta_count",
	"m_sta_count"
};

static void zte_get_nv_value(webs_t wp, const char_t *nv_name);
static void zte_reset_nv_value(webs_t wp, const char_t *nv_name);
extern void zte_get_login_status_value(webs_t wp, char *login_status);
extern void zte_rest_result_write(webs_t wp, char_t *result);

void web_feedback_header(webs_t wp)
{
	websWrite(wp, T("HTTP/1.1 200 OK\n"));
	websWrite(wp, T("Server: %s\r\n"), WEBS_NAME);
#ifdef WEBINSPECT_FIX
	websWrite(wp, T("X-Frame-Options: SAMEORIGIN\n"));
#endif	
	websWrite(wp, T("Pragma: no-cache\n"));
	websWrite(wp, T("Cache-control: no-cache\n"));
	websWrite(wp, T("Content-Type: text/html\n"));

	// ✅ 加入 CORS 头部，解决跨域问题
	websWrite(wp, T("Access-Control-Allow-Origin: *\r\n"));
	websWrite(wp, T("Access-Control-Allow-Methods: POST, GET, OPTIONS\r\n"));
	websWrite(wp, T("Access-Control-Allow-Headers: Content-Type\r\n"));

#ifdef WEBS_SECURITY
	websWrite(wp, T("Expires: 0\n"));
	char cook_token[COOKIE_SESSION_SIZE+1] = {0};
	(void)zte_web_read(NV_WEB_TOKEN, cook_token);
	if(strlen(cook_token) == 0)
	{
		web_make_salt_base64(cook_token, sizeof(cook_token));
		if (websSSLIsOpen())
			websWrite(wp, T("Set-Cookie: token=%s; secure; HttpOnly; SameSite=Lax;\n"),cook_token);
		else
			websWrite(wp, T("Set-Cookie: token=%s; HttpOnly; SameSite=Lax;\n"),cook_token);
		(void)zte_web_write(NV_WEB_TOKEN, cook_token);
	}
#endif	
	websWrite(wp, T("\n"));
}


/* AT */
typedef struct {
	char_t srcCmd[NVIO_DEFAULT_LEN];
	char_t dstCmd[NVIO_DEFAULT_LEN];
} T_WEB_CONVERT_CMD_TAB;

/******************************************************
* Function: zte_web_get_para_xml(webs_t wp, char_t *nv_name)
* Description: read the nv from nvconfig and write to page
* Input:   wp:http request info;nv_name: the name for nv item
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/08/15        V1.0   liuyingnan        create
*******************************************************/
void zte_web_get_para_xml(webs_t wp, char_t *nv_name)
{
	char_t nv_value[NVIO_MAX_LEN] = {0};
	char_t *flag = NULL;
	char_t  convert_nv_name[NVIO_DEFAULT_LEN] = {0};
	int result = 0;

	if ((NULL == wp) || (NULL == nv_name) || ((*nv_name) == '\0')) {
		return;
	}

	flag = websGetVar(wp, T("flag"), T(""));

	if (0 == strcmp(nv_name, NV_LOGINFO)) {
		zte_get_login_status_value(wp, nv_value);
	} else {
		cfg_get_item(nv_name, &nv_value, sizeof(nv_value));
	}
	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write(wp, nv_name, nv_value, 0);
	zte_rest_cmd_write_foot(wp);

	if (0 == strcmp(flag, "0")) {
		cfg_set(nv_name, "");
	}
}

void zte_web_get_para_nologin(webs_t wp, char_t *nv_name)
{
	char_t nv_value[NVIO_MAX_LEN] = {0};
	char_t *flag = NULL;
	unsigned int max_cmd_index = (unsigned int)(sizeof(web_cmd_get_nologin_data_new) / sizeof(web_cmd_nologin_get_s_type));
	int cmd_index = 0;
	int black_list_result = 0;

	if ((NULL == wp) || (NULL == nv_name) || ((*nv_name) == '\0')) {
		return;
	}

	flag = websGetVar(wp, T("flag"), T(""));
	for (cmd_index = 0; cmd_index < max_cmd_index; cmd_index++) {
		if (0 == strcmp(web_cmd_get_nologin_data_new[cmd_index].cmd, nv_name)) {
			black_list_result = 1;
			break;
		}
	}

	cfg_get_item(nv_name, &nv_value, sizeof(nv_value));

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	if (black_list_result == 1) {
		black_list_result = 0;
		if (0 == strcmp(nv_name, NV_LOGINFO)) {
			zte_get_login_status_value(wp, nv_value);
		}
		zte_rest_cmd_write(wp, nv_name, nv_value, 0);
	} else {
		zte_rest_cmd_write(wp, nv_name, "", 0);
	}
	zte_rest_cmd_write_foot(wp);

	if (0 == strcmp(flag, "0")) {
		cfg_set(nv_name, "");
	}

	//save the nvconfig info from memory to flash
}

/******************************************************
* Function: zte_write_result_to_web
* Description: feedback the info to web
* Input:  http request info
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/08/09        V1.0    liuyingnan        modife
*******************************************************/
void zte_write_result_to_web(webs_t wp, char_t *result)
{
	if ((NULL == wp) || (NULL == result)) {
		return;
	}
	//save the nvconfig info from memory to flash
	//cfg_save();

	web_feedback_header(wp);
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		zte_rest_result_write(wp, result);
	} else {
		websWrite(wp, T("{\"result\":\"%s\"}"), result);
	}
}

/**
 * 客户定制的返回格式
 */
void zte_write_result_to_web_qrzl(webs_t wp, char_t *result)
{
	if ((NULL == wp) || (NULL == result)) {
		return;
	}
	//save the nvconfig info from memory to flash
	//cfg_save();

	web_feedback_header(wp);
	websWrite(wp, T("%s"), result);
}

/******************************************************
* Function: zte_get_request_process
* Description: the only entry for handle multiple data for get request
* Input:  wp:http request info;cmd:get request command
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/01/17        V1.0     chenyi        create
*******************************************************/
void zte_get_request_process(webs_t wp, const char_t *cmd)
{
	char_t nv_name[NV_ITEM_STRING_LEN_50] = {0}; //for generic nv name
	int i = 0, j = 0;
	int count = 0;
	int flag = -1;

	web_feedback_header(wp);

	if ((NULL == cmd) || ('\0' == *cmd)) {
		websWrite(wp, T(""));
		return;
	}

	websWrite(wp, T("{"));    //content head

	for (; cmd[i] != '\0';) {
		if (cmd[i] != ',') {
			if (j < (NV_ITEM_STRING_LEN_50 - 1)) {
				nv_name[j] = cmd[i];
			}
			i++;
			j++;
			flag = 0;
		} else {
			zte_get_nv_value(wp, nv_name);
			websWrite(wp, T(","));    //field separator
			count ++;
			j = 0;
			i++;
			flag = 1;
			zte_reset_nv_value(wp, nv_name);
			memset(nv_name, 0, sizeof(nv_name)); //reset
		}
	}

	if (0 == flag) {
		//for only one field or last field
		count ++;
		zte_get_nv_value(wp, nv_name);
		zte_reset_nv_value(wp, nv_name);
	}

	websWrite(wp, T("}"));    //content tail
}

/******************************************************
* Function: zte_get_request_process_xml
* Description: the only entry for handle multiple data for get request
* Input:  wp:http request info;cmd:get request command
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/12/04        V1.0   liuyingnan        create
*******************************************************/
void zte_get_request_process_xml(webs_t wp, const char_t *cmd)
{
	char_t nv_name[NV_ITEM_STRING_LEN_50] = {0}; //for generic nv name
	int i = 0, j = 0;
	int count = 0;
	int flag = -1;

	web_feedback_header(wp);

	if ((NULL == cmd) || ('\0' == *cmd)) {
		if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
			zte_rest_cmd_write_head(wp);
			zte_rest_cmd_write(wp, "empty", "", 0);
			zte_rest_cmd_write_foot(wp);
		} else {
			websWrite(wp, T(""));
		}
		return;
	}

	zte_rest_cmd_write_head(wp);

	for (; cmd[i] != '\0';) {
		if (cmd[i] != ',') {
			if (j < (NV_ITEM_STRING_LEN_50 - 1)) {
				nv_name[j] = cmd[i];
			}
			i++;
			j++;
			flag = 0;
		} else {
			//cfg_set("url_goform_get", nv_name);
			zte_get_nv_value(wp, nv_name);
			if (wp->flags & WEBS_XML_CLIENT_REQUEST)
			{}
			else {
				websWrite(wp, T(","));    //field separator
			}
			count ++;
			j = 0;
			i++;
			flag = 1;
			zte_reset_nv_value(wp, nv_name);
			memset(nv_name, 0, sizeof(nv_name)); //reset
		}
	}

	if (0 == flag) {
		//for only one field or last field
		count ++;
		zte_get_nv_value(wp, nv_name);
		zte_reset_nv_value(wp, nv_name);
	}

	zte_rest_cmd_write_foot(wp);
}

void zte_get_request_process_nologin(webs_t wp, const char_t *cmd)
{
	char_t nv_name[NV_ITEM_STRING_LEN_50] = {0}; //for generic nv name
	int i = 0, j = 0;
	int count = 0;
	int flag = -1;

	//add by liuyingnan for server safe start
	unsigned int max_cmd_index = (unsigned int)(sizeof(web_cmd_get_nologin_data_new) / sizeof(web_cmd_nologin_get_s_type));
	int cmd_index = 0;
	int black_list_result = 0;
	//add by liuyingnan for server safe end

	web_feedback_header(wp);

	if ((NULL == cmd) || ('\0' == *cmd)) {
		if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
			zte_rest_cmd_write_head(wp);
			zte_rest_cmd_write(wp, "empty", "", 0);
			zte_rest_cmd_write_foot(wp);
		} else {
			websWrite(wp, T(""));
		}
		return;
	}

	zte_rest_cmd_write_head(wp);

	for (; cmd[i] != '\0';) {
		if (cmd[i] != ',') {
			if (j < (NV_ITEM_STRING_LEN_50 - 1)) {
				nv_name[j] = cmd[i];
			}
			i++;
			j++;
			flag = 0;
		} else {
			//cfg_set("url_goform_get", nv_name);
			//add by liuyingnan for server safe start
			for (cmd_index = 0; cmd_index < max_cmd_index; cmd_index++) {
				if (0 == strcmp(web_cmd_get_nologin_data_new[cmd_index].cmd, nv_name)) {
					black_list_result = 1;
					break;
				}
			}
			if (black_list_result == 1) {
				black_list_result = 0;
				zte_get_nv_value(wp, nv_name);
			} else {
				zte_rest_cmd_write(wp, nv_name, "", 0);
			}
			//add by liuyingnan for server safe end
			//zte_get_nv_value(wp,nv_name);
			if (wp->flags & WEBS_XML_CLIENT_REQUEST)
			{}
			else {
				websWrite(wp, T(","));    //field separator
			}
			count ++;
			j = 0;
			i++;
			flag = 1;
			zte_reset_nv_value(wp, nv_name);
			memset(nv_name, 0, sizeof(nv_name)); //reset
		}
	}

	if (0 == flag) {
		//for only one field or last field
		count ++;
		zte_get_nv_value(wp, nv_name);
		zte_reset_nv_value(wp, nv_name);
	}

	zte_rest_cmd_write_foot(wp);
}


/******************************************************
* Function: zte_get_nv_value
* Description: to read the specific nv (nv_name)
* Input:  wp:http request info;nv_name:general nv name
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/01/17        V1.0     chenyi        create
*******************************************************/
static void zte_get_nv_value(webs_t wp, const char_t *nv_name)
{
	char_t nv_value[NVIO_MAX_LEN] = {0};

	//add by liuyingnan for server safe start
	unsigned int max_cmd_index = (unsigned int)(sizeof(web_cmd_get_nologin_data_new) / sizeof(web_cmd_nologin_get_s_type));
	int cmd_index = 0;
	int black_list_result = 0;
	char login_info[NV_ITEM_STRING_LEN_20] = {0};
	zte_get_login_status_value(wp, login_info);
	int flag = 0;
	char_t  convert_nv_name[NVIO_DEFAULT_LEN] = {0};

	if ((NULL == nv_name) || ('\0' == *nv_name)) {
		if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
			zte_rest_cmd_write(wp, "empty", "", 0);
		} else {
			zte_rest_cmd_write(wp, "", "", 0);
		}
		return;
	}

	for (cmd_index = 0; cmd_index < max_cmd_index; cmd_index++) {
		if (0 == strcmp(web_cmd_get_nologin_data_new[cmd_index].cmd, nv_name)) {
			black_list_result = 1;
			break;
		}
	}

	if (0 != strcmp("ok", login_info)) {
		if (black_list_result != 1) {
			zte_rest_cmd_write(wp, nv_name, "", 0);
			return;
		}
	}

	cfg_get_item(nv_name, &nv_value, sizeof(nv_value));

	if ((0 != strcmp(nv_value, "")) && (0 != strcmp(nv_name, NV_LOGINFO))) {
		zte_rest_cmd_write(wp, nv_name, nv_value, 0);
	} else {
		if (0 == strcmp(nv_name, NV_LOGINFO)) {
			memset(nv_value, 0, sizeof(nv_value));
			zte_get_login_status_value(wp, nv_value);
			zte_rest_cmd_write(wp, nv_name, nv_value, 0);
		} else if (0 == strcmp(nv_name, NV_SD_CARD_STATE)) {
			websWrite(wp, T("\"sd_card_state\":\"%s\""), T("1"));
		} else if (0 == strcmp(nv_name, NV_SMS_UNREAD_NUM)) {
			zte_get_sms_unread_num(wp);
		} else if (0 == strcmp(nv_name, NV_LOGIN_LOCK_TIME)) {
			zte_get_login_lock_time(wp);
		} else {
			zte_rest_cmd_write(wp, nv_name, "", 0);
		}
	}
}

/******************************************************
* Function: zte_reset_nv_value
* Description: to reset the specific nv (nv_name)
* Input:  wp:http request info;nv_name:general nv name
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2012/01/17        V1.0     chenyi        create
*******************************************************/
static void zte_reset_nv_value(webs_t wp, const char_t *nv_name)
{
	char_t reset_nv_name[NV_ITEM_STRING_LEN_50 + 10] = {0};
	char_t *flag = NULL;

	if (NULL == wp) {
		slog(MISC_PRINT, SLOG_DEBUG, "wp is null.\n"); /*lint !e26*/
		return;
	}

	if ((NULL == nv_name) || ('\0' == *nv_name)) {
		return;
	}

	strncpy(reset_nv_name, nv_name, NV_ITEM_STRING_LEN_50);
	strcat(reset_nv_name, "_flag");

	flag = websGetVar(wp, (char_t *)reset_nv_name, T(""));

	if (0 == strcmp(flag, "0")) {
		cfg_set((char *)nv_name, "");
	}
}

/******************************************************
* Function: zte_lan_user_mac_get
* Description: get current user mac address for black list
* Input:  http request info
* Output:
* Return:
* Others:
* Modify Date       Version     Author              Modification
* 2011/07/02        V1.0       qiuzhenhui        create
*******************************************************/
void zte_lan_user_mac_get(webs_t wp)
{
	char_t user_releasetime[NV_ITEM_STRING_LEN_20] = {0};
	char_t user_macaddr[NV_ITEM_STRING_LEN_20] = {0};
	char_t user_ipaddr[40] = {0};
	char_t user_hostname[NV_ITEM_STRING_LEN_200] = {0};
	char_t user_ipv6macaddr[NV_ITEM_STRING_LEN_64] = {0};
	char_t *ip_address = NULL;
	int match_flag = FALSE;
	FILE *fp = NULL;

	ip_address = websGetRequestIpaddr(wp);
#if 0 // kw 3	
	if (NULL == ip_address) {
		zte_write_result_to_web(wp, FAILURE);
		return ;
	}
#endif	

	fp = fopen("/usr/zte/zte_conf/config/dnsmasq.leases", "r");
	if (fp == NULL) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	while (!feof(fp)) {
		(void)fscanf(fp, "%20s %20s %40s %200s %64s\n", user_releasetime, user_macaddr, user_ipaddr, user_hostname, user_ipv6macaddr);

		if (0 == strncmp(ip_address, user_ipaddr, sizeof(user_ipaddr))) {
			match_flag = TRUE;
			break;
		}
	}

	fclose(fp);

	if (match_flag == TRUE) {
		web_feedback_header(wp);
		zte_rest_cmd_write_head(wp);
		zte_rest_cmd_write(wp, "user_mac_addr", user_macaddr, 0);
		zte_rest_cmd_write_foot(wp);
	} else {
		web_feedback_header(wp);
		zte_rest_cmd_write_head(wp);
		zte_rest_cmd_write(wp, "user_mac_addr", "error", 0);
		zte_rest_cmd_write_foot(wp);
	}

}

/******************************************************
* Function: zte_get_device_mode
* Description: get current device mode
* Input:  http request info
* Output: device_mode
* Return:
* Others:
* Modify Date    Version     Author           Modification
* 2012/05/10    V1.0        qiuzhenhui      create
*******************************************************/
void zte_get_device_mode(webs_t wp)
{

	FILE *fp = NULL;
	char buffer[NVIO_MAX_LEN] = {0};
	char device_mode[NVIO_MAX_LEN] = {0};

	fp = popen("cat /sys/class/android_usb/android0/debug_enable", "r");
	if (NULL == fp) {
		slog(MISC_PRINT, SLOG_ERR, "zte_get_device_mode:  get read fp failed."); /*lint !e26*/
		zte_write_result_to_web(wp, FAILURE);
		return ;
	}
	(void)fgets(buffer, sizeof(buffer), fp);
	(void)sscanf(buffer, "%s", device_mode);
	(void)pclose(fp);

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write(wp, "device_mode", device_mode, 0);
	zte_rest_cmd_write_foot(wp);
}

/**********************************************************************
* Function:         zte_get_login_status_value
* Description:      to get webui login status
* Input:            the web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 2012/09/17    V1.0        chenyi       first version
* 2012/10/18    V2.0        huangmin      modify
**********************************************************************/
void zte_get_login_status_value(webs_t wp, char *login_status)
{

	char loginfo[NV_ITEM_STRING_LEN_20] = {0};
	char_t nv_ipaddr[40] = {0};
	zte_topsw_state_e_type status = ZTE_NVIO_MAX;
	char_t *ip_address = NULL;
	char buf[NV_ITEM_STRING_LEN_10] = {0};
	char cook_id[COOKIE_SESSION_SIZE+1] = {0};

	if (NULL == login_status) {
        slog(MISC_PRINT, SLOG_DEBUG, "login_status is null.\n");
		return;
	}
	status = zte_web_read(NV_LOGINFO, loginfo);
	if (ZTE_NVIO_DONE != status) {
		slog(MISC_PRINT, SLOG_ERR,"zte_get_loginfo: read the nv [loginfo] fail.");       
		return ;
	}
	//get request ip addr
	ip_address = websGetRequestIpaddr(wp);
#if 0	// kw 3 INVARIANT_CONDITION.UNREACH  wp->ipaddr ipaddr is array, it's address can not be NULL
	if (NULL == ip_address) {
		slog(MISC_PRINT, SLOG_ERR,"zte_get_loginfo: ip_address is null.");
		return ;
	}
#endif
	(void)zte_web_read(NV_USER_IP_ADDR, nv_ipaddr);
	(void)zte_web_read(NV_COOKIE_ID, cook_id);
	cfg_get_item(NV_DATA_CARD, buf, sizeof(buf));
	if (((0 == strncmp(loginfo, "ok", NV_ITEM_STRING_LEN_20)) 
#ifdef WEBS_SECURITY	
		&& wp->cookie && strlen(wp->cookie) > strlen(cook_id)
		&& strstr(wp->cookie, cook_id)
#endif
		&& (0 == strncmp(ip_address, nv_ipaddr, sizeof(nv_ipaddr))))
		|| (0 == strncmp(&buf, "1", sizeof(buf)))) {
		strncpy(login_status, "ok", NV_ITEM_STRING_LEN_20);
	} else {
		strncpy(login_status, "", NV_ITEM_STRING_LEN_20);
		//slog(MISC_PRINT, SLOG_DEBUG,"zte_get_login loginfo:%s,ip:%s,nv:%s,cookie:%s,nv:%s,buf:%s!\n",loginfo,ip_address,nv_ipaddr,wp->cookie,cook_id,buf);
	}
}
/******************************************************
* Function: zte_get_login_lock_time
* Description: get current login lock time
* Input:  http request info
* Output:
* Return:
* Others:
* Modify Date       Version     Author              Modification
* 2011/06/16        V1.0       qiuzhenhui        create
*******************************************************/
void zte_get_login_lock_time(webs_t wp)
{
	char last_record_time[LOGIN_RECORD_TIME] = {0};
	int last_record_time_num = 0;
	int curr_record_time_num = 0;
	int lock_time = 0;
	int last_lock_time = 0;
	(void)zte_web_read(NV_LAST_LOGIN_TIME, last_record_time);
	last_record_time_num = atoi(last_record_time);
	if(last_record_time_num < 0 || last_record_time_num > INT_MAX-1)  // kw 3
	{
		last_record_time_num = 0;
	}

	curr_record_time_num = zte_web_getCurrentTime();
	lock_time = curr_record_time_num - last_record_time_num;
	last_lock_time = LOGIN_FAIL_LOCK_TIME - lock_time;

	if (last_lock_time >= 0) {
		(void)websWrite(wp, T("\"login_lock_time\":\"%d\""), last_lock_time);
	} else {
		(void)websWrite(wp, T("\"login_lock_time\":\"-1\""));
	}
}

/******************************************************
* Function: zte_get_connection_mode(webs_t wp)
* Description: get connection mode
* Input:  http request info
* Output:
* Return:
* Others:
* Modify Date    Version   Author         Modification
* 2011/11/15        V1.0     chenyi        create
*******************************************************/
void zte_get_connection_mode(webs_t wp)
{
	char_t connection_mode[NV_ITEM_STRING_LEN_20] = {0};
	char_t auto_conn_when_roaming[NV_ITEM_STRING_LEN_10] = {0};

	(void)zte_web_read(NV_DIAL_MODE, connection_mode);
	(void)zte_web_read(NV_ROAM_SETTING_OPTION, auto_conn_when_roaming);

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write(wp, "connectionMode", connection_mode, 1);
	zte_rest_cmd_write(wp, "autoConnectWhenRoaming", auto_conn_when_roaming, 0);
	zte_rest_cmd_write_foot(wp);
}

#if defined(JCV_FEATURE_ESIM_RSIM_SWITCH) || defined(JCV_FEATURE_SIM_HOTSWAP_SUPPORT)
void zte_get_sim_select(webs_t wp)
{
	char_t sim_select[32] = {0};
	char_t sim_pre_mode[32] = {0};

	(void)zte_web_read("sim_select", sim_select);
	(void)zte_web_read("sim_pre_mode", sim_pre_mode);

#ifdef QRZL_ESIM2_ON_SIM_SLOT
	if (strcmp("RSIM_only", sim_select) == 0) {
		memset(sim_select, 0, sizeof(sim_select));
		strcpy(sim_select, "ESIM2_only");
	}
#endif

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write(wp, "sim_select", sim_select, 1);
	zte_rest_cmd_write(wp, "sim_pre_mode", sim_pre_mode, 0);
	zte_rest_cmd_write_foot(wp);
}

void zte_get_iccid_lock_info(webs_t wp)
{
        char_t *field_index = NULL;
        field_index = websGetVar(wp, T("FieldIndex"), NULL);

        char iccidlock_enable[4] = {0};
        char effective_field_num[4] = {0};
        char BeginIccid_nv_name[32] = {0};
        char EndIccid_nv_name[32] = {0};
        char min_iccid_code[32] = {0};
        char max_iccid_code[32] = {0};

        slog(MISC_PRINT, SLOG_DEBUG, "zte_get_iccid_lock_info: field_index=[%s] atoi(field_index)=%d\n", field_index, atoi(field_index));

        if(field_index != NULL && atoi(field_index) >= 0 && atoi(field_index) <= 9)
        {//
                snprintf(BeginIccid_nv_name, sizeof(BeginIccid_nv_name), "BeginIccid%d", atoi(field_index));
                snprintf(EndIccid_nv_name, sizeof(EndIccid_nv_name), "EndIccid%d", atoi(field_index));

                (void)zte_web_read(BeginIccid_nv_name, min_iccid_code);
                (void)zte_web_read(EndIccid_nv_name, max_iccid_code);

                web_feedback_header(wp);
                zte_rest_cmd_write_head(wp);
                zte_rest_cmd_write(wp, "MinIccidCode", min_iccid_code, 1);
                zte_rest_cmd_write(wp, "MaxIccidCode", max_iccid_code, 0);
                zte_rest_cmd_write_foot(wp);
        }
        else
        {//no prams

                (void)zte_web_read("IccidLockEnable", iccidlock_enable);
                (void)zte_web_read("effective_field_num", effective_field_num);

                web_feedback_header(wp);
                zte_rest_cmd_write_head(wp);
                zte_rest_cmd_write(wp, "IccidLockEnable", iccidlock_enable, 1);
                zte_rest_cmd_write(wp, "EffectiveFieldNum", effective_field_num, 0);
                zte_rest_cmd_write_foot(wp);
        }

}

void zte_get_esim_status(webs_t wp)
{
	char_t current_sim[32] = {0};

	int32_t simCardFlag = 0;
	unsigned long retCode = CPNV_ERROR;
	char rsp_code[2];
	retCode =cpnv_NvItemRead(0x29217d, (unsigned char *)(&simCardFlag), 1);
	if(CPNV_ERROR == retCode) 
	{
		goto write_and_end;
	}

#ifdef QRZL_ESIM2_ON_SIM_SLOT
    if (simCardFlag == 0) {
        strcpy(current_sim, "esim2");
    } else if (simCardFlag == 3) {
       strcpy(current_sim, "esim1");
    }
#else
	if (simCardFlag == 0) {
        strcpy(current_sim, "rsim");
    } else if (simCardFlag == 3) {
		FILE *sim_siwtch_fp;
        int esim_num = -1;

        // 打开 sysfs 文件
        sim_siwtch_fp = fopen("/sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness", "r");
        if (sim_siwtch_fp != NULL) {
            // 读取数值
            if (fscanf(sim_siwtch_fp, "%d", &esim_num) != 1) {
                slog(MISC_PRINT, SLOG_ERR, "Failed to read /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness value");
            }
            // 关闭文件
            fclose(sim_siwtch_fp);
        } else {
            slog(MISC_PRINT, SLOG_ERR, "Failed to open /sys/devices/platform/leds-gpio.1/leds/sim_switch_a_ctrl/brightness");
        }
        if (esim_num == 0) {
            strcpy(current_sim, "esim1");
        } else if (esim_num == 1) {
           strcpy(current_sim, "esim2");
        }
    }
#endif

write_and_end:
        web_feedback_header(wp);
        zte_rest_cmd_write_head(wp);
        zte_rest_cmd_write(wp, "EsimStatus", current_sim, 0);
        zte_rest_cmd_write_foot(wp);
}
#endif

int zte_web_getCurrentTime()
{
	time_t sys_time = 0;
	int currentTime = (int)(time(&sys_time));
	return currentTime;
}

/**
 * base64_decode - Base64 decode
 * @src: Data to be decoded
 * @len: Length of the data to be decoded
 * @out_len: Pointer to output length variable
 * Returns: Allocated buffer of out_len bytes of decoded data,
 * or %NULL on failure
 *
 * Caller is responsible for freeing the returned buffer.
 */
unsigned char * zte_base64_decode(const unsigned char *src, size_t len,
                                  size_t *out_len)
{
	unsigned char dtable[256], *out, *pos, in[4], block[4], tmp;
	size_t i, count, olen;

	memset(dtable, 0x80, 256);
	for (i = 0; i < sizeof(base64_table) - 1; i++)
		dtable[base64_table[i]] = (unsigned char) i;
	dtable['='] = 0;

	count = 0;
	for (i = 0; i < len; i++) {
		if (dtable[src[i]] != 0x80)
			count++;
	}

	if (count == 0 || count % 4)
		return NULL;

	olen = count / 4 * 3;
	pos = out = malloc(olen);
	if (out == NULL)
		return NULL;
	memset(pos, 0, olen);

	count = 0;
	for (i = 0; i < len; i++) {
		tmp = dtable[src[i]];
		if (tmp == 0x80)
			continue;

		in[count] = src[i];
		block[count] = tmp;
		count++;
		if (count == 4) {
			*pos++ = (block[0] << 2) | (block[1] >> 4);
			*pos++ = (block[1] << 4) | (block[2] >> 2);
			*pos++ = (block[2] << 6) | block[3];
			count = 0;
		}
	}

	if (pos > out) {
		if (in[2] == '=')
			pos -= 2;
		else if (in[3] == '=')
			pos--;
	}

	*out_len = pos - out;
	return out;
}
#ifdef WEBS_SECURITY
char *zte_base64_encode(const char *data, int data_len)
{ 
	int prepare = 0; 
	int ret_len; 
	int temp = 0; 
	char *ret = NULL; 
	char *f = NULL; 
	int tmp = 0; 
	char changed[4]; 
	int i = 0; 
	const char base[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; 

	if(data == NULL)
	{
		return NULL;
	}
	if(data_len == 0)
	{
		return NULL;
	}
	ret_len = data_len / 3; 
	temp = data_len % 3; 
	if (temp > 0) 
	{ 
	ret_len += 1; 
	} 
	ret_len = ret_len*4 + 1; 
	ret = (char *)malloc(ret_len); 

	if (ret == NULL) 
	{ 
		printf("No enough memory.\n"); 
		return NULL;
	} 
	memset(ret, 0, ret_len); 
	f = ret; 
	while (tmp < data_len) 
	{ 
		temp = 0; 
		prepare = 0; 
		memset(changed, '\0', 4); 
		while (temp < 3) 
		{ 
			//printf("tmp = %d\n", tmp); 
			if (tmp >= data_len) 
			{ 
				break; 
			} 
			prepare = ((prepare << 8) | (data[tmp] & 0xFF)); 
			tmp++; 
			temp++; 
		} 
		prepare = (prepare<<((3-temp)*8)); 
		//printf("before for : temp = %d, prepare = %d\n", temp, prepare); 
		for (i = 0; i < 4 ;i++ ) 
		{ 
			if (temp < i) 
			{ 
				changed[i] = 0x40; 
			} 
			else 
			{ 
				changed[i] = (prepare>>((3-i)*6)) & 0x3F; 
			} 
			*f = base[changed[i]]; 
			//printf("%.2X", changed[i]); 
			f++; 	
		} 
	} 
	*f = '\0'; 
	return ret; 
} 
#endif
