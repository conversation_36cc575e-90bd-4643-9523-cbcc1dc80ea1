/***********************************************************************************
 * Description:  define handle function for CELL BROADCAST module
 * Modify Date      Version     Author         Modification
 * 2013/01/08        V1.0      chenyi        create
 ***********************************************************************************/
#include "message.h"
#include "zte_web_cell_broadcast.h"
#include "zte_web_interface.h"
//#include "../../sms_pbm/libzte_wms.h"

#define ZTE_CBM_DELETE_COUNT_MAX 100
#define CBM_MSG_ID_LEN 11
#define CBM_SEPARATOR_CHARACTER ';'

static void zte_web_feed_back_cb_msg_empty(webs_t wp);

/******************************************************
* Function: parseStrBySeparator()
* Description: separate the string by a specific character
* Input:
* Output:
* Return: -1--->failed, 0--->successful
* Others:
* Modify Date    Version   Author         Modification
* 2010/10/27        V1.0     huangmin        create
2012/06/20        v2.0       chenyi             modify
*******************************************************/
static unsigned int parseStrBySeparator
(
    unsigned char* input,
    unsigned char* output,
    int destLen,
    unsigned char separator
)
{
	int i, j, k;
	int srcLen = 0;
	i = j = k = 0;

	if (NULL == input || NULL == output || destLen == 0 || strlen(input) == 0) {
		slog(MISC_PRINT, SLOG_DEBUG, "parseStrBySemicolon: param is null!\n");
		return 0;
	}

	srcLen = strlen(input) + 1;//which contains the '\0'

	for (i = 0; i < srcLen; i++) {
		if ('\0' == input[i]) {
			if (k <= (destLen - 1)) {
				*(output + destLen * j + k)  = '\0';
			} else {
				*(output + destLen * j + (destLen - 1))  = '\0';
			}

			return j + 1;
		}

		if (separator == input[i]) {
			if (k <= (destLen - 1)) {
				*(output + destLen * j + k)  = '\0';
			} else {
				*(output + destLen * j + (destLen - 1))  = '\0';
			}

			if ('\0' != input[i + 1]) {
				k = 0;
				j++;
			}
		} else {
			if (k < (destLen - 1)) {
				*(output + destLen * j + k)  = input[i];
				k++;
			}
		}
	}

	return j;
}






/**********************************************************************
* Function:         zte_web_feed_back_cb_msg_empty
* Description:      to write empty cb msg to web
* Input:            the web para
* Output:
* Return:
* Others:
* Modify Date   Version     Author          Modification
* -----------------------------------------------
* 2013/01/08    V1.0        chenyi       first version
**********************************************************************/
static void zte_web_feed_back_cb_msg_empty(webs_t wp)
{
	if (NULL == wp) {
		return;
	}

	web_feedback_header(wp);
	(void)websWrite(wp, T("{\"%s\":[]}"), CB_MSG);
}

