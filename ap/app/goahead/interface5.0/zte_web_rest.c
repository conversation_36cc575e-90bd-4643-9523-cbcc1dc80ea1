#include "message.h"
#include "zte_web_rest.h"
#include "zte_web_pbm.h"
#include "zte_web_sms.h"
//#include "SMS_main.h"


/*lint -e123*/
int websXMLPostHandler(webs_t wp, char_t *urlPrefix, char_t *webDir, int arg, char_t *url, char_t *path, char_t *query)
{
	//int result = 0;
	//char_t *cmd = NULL;
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:START!"); /*lint !e26*/
	if (NULL == wp ) {
		slog(MISC_PRINT, SLOG_ERR, "wp is null!"); /*lint !e26*/
		return 1;
	}
	if (NULL == urlPrefix || NULL == webDir || NULL == url || NULL == path || NULL == query) {
		slog(MISC_PRINT, SLOG_ERR, "In put is null!"); /*lint !e26*/
		websError(wp, 200, T("websXMLPostHandler:input is NULL"));
		return 1;
	}
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:middle!"); /*lint !e26*/

#if 0
	cmd = websGetVar(wp, T("action_process"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:websGetVar->action_process:[%s]", cmd); /*lint !e26*/
	cmd = websGetVar(wp, T("goformId"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:websGetVar->goformid:[%s]", cmd); /*lint !e26*/
	cmd = websGetVar(wp, T("cmd"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:websGetVar->cmd:[%s]", cmd); /*lint !e26*/

	cmd = websGetVar(wp, T("xml_tag"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "[websXMLPostHandler]:websGetVar->setdata:[%s]", cmd); /*lint !e26*/
	websWrite(wp, T("liuyingnan:xml_tag:%s\n"), cmd);
#endif

	zte_xml_switch_handler(wp);
	return 0;
}


void zte_xml_switch_handler(webs_t wp)
{
	char_t *action = NULL;
	char_t *zte_goform_id = NULL;

	slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler>>wp->path]:%s\n", wp->query); /*lint !e26 !e1013*/
	zte_goform_id = websGetVar(wp, T("goformId"), T(""));
	action = websGetVar(wp, T("action_process"), T(""));
	slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler>>test websGetVar]:%s\n", zte_goform_id); /*lint !e26*/
	slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler>>action]:%s\n", action); /*lint !e26*/
	if (0 == strcmp(action, "get")) {

		zte_goform_get_cmd_process(wp,T(""),T("")); /*lint !e1013*/
		slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler]:get action end\n"); /*lint !e26*/
		return;
	} else if (0 == strcmp(action, "set")) {
		zte_goform_set_cmd_process(wp,T(""),T(""));/*lint !e1013*/
		slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler]:set action end\n"); /*lint !e26*/
		return;
	} else {
		slog(MISC_PRINT, SLOG_DEBUG, "[zte_xml_switch_handler]:Can not find action\n"); /*lint !e26*/
		return;
	}

}
#if 0 //for kw		
void zte_change_wp_query(webs_t wp, char_t *xml_str)
{
	//char goformId[50] = {0};
	//char getData[500] = {0};
	//char setData[500] = {0};
	//char action[20] = {0};
	ezxml_root_t root = NULL;
	ezxml_t xml = NULL;
	ezxml_t action_tag = NULL;

	root = (ezxml_root_t)ezxml_parse_str(xml_str, strlen(xml_str));
	xml = & (root->xml);
	if(xml == NULL)
		return;
	//slog(MISC_PRINT,SLOG_DEBUG,"[zte_change_wp_query_new]:xml->name:%s",xml->name);/*lint !e26*/
	action_tag = xml->child;

	if (! action_tag) {
		slog(MISC_PRINT, SLOG_DEBUG, "[zte_change_wp_query_new]:action_tag is NULL or the xml_str is not full!"); /*lint !e26*/
		//zte_write_result_to_web(wp,"action_tag is NULL!");
		return;
	} else {
		while (action_tag) {
			slog(MISC_PRINT, SLOG_DEBUG, "[zte_change_wp_query_new]:name:%s", action_tag->name); /*lint !e26*/
			slog(MISC_PRINT, SLOG_DEBUG, "[zte_change_wp_query_new]:text:%s", action_tag->txt); /*lint !e26*/
			websSetVar(wp, action_tag->name, action_tag->txt);
			action_tag = action_tag->sibling;
		}

	}

	return;
}
#endif

void zte_rest_result_write(webs_t wp, char_t *result)
{
	websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
	websWrite(wp, T("<data>\n"));
	websWrite(wp, T("<result>%s</result>\n"), result);
	websWrite(wp, T("</data>\n"));
	websWrite(wp, T("\n"));
}

void zte_rest_cmd_write(webs_t wp, char_t *name, char_t *value, int r_flag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>%s</%s>\n"), name, value, name);
	} else {
		if (r_flag == 1) {
			websWrite(wp, T("\"%s\":\"%s\","), name, value);
		} else {
			websWrite(wp, T("\"%s\":\"%s\""), name, value);
		}
	}
}

void zte_rest_cmd_write_int(webs_t wp, char_t *name, int value, int r_flag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>%d</%s>\n"), name, value, name);
	} else {
		if (r_flag == 1) {
			websWrite(wp, T("\"%s\":\"%d\","), name, value);
		} else {
			websWrite(wp, T("\"%s\":\"%d\""), name, value);
		}
	}
}

void zte_rest_cmd_write_head(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
	} else {
		websWrite(wp, T("{"));
	}
}

void zte_rest_cmd_write_foot(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("</data>\n"));
	} else {
		websWrite(wp, T("}"));
	}
}

void zte_rest_get_pbm_data_head(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
	} else {
		(void)websWrite(wp, T("{\"%s\":["), PBM_DATA);
	}
}

void zte_rest_get_pbm_data_foot(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("</data>\n"));
	} else {
		(void)websWrite(wp, T("]}"));
	}
}


void zte_rest_get_pbm_data_sim_start(webs_t wp, zte_pbm_query_resp_s_type *p_pbm_query_result, int index)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
		websWrite(wp, T("<%s>\n"), PBM_DATA);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, PBM_ID);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, PBM_INDEX);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, PBM_LOCATION);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, PBM_NAME);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, PBM_NUMBER);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, PBM_TYPE);
		websWrite(wp, T("</%s>\n"), PBM_DATA);
	} else {
		(void)websWrite(wp, T("{\"%s\":[{\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%d\"}"), \
		                PBM_DATA, PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, \
		                PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, \
		                PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, \
		                PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, \
		                PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, \
		                PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type);
	}
}

void zte_rest_get_pbm_data_sim(webs_t wp, zte_pbm_query_resp_s_type *p_pbm_query_result, int index, int tag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>\n"), PBM_DATA);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, PBM_ID);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, PBM_INDEX);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, PBM_LOCATION);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, PBM_NAME);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, PBM_NUMBER);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, PBM_TYPE);
		websWrite(wp, T("</%s>\n"), PBM_DATA);
	} else {
		if (tag == 1) {
			websWrite(wp, T(","));
		}
		(void)websWrite(wp, T("{\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%d\"}"), \
		                PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, \
		                PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, \
		                PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, \
		                PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, \
		                PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, \
		                PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type);
	}

}




void zte_rest_get_pbm_data_other_start(webs_t wp, zte_pbm_query_resp_s_type *p_pbm_query_result, int index)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
		websWrite(wp, T("<%s>\n"), PBM_DATA);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, PBM_ID);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, PBM_INDEX);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, PBM_LOCATION);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, PBM_NAME);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, PBM_NUMBER);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, PBM_TYPE);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_ANR, (*p_pbm_query_result).data[index].zte_pbm_anr, PBM_ANR);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_ANR1, (*p_pbm_query_result).data[index].zte_pbm_anr1, PBM_ANR1);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_EMAIL, (*p_pbm_query_result).data[index].zte_pbm_email, PBM_EMAIL);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_SNE, (*p_pbm_query_result).data[index].zte_pbm_sne, PBM_SNE);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_GROUP, (*p_pbm_query_result).data[index].zte_pbm_group, PBM_GROUP);
		websWrite(wp, T("</%s>\n"), PBM_DATA);
	} else {
		(void)websWrite(wp, T("{\"%s\":[{\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%d\""\
		                      ",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\"}"), \
		                PBM_DATA, PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, \
		                PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, \
		                PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, \
		                PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, \
		                PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, \
		                PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, \
		                PBM_ANR, (*p_pbm_query_result).data[index].zte_pbm_anr, \
		                PBM_ANR1, (*p_pbm_query_result).data[index].zte_pbm_anr1, \
		                PBM_EMAIL, (*p_pbm_query_result).data[index].zte_pbm_email, \
		                PBM_SNE, (*p_pbm_query_result).data[index].zte_pbm_sne, \
		                PBM_GROUP, (*p_pbm_query_result).data[index].zte_pbm_group);
	}
}


void zte_rest_get_pbm_data_other(webs_t wp, zte_pbm_query_resp_s_type *p_pbm_query_result, int index, int tag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>\n"), PBM_DATA);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, PBM_ID);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, PBM_INDEX);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, PBM_LOCATION);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, PBM_NAME);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, PBM_NUMBER);
		websWrite(wp, T("<%s>%d</%s>\n"), PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, PBM_TYPE);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_ANR, (*p_pbm_query_result).data[index].zte_pbm_anr, PBM_ANR);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_ANR1, (*p_pbm_query_result).data[index].zte_pbm_anr1, PBM_ANR1);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_EMAIL, (*p_pbm_query_result).data[index].zte_pbm_email, PBM_EMAIL);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_SNE, (*p_pbm_query_result).data[index].zte_pbm_sne, PBM_SNE);
		websWrite(wp, T("<%s>%s</%s>\n"), PBM_GROUP, (*p_pbm_query_result).data[index].zte_pbm_group, PBM_GROUP);
		websWrite(wp, T("</%s>\n"), PBM_DATA);
	} else {
		if (tag == 1) {
			websWrite(wp, T(","));
		}
		(void)websWrite(wp, T("{\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%d\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%d\""\
		                      ",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\"}"), \
		                PBM_ID, (*p_pbm_query_result).data[index].zte_pbm_id, \
		                PBM_INDEX, (*p_pbm_query_result).data[index].zte_pbm_index, \
		                PBM_LOCATION, (*p_pbm_query_result).data[index].zte_pbm_location, \
		                PBM_NAME, (*p_pbm_query_result).data[index].zte_pbm_name, \
		                PBM_NUMBER, (*p_pbm_query_result).data[index].zte_pbm_number, \
		                PBM_TYPE, (*p_pbm_query_result).data[index].zte_pbm_type, \
		                PBM_ANR, (*p_pbm_query_result).data[index].zte_pbm_anr, \
		                PBM_ANR1, (*p_pbm_query_result).data[index].zte_pbm_anr1, \
		                PBM_EMAIL, (*p_pbm_query_result).data[index].zte_pbm_email, \
		                PBM_SNE, (*p_pbm_query_result).data[index].zte_pbm_sne, \
		                PBM_GROUP, (*p_pbm_query_result).data[index].zte_pbm_group);
	}
}

void zte_rest_get_sms_data_head(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
	} else {
		(void)websWrite(wp, T("{\"%s\":["), SMS_MESSAGES);
	}
}

void zte_rest_get_sms_data_foot(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("</data>\n"));
	} else {
		(void)websWrite(wp, T("]}"));
	}
}

void zte_rest_get_bro_data_head(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
	} else {
		(void)websWrite(wp, T("{\"%s\":"), SMS_MESSAGES);
	}
}
void zte_rest_get_bro_data_foot(webs_t wp)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("</data>\n"));
	} else {
		(void)websWrite(wp, T("}"));
	}
}


void zte_rest_get_sms_data(webs_t wp, zte_sms_query_result *p_sms_query_result, int i, int tag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		slog(MISC_PRINT, SLOG_DEBUG, "zte_rest_get_sms_data WEBS_XML_CLIENT_REQUEST");
		char *tmp_num = NULL;
		char pbm_name[100] = {0};
		int len = 0;
		len = strlen(p_sms_query_result->data[i].number);
		slog(MISC_PRINT, SLOG_DEBUG, "len=%d", len);
		slog(MISC_PRINT, SLOG_DEBUG, "p_sms_query_result->data[i].number=%s", p_sms_query_result->data[i].number);
		if (len > NUMBER_SIZE) {
			tmp_num = p_sms_query_result->data[i].number + (len - NUMBER_SIZE);
		} else {
			tmp_num = p_sms_query_result->data[i].number;
		}
		slog(MISC_PRINT, SLOG_DEBUG, "tmp_num=%s", tmp_num);
		memset(pbm_name, 0, sizeof(pbm_name));
		zte_libpbm_get_name_by_number(tmp_num, pbm_name);
		slog(MISC_PRINT, SLOG_DEBUG, "pbm_name=%s", pbm_name);
		websWrite(wp, T("<%s>\n"), SMS_MESSAGES);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_ID, (*p_sms_query_result).data[i].id, SMS_ID);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_NUMBER, (*p_sms_query_result).data[i].number, SMS_NUMBER);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_NAME, pbm_name, SMS_NAME);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_CONTENT, (*p_sms_query_result).data[i].sms_content, SMS_CONTENT);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_TAG, (*p_sms_query_result).data[i].tag, SMS_TAG);
		websWrite(wp, T("<%s>%s,%s,%s,%s,%s,%s,%s</%s>\n"), SMS_DATE, (*p_sms_query_result).data[i].date.year, \
		          (*p_sms_query_result).data[i].date.month, (*p_sms_query_result).data[i].date.day, \
		          (*p_sms_query_result).data[i].date.hour, (*p_sms_query_result).data[i].date.min, \
		          (*p_sms_query_result).data[i].date.sec, (*p_sms_query_result).data[i].date.timezone, SMS_DATE);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_DRAFT_GROUP_ID, (*p_sms_query_result).data[i].draft_group_id, SMS_DRAFT_GROUP_ID);
		websWrite(wp, T("</%s>\n"), SMS_MESSAGES);
	} else {
		if (tag == 1) {
			websWrite(wp, T(","));
		}
		(void)websWrite(wp, T("{\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s,%s,%s,%s,%s,%s,%s\",\"%s\":\"%s\"}"), \
		                SMS_ID, (*p_sms_query_result).data[i].id, \
		                SMS_NUMBER, (*p_sms_query_result).data[i].number, \
		                SMS_CONTENT, (*p_sms_query_result).data[i].sms_content, \
		                SMS_TAG, (*p_sms_query_result).data[i].tag, \
		                SMS_DATE, (*p_sms_query_result).data[i].date.year, \
		                (*p_sms_query_result).data[i].date.month, (*p_sms_query_result).data[i].date.day, \
		                (*p_sms_query_result).data[i].date.hour, (*p_sms_query_result).data[i].date.min, \
		                (*p_sms_query_result).data[i].date.sec, (*p_sms_query_result).data[i].date.timezone, \
		                SMS_DRAFT_GROUP_ID, (*p_sms_query_result).data[i].draft_group_id);
	}

}

void zte_rest_sms_parameter(webs_t wp, zte_wms_parameter_info_s_type sms_parameter_info)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"));
		websWrite(wp, T("<data>\n"));
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_PARAMETER_SCA, sms_parameter_info.sca, SMS_PARAMETER_SCA);
		websWrite(wp, T("<%s>%d</%s>\n"), SMS_PARAMETER_MEM_STORE, sms_parameter_info.mem_store, SMS_PARAMETER_MEM_STORE);
		websWrite(wp, T("<%s>%u</%s>\n"), SMS_PARAMETER_VALIDITY_PERIOD, sms_parameter_info.tp_validity_period, SMS_PARAMETER_VALIDITY_PERIOD);
		websWrite(wp, T("<%s>%u</%s>\n"), SMS_PARAMETER_STATUS_REPORT, sms_parameter_info.status_report_on, SMS_PARAMETER_STATUS_REPORT);
		websWrite(wp, T("<%s>%u</%s>\n"), SMS_PARAMETER_SEND_RETRY, sms_parameter_info.sendfail_retry_on, SMS_PARAMETER_SEND_RETRY);
		websWrite(wp, T("<%s>%u</%s>\n"), SMS_PARAMETER_OUTDATE_DELETE, sms_parameter_info.outdate_delete_on, SMS_PARAMETER_OUTDATE_DELETE);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_PARAMETER_DEFAULT_STORE, sms_parameter_info.default_store, SMS_PARAMETER_DEFAULT_STORE);
		websWrite(wp, T("</data>\n"));
	} else {
		(void)websWrite(wp, T("{\"%s\":\"%s\",\"%s\":\"%d\",\"%s\":\"%u\",\"%s\":\"%u\",\"%s\":\"%u\",\"%s\":\"%u\",\"%s\":\"%s\"}"), \
		                SMS_PARAMETER_SCA, sms_parameter_info.sca, \
		                SMS_PARAMETER_MEM_STORE, sms_parameter_info.mem_store, \
		                SMS_PARAMETER_VALIDITY_PERIOD, sms_parameter_info.tp_validity_period, \
		                SMS_PARAMETER_STATUS_REPORT, sms_parameter_info.status_report_on, \
		                SMS_PARAMETER_SEND_RETRY, sms_parameter_info.sendfail_retry_on, \
		                SMS_PARAMETER_OUTDATE_DELETE, sms_parameter_info.outdate_delete_on, \
		                SMS_PARAMETER_DEFAULT_STORE, sms_parameter_info.default_store);
	}
}

void zte_rest_sms_status_rpt(webs_t wp, zte_sms_query_result *p_sms_query_result, int i, int tag)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>\n"), SMS_MESSAGES);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_ID, (*p_sms_query_result).data[i].id, SMS_ID);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_NUMBER, (*p_sms_query_result).data[i].number, SMS_NUMBER);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_CONTENT, (*p_sms_query_result).data[i].sms_content, SMS_CONTENT);
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_TAG, (*p_sms_query_result).data[i].tag, SMS_TAG);
		websWrite(wp, T("<%s>%s,%s,%s,%s,%s,%s,%s</%s>\n"), SMS_DATE, (*p_sms_query_result).data[i].date.year, \
		          (*p_sms_query_result).data[i].date.month, (*p_sms_query_result).data[i].date.day, \
		          (*p_sms_query_result).data[i].date.hour, (*p_sms_query_result).data[i].date.min, \
		          (*p_sms_query_result).data[i].date.sec, (*p_sms_query_result).data[i].date.timezone, SMS_DATE);
		websWrite(wp, T("</%s>\n"), SMS_MESSAGES);
	} else {
		if (tag == 1) {
			websWrite(wp, T(","));
		}
		(void)websWrite(wp, T("{\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s,%s,%s,%s,%s,%s,%s\"}"), \
		                SMS_ID, (*p_sms_query_result).data[i].id, \
		                SMS_NUMBER, (*p_sms_query_result).data[i].number, \
		                SMS_CONTENT, (*p_sms_query_result).data[i].sms_content, \
		                SMS_DATE, (*p_sms_query_result).data[i].date.year, (*p_sms_query_result).data[i].date.month, \
		                (*p_sms_query_result).data[i].date.day, (*p_sms_query_result).data[i].date.hour, \
		                (*p_sms_query_result).data[i].date.min, (*p_sms_query_result).data[i].date.sec, \
		                (*p_sms_query_result).data[i].date.timezone);
	}
}


void zte_rest_boradcast_data(webs_t wp, zte_wms_cell_bro_s_type *p_bro_query_result)
{
	if (wp->flags & WEBS_XML_CLIENT_REQUEST) {
		websWrite(wp, T("<%s>%s</%s>\n"), SMS_MESSAGES, (*p_bro_query_result).content, SMS_MESSAGES);
	} else {
		(void)websWrite(wp, T("\"%s\""), (*p_bro_query_result).content);
	}
}










