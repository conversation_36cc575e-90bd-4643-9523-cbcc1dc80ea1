#ifndef ZTE_WEB_NET_LAN_H
#define ZTE_WEB_NET_LAN_H
#include "webs.h"

extern void zte_dhcpv6_state_set(webs_t wp);
extern void quick_dhcp_set(webs_t wp);
extern void zte_dhcp_set(webs_t wp);
extern void zte_static_dhcp_set(webs_t wp);

extern void zte_set_bind_static_address(webs_t wp);
extern void zte_bind_static_address_add(webs_t wp);
extern void zte_bind_static_address_del(webs_t wp);
extern void zte_get_current_static_addr_list(webs_t wp);

extern void zte_get_lan_station_list(webs_t wp);

extern void zte_add_children_device(webs_t wp);
extern void zte_del_children_device(webs_t wp);

extern void zte_add_white_site(webs_t wp);
extern void zte_remove_white_site(webs_t wp);

extern void zte_get_children_device_list(webs_t wp);
extern void zte_get_white_site_list(webs_t wp);



#endif

