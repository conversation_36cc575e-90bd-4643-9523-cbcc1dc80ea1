#include <stdlib.h>
#include "zte_web_interface.h"
#include "zte_web_net_lan.h"
#include "zte_web_net_other.h"

#include "message.h"
#include "netotherapi.h"
#include "mainctl_msg.h"

#define WEB_DHCP_LEASE_TIME_DEFAULT 86400  /* 24*3600 */
#define safe_free(x) do { if(x) {free(x); x=NULL;} } while(0)

/*����system��*/
#define CMD_STR_LEN     256
/*
#define do_cmd(format, cmds...) ({ \
    int __STATAS__; \
    char __TEMP__[CMD_STR_LEN]; \
    sprintf(__TEMP__, format, ##cmds); \
    __STATAS__ = system(__TEMP__); \
    __STATAS__; \
})
*/

void zte_dhcpv6_state_set(webs_t wp)
{
	char_t *dhcpv6stateE = NULL;
	char_t *dhcpv6statelessE = NULL;

	dhcpv6stateE = websGetVar(wp, T("dhcpv6stateEnabled"), T(""));
	dhcpv6statelessE = websGetVar(wp, T("dhcpv6statelessEnabled"), T(""));

	slog(MISC_PRINT, SLOG_DEBUG, "dhcpv6stateE = %s\n", dhcpv6stateE);
	slog(MISC_PRINT, SLOG_DEBUG, "dhcpv6statelessE = %s\n", dhcpv6statelessE);

	if (!dhcpv6stateE || !strlen(dhcpv6stateE)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	if (!dhcpv6statelessE || !strlen(dhcpv6statelessE)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	if ((atoi(dhcpv6stateE) == 0) && (atoi(dhcpv6statelessE) == 0)) {
		zte_web_write("dhcpv6stateEnabled", "0");
		zte_web_write("dhcpv6statelessEnabled", "0");
	} else if ((atoi(dhcpv6stateE) == 1) && (atoi(dhcpv6statelessE) == 0)) {
		zte_web_write("dhcpv6stateEnabled", "1");
		zte_web_write("dhcpv6statelessEnabled", "0");
	} else if ((atoi(dhcpv6stateE) == 0) && (atoi(dhcpv6statelessE) == 1)) {
		zte_web_write("dhcpv6stateEnabled", "0");
		zte_web_write("dhcpv6statelessEnabled", "1");
	} else {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	//cfg_save();
	zte_write_result_to_web(wp, SUCCESS);
	zte_goform_mgmt_reboot_process(wp);
}

static void dhcp_setting_process(char *lanIp, char *lanNetmask, char *lanDhcpType,
                                 char *dhcpStart, char *dhcpEnd, char *dhcpLease)
{
	dhcp_setting_req dhcp_setting = {0};

	strncpy(dhcp_setting.lan_ip, lanIp,sizeof(dhcp_setting.lan_ip)-1);
	strncpy(dhcp_setting.lan_netmask, lanNetmask,sizeof(dhcp_setting.lan_netmask)-1);
	if (!strncmp(lanDhcpType, "DISABLE", strlen("DISABLE"))) {
		dhcp_setting.dhcp_enabled = 0;
	} else {
		dhcp_setting.dhcp_enabled = 1;
		strncpy(dhcp_setting.dhcp_start, dhcpStart,sizeof(dhcp_setting.dhcp_start)-1);
		strncpy(dhcp_setting.dhcp_end, dhcpEnd,sizeof(dhcp_setting.dhcp_end)-1);
		strncpy(dhcp_setting.dhcp_dns, lanIp,sizeof(dhcp_setting.dhcp_dns)-1);
		strncpy(dhcp_setting.dhcp_lease, dhcpLease,sizeof(dhcp_setting.dhcp_lease)-1);
	}

	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, \
	                 MSG_CMD_NET_DHCP_SETTING_REQ, sizeof(dhcp_setting), &dhcp_setting, 0);
}

//����DHCP��������
void quick_dhcp_set(webs_t wp)
{
	// data from form
	char_t *lanIp = NULL;
	char_t *lanNetmask = NULL;
	char_t *lanDhcpType = NULL;
	char_t *dhcpStart = NULL;
	char_t *dhcpEnd = NULL;
	char_t *dhcpDns = NULL;
	char_t *dhcpLease = NULL;
	char dhcp_temp[NVIO_DEFAULT_LEN] = {0};
	int dhcp_lease_time = WEB_DHCP_LEASE_TIME_DEFAULT; /* default 24*3600 */

	int dhcp_lease_hour = 24;

	// get old data
	int bLanNotChangeFlag = 0;
	char lanIp_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char lanNetmask_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpType_read[NV_ITEM_VALUE_YES_NO_LEN] = {0};
	char dhcpStart_read[WEB_IP_ADDR_MAX_LEN]  =  {0};
	char dhcpEnd_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpDns_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpLeaseHour_read[NVIO_DEFAULT_LEN] = {0};

	// wan info
	//char wan_ip[NVIO_DEFAULT_LEN] = {0};

	// Get the params from pages
	lanIp = websGetVar(wp, T("lanIp"), T(""));
	lanNetmask = websGetVar(wp, T("lanNetmask"), T(""));
	lanDhcpType = websGetVar(wp, T("lanDhcpType"), T("SERVER"));
	dhcpStart = websGetVar(wp, T("dhcpStart"), T(""));
	dhcpEnd = websGetVar(wp, T("dhcpEnd"), T(""));
	dhcpDns = websGetVar(wp, T("dhcpDns"), T(lanIp));
	dhcpLease = websGetVar(wp, T("dhcpLease"), T("24"));
#if 0	// kw 3
	dhcp_lease_time = atoi(dhcpLease) * 3600;/* hours to seconds */
#else
    dhcp_lease_hour = atoi(dhcpLease);

    if(dhcp_lease_hour < 0 || dhcp_lease_hour >  INT_MAX-1)
    {
        dhcp_lease_hour = 24;
    }

    dhcp_lease_time = dhcp_lease_hour * 3600;
	
#endif
	sprintf(dhcp_temp, "%d", dhcp_lease_time);

	//add by liuyingnan for server safe for xss attack start
	if (DATA_NO_SAFE == zte_Safe_noSpecialChar(lanIp)
	    || DATA_NO_SAFE == zte_Safe_noSpecialChar(dhcpStart)
	    || DATA_NO_SAFE == zte_Safe_noSpecialChar(dhcpEnd)) {
		cfg_set("data_safe", "failed");
		slog(MISC_PRINT, SLOG_ERR, "Get Data is no Safe:lanIp:%s\n", lanIp); /*lint !e26*/
		//zte_write_result_to_web(wp,FAILURE);

		return;
	}
	//add by liuyingnan for server safe for xss attack end

	// read old data

	(void)zte_web_read("lan_ipaddr", lanIp_read);
	(void)zte_web_read("lan_netmask", lanNetmask_read);
	(void)zte_web_read("dhcpEnabled", dhcpType_read);
	(void)zte_web_read("dhcpStart", dhcpStart_read);
	(void)zte_web_read("dhcpEnd", dhcpEnd_read);
	(void)zte_web_read("dhcpDns", dhcpDns_read);
	(void)zte_web_read("dhcpLease_hour", dhcpLeaseHour_read);

	if (
	    (0 == strcmp(lanDhcpType, "SERVER"))
	    && (0 == strcmp(dhcpType_read, "1"))
	) {
		if (!strcmp(lanIp, lanIp_read) &&
		    !strcmp(lanNetmask, lanNetmask_read) &&
		    !strcmp(dhcpStart, dhcpStart_read) &&
		    !strcmp(dhcpEnd, dhcpEnd_read) &&
		    !strcmp(dhcpDns, dhcpDns_read) &&
		    !strcmp(dhcpLease, dhcpLeaseHour_read)
		   ) {
			bLanNotChangeFlag = 1;
		}
	} else if ((0 == strcmp(lanDhcpType, "DISABLE")) && (0 == strcmp(dhcpType_read, "0"))) {
		if (!strcmp(lanIp, lanIp_read) && !strcmp(lanNetmask, lanNetmask_read)) {
			bLanNotChangeFlag = 1;
		}
	}

	if (1 == bLanNotChangeFlag) {
		return;
	}

	/*
	 * check static ip address:
	 * lan and wan ip should not be the same except in bridge mode
	 */
	if (!strncmp(lanDhcpType, "SERVER", strlen("SERVER"))) { /*lint !e530*/
		if (0 == isIpValid(dhcpStart)) {
			return;
		}

		if (0 == isIpValid(dhcpEnd)) {
			return;
		}
	}

	/* dhcp���÷��͸����� */
	dhcp_setting_process(lanIp, lanNetmask, lanDhcpType, dhcpStart, dhcpEnd, dhcpLease);

}

//DHCP��������
void zte_dhcp_set(webs_t wp)
{
	slog(MISC_PRINT, SLOG_NORMAL, T("UFIx User set dhcp!\n"));
	// data from form
	char_t *lanIp = NULL;
	char_t *lanNetmask = NULL;
	char_t *lanDhcpType = NULL;
	char_t *dhcpStart = NULL;
	char_t *dhcpEnd = NULL;
	char_t *dhcpDns = NULL;
	char_t *dhcpLease = NULL;
	char dhcp_temp[NVIO_DEFAULT_LEN] = {0};
	int dhcp_lease_time = WEB_DHCP_LEASE_TIME_DEFAULT; /* default 24*3600 */

	int dhcp_lease_hour = 24;

	// get old data
	int bLanNotChangeFlag = 0;
	char lanIp_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char lanNetmask_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpType_read[NV_ITEM_VALUE_YES_NO_LEN] = {0};
	char dhcpStart_read[WEB_IP_ADDR_MAX_LEN]  =  {0};
	char dhcpEnd_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpDns_read[WEB_IP_ADDR_MAX_LEN] = {0};
	char dhcpLeaseHour_read[NVIO_DEFAULT_LEN] = {0};

	// Get the params from pages
	lanIp = websGetVar(wp, T("lanIp"), T(""));
	lanNetmask = websGetVar(wp, T("lanNetmask"), T(""));
	lanDhcpType = websGetVar(wp, T("lanDhcpType"), T("SERVER"));
	dhcpStart = websGetVar(wp, T("dhcpStart"), T(""));
	dhcpEnd = websGetVar(wp, T("dhcpEnd"), T(""));
	dhcpDns = websGetVar(wp, T("dhcpDns"), T(lanIp));
	dhcpLease = websGetVar(wp, T("dhcpLease"), T("24"));

#if 0	// kw 3
	dhcp_lease_time = atoi(dhcpLease) * 3600;/* hours to seconds */
#else
	dhcp_lease_hour = atoi(dhcpLease);
	
	if(dhcp_lease_hour < 0 || dhcp_lease_hour >  INT_MAX-1)
	{
		dhcp_lease_hour = 24;
	}
	
	dhcp_lease_time = dhcp_lease_hour * 3600;
		
#endif

	
	sprintf(dhcp_temp, "%d", dhcp_lease_time);
	//add by liuyingnan for server safe for xss attack start
	if (DATA_NO_SAFE == zte_Safe_noSpecialChar(lanIp)
	    || DATA_NO_SAFE == zte_Safe_noSpecialChar(dhcpStart)
	    || DATA_NO_SAFE == zte_Safe_noSpecialChar(dhcpEnd)) {
		cfg_set("data_safe", "failed");
		slog(MISC_PRINT, SLOG_ERR, "Get Data is no Safe:lanIp:%s\n", lanIp); /*lint !e26*/
		zte_write_result_to_web(wp, FAILURE);
		return;
	}
	//add by liuyingnan for server safe for xss attack end

	// read old data

	(void)zte_web_read("lan_ipaddr", lanIp_read);
	(void)zte_web_read("lan_netmask", lanNetmask_read);
	(void)zte_web_read("dhcpEnabled", dhcpType_read);
	(void)zte_web_read("dhcpStart", dhcpStart_read);
	(void)zte_web_read("dhcpEnd", dhcpEnd_read);
	(void)zte_web_read("dhcpDns", dhcpDns_read);
	(void)zte_web_read("dhcpLease_hour", dhcpLeaseHour_read);

	if (
	    (0 == strcmp(lanDhcpType, "SERVER"))
	    && (0 == strcmp(dhcpType_read, "1"))
	) {
		if (!strcmp(lanIp, lanIp_read) &&
		    !strcmp(lanNetmask, lanNetmask_read) &&
		    !strcmp(dhcpStart, dhcpStart_read) &&
		    !strcmp(dhcpEnd, dhcpEnd_read) &&
		    !strcmp(dhcpDns, dhcpDns_read) &&
		    !strcmp(dhcpLease, dhcpLeaseHour_read)
		   ) {
			bLanNotChangeFlag = 1;
		}
	} else if ((0 == strcmp(lanDhcpType, "DISABLE")) && (0 == strcmp(dhcpType_read, "0"))) {
		if (!strcmp(lanIp, lanIp_read) && !strcmp(lanNetmask, lanNetmask_read)) {
			bLanNotChangeFlag = 1;
		}
	}

	if (1 == bLanNotChangeFlag) {
		zte_write_result_to_web(wp, SUCCESS);
		return;
	}

	/*
	 * check static ip address:
	 * lan and wan ip should not be the same except in bridge mode
	 */
	if (!strncmp(lanDhcpType, "SERVER", strlen("SERVER"))) { /*lint !e530*/
		if (0 == isIpValid(dhcpStart)) {
			zte_write_result_to_web(wp, FAILURE);
			return;
		}

		if (0 == isIpValid(dhcpEnd)) {
			zte_write_result_to_web(wp, FAILURE);
			return;
		}
	}

	/* dhcp���÷��͸����� */
	dhcp_setting_process(lanIp, lanNetmask, lanDhcpType, dhcpStart, dhcpEnd, dhcpLease);

	zte_write_result_to_web(wp, SUCCESS);

	zte_goform_mgmt_reboot_process(wp);
}

//����DHCP��ַ���еľ�̬IP�б�
void zte_static_dhcp_set(webs_t wp)
{
	char_t *mac_ip_list = NULL;
	mac_ip_list = websGetVar(wp, T("mac_ip_list"), T(""));

	slog(MISC_PRINT, SLOG_DEBUG, "add======mac_ip_list=%s\n", mac_ip_list);
	if (!mac_ip_list)
		return;
	cfg_set("mac_ip_list", mac_ip_list);
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_STATIC_DHCP, 0, NULL, 0);
	//cfg_save();
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_set_bind_static_address(webs_t wp)
{
	char *bind_enable = NULL;    /* 0: Disabled  1: Enabled */
	bind_enable = websGetVar(wp, T("mac_ip_status"), T("0"));

	switch (atoi(bind_enable)) {
	case 0:
		(void)zte_web_write("static_dhcp_enable", "0");    /* Disable */
		break;
	case 1:
		(void)zte_web_write("static_dhcp_enable", "1");    /* Enable */
		break;
	default:
		(void)zte_web_write("static_dhcp_enable", "0");    /* Disable */
		break;
	}

	slog(MISC_PRINT, SLOG_NORMAL, "zte_set_bind_static_address SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_BIND_STATIC_ADDRESS, 0, NULL, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_set_bind_static_address SEND MESSAGE TO MC END"); /*lint !e26*/
	//cfg_save();
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_bind_static_address_add(webs_t wp)
{
	char *ip = NULL;
	char *mac = NULL;
	struct static_macip_info static_macip_info;

	ip = websGetVar(wp, T("ip_address"), T(""));
	mac = websGetVar(wp, T("mac_address"), T(""));

	if (!ip || !strlen(ip)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}
	if ((!mac) || (strlen(mac) != 17)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	strncpy(static_macip_info.mac, mac,sizeof(static_macip_info.mac)-1);
	strncpy(static_macip_info.ip, ip,sizeof(static_macip_info.ip)-1);

	slog(MISC_PRINT, SLOG_NORMAL, "zte_bind_static_address_add SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_BIND_STATIC_ADDRESS_ADD, sizeof(struct static_macip_info), (UCHAR *)&static_macip_info, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_bind_static_address_add SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_bind_static_address_del(webs_t wp)
{
	char *mac = NULL;
	mac = websGetVar(wp, T("mac_address"), T(""));

	if ((!mac) || (strlen(mac) != 17)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	slog(MISC_PRINT, SLOG_NORMAL, "zte_bind_static_address_del SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_BIND_STATIC_ADDRESS_DEL, strlen(mac), (UCHAR *)mac, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_bind_static_address_del SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_get_current_static_addr_list(webs_t wp)
{
	FILE *static_macip_file = NULL;
	char line[100] = {0};
	char mac_address[18] = {0};
	char ip_address[50] = {0};

	BOOL first = TRUE;

	char path_conf[50] = {0};
	char path_file[100] = {0};
	cfg_get_item("path_conf", path_conf, sizeof(path_conf));
	sprintf(path_file, "%s/static_macip_file", path_conf);

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	(void)websWrite(wp, T("\"%s\":["), CMD_CURRENT_STATICADDR_LIST);
	static_macip_file = fopen(path_file, "r");
	if (static_macip_file == NULL) {
		fprintf(stderr, "can not open file static_macip_file.");
		goto out;
	}

	while (fgets(line, sizeof(line), static_macip_file) != NULL) {
		if (first == FALSE) {
			(void)websWrite(wp, T(","));
		} else {
			first = FALSE;
		}
		strncpy(mac_address, line, sizeof(mac_address)-1);
		if(strlen(line) - 19 > 0 && strlen(line) - 19 < 50){
			snprintf(ip_address,strlen(line)-18,"%s",line + 18);
		}
		//strncpy(ip_address, line + 18, strlen(line) - 19);
		if (strncmp(mac_address, "", sizeof(mac_address)) != 0 && strncmp(ip_address, "", sizeof(ip_address)) != 0) {
			(void)websWrite(wp, T("{\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\",\"%s\":\"%s\"}"), HOSTANME, "", MAC, mac_address, ADDR, ip_address, DOMAIN, "");
		}

		memset(line, 0, sizeof(line));
		memset(mac_address, 0, sizeof(mac_address));
		memset(ip_address, 0, sizeof(ip_address));

	}
	fclose(static_macip_file);

out:
	(void)websWrite(wp, T("]"));
	zte_rest_cmd_write_foot(wp);
}

/*��ҳ������ʾ���н���DHCP client ��MAC��ַ��IP��ַ*/
static int zte_dhcp_client_list(int eid, webs_t wp, int argc, char_t **argv)
{
	FILE *fp = NULL;/*lint !e63*/
	typedef struct _DHCPOFFERINFO {
		unsigned long expires;
		unsigned long ip;
		unsigned char mac[6];
		unsigned char host_name[20];
		unsigned char pad[2];
	} DHCPOFFERINFO;
	struct in_addr addr;/*lint !e1080 !e565 */
	DHCPOFFERINFO addrlist;
	int64_t written_at;/*lint !e522*/
	int i = 0;
	memset(&addrlist, 0, sizeof(addrlist));
	// memset(&dclist,0,sizeof(DHCPCLIENTINFO)*MAX_DHCP_CLIENT_NUM);
	system("killall -q -USR1 udhcpd");
	fp = fopen("/var/udhcpd.leases", "r"); /*lint !e63*/

	if (NULL == fp) {
		slog(MISC_PRINT, SLOG_ERR, "can not open file/var/udhcpd.leases.");
		return -1;
	}
	if (fread(&written_at, 1, sizeof(written_at), fp) != sizeof(written_at)) {
		slog(MISC_PRINT, SLOG_DEBUG, "read the first part of udhcpd.leases fail!");
	}

	while (fread(&addrlist, 1, sizeof(addrlist), fp) == sizeof(addrlist)) {
		addr.s_addr = addrlist.ip;	/*lint !e115 !e1013 !e63 */
		websWrite(wp, T("<tr><td align=\"center\" width=\"10%%\" class=\"head\">%d</td>"), i + 1);
		websWrite(wp, T("<td align=\"center\" width=\"40%%\" class=\"tail\">%02X:%02X:%02X:%02X:%02X:%02X</td>"),
		          addrlist.mac[0], addrlist.mac[1], addrlist.mac[2], addrlist.mac[3], addrlist.mac[4], addrlist.mac[5]);
		websWrite(wp, T("<td align=\"center\"width=\"40%%\" class=\"tail\">%s</td>"), inet_ntoa(addr));
		websWrite(wp, T("</tr>"));
		i++;
	}
	slog(MISC_PRINT, SLOG_DEBUG, "the number of dhcp client access is %d", i);	/*lint !e26*/

//	if (NULL != fp) {  // kw 3
		fclose(fp);
//	}
	return 0;
}
#ifdef WEB_ASP
void init_router_web(void)
{
	websAspDefine(T("zte_dhcp_client_list"), zte_dhcp_client_list);
}
#endif
//��ȡ��wifi lan����Ϣ: ��wifi�ֿ���ԭ���ھ��б���lease���޷�����˭��wifi��ֻ��wifiоƬ֪��
void zte_get_lan_station_list(webs_t wp)
{
	struct pc_node *mypc_node;
	DHCPOFFERADDR_LIST_t * list_head = NULL;
	DHCPOFFERADDR_LIST_t * list_current = NULL;
	char hostname[50] = {0};
	char mac[18] = {0};
	char mac_temp[18] = {0};
	int result = 0;
	int result1 = 0;
	int i = 0;
	BOOL first = TRUE;
	struct list_head dhcp_info_list;
	INIT_LIST_HEAD(&dhcp_info_list);

	char eth_lan[NV_NAME_LEN] = {0};
	char usb_lan[NV_NAME_LEN] = {0};
	cfg_get_item("ethlan", eth_lan, sizeof(eth_lan));
	cfg_get_item("usblan", usb_lan, sizeof(usb_lan));

	web_feedback_header(wp);
	(void)websWrite(wp, T("{\"%s\":["), CMD_LAN_STATION_LIST);

	mypc_node = (struct pc_node*)malloc(sizeof(struct pc_node));
	if (!mypc_node) {
		slog(MISC_PRINT, SLOG_ERR, "malloc err");
		(void)websWrite(wp, T("]}"));
		return;
	}

	if (get_dev_list(mypc_node) < 0) {
		(void)websWrite(wp, T("]}"));
		safe_free(mypc_node);
		return;
	}
	slog(MISC_PRINT, SLOG_DEBUG, "num: %d\n", mypc_node->num);

	result1 = zte_get_mac_list_from_lease(&dhcp_info_list);

	//if((-1 == result1) || (-2 == result1) || (-3 == result1) )
	//{
	//	(void)websWrite(wp, T("]}"));
	//	safe_free(dhcp_info_list);
	//	return;
	//}

//	if (0 == result) {  // kw 3 result is 0
		for (i = 0; i < mypc_node->num; i++) {
			memset(mac, 0, sizeof(mac));
			memset(hostname, 0, sizeof(hostname));
			sprintf(mac, "%02X:%02X:%02X:%02X:%02X:%02X", \
			        mypc_node->info[i].mac_addr[0], mypc_node->info[i].mac_addr[1], \
			        mypc_node->info[i].mac_addr[2], mypc_node->info[i].mac_addr[3], \
			        mypc_node->info[i].mac_addr[4], mypc_node->info[i].mac_addr[5]);

			if ((0 != strncmp(mypc_node->info[i].dev_name, eth_lan, strlen(eth_lan) - 1))\
			    && (0 != strncmp(mypc_node->info[i].dev_name, usb_lan, strlen(usb_lan) - 1)))
				continue;

			if (first == FALSE) {
				(void)websWrite(wp, T(","));
			} else {
				first = FALSE;
			}

			list_for_each_entry(list_head, &dhcp_info_list, list) {
				memset(mac_temp, 0, sizeof(mac_temp));
				sprintf(mac_temp, "%02X:%02X:%02X:%02X:%02X:%02X", \
				        list_head->dhcp_info.mac[0], list_head->dhcp_info.mac[1], \
				        list_head->dhcp_info.mac[2], list_head->dhcp_info.mac[3], \
				        list_head->dhcp_info.mac[4], list_head->dhcp_info.mac[5]);

				if (0 == strcmp(mac, mac_temp)) {
					strcpy(hostname, list_head->dhcp_info.host_name);
					break;
				}
			}

			if (strlen(hostname)) {
				(void)websWrite(wp, T("{\"%s\":\"%s\","), HOSTANME, hostname);
			} else {
				(void)websWrite(wp, T("{\"%s\":\"--\","), HOSTANME);
			}
			(void)websWrite(wp, T("\"%s\":\"%s\"}"), MAC_ADDR, mac);
		}
//	}

	free(mypc_node);

	(void)websWrite(wp, T("]}"));
#if 0
	while (NULL != list_head) {
		list_current = list_head;
		list_head = list_head->next;
		safe_free(list_current);
	}
#endif
	free_dhcp_list(&dhcp_info_list);
}

void zte_add_children_device(webs_t wp)
{
	char *mac = NULL;
	char mac_buf[18] = {0};
	struct mac_hostname_info mac_hostname_info;
	int result = 0;
	struct list_head dhcp_info_list;
	DHCPOFFERADDR_LIST_t * list_head = NULL;
	DHCPOFFERADDR_LIST_t * list_current = NULL;
	INIT_LIST_HEAD(&dhcp_info_list);
	mac = websGetVar(wp, T("mac"), T(""));

	if ((!mac) || (strlen(mac) != 17)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}
	strncpy(mac_hostname_info.mac, mac,sizeof(mac_hostname_info.mac)-1);
	memset(mac_hostname_info.hostname, 0, sizeof(mac_hostname_info.hostname));

	//��udhcpd.lease�ļ��ж�ȡ�ն��豸��Ϣ
	//�ҵ�mac��ַ��Ӧ��hostname
	result = zte_get_mac_list_from_lease(&dhcp_info_list);
	if (result < 0) {
		zte_write_result_to_web(wp, FAILURE);
		free_dhcp_list(&dhcp_info_list);
		return;
	}

	if (0 == result) {
		list_for_each_entry(list_head, &dhcp_info_list, list) {
			sprintf(mac_buf, "%02X:%02X:%02X:%02X:%02X:%02X", \
			        list_head->dhcp_info.mac[0], list_head->dhcp_info.mac[1], \
			        list_head->dhcp_info.mac[2], list_head->dhcp_info.mac[3], \
			        list_head->dhcp_info.mac[4], list_head->dhcp_info.mac[5]);

			if (0 == strcmp(mac_hostname_info.mac, mac_buf)) {
				strcpy(mac_hostname_info.hostname, list_head->dhcp_info.host_name);
				break;
			}
			memset(mac_buf, 0, sizeof(mac_buf));
		}
	}

	if (0 == strlen(mac_hostname_info.hostname)) {
		sprintf(mac_hostname_info.hostname, "%s", "--");
	}
#if 0
	while (NULL != list_head) {
		list_current = list_head;
		list_head = list_head->next;
		safe_free(list_current);
	}
#endif
	free_dhcp_list(&dhcp_info_list);

	slog(MISC_PRINT, SLOG_NORMAL, "zte_add_children_device SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_ADD_CHILDREN_DEVICE, sizeof(struct mac_hostname_info), (UCHAR *)&mac_hostname_info, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_add_children_device SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_del_children_device(webs_t wp)
{
	char *mac = NULL;
	mac = websGetVar(wp, T("mac"), T(""));

	if ((!mac) || (strlen(mac) != 17)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	slog(MISC_PRINT, SLOG_NORMAL, "zte_del_children_device SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_DEL_CHILDREN_DEVICE, strlen(mac), (UCHAR *)mac, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_del_children_device SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_add_white_site(webs_t wp)
{
	char *name = NULL;
	char *site = NULL;
	struct white_site_info white_site;

	name = websGetVar(wp, T("name"), T(""));
	site = websGetVar(wp, T("site"), T(""));
	slog(NET_PRINT, SLOG_DEBUG, "zte_add_white_site:name= %s, site = %s\n", name, site);
	if (!name) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}
	if (!site || !strlen(site)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	strncpy(white_site.name, name, sizeof(white_site.name)-1);
	strncpy(white_site.site, site, sizeof(white_site.site)-1);

	slog(MISC_PRINT, SLOG_NORMAL, "zte_add_white_site SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_ADD_WHITE_SITE, sizeof(struct white_site_info), (UCHAR *)&white_site, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_add_white_site SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_remove_white_site(webs_t wp)
{
	char *ids = NULL;
	ids = websGetVar(wp, T("ids"), T(""));

	if (!ids || !strlen(ids)) {
		zte_write_result_to_web(wp, FAILURE);
		return;
	}

	slog(MISC_PRINT, SLOG_NORMAL, "zte_remove_white_site SEND MESSAGE TO MC START"); /*lint !e26*/
	ipc_send_message(MODULE_ID_WEB_CGI, MODULE_ID_MAIN_CTRL, MSG_CMD_NET_REMOVE_WHITE_SITE, strlen(ids), (UCHAR *)ids, 0);
	slog(MISC_PRINT, SLOG_DEBUG, "zte_remove_white_site SEND MESSAGE TO MC END"); /*lint !e26*/
	Sleep(1);
	zte_write_result_to_web(wp, SUCCESS);
}

void zte_get_children_device_list(webs_t wp)
{
	slog(NET_PRINT, SLOG_NORMAL, "zte_get_children_device_list start!\n");
	FILE *chilren_device_file = NULL;
	char line[200] = {0};
	char mac[18] = {0};
	char hostname[150] = {0};

	BOOL first = TRUE;

	char path_conf[50] = {0};
	char path_file[100] = {0};
	cfg_get_item("path_conf", path_conf, sizeof(path_conf));
	sprintf(path_file, "%s/children_device_file", path_conf);

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	(void)websWrite(wp, T("\"%s\":["), DEVICES);
	chilren_device_file = fopen(path_file, "r");
	if (chilren_device_file == NULL) {
		slog(MISC_PRINT, SLOG_ERR, "can not open file children_device_file.");
		goto out;
	}

	while (fgets(line, sizeof(line), chilren_device_file) != NULL) {
		if (first == FALSE) {
			(void)websWrite(wp, T(","));
		} else {
			first = FALSE;
		}

		strncpy(mac, line, sizeof(mac)-1);
		if(strlen(line) - 19 > 0 && strlen(line) - 19 < 150)
			snprintf(hostname,strlen(line) - 18,"%s",line + 18);
		//strncpy(hostname, line + 18, strlen(line) - 19);

		if (strncmp(mac, "", sizeof(mac)) != 0 && strncmp(hostname, "", sizeof(hostname)) != 0) {
			(void)websWrite(wp, T("{\"%s\":\"%s\",\"%s\":\"%s\"}"), HOSTANME, hostname, MAC, mac);
		}

		memset(line, 0, sizeof(line));
		memset(mac, 0, sizeof(mac));
		memset(hostname, 0, sizeof(hostname));

	}
	fclose(chilren_device_file);

out:
	(void)websWrite(wp, T("]"));
	zte_rest_cmd_write_foot(wp);
}

void zte_get_white_site_list(webs_t wp)
{
	slog(NET_PRINT, SLOG_ERR, "zte_get_white_site_list start!\n");
	FILE *white_site_file = NULL;
	char line[600] = {0};
	unsigned int id = 1;
	char name[150] = {0};
	char site[400] = {0};
	unsigned int len = 0;

	BOOL first = TRUE;

	char path_conf[50] = {0};
	char path_file[100] = {0};
	cfg_get_item("path_conf", path_conf, sizeof(path_conf));
	sprintf(path_file, "%s/white_site_file", path_conf);

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	(void)websWrite(wp, T("\"%s\":["), SITELIST);
	white_site_file = fopen(path_file, "r");
	if (white_site_file == NULL) {
		slog(MISC_PRINT, SLOG_ERR, "can not open file white_site_file.");
		goto out;
	}

	while (fgets(line, sizeof(line), white_site_file) != NULL) {
		if (first == FALSE)
			(void)websWrite(wp, T(","));
		else
			first = FALSE;

		int i = 0;
		for (i = 0; i < sizeof(line); i++) {
			if (line[i] == ',') {
				len = i;
				break;
			}
		}
		if(len < sizeof(site) && len > 0)
			snprintf(site,len+1,"%s",line);
		//strncpy(site, line, len);
		//ÿ�ж���һ��'\n'
		int name_len = strlen(line) - (len + 2);
		if(len < 598 && name_len > 0 && name_len < 150)
			snprintf(name,name_len+1,"%s",line + len + 1);
		//strncpy(name, line + len + 1, name_len);
		if (strcmp(site, "") != 0) {
			(void)websWrite(wp, T("{\"%s\":\"%d\",\"%s\":\"%s\",\"%s\":\"%s\"}"), ID, id, NAME, name, SITE, site);
		}

		id = id + 1;
		memset(line, 0, sizeof(line));
		memset(name, 0, sizeof(name));
		memset(site, 0, sizeof(site));
		len = 0;

	}

	fclose(white_site_file);

out:
	(void)websWrite(wp, T("]"));
	zte_rest_cmd_write_foot(wp);
}

//��ȡ������Ϣ
void zte_get_lan_dev_info(webs_t wp)
{
	char_t *dev_name = NULL;
	LAN_INFO_t lan_info;

	slog(MISC_PRINT, SLOG_NORMAL, "zte_get_lan_dev_info enter \n");
	//�Ƚ��ÿ���ʱ��ѿ�ܴ��, else��ƴ�������⣬��ʱ���ṩwebserver���ͻ���
	//�˹�����station_list/wifi_station_list�������ص��������Ż���ͳһ��
#if 1
	unsigned long cur_time;

	cur_time = time_sec();

	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write_int(wp, "poweron_time", cur_time, 0);
	zte_rest_cmd_write_foot(wp);
	slog(MISC_PRINT, SLOG_DEBUG,"zte_get_lan_dev_info cur_time:%lu \n", cur_time);
#else
	dev_name = websGetVar(wp, "dev_name", T(""));
	printf(LOG_INFO, "zte_get_lan_dev_info dev:%s \n", dev_name);
	if ('\0' == (*dev_name)) {
		return;
	}

	if (get_laninfo_byname(dev_name, &lan_info) == -1)
		return;

	printf(LOG_INFO, "zte_get_lan_dev_info mac:%02x:%02x:%02x:%02x:%02x:%02x \n", lan_info.mac[0], lan_info.mac[1], lan_info.mac[2], lan_info.mac[3], lan_info.mac[4], lan_info.mac[5]);
	printf(LOG_INFO, "zte_get_lan_dev_info ip:%lu, host:%s, dev:%s, remain:%d, last:%d \n", lan_info.ip, lan_info.host_name, lan_info.dev_name, lan_info.time_remain, lan_info.time_last);
	web_feedback_header(wp);
	zte_rest_cmd_write_head(wp);
	zte_rest_cmd_write_int(wp, "mac", lan_info.mac, 1);
	zte_rest_cmd_write_int(wp, "ip", lan_info.ip, 1);
	zte_rest_cmd_write_int(wp, "host_name", lan_info.host_name, 1);
	zte_rest_cmd_write_int(wp, "dev_name", lan_info.dev_name, 1);
	zte_rest_cmd_write_int(wp, "time_remain", lan_info.time_remain, 1);
	zte_rest_cmd_write_int(wp, "time_last", lan_info.time_last, 0);
	zte_rest_cmd_write_foot(wp);
#endif
}

#ifdef ZXIC_ONELINK_TEST
/*
 * 根据 IP 地址获取 MAC 地址
 */
char* zte_get_mac_from_ip(char *ip_address)
{
    struct list_head dhcp_info_list;
    DHCPOFFERADDR_LIST_t *list_head = NULL;
    char mac_address[20] = {0};
    struct in_addr addr;
    int result = 0;

    INIT_LIST_HEAD(&dhcp_info_list);

    result = zte_get_mac_list_from_lease(&dhcp_info_list);
    if (result < 0) {
        return NULL;
    }

    inet_aton(ip_address, &addr);

    list_for_each_entry(list_head, &dhcp_info_list, list) {
        if (list_head->dhcp_info.ip == addr.s_addr) {
            sprintf(mac_address, "%02X:%02X:%02X:%02X:%02X:%02X",
                    list_head->dhcp_info.mac[0], list_head->dhcp_info.mac[1],
                    list_head->dhcp_info.mac[2], list_head->dhcp_info.mac[3],
                    list_head->dhcp_info.mac[4], list_head->dhcp_info.mac[5]);
            free_dhcp_list(&dhcp_info_list);
            return strdup(mac_address);
        }
    }

    free_dhcp_list(&dhcp_info_list);
    return NULL;
}
#endif


