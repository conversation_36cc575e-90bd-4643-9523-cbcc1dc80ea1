#ifndef ZTE_WEB_NET_WAN_H
#define ZTE_WEB_NET_WAN_H

#include "webs.h"

/********************************Constant************************/
#define PROTO_UNKNOWN    0
#define PROTO_TCP        1
#define PROTO_UDP        2
#define PROTO_TCP_UDP    3
#define PROTO_ICMP        4
#define PROTO_NONE        5

#define RULE_MODE_DISABLE    0
#define RULE_MODE_DROP        1
#define RULE_MODE_ACCEPT    2

#define ACTION_DROP        0
#define ACTION_ACCEPT    1

#define NV_FW_EACH_RULE_LEN 150
#define NV_FW_RULE_MAX_LEN 300

#define WEB_PORT_MAX    65535
#define ZTE_FW_IP_ADDR_LEN 16
#define ZTE_FW_MAC_ADDR_LEN 18
#define ZTE_FW_PORT_LEN 6
#define ZTE_FW_FLAG_LEN 2
#define ZTE_FW_SHORT_COMMENT_LEN 16
#define ZTE_FW_MAC_FILTER_MAX_LEN 500
#define ZTE_FW_IP_ADDR_LEN_V6  45

#define WEB_IP_ADDR_MAX_LEN 32
#define WEB_IP_PORT_MAX_LEN 8
#define WEB_MAC_ADDR_MAX_LEN 32
#define WEB_RULE_COMMENT_LEN 32


#define FMT_ECHO_IPTABLES_CMD " 1>/dev/null 2>&1"
#define IPPORT_FILTER_CHAIN      "macipport_filter"
#define VPN_CHAIN                        "vpn_filter"
#define PORT_FORWARD_CHAIN    "port_forward"

#define WEB_FILTER_CHAIN	"web_filter"
#define DMZ_CHAIN			"DMZ"

/* Same as the file "linux/netfilter_ipv4/ipt_webstr.h" */
#define BLK_JAVA                 0x01
#define BLK_ACTIVE              0x02
#define BLK_COOKIE              0x04
#define BLK_PROXY                0x08

typedef struct _fw_ipport_filter_type {
	char_t *mac_address;
	char_t *sip_1;
	char_t *sip_2;
	char_t *sprf;
	char_t *sprt;
	char_t *dip_1;
	char_t *dip_2;
	char_t *dprf;
	char_t *dprt;
	char_t *protocol;
	char_t *action_str;
	char_t *comment;
	int sprf_int;
	int sprt_int;
	int dprf_int;
	int dprt_int;
	int proto;
	int action;
} fw_ipport_filter_type;

typedef struct _fw_port_map_type {
	char_t *ip_address;
	char_t *spr;
	char_t *dpr;
	char_t *protocol;
	char_t *comment;
	int spr_int;
	int dpr_int;
	int proto;

} fw_port_map_type;

typedef struct _fw_port_forward_type {
	char_t *pfe;
	char_t *ip_address;
	char_t *prf;
	char_t *prt;
	char_t *protocol;
	char_t *comment;
	int prf_int;
	int prt_int;
	int proto;

} fw_port_forward_type;

extern void zte_goform_url_filter_add_process(webs_t wp);
extern void zte_goform_url_filter_delete_process(webs_t wp);

extern void init_router_web(void);
//static int zte_dhcp_client_list(int eid, webs_t wp, int argc, char_t **argv);

//static void zte_fw_ipport_filter_del_v4(webs_t wp);
//static void zte_fw_ipport_filter_del_v6(webs_t wp);

extern void zte_fw_basic_setting(webs_t wp);
extern void zte_fw_forward_setting(webs_t wp);
extern void zte_fw_sys_security_setting(webs_t wp);
extern void zte_fw_ipport_filter_add(webs_t wp);
extern void zte_fw_ipport_filter_del(webs_t wp);
extern void zte_fw_port_forward_del(webs_t wp);
//static int zte_fw_port_map_check(webs_t wp, fw_port_map_type *fw_port_map);
extern void zte_fw_port_map_add(webs_t wp);
extern void zte_fw_port_map_del(webs_t wp);


//extern int zte_get_fw_rules_num(webs_t wp, char* ruleName);
extern void zte_fw_dmz(webs_t wp);
extern void zte_make_filter_rules_v6(char *buf, int len, char *mac_address,
                                     char *sip_1, char *sip_2, int sprf_int, int sprt_int,
                                     char *dip_1, char *dip_2, int dprf_int, int dprt_int, int proto, int action);
extern void zte_parse_mac_ip_port_filter_rules_v6(char* rules, char* mac_address);
extern void zte_run_mac_ip_port_filter_rules_v6(void);
extern void zte_run_all_filter_rules_v6(void);
//static int changeNthValue(int index, char *value, char delimit, char *result, char *web_list);
//static int deleteNthValue(int index,  char *value, char delimit);
//static int deleteNthValue(int index,  char *value, char delimit);
extern void zte_static_route_list_ini_run(void);
extern void zte_static_route_list_add(char *name, char *private, char *active, char *des_ip, char *subnet_mask, char *gateway, char *metric);
extern void zte_static_route_list_run_one(char *list, char delimit);
extern void zte_static_route_list_del_one(char *list, char delimit);
extern void zte_static_route_list_del(int index, char delimit);
extern int zte_static_route_list_edit_one(int index, char *value, char delimit, char *result, char *web_list);
extern void zte_static_route_list_edit(char *index, char *web_name, char *web_private, char *web_active, char *web_des_ip, char *web_subnet_mask, char *web_gateway, char *web_metric);
extern void zte_fw_port_forward_add(webs_t wp);
//static void zte_fw_ipport_filter_del_v6(webs_t wp);
extern void zte_fw_ipport_filter_del_v4_v6(webs_t wp);
//static void zte_goform_maciplist_add_process(char_t *web_mac_ip_list);

extern void zte_fw_upnp_set(webs_t wp, char_t *path, char_t *query);
extern void zte_qos_list_run(void);
extern void zte_goform_qoslist_add_process(char_t *web_ip, char_t *max_download, char_t *max_upload, char_t *web_comment);
extern void zte_goform_qoslist_del_process(char_t *index);/*lint !e129*/
extern void zte_goform_qoslist_edit_process(char_t *web_ip, char_t *maxdownload, char_t *maxupload, char_t *web_comment, char_t *index); /*lint !e129*/

//==============================ASP Functions================================//

//static int showPortForwardRulesASP(int eid, webs_t wp, int argc, char_t **argv);/* PortForwardRules */
//static int showIPPortFilterRulesASP(int eid, webs_t wp, int argc, char_t **argv);  /* IPPortFilterRules */
//static int getDMZEnableASP(int eid, webs_t wp, int argc, char_t **argv);
//static int showDMZIPAddressASP(int eid, webs_t wp, int argc, char_t **argv);


/* added by liuweipeng for ipv6 firwall 20120315 end */
//==========================Functions to add rules===========================//
//static void zte_fw_ipport_filter_add_v4(webs_t wp);
//static void zte_fw_ipport_filter_add_v6(webs_t wp);
//static int zte_fw_ipport_filter_check(webs_t wp, fw_ipport_filter_type *fw_ipport_filter);
//static int zte_fw_port_forward_check(webs_t wp, fw_port_forward_type *fw_port_forward);
//==========================Functions to clear iptables rules===========================//
//static void zte_iptables_filter_flush(void);
//static void zte_iptables_nat_flush(void);

//======================Functions to make rules=============================//
/* Mac/Ip/Port Filtering Web page */
//static void zte_iptables_basic_setting(void);
//static void zte_iptables_filter_run(void);
//static void zte_iptables_make_filter_rule(char *buf, int len, char *mac_address,
//                                                               char *sip_1, char *sip_2, int sprf_int, int sprt_int,
//                                                               char *dip_1, char *dip_2, int dprf_int, int dprt_int, int proto, int action);
/* Port Forwarding Web page */
//static void zte_iptables_port_forward_run(void);
//static void zte_iptables_make_port_forward_rule(char *buf, int len, char *wan_name,
//                                                                 char *ip_address, int proto, int prf_int, int prt_int);
/* System Security Web page */
/* Run all rules */

//======================Util=============================//
//static int static_deleteNthValueMulti(int index[], int count, char *value, char delimit);
//static int static_getNthValueSafe(int index, char *value, char delimit, char *result, int len);/*lint !e402*/
//static int getNums(char *value, char delimit);
//static int getRuleNums(char *rules);
//static int isMacValid(char *str);
//static int isIpNetmaskValid(char *s);
extern int isIpValid(char *str);
//static int isNumOnly(char *str);
//static int isAllNumAndSlash(char *str);
//static int isOnlyOneSlash(char *str);
/*lint +e402*/


/*************************** wan pppoe ***************************/
extern void zte_goform_set_wan_gatewaymode_PPPOE(webs_t wp);
extern void zte_goform_set_wan_gatewaymode_STATIC(webs_t wp);
extern void zte_goform_set_wan_gatewaymode_DHCP(webs_t wp);
extern void zte_goform_set_wan_gatewaymode_AUTO(webs_t wp);
extern void zte_goform_set_operation_mode(webs_t wp);


#endif



